<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - MICRA飞检数据处理工具箱</title>
    
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            background: linear-gradient(135deg, #0078D4 0%, #106ebe 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        .login-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #0078D4;
            box-shadow: 0 0 0 0.2rem rgba(0, 120, 212, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #0078D4 0%, #106ebe 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 120, 212, 0.3);
        }

        .form-check {
            margin: 1rem 0;
        }

        .form-check-input:checked {
            background-color: #0078D4;
            border-color: #0078D4;
        }

        .alert {
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .login-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }

        .divider span {
            background: white;
            padding: 0 1rem;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .btn-wechat {
            background: linear-gradient(135deg, #07C160 0%, #06AD56 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-weight: 600;
            width: 100%;
            color: white;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .btn-wechat:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(7, 193, 96, 0.3);
            color: white;
        }

        .btn-wechat i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="fas fa-tools fa-2x mb-3"></i>
            <h2>MICRA工具箱</h2>
            <p>飞检数据处理工具箱</p>
        </div>
        
        <div class="login-body">
            <!-- Flash消息 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST">
                {{ form.hidden_tag() }}
                
                <div class="form-floating">
                    {{ form.username(class="form-control", placeholder="用户名或邮箱") }}
                    {{ form.username.label(class="form-label") }}
                    {% if form.username.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.username.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-floating">
                    {{ form.password(class="form-control", placeholder="密码") }}
                    {{ form.password.label(class="form-label") }}
                    {% if form.password.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.password.errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-check">
                    {{ form.remember_me(class="form-check-input") }}
                    {{ form.remember_me.label(class="form-check-label") }}
                </div>

                {{ form.submit(class="btn btn-primary btn-login") }}
            </form>

            <!-- 微信登录分隔线 -->
            <div class="divider">
                <span>或</span>
            </div>

            <!-- 微信登录按钮 -->
            <a href="{{ url_for('auth.wechat_login') }}" class="btn btn-wechat">
                <i class="fab fa-weixin"></i>
                微信快速注册/登录
            </a>
        </div>
        
        <div class="login-footer">
            <small>&copy; 2024 MICRA飞检数据处理工具箱</small>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
