{% extends "base.html" %}

{% block title %}数据上传 - 自查自纠{% endblock %}

{% block page_title %}
<i class="fas fa-upload me-2"></i>数据上传
{% endblock %}

{% block content %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<div class="container-fluid">
    <!-- 文件上传区域 -->
    {% if current_user.has_permission('selfcheck.uploads.create') %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cloud-upload-alt me-2"></i>
                        文件上传
                    </h6>
                </div>
                <div class="card-body">
                    <div class="upload-area border-2 border-dashed border-primary rounded p-4 text-center" 
                         id="uploadArea" 
                         ondrop="handleDrop(event)" 
                         ondragover="handleDragOver(event)" 
                         ondragleave="handleDragLeave(event)">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h5>拖拽文件到此处或点击选择文件</h5>
                            <p class="text-muted mb-3">
                                支持格式：CSV、DMP、DP、BAK<br>
                                单文件大小限制：10MB以下<br>
                                文件夹总大小限制：200MB以下
                            </p>
                            <input type="file" id="fileInput" class="d-none" accept=".csv,.dmp,.dp,.bak" multiple onchange="handleFileSelect(event)">
                            <input type="file" id="folderInput" class="d-none" webkitdirectory directory multiple onchange="handleFolderSelect(event)">
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary" onclick="$('#fileInput').click()">
                                    <i class="fas fa-file me-1"></i>选择文件
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="$('#folderInput').click()">
                                    <i class="fas fa-folder me-1"></i>选择文件夹
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 上传进度 -->
                    <div id="uploadProgress" class="mt-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 style="width: 0%" 
                                 id="progressBar">0%</div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted" id="uploadStatus">准备上传...</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 操作工具栏 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="row g-2">
                                {% if current_user.is_admin %}
                                <div class="col-md-2">
                                    <select class="form-select" id="searchUser">
                                        <option value="">所有用户</option>
                                        <!-- 用户选项通过JavaScript加载 -->
                                    </select>
                                </div>
                                {% endif %}
                                <div class="col-md-{{ '2' if current_user.is_admin else '3' }}">
                                    <input type="text" class="form-control" id="searchFileName" placeholder="文件名">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="searchFileType">
                                        <option value="">所有类型</option>
                                        <option value="csv">CSV</option>
                                        <option value="dmp">DMP</option>
                                        <option value="dp">DP</option>
                                        <option value="bak">BAK</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="searchStatus">
                                        <option value="">所有状态</option>
                                        <option value="pending">待处理</option>
                                        <option value="validating">验证中</option>
                                        <option value="validated">已验证</option>
                                        <option value="failed">失败</option>
                                    </select>
                                </div>
                                <div class="col-md-{{ '4' if current_user.is_admin else '3' }}">
                                    <button type="button" class="btn btn-primary" onclick="searchUploads()">
                                        <i class="fas fa-search me-1"></i>搜索
                                    </button>
                                    <button type="button" class="btn btn-secondary ms-1" onclick="resetSearch()">
                                        <i class="fas fa-undo me-1"></i>重置
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <button type="button" class="btn btn-info" onclick="loadUploads()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 上传记录列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="uploadsTable">
                            <thead class="table-dark">
                                <tr>
                                    {% if current_user.is_admin %}
                                    <th>用户</th>
                                    {% endif %}
                                    <th>文件名</th>
                                    <th>文件类型</th>
                                    <th>文件大小</th>
                                    <th>状态</th>
                                    <th>记录数</th>
                                    <th>上传时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="uploadsTableBody">
                                <!-- 数据通过AJAX加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <nav aria-label="上传记录分页" id="uploadsPagination">
                        <!-- 分页控件通过JavaScript生成 -->
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传详情模态框 -->
<div class="modal fade" id="uploadDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="uploadDetailContent">
                <!-- 详情内容通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 传递服务器端变量到JavaScript -->
<script>
window.APP_CONFIG = {
    isAdmin: {{ 'true' if current_user.is_admin else 'false' }},
    colspanCount: {{ '8' if current_user.is_admin else '7' }},
    hasDeletePermission: {{ 'true' if current_user.has_permission('selfcheck.uploads.delete') else 'false' }},
    hasValidatePermission: {{ 'true' if current_user.has_permission('selfcheck.uploads.validate') else 'false' }}
};
</script>

<script>
let currentPage = 1;
let totalPages = 1;
let userLimits = {
    max_size_mb: 350,
    single_file_max_mb: 10,
    allowed_types: ['csv', 'dmp', 'dp', 'bak']
};

// 页面加载完成后初始化
$(document).ready(function() {
    loadUserLimits();
    loadUploads();

    // 如果是管理员，加载用户列表
    if (window.APP_CONFIG.isAdmin) {
        loadUsers();
    }

    // 阻止默认的拖拽行为
    $(document).on('dragover drop', function(e) {
        e.preventDefault();
    });
});

// 加载用户上传限制
function loadUserLimits() {
    $.get('/selfcheck/api/uploads/limits')
        .done(function(response) {
            if (response.success) {
                userLimits = response.limits;
                updateUploadUI();
            }
        })
        .fail(function() {
            console.warn('获取用户上传限制失败，使用默认值');
        });
}

// 更新上传界面显示
function updateUploadUI() {
    const maxSizeText = userLimits.max_size_mb === -1 ? '无限制' : `${userLimits.max_size_mb}MB以下`;
    const singleFileText = userLimits.single_file_max_mb === -1 ? '无限制' : `${userLimits.single_file_max_mb}MB以下`;

    // 更新界面显示的限制信息
    $('.upload-content p').html(`
        支持格式：${userLimits.allowed_types.map(t => t.toUpperCase()).join('、')}<br>
        单文件大小限制：${singleFileText}<br>
        文件夹总大小限制：${maxSizeText}
    `);
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    $('#uploadArea').addClass('border-success bg-light');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.preventDefault();
    $('#uploadArea').removeClass('border-success bg-light');
}

// 处理文件拖拽放置
function handleDrop(event) {
    event.preventDefault();
    $('#uploadArea').removeClass('border-success bg-light');

    const files = Array.from(event.dataTransfer.files);
    if (files.length > 0) {
        if (files.length === 1) {
            uploadFile(files[0]);
        } else {
            uploadMultipleFiles(files);
        }
    }
}

// 处理文件选择
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    if (files.length > 0) {
        if (files.length === 1) {
            uploadFile(files[0]);
        } else {
            uploadMultipleFiles(files);
        }
    }
}

// 处理文件夹选择
function handleFolderSelect(event) {
    const files = Array.from(event.target.files);
    if (files.length > 0) {
        uploadFolder(files);
    }
}

// 上传文件
function uploadFile(file) {
    // 验证文件类型
    const allowedTypes = userLimits.allowed_types;
    const fileExtension = file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
        showAlert('不支持的文件类型，仅支持: ' + allowedTypes.map(t => t.toUpperCase()).join(', '), 'danger');
        return;
    }

    // 验证单文件大小
    if (userLimits.single_file_max_mb !== -1 && file.size > userLimits.single_file_max_mb * 1024 * 1024) {
        showAlert(`文件大小超过${userLimits.single_file_max_mb}MB限制`, 'danger');
        return;
    }

    // 显示上传进度
    $('#uploadProgress').show();
    $('#progressBar').css('width', '0%').text('0%');
    $('#uploadStatus').text('准备上传...');

    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    // 上传文件
    $.ajax({
        url: '/selfcheck/api/uploads',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRFToken': csrfToken
        },
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    $('#progressBar').css('width', percentComplete + '%').text(percentComplete + '%');
                    $('#uploadStatus').text('上传中...');
                }
            }, false);
            return xhr;
        }
    })
    .done(function(response) {
        $('#progressBar').css('width', '100%').text('100%');
        if (response.success) {
            $('#uploadStatus').text('上传成功');
            showAlert(response.message, 'success');
            loadUploads(currentPage);

            // 如果是管理员，重新加载用户列表
            if (window.APP_CONFIG.isAdmin) {
                loadUsers();
            }

            // 重置文件输入
            $('#fileInput').val('');

            // 3秒后隐藏进度条
            setTimeout(function() {
                $('#uploadProgress').hide();
            }, 3000);
        } else {
            $('#uploadStatus').text('上传失败');
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        $('#uploadStatus').text('上传失败');
        showAlert('文件上传失败', 'danger');
    });
}

// 上传多个文件
function uploadMultipleFiles(files) {
    // 过滤出支持的文件类型
    const allowedTypes = userLimits.allowed_types;
    const validFiles = files.filter(file => {
        const fileExtension = file.name.split('.').pop().toLowerCase();
        return allowedTypes.includes(fileExtension);
    });

    if (validFiles.length === 0) {
        showAlert(`没有支持的文件类型（${allowedTypes.map(t => t.toUpperCase()).join('、')}）`, 'danger');
        return;
    }

    // 计算总文件大小
    const totalSize = validFiles.reduce((sum, file) => sum + file.size, 0);

    // 检查总大小限制
    if (userLimits.max_size_mb !== -1) {
        const maxFolderSize = userLimits.max_size_mb * 1024 * 1024;
        if (totalSize > maxFolderSize) {
            showAlert(`文件总大小超过${userLimits.max_size_mb}MB限制（当前：${formatFileSize(totalSize)}）`, 'danger');
            return;
        }
    }

    // 检查单个文件大小
    if (userLimits.single_file_max_mb !== -1) {
        const oversizedFiles = validFiles.filter(file => file.size > userLimits.single_file_max_mb * 1024 * 1024);
        if (oversizedFiles.length > 0) {
            showAlert(`以下文件超过${userLimits.single_file_max_mb}MB限制：${oversizedFiles.map(f => f.name).join(', ')}`, 'danger');
            return;
        }
    }

    // 显示上传进度
    $('#uploadProgress').show();
    $('#progressBar').css('width', '0%').text('0%');
    $('#uploadStatus').text(`准备上传 ${validFiles.length} 个文件...`);

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    // 创建FormData
    const formData = new FormData();
    validFiles.forEach((file, index) => {
        formData.append('files', file);
    });

    // 添加多文件标识
    formData.append('is_multiple', 'true');

    // 上传多个文件
    $.ajax({
        url: '/selfcheck/api/uploads/folder',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRFToken': csrfToken
        },
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    $('#progressBar').css('width', percentComplete + '%').text(percentComplete + '%');
                    $('#uploadStatus').text('上传中...');
                }
            }, false);
            return xhr;
        }
    })
    .done(function(response) {
        $('#progressBar').css('width', '100%').text('100%');
        if (response.success) {
            $('#uploadStatus').text('多文件上传成功');
            showAlert(`${response.message}，成功上传 ${response.uploaded_count} 个文件`, 'success');
            loadUploads(currentPage);

            // 如果是管理员，重新加载用户列表
            if (window.APP_CONFIG.isAdmin) {
                loadUsers();
            }

            // 重置文件输入
            $('#fileInput').val('');

            // 3秒后隐藏进度条
            setTimeout(function() {
                $('#uploadProgress').hide();
            }, 3000);
        } else {
            $('#uploadStatus').text('多文件上传失败');
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        $('#uploadStatus').text('多文件上传失败');
        showAlert('多文件上传失败', 'danger');
    });
}

// 上传文件夹
function uploadFolder(files) {
    // 过滤出支持的文件类型
    const allowedTypes = userLimits.allowed_types;
    const validFiles = files.filter(file => {
        const fileExtension = file.name.split('.').pop().toLowerCase();
        return allowedTypes.includes(fileExtension);
    });

    if (validFiles.length === 0) {
        showAlert(`文件夹中没有支持的文件类型（${allowedTypes.map(t => t.toUpperCase()).join('、')}）`, 'danger');
        return;
    }

    // 计算总文件大小
    const totalSize = validFiles.reduce((sum, file) => sum + file.size, 0);

    // 检查总大小限制
    if (userLimits.max_size_mb !== -1) {
        const maxFolderSize = userLimits.max_size_mb * 1024 * 1024;
        if (totalSize > maxFolderSize) {
            showAlert(`文件夹总大小超过${userLimits.max_size_mb}MB限制（当前：${formatFileSize(totalSize)}）`, 'danger');
            return;
        }
    }

    // 检查单个文件大小
    if (userLimits.single_file_max_mb !== -1) {
        const oversizedFiles = validFiles.filter(file => file.size > userLimits.single_file_max_mb * 1024 * 1024);
        if (oversizedFiles.length > 0) {
            showAlert(`以下文件超过${userLimits.single_file_max_mb}MB限制：${oversizedFiles.map(f => f.name).join(', ')}`, 'danger');
            return;
        }
    }

    // 显示上传进度
    $('#uploadProgress').show();
    $('#progressBar').css('width', '0%').text('0%');
    $('#uploadStatus').text(`准备上传 ${validFiles.length} 个文件...`);

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    // 创建FormData
    const formData = new FormData();
    validFiles.forEach((file, index) => {
        formData.append('files', file);
    });

    // 添加文件夹标识
    formData.append('is_folder', 'true');
    formData.append('folder_name', validFiles[0].webkitRelativePath.split('/')[0] || 'folder');

    // 上传文件夹
    $.ajax({
        url: '/selfcheck/api/uploads/folder',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRFToken': csrfToken
        },
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    $('#progressBar').css('width', percentComplete + '%').text(percentComplete + '%');
                    $('#uploadStatus').text('上传中...');
                }
            }, false);
            return xhr;
        }
    })
    .done(function(response) {
        $('#progressBar').css('width', '100%').text('100%');
        if (response.success) {
            $('#uploadStatus').text('文件夹上传成功');
            showAlert(`${response.message}，成功上传 ${response.uploaded_count} 个文件`, 'success');
            loadUploads(currentPage);

            // 如果是管理员，重新加载用户列表
            if (window.APP_CONFIG.isAdmin) {
                loadUsers();
            }

            // 重置文件输入
            $('#folderInput').val('');

            // 3秒后隐藏进度条
            setTimeout(function() {
                $('#uploadProgress').hide();
            }, 3000);
        } else {
            $('#uploadStatus').text('文件夹上传失败');
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        $('#uploadStatus').text('文件夹上传失败');
        showAlert('文件夹上传失败', 'danger');
    });
}

// 搜索上传记录
function searchUploads() {
    loadUploads(1);
}

// 重置搜索
function resetSearch() {
    $('#searchFileName').val('');
    $('#searchFileType').val('');
    $('#searchStatus').val('');
    if (window.APP_CONFIG.isAdmin) {
        $('#searchUser').val('');
    }
    loadUploads(1);
}

// 加载上传记录
function loadUploads(page = 1) {
    currentPage = page;

    // 构建查询参数
    const params = new URLSearchParams({
        page: page,
        per_page: 10,
        file_name: $('#searchFileName').val() || '',
        file_type: $('#searchFileType').val() || '',
        status: $('#searchStatus').val() || ''
    });

    // 如果是管理员，添加用户选择参数
    if (window.APP_CONFIG.isAdmin) {
        const selectedUserId = $('#searchUser').val();
        if (selectedUserId) {
            params.append('selected_user_id', selectedUserId);
        }
    }

    // 移除空参数
    for (let [key, value] of [...params]) {
        if (!value) {
            params.delete(key);
        }
    }

    // 发送请求
    $.get('/selfcheck/api/uploads?' + params.toString())
        .done(function(response) {
            if (response.success) {
                renderUploadsTable(response.uploads);
                renderPagination(response.pagination);
            } else {
                showAlert(response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载上传记录失败', 'danger');
        });
}

// 渲染上传记录表格
function renderUploadsTable(uploads) {
    const tbody = $('#uploadsTableBody');
    tbody.empty();

    if (uploads.length === 0) {
        const colspan = window.APP_CONFIG.colspanCount;
        tbody.append(`
            <tr>
                <td colspan="${colspan}" class="text-center text-muted">暂无数据</td>
            </tr>
        `);
        return;
    }

    uploads.forEach(function(upload) {
        const fileSize = formatFileSize(upload.file_size);
        const statusBadge = getStatusBadge(upload.status, upload.message);
        const actions = getActionButtons(upload);

        let rowHtml = '<tr>';

        if (window.APP_CONFIG.isAdmin) {
            rowHtml += `<td><span class="badge bg-info">${upload.real_name || upload.username || '-'}</span></td>`;
        }

        rowHtml += `
            <td>${upload.file_name}</td>
            <td><span class="badge bg-secondary">${upload.file_type.toUpperCase()}</span></td>
            <td>${fileSize}</td>
            <td>${statusBadge}</td>
            <td>${upload.record_count || '-'}</td>
            <td>${formatDateTime(upload.upload_time)}</td>
            <td>${actions}</td>
        </tr>`;

        tbody.append(rowHtml);
    });
}

// 获取状态徽章
function getStatusBadge(status, message) {
    const statusMap = {
        'pending': '<span class="badge bg-warning">待处理</span>',
        'validating': '<span class="badge bg-info">验证中</span>',
        'validated': '<span class="badge bg-success">已验证</span>',
        'importing': '<span class="badge bg-primary">导入中</span>',
        'imported': '<span class="badge bg-success">已导入</span>',
        'import_failed': '<span class="badge bg-danger">导入失败</span>',
        'failed': '<span class="badge bg-danger">失败</span>',
        'validation_failed': '<span class="badge bg-warning">验证失败</span>'
    };

    let badge = statusMap[status] || '<span class="badge bg-secondary">未知</span>';

    // 如果有失败消息，添加提示图标
    if (status === 'failed' || status === 'validation_failed') {
        if (message && message.trim()) {
            badge += ' <i class="fas fa-info-circle text-muted ms-1" title="点击查看详情" style="cursor: pointer;"></i>';
        }
    }

    return badge;
}

// 获取操作按钮
function getActionButtons(upload) {
    let buttons = `
        <button type="button" class="btn btn-outline-info btn-sm" onclick="viewUploadDetail(${upload.id})" title="查看详情">
            <i class="fas fa-eye"></i>
        </button>
    `;

    // 文件验证按钮（支持CSV、DMP、DP、BAK格式）
    if (upload.status === 'pending' && window.APP_CONFIG.hasValidatePermission) {
        const fileType = upload.file_type.toLowerCase();
        const supportedTypes = ['csv', 'dmp', 'dp', 'bak'];

        if (supportedTypes.includes(fileType)) {
            let validateTitle = '';
            let validateIcon = 'fas fa-check-circle';

            switch(fileType) {
                case 'csv':
                    validateTitle = '验证CSV表头';
                    validateIcon = 'fas fa-table';
                    break;
                case 'dmp':
                case 'dp':
                    validateTitle = '验证Oracle导出文件';
                    validateIcon = 'fas fa-database';
                    break;
                case 'bak':
                    validateTitle = '验证SQL Server备份文件';
                    validateIcon = 'fas fa-server';
                    break;
            }

            buttons += `
                <button type="button" class="btn btn-outline-warning btn-sm ms-1" onclick="validateFileStructure(${upload.id})" title="${validateTitle}">
                    <i class="${validateIcon}"></i>
                </button>
            `;
        }
    }

    // 添加数据导入按钮
    if (upload.status === 'pending' || upload.status === 'validated') {
        buttons += `
            <button type="button" class="btn btn-outline-success btn-sm ms-1" onclick="importUploadData(${upload.id})" title="导入数据">
                <i class="fas fa-download"></i>
            </button>
        `;
    }

    // 查看导入数据按钮
    if (upload.status === 'imported') {
        buttons += `
            <button type="button" class="btn btn-outline-primary btn-sm ms-1" onclick="viewImportedData(${upload.id})" title="查看导入数据">
                <i class="fas fa-table"></i>
            </button>
        `;
    }

    // 查看验证详情按钮（验证失败时）
    if ((upload.status === 'failed' || upload.status === 'validation_failed') && upload.message && upload.message.trim()) {
        buttons += `
            <button type="button" class="btn btn-outline-warning btn-sm ms-1" onclick="viewValidationDetails(${upload.id})" title="查看验证详情">
                <i class="fas fa-exclamation-triangle"></i>
            </button>
        `;
    }

    // 查看操作日志按钮
    buttons += `
        <button type="button" class="btn btn-outline-secondary btn-sm ms-1" onclick="viewOperationLogs(${upload.id})" title="查看操作日志">
            <i class="fas fa-history"></i>
        </button>
    `;

    if (window.APP_CONFIG.hasDeletePermission) {
        buttons += `
            <button type="button" class="btn btn-outline-danger btn-sm ms-1" onclick="deleteUpload(${upload.id})" title="删除">
                <i class="fas fa-trash"></i>
            </button>
        `;
    }

    return buttons;
}

// 渲染分页
function renderPagination(pagination) {
    const container = $('#uploadsPagination');
    container.empty();

    if (pagination.pages <= 1) {
        return;
    }

    totalPages = pagination.pages;
    const currentPage = pagination.page;

    let paginationHtml = '<ul class="pagination justify-content-center">';

    // 上一页
    if (currentPage > 1) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadUploads(${currentPage - 1})">上一页</a></li>`;
    } else {
        paginationHtml += '<li class="page-item disabled"><span class="page-link">上一页</span></li>';
    }

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadUploads(${i})">${i}</a></li>`;
        }
    }

    // 下一页
    if (currentPage < totalPages) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadUploads(${currentPage + 1})">下一页</a></li>`;
    } else {
        paginationHtml += '<li class="page-item disabled"><span class="page-link">下一页</span></li>';
    }

    paginationHtml += '</ul>';
    container.html(paginationHtml);
}

// 查看上传详情
function viewUploadDetail(uploadId) {
    $.get(`/selfcheck/api/uploads/${uploadId}`)
        .done(function(response) {
            if (response.success) {
                const upload = response.upload;
                const detailHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>文件名:</strong> ${upload.file_name}<br>
                            <strong>文件类型:</strong> ${upload.file_type.toUpperCase()}<br>
                            <strong>文件大小:</strong> ${formatFileSize(upload.file_size)}<br>
                            <strong>记录数:</strong> ${upload.record_count || '-'}<br>
                        </div>
                        <div class="col-md-6">
                            <strong>状态:</strong> ${getStatusBadge(upload.status)}<br>
                            <strong>上传时间:</strong> ${formatDateTime(upload.upload_time)}<br>
                            <strong>上传用户:</strong> ${upload.real_name || upload.username || '-'}<br>
                        </div>
                    </div>
                    ${upload.error_message ? `<div class="mt-3"><strong>错误信息:</strong><br><div class="alert alert-danger">${upload.error_message}</div></div>` : ''}
                `;
                $('#uploadDetailContent').html(detailHtml);
                $('#uploadDetailModal').modal('show');
            } else {
                showAlert(response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('获取上传详情失败', 'danger');
        });
}

// 删除上传记录
function deleteUpload(uploadId) {
    if (!confirm('确定要删除这个上传记录吗？')) {
        return;
    }

    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: `/selfcheck/api/uploads/${uploadId}`,
        type: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            loadUploads(currentPage);

            // 如果是管理员，重新加载用户列表
            if (window.APP_CONFIG.isAdmin) {
                loadUsers();
            }
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        showAlert('删除上传记录失败', 'danger');
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 校验CSV文件表头
function validateCSVHeaders(uploadId) {
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    showAlert('正在校验CSV文件表头...', 'info');

    $.ajax({
        url: `/selfcheck/api/uploads/${uploadId}/validate-headers`,
        type: 'POST',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert('CSV文件表头校验通过！', 'success');

            // 显示校验详情
            const detailHtml = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 表头校验通过</h6>
                    <p>文件表头与标准格式完全匹配，可以进行数据导入。</p>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6>文件表头 (${response.total_headers}个字段):</h6>
                        <ul class="list-group list-group-flush">
                            ${response.file_headers.map(header => `<li class="list-group-item py-1">${header}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>匹配字段 (${response.matched_headers}个):</h6>
                        <div class="text-success">
                            <i class="fas fa-check"></i> 所有必需字段都已包含
                        </div>
                        ${response.extra_headers.length > 0 ? `
                            <div class="mt-2">
                                <small class="text-muted">额外字段: ${response.extra_headers.join(', ')}</small>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            $('#uploadDetailContent').html(detailHtml);
            $('#uploadDetailModal').modal('show');

        } else {
            showAlert(`表头校验失败: ${response.error}`, 'danger');

            // 显示校验失败详情
            if (response.missing_headers && response.missing_headers.length > 0) {
                const detailHtml = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> 表头校验失败</h6>
                        <p>CSV文件表头与标准格式不匹配，无法进行数据导入。</p>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>缺少的必需字段:</h6>
                            <ul class="list-group list-group-flush">
                                ${response.missing_headers.map(header => `<li class="list-group-item py-1 text-danger">${header}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>当前文件表头:</h6>
                            <ul class="list-group list-group-flush">
                                ${response.file_headers.map(header => `<li class="list-group-item py-1">${header}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>标准表头格式:</h6>
                        <div class="bg-light p-2 rounded">
                            <small>${response.standard_headers.join(', ')}</small>
                        </div>
                    </div>
                `;

                $('#uploadDetailContent').html(detailHtml);
                $('#uploadDetailModal').modal('show');
            }
        }
    })
    .fail(function() {
        showAlert('表头校验请求失败', 'danger');
    });
}

// 验证文件结构（支持CSV、DMP、DP、BAK格式）
function validateFileStructure(uploadId) {
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    showAlert('正在验证文件结构...', 'info');

    $.ajax({
        url: `/selfcheck/api/uploads/${uploadId}/validate-structure`,
        type: 'POST',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert('文件结构验证通过！', 'success');

            // 根据文件类型显示不同的验证详情
            let detailHtml = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 文件结构验证通过</h6>
                    <p>${response.message}</p>
                </div>
            `;

            // CSV文件显示详细的表头信息
            if (response.table_name) {
                detailHtml += `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>识别的表类型:</h6>
                            <div class="alert alert-info">
                                <strong>${response.table_display_name}</strong> (${response.table_name})
                            </div>
                            <p><strong>匹配度:</strong> ${(response.match_score * 100).toFixed(1)}%</p>
                        </div>
                        <div class="col-md-6">
                            <h6>文件表头 (${response.total_headers || response.file_headers_count}个字段):</h6>
                            <ul class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                ${response.file_headers.map(header => `<li class="list-group-item py-1">${header}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `;

                // 显示匹配字段和缺少字段的对比
                if (response.matched_fields || response.missing_fields) {
                    detailHtml += `
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6 class="text-success">已匹配字段 (${response.matched_fields_count || response.matched_headers}个):</h6>
                                <ul class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                    ${(response.matched_fields || []).map(field => `<li class="list-group-item py-1 text-dark">${field}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="col-md-6">
                                ${response.missing_fields && response.missing_fields.length > 0 ? `
                                    <h6 class="text-danger">缺少的字段 (${response.missing_fields_count || response.missing_fields.length}个):</h6>
                                    <ul class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                        ${response.missing_fields.map(field => `<li class="list-group-item py-1 text-danger">${field}</li>`).join('')}
                                    </ul>
                                ` : `
                                    <h6 class="text-success">✅ 所有必需字段都已包含</h6>
                                `}
                            </div>
                        </div>
                    `;
                }

                if (response.extra_fields && response.extra_fields.length > 0) {
                    detailHtml += `
                        <div class="mt-3">
                            <h6 class="text-info">额外的字段 (${response.extra_fields_count || response.extra_fields.length}个):</h6>
                            <div class="bg-info bg-opacity-10 p-2 rounded">
                                <small>${response.extra_fields.join(', ')}</small>
                            </div>
                        </div>
                    `;
                }
            } else {
                // 非CSV文件显示基本信息
                detailHtml += `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>文件类型:</h6>
                            <p>${response.file_type}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>文件大小:</h6>
                            <p>${formatFileSize(response.file_size)}</p>
                        </div>
                    </div>
                `;

                if (response.note) {
                    detailHtml += `
                        <div class="alert alert-info">
                            <small><i class="fas fa-info-circle"></i> ${response.note}</small>
                        </div>
                    `;
                }
            }

            $('#uploadDetailContent').html(detailHtml);
            $('#uploadDetailModal').modal('show');

        } else {
            showAlert(`文件结构验证失败: ${response.error}`, 'danger');

            // 显示验证失败详情
            if (response.file_headers || response.suggestions) {
                let detailHtml = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> 文件结构验证失败</h6>
                        <p>${response.error}</p>
                    </div>
                `;

                // 如果是表头不匹配的详细错误
                if (response.missing_fields) {
                    detailHtml += `
                        <div class="row">
                            <div class="col-md-4">
                                <h6>当前文件表头 (${response.file_headers_count}个字段):</h6>
                                <ul class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                    ${response.file_headers.map(header => `<li class="list-group-item py-1">${header}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-success">已匹配字段 (${response.matched_fields_count}个):</h6>
                                <ul class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                    ${response.matched_fields.map(field => `<li class="list-group-item py-1 text-dark">${field}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-danger">缺少的必需字段 (${response.missing_fields_count}个):</h6>
                                <ul class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                    ${response.missing_fields.map(field => `<li class="list-group-item py-1 text-danger">${field}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                        <div class="mt-3">
                            <h6>标准表: ${response.table_display_name}</h6>
                            <p><strong>匹配度:</strong> ${(response.match_score * 100).toFixed(1)}% (${response.matched_fields_count}/${response.required_fields_count}个字段匹配)</p>
                        </div>
                    `;
                } else if (response.suggestions) {
                    // 无法识别表类型的错误
                    detailHtml += `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>当前文件表头 (${response.file_headers_count}个字段):</h6>
                                <ul class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                    ${response.file_headers.map(header => `<li class="list-group-item py-1">${header}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>支持的表类型:</h6>
                                <div class="list-group" style="max-height: 200px; overflow-y: auto;">
                                    ${response.suggestions.map(table => `
                                        <div class="list-group-item">
                                            <h6 class="mb-1">${table.display_name}</h6>
                                            <p class="mb-1"><small class="text-muted">表名: ${table.table_name}</small></p>
                                            <small>示例字段: ${table.sample_fields.slice(0, 5).join(', ')}...</small>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    `;
                }

                // 添加修正指导
                if (response.guidance) {
                    detailHtml += `
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb"></i> ${response.guidance.title}</h6>
                                ${response.guidance.missing_fields_guidance ? `<p><strong>${response.guidance.missing_fields_guidance}</strong></p>` : ''}
                                <ol class="mb-0">
                                    ${response.guidance.steps.map(step => `<li>${step}</li>`).join('')}
                                </ol>
                            </div>
                        </div>
                    `;
                }

                $('#uploadDetailContent').html(detailHtml);
                $('#uploadDetailModal').modal('show');
            }
        }

        // 刷新上传列表以更新状态
        loadUploads();
    })
    .fail(function() {
        showAlert('文件结构验证请求失败', 'danger');
    });
}

// 查看验证详情
function viewValidationDetails(uploadId) {
    $.get(`/selfcheck/api/uploads/${uploadId}`)
        .done(function(response) {
            if (response.success && response.upload) {
                const upload = response.upload;

                let detailHtml = `
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> 验证失败详情</h6>
                        <p><strong>文件:</strong> ${upload.file_name}</p>
                        <p><strong>文件类型:</strong> ${upload.file_type.toUpperCase()}</p>
                        <p><strong>上传时间:</strong> ${formatDateTime(upload.created_at)}</p>
                    </div>
                `;

                if (upload.message) {
                    // 尝试解析JSON格式的验证详情
                    try {
                        const validationResult = JSON.parse(upload.message);

                        if (validationResult.table_display_name) {
                            detailHtml += `
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>识别的表类型:</h6>
                                        <div class="alert alert-info">
                                            <strong>${validationResult.table_display_name}</strong>
                                        </div>
                                        <p><strong>匹配度:</strong> ${(validationResult.match_score * 100).toFixed(1)}%</p>
                                        <p><strong>匹配字段:</strong> ${validationResult.matched_fields_count}/${validationResult.required_fields_count}</p>
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="text-success">已匹配字段 (${validationResult.matched_fields_count}个):</h6>
                                        <ul class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                            ${(validationResult.matched_fields || []).map(field => `<li class="list-group-item py-1 text-dark">${field}</li>`).join('')}
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="text-danger">缺少的字段 (${validationResult.missing_fields_count}个):</h6>
                                        <ul class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                            ${(validationResult.missing_fields || []).map(field => `<li class="list-group-item py-1 text-danger">${field}</li>`).join('')}
                                        </ul>
                                    </div>
                                </div>
                            `;

                            if (validationResult.guidance) {
                                detailHtml += `
                                    <div class="mt-3">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-lightbulb"></i> ${validationResult.guidance.title}</h6>
                                            ${validationResult.guidance.missing_fields_guidance ? `<p><strong>${validationResult.guidance.missing_fields_guidance}</strong></p>` : ''}
                                            <ol class="mb-0">
                                                ${validationResult.guidance.steps.map(step => `<li>${step}</li>`).join('')}
                                            </ol>
                                        </div>
                                    </div>
                                `;
                            }
                        } else {
                            // 简单的错误消息
                            detailHtml += `
                                <div class="alert alert-danger">
                                    <h6>错误信息:</h6>
                                    <p>${validationResult.error || upload.message}</p>
                                </div>
                            `;
                        }
                    } catch (e) {
                        // 不是JSON格式，直接显示消息
                        detailHtml += `
                            <div class="alert alert-danger">
                                <h6>错误信息:</h6>
                                <p>${upload.message}</p>
                            </div>
                        `;
                    }
                }

                $('#uploadDetailContent').html(detailHtml);
                $('#uploadDetailModal').modal('show');
            } else {
                showAlert('获取验证详情失败', 'danger');
            }
        })
        .fail(function() {
            showAlert('获取验证详情失败', 'danger');
        });
}

// 查看操作日志
function viewOperationLogs(uploadId) {
    $.get(`/selfcheck/api/uploads/${uploadId}/logs`)
        .done(function(response) {
            if (response.success && response.logs) {
                const logs = response.logs;

                let detailHtml = `
                    <div class="alert alert-info">
                        <h6><i class="fas fa-history"></i> 操作日志</h6>
                        <p>文件ID: ${uploadId} 的所有操作记录</p>
                    </div>
                `;

                if (logs.length > 0) {
                    detailHtml += `
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>操作</th>
                                        <th>操作者</th>
                                        <th>结果</th>
                                        <th>详情</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    logs.forEach(function(log) {
                        const actionMap = {
                            'upload': '文件上传',
                            'validate': '文件验证',
                            'import': '数据导入',
                            'delete': '文件删除',
                            'view': '查看详情'
                        };

                        const actionName = actionMap[log.action] || log.action;
                        const resultBadge = log.details.includes('成功') || log.details.includes('通过') ?
                            '<span class="badge bg-success">成功</span>' :
                            '<span class="badge bg-danger">失败</span>';

                        detailHtml += `
                            <tr>
                                <td><small>${formatDateTime(log.created_at)}</small></td>
                                <td>${actionName}</td>
                                <td>${log.username || log.user_id}</td>
                                <td>${resultBadge}</td>
                                <td><small>${log.details}</small></td>
                            </tr>
                        `;
                    });

                    detailHtml += `
                                </tbody>
                            </table>
                        </div>
                    `;
                } else {
                    detailHtml += `
                        <div class="alert alert-warning">
                            <p>暂无操作日志记录</p>
                        </div>
                    `;
                }

                $('#uploadDetailContent').html(detailHtml);
                $('#uploadDetailModal').modal('show');
            } else {
                showAlert('获取操作日志失败', 'danger');
            }
        })
        .fail(function() {
            showAlert('获取操作日志失败', 'danger');
        });
}

// 导入上传数据
function importUploadData(uploadId) {
    if (!confirm('确定要导入这个文件的数据吗？导入后数据将存储到您的专属Schema中。')) {
        return;
    }

    const csrfToken = $('meta[name=csrf-token]').attr('content');

    showAlert('正在导入数据，请稍候...', 'info');

    $.ajax({
        url: `/selfcheck/api/uploads/${uploadId}/import`,
        type: 'POST',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert(`数据导入成功！共导入 ${response.imported_count} 条记录`, 'success');
            loadUploads(currentPage); // 刷新列表
        } else {
            showAlert(`数据导入失败: ${response.error}`, 'danger');
        }
    })
    .fail(function() {
        showAlert('数据导入请求失败', 'danger');
    });
}

// 查看导入的数据
function viewImportedData(uploadId) {
    $.get(`/selfcheck/api/uploads/${uploadId}/data?page=1&per_page=10`)
        .done(function(response) {
            if (response.success && response.data && response.data.length > 0) {
                const data = response.data;
                const columns = Object.keys(data[0]).filter(key => !['id', 'row_number'].includes(key));

                let tableHtml = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        共 ${response.total} 条记录，当前显示前 ${data.length} 条
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>行号</th>
                                    ${columns.slice(0, 8).map(col => `<th>${col}</th>`).join('')}
                                    ${columns.length > 8 ? '<th>...</th>' : ''}
                                </tr>
                            </thead>
                            <tbody>
                `;

                data.forEach(row => {
                    tableHtml += `
                        <tr>
                            <td>${row.row_number}</td>
                            ${columns.slice(0, 8).map(col => `<td>${row[col] || '-'}</td>`).join('')}
                            ${columns.length > 8 ? '<td>...</td>' : ''}
                        </tr>
                    `;
                });

                tableHtml += `
                            </tbody>
                        </table>
                    </div>
                `;

                $('#uploadDetailContent').html(tableHtml);
                $('#uploadDetailModal').modal('show');
            } else {
                showAlert('暂无导入数据', 'warning');
            }
        })
        .fail(function() {
            showAlert('获取导入数据失败', 'danger');
        });
}

// 加载用户列表（仅管理员）
function loadUsers() {
    if (!window.APP_CONFIG.isAdmin) {
        return;
    }

    $.get('/selfcheck/api/uploads/users')
        .done(function(response) {
            if (response.success) {
                const select = $('#searchUser');
                select.find('option:not(:first)').remove();

                response.users.forEach(function(user) {
                    select.append(`<option value="${user.id}">${user.real_name || user.username}</option>`);
                });
            } else {
                console.error('加载用户列表失败');
            }
        })
        .fail(function() {
            console.error('加载用户列表失败');
        });
}
</script>
{% endblock %}
