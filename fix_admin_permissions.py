#!/usr/bin/env python3
"""
修复admin用户权限
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission

def fix_admin_permissions():
    """修复admin用户权限"""
    app = create_app()
    
    with app.app_context():
        # 查找admin用户
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("❌ 未找到admin用户")
            return
        
        print(f"找到admin用户: {admin_user.username} (ID: {admin_user.id})")
        
        # 查找或创建超级管理员角色
        super_admin_role = Role.query.filter_by(name='超级管理员').first()
        if not super_admin_role:
            print("❌ 未找到超级管理员角色")
            return
        
        print(f"找到超级管理员角色: {super_admin_role.name} (ID: {super_admin_role.id})")
        
        # 为超级管理员角色分配所有权限
        all_permissions = Permission.query.filter_by(is_active=True).all()
        print(f"找到 {len(all_permissions)} 个活跃权限")
        
        # 清空角色现有权限
        super_admin_role.permissions.clear()
        
        # 添加所有权限
        for permission in all_permissions:
            super_admin_role.permissions.append(permission)
        
        print(f"✅ 为超级管理员角色分配了 {len(all_permissions)} 个权限")
        
        # 为admin用户分配超级管理员角色
        if super_admin_role not in admin_user.roles:
            admin_user.roles.append(super_admin_role)
            print("✅ 为admin用户分配了超级管理员角色")
        else:
            print("admin用户已经有超级管理员角色")
        
        # 提交更改
        db.session.commit()
        print("✅ 权限修复完成")
        
        # 验证权限
        print("\n=== 验证admin用户权限 ===")
        admin_permissions = set()
        for role in admin_user.roles:
            for permission in role.permissions:
                admin_permissions.add(permission.code)
        
        print(f"admin用户现在有 {len(admin_permissions)} 个权限")
        
        # 检查关键权限
        key_permissions = [
            'system.permission.view',
            'system.role.view',
            'system.role.edit',
            'system.user.view',
            'selfcheck'
        ]
        
        for perm in key_permissions:
            if perm in admin_permissions:
                print(f"  ✅ {perm}")
            else:
                print(f"  ❌ {perm}")

if __name__ == '__main__':
    fix_admin_permissions()
