{% extends "base.html" %}

{% block title %}自查任务 - 自查自纠{% endblock %}

{% block page_title %}
<i class="fas fa-tasks me-2"></i>自查任务
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 操作工具栏 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="searchTaskName" placeholder="任务名称">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="searchStatus">
                                        <option value="">所有状态</option>
                                        <option value="pending">待执行</option>
                                        <option value="running">执行中</option>
                                        <option value="completed">已完成</option>
                                        <option value="failed">失败</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-primary" onclick="searchTasks()">
                                        <i class="fas fa-search me-1"></i>搜索
                                    </button>
                                    <button type="button" class="btn btn-secondary ms-1" onclick="resetSearch()">
                                        <i class="fas fa-undo me-1"></i>重置
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            {% if current_user.has_permission('selfcheck.tasks.create') %}
                            <button type="button" class="btn btn-success" onclick="showCreateModal()">
                                <i class="fas fa-plus me-1"></i>创建任务
                            </button>
                            {% endif %}
                            <button type="button" class="btn btn-info ms-2" onclick="loadTasks()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="tasksTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>任务名称</th>
                                    <th>数据源</th>
                                    <th>检查方案</th>
                                    <th>状态</th>
                                    <th>进度</th>
                                    <th>错误数</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tasksTableBody">
                                <!-- 数据通过AJAX加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <nav aria-label="任务列表分页" id="tasksPagination">
                        <!-- 分页控件通过JavaScript生成 -->
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建任务模态框 -->
<div class="modal fade" id="createTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建自查任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createTaskForm">
                    <div class="mb-3">
                        <label for="taskName" class="form-label">任务名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="taskName" name="task_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="dataSourceSelect" class="form-label">选择数据文件 <span class="text-danger">*</span></label>
                        <select class="form-select" id="dataSourceSelect" name="data_source" required>
                            <option value="">请选择数据源</option>
                        </select>
                        <div class="form-text">选择用户允许使用的数据源（每个Oracle实例名称，除orcl以外）</div>
                    </div>
                    <div class="mb-3">
                        <label for="schemeSelect" class="form-label">选择检查规则 <span class="text-danger">*</span></label>
                        <select class="form-select" id="schemeSelect" name="scheme_id" required>
                            <option value="">请选择检查方案</option>
                        </select>
                        <div class="form-text">选择方案管理中授权使用的方案</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createTask()">创建任务</button>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任务详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="taskDetailContent">
                <!-- 详情内容通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-info me-2" id="previewReportBtn" onclick="previewReport()" style="display: none;">
                    <i class="fas fa-eye me-1"></i>预览报告
                </button>
                <button type="button" class="btn btn-success me-2" id="downloadReportBtn" onclick="downloadReport()" style="display: none;">
                    <i class="fas fa-download me-1"></i>下载报告
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let totalPages = 1;

// 页面加载完成后初始化
$(document).ready(function() {
    loadTasks();
});

// 加载任务列表
function loadTasks(page = 1) {
    currentPage = page;
    
    const params = new URLSearchParams({
        page: page,
        per_page: 20,
        task_name: $('#searchTaskName').val(),
        status: $('#searchStatus').val()
    });
    
    // 移除空参数
    for (let [key, value] of [...params]) {
        if (!value) params.delete(key);
    }
    
    $.get(`/selfcheck/api/tasks?${params.toString()}`)
        .done(function(response) {
            if (response.success) {
                renderTasksTable(response.tasks);
                renderPagination(response.page, response.pages, response.total);
            } else {
                showAlert('加载任务列表失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载任务列表失败', 'danger');
        });
}

// 渲染任务表格
function renderTasksTable(tasks) {
    const tbody = $('#tasksTableBody');
    tbody.empty();
    
    if (tasks.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted">暂无数据</td>
            </tr>
        `);
        return;
    }
    
    tasks.forEach(task => {
        const statusBadge = getStatusBadge(task.status);
        const progressBar = getProgressBar(task.progress, task.status);
        
        const actions = `
            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewTask(${task.id})" title="查看">
                <i class="fas fa-eye"></i>
            </button>
            {% if current_user.has_permission('selfcheck.tasks.execute') %}
            ${task.status === 'pending' ? `
            <button class="btn btn-sm btn-outline-success me-1" onclick="executeTask(${task.id})" title="执行">
                <i class="fas fa-play"></i>
            </button>
            ` : ''}
            {% endif %}
            {% if current_user.has_permission('selfcheck.tasks.result') %}
            ${task.status === 'completed' ? `
            <button class="btn btn-sm btn-outline-info me-1" onclick="viewResult(${task.id})" title="查看结果">
                <i class="fas fa-chart-bar"></i>
            </button>
            ` : ''}
            {% endif %}
            {% if current_user.has_permission('selfcheck.tasks.delete') %}
            <button class="btn btn-sm btn-outline-danger" onclick="deleteTask(${task.id}, '${task.task_name}')" title="删除">
                <i class="fas fa-trash"></i>
            </button>
            {% endif %}
        `;
        
        // 获取数据源显示名称
        const dataSourceName = getDataSourceDisplayName(task.data_source);

        tbody.append(`
            <tr>
                <td>${task.task_name}</td>
                <td>${dataSourceName}</td>
                <td>${task.scheme_name || '-'}</td>
                <td>${statusBadge}</td>
                <td>${progressBar}</td>
                <td>${task.error_count || 0}</td>
                <td>${formatDateTime(task.created_at)}</td>
                <td>${actions}</td>
            </tr>
        `);
    });
}

// 获取状态徽章
function getStatusBadge(status) {
    const statusMap = {
        'pending': '<span class="badge bg-warning">待执行</span>',
        'running': '<span class="badge bg-info">执行中</span>',
        'completed': '<span class="badge bg-success">已完成</span>',
        'failed': '<span class="badge bg-danger">失败</span>'
    };
    return statusMap[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取进度条
function getProgressBar(progress, status) {
    if (status === 'pending') {
        return '<span class="text-muted">-</span>';
    }
    
    const progressValue = progress || 0;
    let progressClass = 'bg-info';
    
    if (status === 'completed') {
        progressClass = 'bg-success';
    } else if (status === 'failed') {
        progressClass = 'bg-danger';
    }
    
    return `
        <div class="progress" style="height: 20px;">
            <div class="progress-bar ${progressClass}" 
                 role="progressbar" 
                 style="width: ${progressValue}%" 
                 aria-valuenow="${progressValue}" 
                 aria-valuemin="0" 
                 aria-valuemax="100">
                ${progressValue}%
            </div>
        </div>
    `;
}

// 渲染分页
function renderPagination(page, pages, total) {
    const pagination = $('#tasksPagination');
    pagination.empty();
    
    if (pages <= 1) return;
    
    let paginationHtml = `
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                共 ${total} 条记录，第 ${page} / ${pages} 页
            </div>
            <ul class="pagination mb-0">
    `;
    
    // 上一页
    if (page > 1) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadTasks(${page - 1})">上一页</a></li>`;
    }
    
    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(pages, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === page ? 'active' : '';
        paginationHtml += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadTasks(${i})">${i}</a></li>`;
    }
    
    // 下一页
    if (page < pages) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadTasks(${page + 1})">下一页</a></li>`;
    }
    
    paginationHtml += '</ul></div>';
    pagination.html(paginationHtml);
}

// 搜索任务
function searchTasks() {
    loadTasks(1);
}

// 重置搜索
function resetSearch() {
    $('#searchTaskName').val('');
    $('#searchStatus').val('');
    loadTasks(1);
}

// 显示创建任务模态框
function showCreateModal() {
    // 加载数据源列表
    loadDataSources();
    // 加载授权方案列表
    loadAuthorizedSchemes();

    $('#createTaskForm')[0].reset();
    $('#createTaskModal').modal('show');
}

// 加载数据源列表
function loadDataSources() {
    $.get('/selfcheck/api/data-sources')
        .done(function(response) {
            if (response.success) {
                const select = $('#dataSourceSelect');
                select.empty().append('<option value="">请选择数据源</option>');

                response.data_sources.forEach(dataSource => {
                    select.append(`<option value="${dataSource.id}" title="${dataSource.description}">${dataSource.name}</option>`);
                });
            } else {
                showAlert('加载数据源失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载数据源失败', 'danger');
        });
}

// 加载授权方案列表
function loadAuthorizedSchemes() {
    $.get('/selfcheck/api/authorized-schemes')
        .done(function(response) {
            if (response.success) {
                const select = $('#schemeSelect');
                select.empty().append('<option value="">请选择检查方案</option>');

                response.schemes.forEach(scheme => {
                    const ruleCountText = scheme.rule_count > 0 ? ` (${scheme.rule_count}个规则)` : ' (无规则)';
                    select.append(`<option value="${scheme.id}" title="${scheme.description}">${scheme.scheme_name}${ruleCountText}</option>`);
                });
            } else {
                showAlert('加载授权方案失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载授权方案失败', 'danger');
        });
}

// 创建任务
function createTask() {
    const formData = {
        task_name: $('#taskName').val(),
        data_source: $('#dataSourceSelect').val(),
        scheme_id: parseInt($('#schemeSelect').val())
    };

    // 验证必填字段
    if (!formData.task_name || !formData.data_source || !formData.scheme_id) {
        showAlert('请填写所有必填字段', 'warning');
        return;
    }

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/selfcheck/api/tasks',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify(formData)
    })
    .done(function(response) {
        if (response.success) {
            $('#createTaskModal').modal('hide');
            showAlert(response.message, 'success');
            loadTasks(currentPage);
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        showAlert('创建任务失败', 'danger');
    });
}

// 查看任务详情
function viewTask(taskId) {
    // 保存当前查看的任务ID
    window.currentViewTaskId = taskId;

    $.get(`/selfcheck/api/tasks/${taskId}/detail`)
        .done(function(response) {
            if (response.success) {
                const task = response.task;
                const rules = response.rules || [];

                let content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>基本信息</h6>
                            <table class="table table-sm table-bordered">
                                <tr><td class="fw-bold">任务名称</td><td>${task.task_name}</td></tr>
                                <tr><td class="fw-bold">数据源</td><td>${task.data_source_name || task.data_source}</td></tr>
                                <tr><td class="fw-bold">检查方案</td><td>${task.scheme_name}</td></tr>
                                <tr><td class="fw-bold">状态</td><td>${getStatusBadge(task.status)}</td></tr>
                                <tr><td class="fw-bold">总体进度</td><td>${getProgressBar(task.progress, task.status)}</td></tr>
                                <tr><td class="fw-bold">创建时间</td><td>${formatDateTime(task.created_at)}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-line me-2"></i>执行信息</h6>
                            <table class="table table-sm table-bordered">
                                <tr><td class="fw-bold">开始时间</td><td>${formatDateTime(task.start_time)}</td></tr>
                                <tr><td class="fw-bold">结束时间</td><td>${formatDateTime(task.end_time)}</td></tr>
                                <tr><td class="fw-bold">错误数</td><td><span class="badge bg-danger">${task.error_count || 0}</span></td></tr>
                                <tr><td class="fw-bold">警告数</td><td><span class="badge bg-warning">${task.warning_count || 0}</span></td></tr>
                                <tr><td class="fw-bold">规则总数</td><td><span class="badge bg-info">${rules.length}</span></td></tr>
                                <tr><td class="fw-bold">已完成规则</td><td><span class="badge bg-success">${rules.filter(r => r.status === 'completed').length}</span></td></tr>
                            </table>
                        </div>
                    </div>
                `;

                // 添加规则执行详情
                if (rules.length > 0) {
                    content += `
                        <div class="mt-4">
                            <h6><i class="fas fa-list-check me-2"></i>规则执行详情</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>规则名称</th>
                                            <th>状态</th>
                                            <th>进度</th>
                                            <th>结果数量</th>
                                            <th>错误信息</th>
                                            <th>执行时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                    `;

                    rules.forEach(rule => {
                        const ruleStatus = getRuleStatusBadge(rule.status);
                        const ruleProgress = rule.progress || 0;
                        const progressBar = `
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar ${rule.status === 'completed' ? 'bg-success' : rule.status === 'failed' ? 'bg-danger' : 'bg-primary'}"
                                     style="width: ${ruleProgress}%">${ruleProgress}%</div>
                            </div>
                        `;

                        content += `
                            <tr>
                                <td>${rule.rule_name}</td>
                                <td>${ruleStatus}</td>
                                <td>${progressBar}</td>
                                <td>${rule.result_count || 0}</td>
                                <td>${rule.error_message ? `<small class="text-danger">${rule.error_message}</small>` : '-'}</td>
                                <td>${formatDateTime(rule.executed_at)}</td>
                            </tr>
                        `;
                    });

                    content += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    `;
                }

                // 添加错误信息
                if (task.error_message) {
                    content += `
                        <div class="mt-4">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>错误信息</h6>
                            <div class="alert alert-danger">${task.error_message}</div>
                        </div>
                    `;
                }

                $('#taskDetailContent').html(content);

                // 显示/隐藏报告按钮
                if (task.status === 'completed') {
                    $('#previewReportBtn, #downloadReportBtn').show();
                } else {
                    $('#previewReportBtn, #downloadReportBtn').hide();
                }

                $('#taskDetailModal').modal('show');
            } else {
                showAlert(response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('获取任务详情失败', 'danger');
        });
}

// 执行任务
function executeTask(id) {
    if (!confirm('确定要执行这个任务吗？')) return;
    
    showAlert('任务执行功能正在开发中...', 'info');
}

// 查看结果
function viewResult(id) {
    showAlert('结果查看功能正在开发中...', 'info');
}

// 使用base.html中的统一showAlert函数

// 获取数据源显示名称
function getDataSourceDisplayName(dataSourceId) {
    // 这里可以缓存数据源信息，避免重复请求
    if (!dataSourceId) return '-';

    // 简单的显示逻辑，可以根据需要优化
    if (dataSourceId.includes('pool')) {
        return `Oracle-${dataSourceId}`;
    } else if (dataSourceId.includes('pg')) {
        return `PostgreSQL-${dataSourceId}`;
    } else if (dataSourceId.includes('mysql')) {
        return `MySQL-${dataSourceId}`;
    } else if (dataSourceId.includes('mssql')) {
        return `SQLServer-${dataSourceId}`;
    }

    return dataSourceId;
}

// 删除任务
function deleteTask(taskId, taskName) {
    if (!confirm(`确定要删除任务"${taskName}"吗？\n\n删除后将无法恢复！`)) {
        return;
    }

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: `/selfcheck/api/tasks/${taskId}`,
        method: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            loadTasks(currentPage);
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        showAlert('删除任务失败', 'danger');
    });
}

// 获取规则状态徽章
function getRuleStatusBadge(status) {
    const statusMap = {
        'pending': '<span class="badge bg-secondary">待执行</span>',
        'running': '<span class="badge bg-primary">执行中</span>',
        'completed': '<span class="badge bg-success">已完成</span>',
        'failed': '<span class="badge bg-danger">失败</span>'
    };
    return statusMap[status] || '<span class="badge bg-secondary">未知</span>';
}

// 预览报告
function previewReport() {
    if (!window.currentViewTaskId) {
        showAlert('无法获取任务信息', 'danger');
        return;
    }

    // 在新窗口中打开报告预览
    const previewUrl = `/selfcheck/api/tasks/${window.currentViewTaskId}/report/preview`;
    window.open(previewUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

// 下载报告
function downloadReport() {
    if (!window.currentViewTaskId) {
        showAlert('无法获取任务信息', 'danger');
        return;
    }

    // 创建下载链接
    const downloadUrl = `/selfcheck/api/tasks/${window.currentViewTaskId}/report/download`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `自查报告_任务${window.currentViewTaskId}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showAlert('报告下载已开始', 'success');
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}
</script>
{% endblock %}
