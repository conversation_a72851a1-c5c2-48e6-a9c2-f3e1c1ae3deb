#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志配置
"""

import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),  # 输出到标准输出
    ]
)

# 创建logger
logger = logging.getLogger(__name__)

def test_logging():
    """测试日志输出"""
    print("=== 测试日志输出 ===")
    
    logger.debug("这是DEBUG级别的日志")
    logger.info("这是INFO级别的日志")
    logger.warning("这是WARNING级别的日志")
    logger.error("这是ERROR级别的日志")
    logger.critical("这是CRITICAL级别的日志")
    
    print("=== 日志测试完成 ===")

if __name__ == '__main__':
    test_logging()
