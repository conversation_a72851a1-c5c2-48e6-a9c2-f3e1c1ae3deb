#!/usr/bin/env python3
"""
测试数据库连接和规则表数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import db_manager, execute_rules_query
import pandas as pd

def test_database_connection():
    """测试数据库连接"""
    try:
        with db_manager.get_connection() as conn:
            print("✅ 数据库连接成功")
            
            # 测试基本查询
            test_query = "SELECT 1 as test_col FROM DUAL"
            df = execute_rules_query(conn, test_query)
            print(f"✅ 基本查询成功: {df}")
            
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_rules_tables():
    """测试规则相关表"""
    try:
        with db_manager.get_connection() as conn:
            # 检查飞检规则知识库表
            print("\n检查 飞检规则知识库 表:")
            try:
                query1 = "SELECT COUNT(*) as count FROM 飞检规则知识库"
                df1 = execute_rules_query(conn, query1)
                count1 = df1['COUNT'].iloc[0] if not df1.empty else 0
                print(f"✅ 飞检规则知识库表存在，记录数: {count1}")
                
                if count1 > 0:
                    # 查看表结构
                    query_structure = "SELECT * FROM 飞检规则知识库 WHERE ROWNUM <= 1"
                    df_structure = execute_rules_query(conn, query_structure)
                    print(f"表字段: {list(df_structure.columns)}")
                    
                    # 查看前几条记录
                    query_sample = "SELECT * FROM 飞检规则知识库 WHERE ROWNUM <= 3"
                    df_sample = execute_rules_query(conn, query_sample)
                    print(f"样本数据:\n{df_sample}")
                
            except Exception as e:
                print(f"❌ 飞检规则知识库表查询失败: {e}")
            
            # 检查规则医保编码对照表
            print("\n检查 规则医保编码对照 表:")
            try:
                query2 = "SELECT COUNT(*) as count FROM 规则医保编码对照"
                df2 = execute_rules_query(conn, query2)
                count2 = df2['COUNT'].iloc[0] if not df2.empty else 0
                print(f"✅ 规则医保编码对照表存在，记录数: {count2}")
                
                if count2 > 0:
                    # 查看表结构
                    query_structure2 = "SELECT * FROM 规则医保编码对照 WHERE ROWNUM <= 1"
                    df_structure2 = execute_rules_query(conn, query_structure2)
                    print(f"表字段: {list(df_structure2.columns)}")
                
            except Exception as e:
                print(f"❌ 规则医保编码对照表查询失败: {e}")
                
    except Exception as e:
        print(f"❌ 表检查失败: {e}")

def test_rules_service_query():
    """测试RuleService的查询"""
    try:
        with db_manager.get_connection() as conn:
            print("\n测试RuleService查询:")
            
            # 测试get_all_rules的查询
            count_query = """
            SELECT COUNT(DISTINCT a.id) as total
            FROM 飞检规则知识库 a, 规则医保编码对照 b 
            WHERE a.id = b.规则id
            """
            count_df = execute_rules_query(conn, count_query)
            total = count_df['TOTAL'].iloc[0] if not count_df.empty else 0
            print(f"✅ 关联查询总记录数: {total}")
            
            if total > 0:
                # 测试分页查询
                query = """
                SELECT * FROM (
                    SELECT 
                        ROW_NUMBER() OVER (ORDER BY a.id) as rn,
                        b.对照id,
                        a.id as ID,
                        a.id as rule_id,
                        b.序号_S as 序号,
                        行为认定,
                        规则内涵,
                        a.规则名称,
                        适用范围,
                        b.城市,
                        b.规则来源,
                        医保编码1,
                        医保名称1,
                        医保编码2,
                        医保名称2,
                        违规数量,
                        违规金额,   
                        类型,
                        规则类型,
                        排除诊断,
                        排除科室,
                        包含诊断,
                        包含科室,
                        时间类型,
                        物价编码,
                        备注,
                        性别,
                        用途
                    FROM 飞检规则知识库 a, 规则医保编码对照 b 
                    WHERE a.id = b.规则id
                ) WHERE rn > 0 AND rn <= 5
                """
                
                df = execute_rules_query(conn, query)
                print(f"✅ 分页查询成功，返回 {len(df)} 条记录")
                if not df.empty:
                    print(f"字段列表: {list(df.columns)}")
                    print(f"第一条记录: {df.iloc[0].to_dict()}")
            
    except Exception as e:
        print(f"❌ RuleService查询测试失败: {e}")

if __name__ == "__main__":
    print("开始数据库连接测试...")
    
    if test_database_connection():
        test_rules_tables()
        test_rules_service_query()
    else:
        print("数据库连接失败，无法继续测试")