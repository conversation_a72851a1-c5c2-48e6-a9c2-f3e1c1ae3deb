#!/usr/bin/env python3
"""
测试Gemini API调用
"""

import requests
import json
from app.config.gemini_config import GEMINI_API_KEY, GEMINI_API_URL

def test_gemini_api():
    """测试Gemini API调用"""
    
    print("=== 测试Gemini API调用 ===")
    print(f"API URL: {GEMINI_API_URL}")
    print(f"API Key: {GEMINI_API_KEY[:10]}...")
    
    # 测试数据
    test_data = {
        "contents": [
            {
                "parts": [
                    {
                        "text": "请分析以下医保规则文本，提取规则信息：\n\n严禁重复开具同一检查项目，违反医保基金管理规定。开展心电监护时，重复收取动态血压监测或动态心电图监测费用。"
                    }
                ]
            }
        ]
    }
    
    headers = {
        'Content-Type': 'application/json',
    }
    
    try:
        print("\n发送API请求...")
        response = requests.post(
            f"{GEMINI_API_URL}?key={GEMINI_API_KEY}",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            
            if 'candidates' in result and len(result['candidates']) > 0:
                content = result['candidates'][0]['content']['parts'][0]['text']
                print(f"响应内容: {content[:200]}...")
            else:
                print("响应格式异常:", result)
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
    except Exception as e:
        print(f"❌ 其他错误: {str(e)}")

if __name__ == "__main__":
    test_gemini_api()
