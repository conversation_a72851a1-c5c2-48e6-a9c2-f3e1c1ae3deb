#!/usr/bin/env python3
"""检查selfcheck_tasks表是否存在"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def check_tasks_table():
    """检查任务表是否存在"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            # 检查表是否存在
            table_query = """
            SELECT table_name 
            FROM user_tables 
            WHERE table_name = 'SELFCHECK_TASKS'
            """
            table_result = db_manager.execute_query(table_query)
            
            if table_result:
                print("✅ selfcheck_tasks表存在")
                
                # 检查表结构
                structure_query = """
                SELECT column_name, data_type, nullable, data_default
                FROM user_tab_columns 
                WHERE table_name = 'SELFCHECK_TASKS'
                ORDER BY column_id
                """
                columns = db_manager.execute_query(structure_query)
                
                print("\n表结构:")
                for col in columns:
                    nullable = "NULL" if col['nullable'] == 'Y' else "NOT NULL"
                    default = f" DEFAULT {col['data_default']}" if col['data_default'] else ""
                    print(f"  {col['column_name']}: {col['data_type']} {nullable}{default}")
                
                # 检查序列是否存在
                seq_query = """
                SELECT sequence_name 
                FROM user_sequences 
                WHERE sequence_name = 'SELFCHECK_TASKS_SEQ'
                """
                seq_result = db_manager.execute_query(seq_query)
                
                if seq_result:
                    print("\n✅ selfcheck_tasks_seq序列存在")
                else:
                    print("\n❌ selfcheck_tasks_seq序列不存在")
                    
            else:
                print("❌ selfcheck_tasks表不存在")
                print("\n需要创建表和序列...")
                
                # 创建序列
                create_seq_sql = """
                CREATE SEQUENCE selfcheck_tasks_seq
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                """
                
                # 创建表
                create_table_sql = """
                CREATE TABLE selfcheck_tasks (
                    id NUMBER PRIMARY KEY,
                    task_name VARCHAR2(200) NOT NULL,
                    upload_id NUMBER NOT NULL,
                    rule_id NUMBER NOT NULL,
                    description CLOB,
                    user_id NUMBER NOT NULL,
                    status VARCHAR2(50) DEFAULT 'pending',
                    progress NUMBER DEFAULT 0,
                    error_count NUMBER DEFAULT 0,
                    result_data CLOB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT fk_tasks_upload FOREIGN KEY (upload_id) REFERENCES selfcheck_uploads(id),
                    CONSTRAINT fk_tasks_rule FOREIGN KEY (rule_id) REFERENCES selfcheck_rules(id),
                    CONSTRAINT fk_tasks_user FOREIGN KEY (user_id) REFERENCES users(id)
                )
                """
                
                try:
                    print("创建序列...")
                    db_manager.execute_update(create_seq_sql)
                    print("✅ 序列创建成功")
                    
                    print("创建表...")
                    db_manager.execute_update(create_table_sql)
                    print("✅ 表创建成功")
                    
                except Exception as e:
                    print(f"❌ 创建失败: {str(e)}")
                    
        except Exception as e:
            print(f"❌ 检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_tasks_table()
