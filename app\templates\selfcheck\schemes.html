{% extends "base.html" %}

{% block title %}方案管理 - 自查自纠 - MICRA飞检数据处理工具箱{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">


            <div class="card">
                <div class="card-header bg-info text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="fas fa-layer-group me-2"></i>
                                方案管理
                            </h5>
                        </div>

                    </div>
                </div>
                <div class="card-body">
                    <!-- 操作按钮 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            {% if current_user.has_permission('selfcheck.schemes.create') %}
                            <button type="button" class="btn btn-primary" onclick="showCreateSchemeModal()">
                                <i class="fas fa-plus me-1"></i>
                                新建方案
                            </button>
                            {% endif %}

                        </div>
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-4">
                                    <input type="text" class="form-control form-control-sm" id="searchSchemeName" placeholder="方案名称">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select form-select-sm" id="searchStatus">
                                        <option value="">全部状态</option>
                                        <option value="active">启用</option>
                                        <option value="inactive">禁用</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <input type="text" class="form-control form-control-sm" id="searchCreatedBy" placeholder="创建人">
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="searchSchemes()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 方案列表 -->
                    <div class="table-responsive">
                        <table id="schemesTable" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>方案名称</th>
                                    <th width="80">规则数</th>
                                    <th width="80">状态</th>
                                    <th width="100">创建人</th>
                                    <th width="150">创建时间</th>
                                    <th width="200">操作</th>
                                </tr>
                            </thead>
                            <tbody id="schemesTableBody">
                                <!-- 数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div id="paginationInfo" class="text-muted"></div>
                        </div>
                        <div class="col-md-6">
                            <nav>
                                <ul id="pagination" class="pagination pagination-sm justify-content-end">
                                    <!-- 分页按钮将通过JavaScript生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建方案模态框 -->
<div class="modal fade" id="createSchemeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    新建方案
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createSchemeForm">
                    <div class="mb-3">
                        <label for="schemeName" class="form-label">方案名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="schemeName" required>
                        <div class="form-text">请输入唯一的方案名称</div>
                    </div>
                    <div class="mb-3">
                        <label for="schemeDescription" class="form-label">方案描述</label>
                        <textarea class="form-control" id="schemeDescription" rows="3" placeholder="请描述该方案的用途和包含的检查内容"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createScheme()">
                    <i class="fas fa-save me-1"></i>
                    创建方案
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑方案模态框 -->
<div class="modal fade" id="editSchemeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    编辑方案
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editSchemeForm">
                    <input type="hidden" id="editSchemeId">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="editSchemeName" class="form-label">方案名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="editSchemeName" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="editSchemeStatus" class="form-label">状态</label>
                                <select class="form-select" id="editSchemeStatus">
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editSchemeDescription" class="form-label">方案描述</label>
                        <textarea class="form-control" id="editSchemeDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateScheme()">
                    <i class="fas fa-save me-1"></i>
                    保存更改
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 方案详情模态框 -->
<div class="modal fade" id="schemeDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    方案详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="schemeDetailContent">
                    <!-- 详情内容将通过JavaScript加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 规则管理模态框 -->
<div class="modal fade" id="ruleManagementModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ruleManagementModalLabel">
                    <i class="fas fa-cogs me-2"></i>
                    管理方案规则
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="currentSchemeId">

                <!-- 规则管理选项卡 -->
                <ul class="nav nav-tabs" id="ruleManagementTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="current-rules-tab" data-bs-toggle="tab" data-bs-target="#current-rules" type="button" role="tab">
                            <i class="fas fa-list me-1"></i>
                            当前规则
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="add-rules-tab" data-bs-toggle="tab" data-bs-target="#add-rules" type="button" role="tab">
                            <i class="fas fa-plus me-1"></i>
                            添加规则
                        </button>
                    </li>
                </ul>

                <div class="tab-content mt-3" id="ruleManagementTabContent">
                    <!-- 当前规则选项卡 -->
                    <div class="tab-pane fade show active" id="current-rules" role="tabpanel">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6 class="text-primary">方案中的规则</h6>
                            </div>
                            <div class="col-md-6 text-end">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshSchemeRules()">
                                    <i class="fas fa-refresh me-1"></i>
                                    刷新
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="60">序号</th>
                                        <th>规则名称</th>
                                        <th width="100">规则类型</th>
                                        <th width="80">城市</th>
                                        <th width="80">状态</th>
                                        <th width="150">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="currentRulesTableBody">
                                    <!-- 数据将通过JavaScript加载 -->
                                </tbody>
                            </table>
                        </div>

                        <div id="currentRulesEmpty" class="text-center text-muted py-4" style="display: none;">
                            <i class="fas fa-inbox fa-2x mb-2"></i><br>
                            该方案暂无规则，请在"添加规则"选项卡中添加规则
                        </div>
                    </div>

                    <!-- 添加规则选项卡 -->
                    <div class="tab-pane fade" id="add-rules" role="tabpanel">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-plus-circle me-2"></i>可添加的规则
                                </h6>

                                <!-- 检索条件区域 -->
                                <div class="card border-light bg-light mb-3">
                                    <div class="card-body py-3">
                                        <div class="row g-3">
                                            <div class="col-md-3">
                                                <label class="form-label text-muted small mb-1">规则名称</label>
                                                <input type="text" class="form-control form-control-sm" id="searchRuleName" placeholder="输入规则名称进行搜索">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label text-muted small mb-1">规则类型</label>
                                                <select class="form-select form-select-sm" id="searchRuleType">
                                                    <option value="">全部类型</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label text-muted small mb-1">规则来源</label>
                                                <select class="form-select form-select-sm" id="searchRuleSource">
                                                    <option value="">全部来源</option>
                                                </select>
                                            </div>
                                            <div class="col-md-1">
                                                <label class="form-label text-muted small mb-1">用途</label>
                                                <select class="form-select form-select-sm" id="searchVisitType">
                                                    <option value="">全部</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label text-muted small mb-1">类型</label>
                                                <select class="form-select form-select-sm" id="searchTypes">
                                                    <option value="">全部</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label text-muted small mb-1">城市</label>
                                                <select class="form-select form-select-sm" id="searchRuleCity">
                                                    <option value="">全部城市</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    可以组合多个条件进行精确搜索
                                                </small>
                                            </div>
                                            <div class="col-md-6 text-end">
                                                <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="clearSearchFilters()">
                                                    <i class="fas fa-times me-1"></i>清空条件
                                                </button>
                                                <button type="button" class="btn btn-primary btn-sm" onclick="searchAvailableRules()">
                                                    <i class="fas fa-search me-1"></i>搜索规则
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 表格上方的分页控制区域 -->
                        <div class="row mb-3 align-items-center">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <label class="form-label me-2 mb-0 small">每页显示:</label>
                                    <select class="form-select form-select-sm" id="availableRulesPerPage" style="width: auto;" onchange="changeAvailableRulesPerPage()">
                                        <option value="10">10条</option>
                                        <option value="20" selected>20条</option>
                                        <option value="50">50条</option>
                                        <option value="100">100条</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <nav aria-label="可添加规则分页">
                                    <ul class="pagination pagination-sm justify-content-center mb-0" id="availableRulesPagination">
                                        <!-- 分页按钮将通过JavaScript生成 -->
                                    </ul>
                                </nav>
                            </div>
                            <div class="col-md-3">
                                <div id="availableRulesPaginationInfo" class="text-end small text-muted"></div>
                            </div>
                        </div>

                        <!-- 批量操作区域 -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <button type="button" class="btn btn-success" onclick="addSelectedRulesToScheme()">
                                    <i class="fas fa-plus me-1"></i>
                                    添加选中规则
                                </button>
                                <small class="text-muted ms-2">
                                    <i class="fas fa-info-circle"></i>
                                    先选择规则，然后点击此按钮批量添加
                                </small>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAllAvailableRules" onchange="toggleSelectAllAvailableRules()">
                                        </th>
                                        <th>规则名称</th>
                                        <th width="80">规则类型</th>
                                        <th width="80">门诊住院</th>
                                        <th width="80">定量定性</th>
                                        <th width="80">城市</th>
                                        <th width="100">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="availableRulesTableBody">
                                    <!-- 数据将通过JavaScript加载 -->
                                </tbody>
                            </table>
                        </div>

                        <div id="availableRulesEmpty" class="text-center text-muted py-4" style="display: none;">
                            <i class="fas fa-search fa-2x mb-2"></i><br>
                            没有找到可添加的规则
                        </div>

                        <!-- 表格下方的分页控制区域 -->
                        <div class="row mt-3 align-items-center">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <label class="form-label me-2 mb-0 small">每页显示:</label>
                                    <select class="form-select form-select-sm" id="availableRulesPerPageBottom" style="width: auto;" onchange="changeAvailableRulesPerPageFromBottom()">
                                        <option value="10">10条</option>
                                        <option value="20" selected>20条</option>
                                        <option value="50">50条</option>
                                        <option value="100">100条</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <nav aria-label="可添加规则分页">
                                    <ul class="pagination pagination-sm justify-content-center mb-0" id="availableRulesPaginationBottom">
                                        <!-- 分页按钮将通过JavaScript生成 -->
                                    </ul>
                                </nav>
                            </div>
                            <div class="col-md-3">
                                <div id="availableRulesPaginationInfoBottom" class="text-end small text-muted"></div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局变量
let currentPage = 1;
let totalPages = 0;
let selectedSchemes = new Set();

// 页面加载完成后初始化
$(document).ready(function() {
    loadSchemes(1);
    
    // 绑定搜索框回车事件
    $('#searchSchemeName, #searchCreatedBy').on('keypress', function(e) {
        if (e.which === 13) {
            searchSchemes();
        }
    });
});

// 加载方案列表
function loadSchemes(page = 1) {
    currentPage = page;
    
    const params = new URLSearchParams({
        page: page,
        per_page: 20
    });
    
    // 添加搜索条件
    const schemeName = $('#searchSchemeName').val().trim();
    const status = $('#searchStatus').val();
    const createdBy = $('#searchCreatedBy').val().trim();
    
    if (schemeName) params.append('scheme_name', schemeName);
    if (status) params.append('status', status);
    if (createdBy) params.append('created_by', createdBy);
    
    fetch(`/selfcheck/api/schemes?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderSchemesTable(data.schemes);
                renderPagination(data.page, data.pages, data.total);
            } else {
                showAlert('加载方案列表失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('加载方案列表失败', 'danger');
        });
}

// 渲染方案表格
function renderSchemesTable(schemes) {
    const tbody = $('#schemesTableBody');
    tbody.empty();
    
    if (schemes.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    暂无方案数据
                </td>
            </tr>
        `);
        return;
    }
    
    schemes.forEach(scheme => {
        const statusBadge = scheme.status === 'active' 
            ? '<span class="badge bg-success">启用</span>'
            : '<span class="badge bg-secondary">禁用</span>';
        
        const createdAt = scheme.created_at ? new Date(scheme.created_at).toLocaleString('zh-CN') : '-';
        
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="scheme-checkbox" value="${scheme.id}" 
                           onchange="updateSelectedSchemes()">
                </td>
                <td>
                    <strong>${escapeHtml(scheme.scheme_name)}</strong>
                    ${scheme.description ? `<br><small class="text-muted">${escapeHtml(scheme.description)}</small>` : ''}
                </td>
                <td class="text-center">
                    <span class="badge bg-info">${scheme.rule_count || 0}</span>
                </td>
                <td>${statusBadge}</td>
                <td>${escapeHtml(scheme.created_by_name || '-')}</td>
                <td><small>${createdAt}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-info" onclick="viewScheme(${scheme.id})" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="manageSchemeRules(${scheme.id})" title="规则管理">
                            <i class="fas fa-cogs"></i>
                        </button>
                        {% if current_user.has_permission('selfcheck.schemes.edit') %}
                        <button type="button" class="btn btn-outline-primary" onclick="editScheme(${scheme.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        {% endif %}
                        {% if current_user.has_permission('selfcheck.schemes.delete') %}
                        <button type="button" class="btn btn-outline-danger" onclick="deleteScheme(${scheme.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                        {% endif %}
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 渲染分页
function renderPagination(page, pages, total) {
    totalPages = pages;
    
    // 更新信息
    $('#paginationInfo').text(`共 ${total} 条记录，第 ${page} 页，共 ${pages} 页`);
    
    // 生成分页按钮
    const pagination = $('#pagination');
    pagination.empty();
    
    if (pages <= 1) return;
    
    // 上一页
    pagination.append(`
        <li class="page-item ${page <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadSchemes(${page - 1})">上一页</a>
        </li>
    `);
    
    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(pages, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        pagination.append(`
            <li class="page-item ${i === page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadSchemes(${i})">${i}</a>
            </li>
        `);
    }
    
    // 下一页
    pagination.append(`
        <li class="page-item ${page >= pages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadSchemes(${page + 1})">下一页</a>
        </li>
    `);
}

// 搜索方案
function searchSchemes() {
    loadSchemes(1);
}

// 显示创建方案模态框
function showCreateSchemeModal() {
    $('#createSchemeForm')[0].reset();
    $('#createSchemeModal').modal('show');
}

// 创建方案
function createScheme() {
    const schemeName = $('#schemeName').val().trim();
    const description = $('#schemeDescription').val().trim();
    
    if (!schemeName) {
        showAlert('请输入方案名称', 'warning');
        return;
    }
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');
    
    fetch('/selfcheck/api/schemes', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            scheme_name: schemeName,
            description: description
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#createSchemeModal').modal('hide');
            showAlert('方案创建成功', 'success');
            loadSchemes(currentPage);
        } else {
            showAlert('创建失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('创建方案失败', 'danger');
    });
}

// 编辑方案
function editScheme(schemeId) {
    // 获取方案详情
    fetch(`/selfcheck/api/schemes/${schemeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const scheme = data.scheme;

                // 填充编辑表单
                $('#editSchemeId').val(scheme.id);
                $('#editSchemeName').val(scheme.scheme_name);
                $('#editSchemeDescription').val(scheme.description || '');
                $('#editSchemeStatus').val(scheme.status || 'active');

                // 显示编辑模态框
                $('#editSchemeModal').modal('show');
            } else {
                showAlert('获取方案详情失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('获取方案详情失败', 'danger');
        });
}

// 更新方案
function updateScheme() {
    const schemeId = $('#editSchemeId').val();
    const schemeName = $('#editSchemeName').val().trim();
    const description = $('#editSchemeDescription').val().trim();
    const status = $('#editSchemeStatus').val();

    if (!schemeName) {
        showAlert('请输入方案名称', 'warning');
        return;
    }

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    fetch(`/selfcheck/api/schemes/${schemeId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            scheme_name: schemeName,
            description: description,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            $('#editSchemeModal').modal('hide');
            showAlert('方案更新成功', 'success');
            loadSchemes(currentPage);
        } else {
            showAlert('更新失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('更新方案失败', 'danger');
    });
}

// 查看方案详情
function viewScheme(schemeId) {
    // 获取方案详情
    fetch(`/selfcheck/api/schemes/${schemeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const scheme = data.scheme;

                // 构建详情内容
                const detailContent = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">基本信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td width="100"><strong>方案名称:</strong></td>
                                    <td>${escapeHtml(scheme.scheme_name)}</td>
                                </tr>
                                <tr>
                                    <td><strong>状态:</strong></td>
                                    <td>
                                        ${scheme.status === 'active'
                                            ? '<span class="badge bg-success">启用</span>'
                                            : '<span class="badge bg-secondary">禁用</span>'
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>创建人:</strong></td>
                                    <td>${escapeHtml(scheme.created_by_name || '-')}</td>
                                </tr>
                                <tr>
                                    <td><strong>创建时间:</strong></td>
                                    <td>${scheme.created_at ? new Date(scheme.created_at).toLocaleString('zh-CN') : '-'}</td>
                                </tr>
                                <tr>
                                    <td><strong>更新时间:</strong></td>
                                    <td>${scheme.updated_at ? new Date(scheme.updated_at).toLocaleString('zh-CN') : '-'}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info">方案描述</h6>
                            <div class="border rounded p-3 bg-light">
                                ${scheme.description ? escapeHtml(scheme.description) : '<em class="text-muted">暂无描述</em>'}
                            </div>

                            <h6 class="text-success mt-3">规则统计</h6>
                            <div id="schemeStatistics" class="row text-center">
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h5 mb-0 text-primary">-</div>
                                        <small class="text-muted">总规则数</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h5 mb-0 text-success">-</div>
                                        <small class="text-muted">启用规则</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h5 mb-0 text-warning">-</div>
                                        <small class="text-muted">禁用规则</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('#schemeDetailContent').html(detailContent);

                // 加载统计信息
                loadSchemeStatistics(schemeId);

                $('#schemeDetailModal').modal('show');
            } else {
                showAlert('获取方案详情失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('获取方案详情失败', 'danger');
        });
}

// 加载方案统计信息
function loadSchemeStatistics(schemeId) {
    fetch(`/selfcheck/api/schemes/${schemeId}/statistics`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.statistics;
                const statisticsDiv = $('#schemeStatistics');

                statisticsDiv.html(`
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <div class="h5 mb-0 text-primary">${stats.total_rules}</div>
                            <small class="text-muted">总规则数</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <div class="h5 mb-0 text-success">${stats.enabled_rules}</div>
                            <small class="text-muted">启用规则</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-2">
                            <div class="h5 mb-0 text-warning">${stats.disabled_rules}</div>
                            <small class="text-muted">禁用规则</small>
                        </div>
                    </div>
                `);
            } else {
                console.error('加载方案统计信息失败:', data.message);
            }
        })
        .catch(error => {
            console.error('加载方案统计信息失败:', error);
        });
}

// 管理方案规则
function manageSchemeRules(schemeId) {
    if (!schemeId) {
        showAlert('无效的方案ID', 'warning');
        return;
    }

    // 获取方案信息
    fetch(`/selfcheck/api/schemes/${schemeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const scheme = data.scheme;

                // 设置规则管理模态框的标题
                $('#ruleManagementModalLabel').text(`管理方案规则 - ${scheme.scheme_name}`);
                $('#currentSchemeId').val(schemeId);

                // 加载方案中的规则
                loadSchemeRules(schemeId);

                // 显示规则管理模态框
                $('#ruleManagementModal').modal('show');
            } else {
                showAlert('获取方案信息失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('获取方案信息失败', 'danger');
        });
}

// 删除方案
function deleteScheme(schemeId) {
    if (!confirm('确定要删除这个方案吗？删除后无法恢复。')) return;
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');
    
    fetch(`/selfcheck/api/schemes/${schemeId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('方案删除成功', 'success');
            loadSchemes(currentPage);
        } else {
            showAlert('删除失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('删除方案失败', 'danger');
    });
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = $('#selectAll').prop('checked');
    $('.scheme-checkbox').prop('checked', selectAll);
    updateSelectedSchemes();
}

// 更新选中的方案
function updateSelectedSchemes() {
    selectedSchemes.clear();
    $('.scheme-checkbox:checked').each(function() {
        selectedSchemes.add(parseInt($(this).val()));
    });
    
    // 更新全选状态
    const totalCheckboxes = $('.scheme-checkbox').length;
    const checkedCheckboxes = $('.scheme-checkbox:checked').length;
    
    $('#selectAll').prop('checked', totalCheckboxes > 0 && checkedCheckboxes === totalCheckboxes);
    $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
}

// 批量启用方案
function batchEnableSchemes() {
    if (selectedSchemes.size === 0) {
        showAlert('请先选择要启用的方案', 'warning');
        return;
    }
    showAlert('批量启用功能正在开发中...', 'info');
}

// 批量禁用方案
function batchDisableSchemes() {
    if (selectedSchemes.size === 0) {
        showAlert('请先选择要禁用的方案', 'warning');
        return;
    }
    showAlert('批量禁用功能正在开发中...', 'info');
}

// 批量删除方案
function batchDeleteSchemes() {
    if (selectedSchemes.size === 0) {
        showAlert('请先选择要删除的方案', 'warning');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedSchemes.size} 个方案吗？删除后无法恢复。`)) return;
    
    showAlert('批量删除功能正在开发中...', 'info');
}

// ==================== 规则管理功能 ====================

// 加载方案中的规则
function loadSchemeRules(schemeId) {
    fetch(`/selfcheck/api/schemes/${schemeId}/rules`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderSchemeRulesTable(data.rules);
            } else {
                showAlert('加载方案规则失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('加载方案规则失败', 'danger');
        });
}

// 渲染方案规则表格
function renderSchemeRulesTable(rules) {
    const tbody = $('#currentRulesTableBody');
    const emptyDiv = $('#currentRulesEmpty');

    tbody.empty();

    if (rules.length === 0) {
        tbody.parent().hide();
        emptyDiv.show();
        return;
    }

    tbody.parent().show();
    emptyDiv.hide();

    rules.forEach((rule, index) => {
        let statusBadge;
        let actions;

        // 检查规则是否已删除
        if (rule.rule_status === 'deleted') {
            statusBadge = '<span class="badge bg-danger">规则已删除</span>';
            actions = `
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary" disabled title="规则已删除">
                        <i class="fas fa-trash"></i> 已删除
                    </button>
                    <button type="button" class="btn btn-outline-danger"
                            onclick="removeRuleFromScheme(${rule.rule_id})"
                            title="从方案中移除">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        } else {
            // 正常规则的状态显示
            statusBadge = rule.is_enabled
                ? '<span class="badge bg-success">启用</span>'
                : '<span class="badge bg-secondary">禁用</span>';

            actions = `
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-${rule.is_enabled ? 'warning' : 'success'}"
                            onclick="toggleRuleStatus(${rule.rule_id}, ${!rule.is_enabled})"
                            title="${rule.is_enabled ? '禁用' : '启用'}">
                        <i class="fas fa-${rule.is_enabled ? 'pause' : 'play'}"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger"
                            onclick="removeRuleFromScheme(${rule.rule_id})"
                            title="移除">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }

        const row = `
            <tr class="${rule.rule_status === 'deleted' ? 'table-warning' : ''}">
                <td>
                    <span class="badge bg-secondary">${index + 1}</span>
                </td>
                <td>
                    <strong>${escapeHtml(rule.rule_name)}</strong>
                    ${rule.description ? `<br><small class="text-muted">${escapeHtml(rule.description)}</small>` : ''}
                    ${rule.rule_status === 'deleted' ? '<br><small class="text-danger"><i class="fas fa-exclamation-triangle"></i> 此规则已被删除</small>' : ''}
                </td>
                <td>${escapeHtml(rule.rule_type || '-')}</td>
                <td>${escapeHtml(rule.city || '-')}</td>
                <td>${statusBadge}</td>
                <td>${actions}</td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 刷新方案规则
function refreshSchemeRules() {
    const schemeId = $('#currentSchemeId').val();
    if (schemeId) {
        loadSchemeRules(schemeId);
    }
}



// 切换规则状态
function toggleRuleStatus(ruleId, isEnabled) {
    const schemeId = $('#currentSchemeId').val();
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    fetch(`/selfcheck/api/schemes/${schemeId}/rules/${ruleId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            is_enabled: isEnabled
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`规则${isEnabled ? '启用' : '禁用'}成功`, 'success');
            refreshSchemeRules();
        } else {
            showAlert(`规则状态更新失败: ${data.message}`, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('规则状态更新失败', 'danger');
    });
}

// 从方案中移除规则
function removeRuleFromScheme(ruleId) {
    if (!confirm('确定要从方案中移除这个规则吗？')) return;

    const schemeId = $('#currentSchemeId').val();
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    fetch(`/selfcheck/api/schemes/${schemeId}/rules/${ruleId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('规则移除成功', 'success');
            refreshSchemeRules();
            // 刷新可用规则列表
            loadAvailableRules();
        } else {
            showAlert('规则移除失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('规则移除失败', 'danger');
    });
}

// ==================== 可用规则管理 ====================

// 加载可用规则
function loadAvailableRules(page = 1) {
    const schemeId = $('#currentSchemeId').val();

    const params = new URLSearchParams();

    // 添加分页参数
    params.append('page', page);
    const perPage = $('#availableRulesPerPage').val() || 20;
    params.append('per_page', perPage);

    // 添加搜索条件
    const ruleName = $('#searchRuleName').val().trim();
    const ruleType = $('#searchRuleType').val();
    const ruleSource = $('#searchRuleSource').val();
    const visitType = $('#searchVisitType').val();
    const types = $('#searchTypes').val();
    const city = $('#searchRuleCity').val();

    if (ruleName) params.append('rule_name', ruleName);
    if (ruleType) params.append('rule_type', ruleType);
    if (ruleSource) params.append('rule_source', ruleSource);
    if (visitType) params.append('visit_type', visitType);
    if (types) params.append('types', types);
    if (city) params.append('city', city);

    fetch(`/selfcheck/api/schemes/${schemeId}/available-rules?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderAvailableRulesTable(data.rules);
                // 如果后端返回分页信息，则渲染分页
                if (data.page && data.pages && data.total) {
                    renderAvailableRulesPagination(data.page, data.pages, data.total, perPage);
                }
            } else {
                showAlert('加载可用规则失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('加载可用规则失败', 'danger');
        });
}

// 渲染可用规则表格
function renderAvailableRulesTable(rules) {
    const tbody = $('#availableRulesTableBody');
    const emptyDiv = $('#availableRulesEmpty');

    tbody.empty();

    if (rules.length === 0) {
        tbody.parent().hide();
        emptyDiv.show();
        return;
    }

    tbody.parent().show();
    emptyDiv.hide();

    rules.forEach(rule => {
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="available-rule-checkbox" value="${rule.id}">
                </td>
                <td>
                    <strong>${escapeHtml(rule.rule_name)}</strong>
                    ${rule.description ? `<br><small class="text-muted">${escapeHtml(rule.description)}</small>` : ''}
                </td>
                <td>${escapeHtml(rule.rule_type || '-')}</td>
                <td>${escapeHtml(rule.visit_type || '-')}</td>
                <td>${escapeHtml(rule.types || '-')}</td>
                <td>${escapeHtml(rule.city || '-')}</td>
                <td>
                    <button type="button" class="btn btn-outline-success btn-sm"
                            onclick="addSingleRuleToScheme(${rule.id})"
                            title="添加到方案">
                        <i class="fas fa-plus"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 搜索可用规则
function searchAvailableRules() {
    loadAvailableRules(1); // 重置到第一页
}

// 清空搜索条件
function clearSearchFilters() {
    $('#searchRuleName').val('');
    $('#searchRuleType').val('');
    $('#searchRuleSource').val('');
    $('#searchVisitType').val('');
    $('#searchTypes').val('');
    $('#searchRuleCity').val('');
    loadAvailableRules(1); // 重新加载第一页
}

// 改变每页显示数量（上方控制）
function changeAvailableRulesPerPage() {
    const perPage = $('#availableRulesPerPage').val();
    $('#availableRulesPerPageBottom').val(perPage); // 同步下方选择器
    loadAvailableRules(1); // 重置到第一页
}

// 改变每页显示数量（下方控制）
function changeAvailableRulesPerPageFromBottom() {
    const perPage = $('#availableRulesPerPageBottom').val();
    $('#availableRulesPerPage').val(perPage); // 同步上方选择器
    loadAvailableRules(1); // 重置到第一页
}

// 渲染可用规则分页
function renderAvailableRulesPagination(page, pages, total, perPage) {
    const paginationInfo = `共 ${total} 条记录，第 ${page} 页，共 ${pages} 页`;

    // 更新上方和下方的分页信息
    $('#availableRulesPaginationInfo').text(paginationInfo);
    $('#availableRulesPaginationInfoBottom').text(paginationInfo);

    // 同步每页显示数量选择器
    $('#availableRulesPerPage').val(perPage);
    $('#availableRulesPerPageBottom').val(perPage);

    // 生成分页按钮HTML
    function generatePaginationHTML() {
        if (pages <= 1) return '';

        let html = '';

        // 上一页
        html += `
            <li class="page-item ${page <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadAvailableRules(${page - 1})">上一页</a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="loadAvailableRules(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        html += `
            <li class="page-item ${page >= pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadAvailableRules(${page + 1})">下一页</a>
            </li>
        `;

        return html;
    }

    // 更新上方和下方的分页按钮
    const paginationHTML = generatePaginationHTML();
    $('#availableRulesPagination').html(paginationHTML);
    $('#availableRulesPaginationBottom').html(paginationHTML);
}

// 全选/取消全选可用规则
function toggleSelectAllAvailableRules() {
    const selectAll = $('#selectAllAvailableRules').prop('checked');
    $('.available-rule-checkbox').prop('checked', selectAll);
}

// 添加单个规则到方案
function addSingleRuleToScheme(ruleId) {
    const schemeId = $('#currentSchemeId').val();
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    fetch(`/selfcheck/api/schemes/${schemeId}/rules`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            rule_id: ruleId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('规则添加成功', 'success');
            refreshSchemeRules();
            loadAvailableRules(); // 刷新可用规则列表
        } else {
            showAlert('规则添加失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('规则添加失败', 'danger');
    });
}

// 添加选中的规则到方案
function addSelectedRulesToScheme() {
    const selectedRules = [];
    $('.available-rule-checkbox:checked').each(function() {
        selectedRules.push(parseInt($(this).val()));
    });

    if (selectedRules.length === 0) {
        showAlert('请先选择要添加的规则', 'warning');
        return;
    }

    const schemeId = $('#currentSchemeId').val();
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    // 批量添加规则
    let addedCount = 0;
    let failedCount = 0;

    const addPromises = selectedRules.map(ruleId => {
        return fetch(`/selfcheck/api/schemes/${schemeId}/rules`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({
                rule_id: ruleId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addedCount++;
            } else {
                failedCount++;
            }
        })
        .catch(error => {
            failedCount++;
        });
    });

    Promise.all(addPromises).then(() => {
        if (addedCount > 0) {
            showAlert(`成功添加 ${addedCount} 个规则${failedCount > 0 ? `，${failedCount} 个失败` : ''}`,
                     failedCount > 0 ? 'warning' : 'success');
            refreshSchemeRules();
            loadAvailableRules();

            // 取消选中
            $('#selectAllAvailableRules').prop('checked', false);
            $('.available-rule-checkbox').prop('checked', false);
        } else {
            showAlert('所有规则添加失败', 'danger');
        }
    });
}

// 加载过滤选项
function loadFilterOptions() {
    fetch('/selfcheck/api/rule-filter-options')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const options = data.options;

                // 填充规则类型选项
                const ruleTypeSelect = $('#searchRuleType');
                ruleTypeSelect.empty().append('<option value="">全部</option>');
                options.rule_types.forEach(type => {
                    ruleTypeSelect.append(`<option value="${escapeHtml(type)}">${escapeHtml(type)}</option>`);
                });

                // 填充规则来源选项
                const ruleSourceSelect = $('#searchRuleSource');
                ruleSourceSelect.empty().append('<option value="">全部来源</option>');
                if (options.rule_sources) {
                    options.rule_sources.forEach(source => {
                        ruleSourceSelect.append(`<option value="${escapeHtml(source)}">${escapeHtml(source)}</option>`);
                    });
                }

                // 填充门诊住院类型选项
                const visitTypeSelect = $('#searchVisitType');
                visitTypeSelect.empty().append('<option value="">全部</option>');
                options.visit_types.forEach(type => {
                    visitTypeSelect.append(`<option value="${escapeHtml(type)}">${escapeHtml(type)}</option>`);
                });

                // 填充定量定性类型选项
                const typesSelect = $('#searchTypes');
                typesSelect.empty().append('<option value="">全部</option>');
                options.types.forEach(type => {
                    typesSelect.append(`<option value="${escapeHtml(type)}">${escapeHtml(type)}</option>`);
                });

                // 填充城市选项
                const citySelect = $('#searchRuleCity');
                citySelect.empty().append('<option value="">全部</option>');
                options.cities.forEach(city => {
                    citySelect.append(`<option value="${escapeHtml(city)}">${escapeHtml(city)}</option>`);
                });
            } else {
                console.error('加载过滤选项失败:', data.message);
            }
        })
        .catch(error => {
            console.error('加载过滤选项失败:', error);
        });
}

// 当规则管理模态框显示时，加载过滤选项和可用规则
$('#ruleManagementModal').on('shown.bs.modal', function() {
    loadFilterOptions();
    loadAvailableRules();
});

// 绑定搜索框回车事件
$(document).ready(function() {
    $('#searchRuleName, #searchRuleCity').on('keypress', function(e) {
        if (e.which === 13) {
            searchAvailableRules();
        }
    });
});

// HTML转义函数
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
{% endblock %}
