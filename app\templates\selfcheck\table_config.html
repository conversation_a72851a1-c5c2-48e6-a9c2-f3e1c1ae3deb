{% extends "base.html" %}

{% block title %}表头配置管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table"></i> 表头配置管理
                    </h5>
                    <div>
                        <button type="button" class="btn btn-success btn-sm" onclick="showAddTableModal()">
                            <i class="fas fa-plus"></i> 添加表格式
                        </button>
                        <button type="button" class="btn btn-info btn-sm" onclick="showImportExcelModal()">
                            <i class="fas fa-file-excel"></i> 从Excel导入
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="exportToExcel()">
                            <i class="fas fa-download"></i> 导出Excel
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 表格式列表 -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>表名称</th>
                                    <th>字段数量</th>
                                    <th>匹配模式</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tableFormatsBody">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加表格式模态框 -->
<div class="modal fade" id="addTableModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加表格式</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTableForm">
                    <div class="mb-3">
                        <label class="form-label">表名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="tableName" required>
                        <div class="form-text">用于标识表格式的唯一名称</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">文件名匹配模式</label>
                        <input type="text" class="form-control" id="tablePatterns" placeholder="用逗号分隔多个模式">
                        <div class="form-text">文件名包含这些关键词时会使用此表格式</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">表头字段 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="tableHeaders" rows="8" required placeholder="请输入表头字段，每行一个字段"></textarea>
                        <div class="form-text">每行输入一个字段名，或者粘贴Excel表头（逗号分隔）</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <input type="text" class="form-control" id="tableDescription" placeholder="可选的表格式描述">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveTableFormat()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- Excel导入模态框 -->
<div class="modal fade" id="importExcelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">从Excel导入表格式</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">选择Excel文件</label>
                    <input type="file" class="form-control" id="excelFile" accept=".xlsx,.xls">
                    <div class="form-text">
                        系统会读取Excel中每个工作表的表头作为表格式配置。<br>
                        工作表名称将作为表名称，第一行将作为表头字段。
                    </div>
                </div>
                <div id="importProgress" style="display: none;">
                    <div class="progress mb-2">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="text-center">
                        <small id="importStatus">准备导入...</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="importFromExcel()">导入</button>
            </div>
        </div>
    </div>
</div>

<!-- 查看表头详情模态框 -->
<div class="modal fade" id="viewHeadersModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">表头详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="headersContent">
                    <!-- 动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    loadTableFormats();
});

// 加载表格式列表
function loadTableFormats() {
    $.get('/selfcheck/api/table-formats')
        .done(function(response) {
            if (response.success) {
                renderTableFormats(response.formats, response.patterns);
            } else {
                showAlert('加载表格式配置失败: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载表格式配置失败', 'danger');
        });
}

// 渲染表格式列表
function renderTableFormats(formats, patterns) {
    const tbody = $('#tableFormatsBody');
    tbody.empty();

    if (Object.keys(formats).length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted">暂无表格式配置</td>
            </tr>
        `);
        return;
    }

    for (const [tableName, headers] of Object.entries(formats)) {
        const tablePatterns = patterns[tableName] || [];
        const patternsText = tablePatterns.length > 0 ? tablePatterns.join(', ') : '-';
        
        tbody.append(`
            <tr>
                <td><strong>${tableName}</strong></td>
                <td><span class="badge bg-info">${headers.length} 个字段</span></td>
                <td><small>${patternsText}</small></td>
                <td>-</td>
                <td>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="viewHeaders('${tableName}')" title="查看表头">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm ms-1" onclick="editTableFormat('${tableName}')" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${tableName !== 'default' ? `
                        <button type="button" class="btn btn-outline-danger btn-sm ms-1" onclick="deleteTableFormat('${tableName}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </td>
            </tr>
        `);
    }
}

// 显示添加表格式模态框
function showAddTableModal() {
    $('#addTableForm')[0].reset();
    $('#addTableModal').modal('show');
}

// 保存表格式
function saveTableFormat() {
    const tableName = $('#tableName').val().trim();
    const patternsText = $('#tablePatterns').val().trim();
    const headersText = $('#tableHeaders').val().trim();
    
    if (!tableName || !headersText) {
        showAlert('请填写必填字段', 'warning');
        return;
    }
    
    // 解析表头字段
    let headers = [];
    if (headersText.includes(',')) {
        // 逗号分隔格式
        headers = headersText.split(',').map(h => h.trim()).filter(h => h);
    } else {
        // 每行一个字段格式
        headers = headersText.split('\n').map(h => h.trim()).filter(h => h);
    }
    
    if (headers.length === 0) {
        showAlert('请至少输入一个表头字段', 'warning');
        return;
    }
    
    // 解析匹配模式
    const patterns = patternsText ? patternsText.split(',').map(p => p.trim()).filter(p => p) : [];
    
    const data = {
        table_name: tableName,
        headers: headers,
        patterns: patterns
    };
    
    const csrfToken = $('meta[name=csrf-token]').attr('content');
    
    $.ajax({
        url: '/selfcheck/api/table-formats',
        type: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify(data)
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            $('#addTableModal').modal('hide');
            loadTableFormats();
        } else {
            showAlert('保存失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('保存表格式失败', 'danger');
    });
}

// 查看表头详情
function viewHeaders(tableName) {
    $.get(`/selfcheck/api/table-formats/${tableName}`)
        .done(function(response) {
            if (response.success) {
                const headers = response.headers;
                const patterns = response.patterns;
                
                let content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>表名称: ${tableName}</h6>
                            <p><strong>字段数量:</strong> ${headers.length}</p>
                            <p><strong>匹配模式:</strong> ${patterns.length > 0 ? patterns.join(', ') : '无'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>创建时间:</strong> ${response.created_at || '-'}</p>
                            <p><strong>更新时间:</strong> ${response.updated_at || '-'}</p>
                        </div>
                    </div>
                    <hr>
                    <h6>表头字段:</h6>
                    <div class="row">
                `;
                
                headers.forEach((header, index) => {
                    content += `
                        <div class="col-md-4 mb-2">
                            <span class="badge bg-light text-dark">${index + 1}. ${header}</span>
                        </div>
                    `;
                });
                
                content += '</div>';
                
                $('#headersContent').html(content);
                $('#viewHeadersModal').modal('show');
            } else {
                showAlert('获取表头详情失败: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('获取表头详情失败', 'danger');
        });
}

// 显示Excel导入模态框
function showImportExcelModal() {
    $('#excelFile').val('');
    $('#importProgress').hide();
    $('#importExcelModal').modal('show');
}

// 从Excel导入
function importFromExcel() {
    const fileInput = $('#excelFile')[0];
    if (!fileInput.files.length) {
        showAlert('请选择Excel文件', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('excel_file', fileInput.files[0]);
    
    const csrfToken = $('meta[name=csrf-token]').attr('content');
    
    $('#importProgress').show();
    $('.progress-bar').css('width', '0%');
    $('#importStatus').text('正在上传文件...');
    
    $.ajax({
        url: '/selfcheck/api/table-formats/import-excel',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRFToken': csrfToken
        },
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    $('.progress-bar').css('width', percentComplete + '%');
                    $('#importStatus').text('上传中...');
                }
            }, false);
            return xhr;
        }
    })
    .done(function(response) {
        $('.progress-bar').css('width', '100%');
        if (response.success) {
            $('#importStatus').text('导入成功！');
            showAlert(response.message, 'success');
            setTimeout(() => {
                $('#importExcelModal').modal('hide');
                loadTableFormats();
            }, 2000);
        } else {
            $('#importStatus').text('导入失败');
            showAlert('导入失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        $('#importStatus').text('导入失败');
        showAlert('Excel导入失败', 'danger');
    });
}

// 导出到Excel
function exportToExcel() {
    window.location.href = '/selfcheck/api/table-formats/export-excel';
}

// 删除表格式
function deleteTableFormat(tableName) {
    if (!confirm(`确定要删除表格式 "${tableName}" 吗？`)) {
        return;
    }
    
    const csrfToken = $('meta[name=csrf-token]').attr('content');
    
    $.ajax({
        url: `/selfcheck/api/table-formats/${tableName}`,
        type: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            loadTableFormats();
        } else {
            showAlert('删除失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('删除表格式失败', 'danger');
    });
}
</script>
{% endblock %}
