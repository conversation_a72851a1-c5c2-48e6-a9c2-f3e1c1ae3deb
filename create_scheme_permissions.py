#!/usr/bin/env python3
"""创建方案管理相关权限"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def create_scheme_permissions():
    """创建方案管理权限"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            print("=== 创建方案管理权限 ===")
            
            # 定义方案管理权限
            scheme_permissions = [
                {
                    'name': 'selfcheck.schemes.view',
                    'description': '查看自查方案'
                },
                {
                    'name': 'selfcheck.schemes.create',
                    'description': '创建自查方案'
                },
                {
                    'name': 'selfcheck.schemes.edit',
                    'description': '编辑自查方案'
                },
                {
                    'name': 'selfcheck.schemes.delete',
                    'description': '删除自查方案'
                },
                {
                    'name': 'selfcheck.schemes.manage_rules',
                    'description': '管理方案规则'
                },
                {
                    'name': 'selfcheck.schemes.import_export',
                    'description': '导入导出方案'
                }
            ]
            
            print("\n1. 创建权限...")
            created_permissions = []
            
            for perm in scheme_permissions:
                # 检查权限是否已存在
                check_query = "SELECT id FROM permissions WHERE name = :name"
                existing = db_manager.execute_query(check_query, {'name': perm['name']})
                
                if existing:
                    print(f"   ✅ 权限已存在: {perm['name']}")
                    created_permissions.append({
                        'id': existing[0]['id'],
                        'name': perm['name']
                    })
                else:
                    try:
                        # 创建权限
                        insert_query = """
                        INSERT INTO permissions (id, name, description, created_at)
                        VALUES (permissions_seq.NEXTVAL, :name, :description, CURRENT_TIMESTAMP)
                        """
                        db_manager.execute_update(insert_query, {
                            'name': perm['name'],
                            'description': perm['description']
                        })
                        
                        # 获取新创建的权限ID
                        id_query = "SELECT permissions_seq.CURRVAL as perm_id FROM dual"
                        id_result = db_manager.execute_query(id_query)
                        perm_id = id_result[0]['perm_id'] if id_result else None
                        
                        print(f"   ✅ 创建权限: {perm['name']} (ID: {perm_id})")
                        created_permissions.append({
                            'id': perm_id,
                            'name': perm['name']
                        })
                        
                    except Exception as e:
                        print(f"   ❌ 创建权限失败 {perm['name']}: {str(e)}")
            
            print(f"\n2. 为管理员角色添加权限...")
            
            # 获取管理员角色ID
            admin_role_query = "SELECT id FROM roles WHERE name = 'admin'"
            admin_role = db_manager.execute_query(admin_role_query)
            
            if not admin_role:
                print("   ❌ 找不到管理员角色")
                return
            
            admin_role_id = admin_role[0]['id']
            print(f"   管理员角色ID: {admin_role_id}")
            
            # 为管理员角色添加所有方案权限
            for perm in created_permissions:
                if not perm['id']:
                    continue
                    
                # 检查是否已经有这个权限
                check_role_perm = """
                SELECT 1 FROM role_permissions 
                WHERE role_id = :role_id AND permission_id = :permission_id
                """
                exists = db_manager.execute_query(check_role_perm, {
                    'role_id': admin_role_id,
                    'permission_id': perm['id']
                })
                
                if exists:
                    print(f"   ✅ 权限已存在: {perm['name']}")
                else:
                    try:
                        # 添加权限到角色
                        insert_role_perm = """
                        INSERT INTO role_permissions (role_id, permission_id, created_at)
                        VALUES (:role_id, :permission_id, CURRENT_TIMESTAMP)
                        """
                        db_manager.execute_update(insert_role_perm, {
                            'role_id': admin_role_id,
                            'permission_id': perm['id']
                        })
                        print(f"   ✅ 添加权限: {perm['name']}")
                        
                    except Exception as e:
                        print(f"   ❌ 添加权限失败 {perm['name']}: {str(e)}")
            
            print("\n3. 验证权限配置...")
            
            # 验证管理员用户的方案权限
            verify_query = """
            SELECT p.name as permission_name, p.description
            FROM users u
            JOIN user_roles ur ON u.id = ur.user_id
            JOIN roles r ON ur.role_id = r.id
            JOIN role_permissions rp ON r.id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            WHERE u.username = 'admin' AND p.name LIKE 'selfcheck.schemes.%'
            ORDER BY p.name
            """
            
            admin_perms = db_manager.execute_query(verify_query)
            
            if admin_perms:
                print("   管理员拥有的方案权限:")
                for perm in admin_perms:
                    print(f"     ✅ {perm['permission_name']}: {perm['description']}")
            else:
                print("   ❌ 管理员没有方案权限")
            
            print("\n🎉 方案管理权限配置完成！")
            
        except Exception as e:
            print(f"❌ 权限配置失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    create_scheme_permissions()
