"""
数据库迁移脚本：添加微信相关字段
"""
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.utils.database import get_connection

logger = logging.getLogger(__name__)

def upgrade():
    """添加微信相关字段"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查字段是否已存在
            cursor.execute("""
                SELECT COUNT(*) FROM user_tab_columns 
                WHERE table_name = 'USERS' AND column_name = 'WECHAT_OPENID'
            """)
            
            if cursor.fetchone()[0] == 0:
                # 添加微信相关字段
                sql_commands = [
                    "ALTER TABLE users ADD wechat_openid VARCHAR2(100)",
                    "ALTER TABLE users ADD wechat_unionid VARCHAR2(100)",
                    "ALTER TABLE users ADD wechat_nickname VARCHAR2(100)",
                    "ALTER TABLE users ADD wechat_avatar VARCHAR2(500)",
                    "ALTER TABLE users ADD wechat_sex NUMBER(1)",
                    "ALTER TABLE users ADD register_type VARCHAR2(20) DEFAULT 'normal'",
                    
                    # 创建索引
                    "CREATE UNIQUE INDEX idx_users_wechat_openid ON users(wechat_openid)",
                    "CREATE INDEX idx_users_wechat_unionid ON users(wechat_unionid)",
                    "CREATE INDEX idx_users_register_type ON users(register_type)",
                    
                    # 添加约束
                    "ALTER TABLE users ADD CONSTRAINT chk_wechat_sex CHECK (wechat_sex IN (0, 1, 2))",
                    "ALTER TABLE users ADD CONSTRAINT chk_register_type CHECK (register_type IN ('normal', 'wechat'))"
                ]
                
                for sql in sql_commands:
                    try:
                        cursor.execute(sql)
                        logger.info(f"执行成功: {sql}")
                    except Exception as e:
                        logger.warning(f"执行失败 (可能已存在): {sql}, 错误: {str(e)}")
                
                conn.commit()
                logger.info("微信字段添加完成")
            else:
                logger.info("微信字段已存在，跳过迁移")
                
    except Exception as e:
        logger.error(f"数据库迁移失败: {str(e)}")
        raise

def downgrade():
    """回滚微信相关字段"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # 删除约束和索引
            drop_commands = [
                "DROP INDEX idx_users_wechat_openid",
                "DROP INDEX idx_users_wechat_unionid", 
                "DROP INDEX idx_users_register_type",
                "ALTER TABLE users DROP CONSTRAINT chk_wechat_sex",
                "ALTER TABLE users DROP CONSTRAINT chk_register_type",
                
                # 删除字段
                "ALTER TABLE users DROP COLUMN wechat_openid",
                "ALTER TABLE users DROP COLUMN wechat_unionid",
                "ALTER TABLE users DROP COLUMN wechat_nickname",
                "ALTER TABLE users DROP COLUMN wechat_avatar",
                "ALTER TABLE users DROP COLUMN wechat_sex",
                "ALTER TABLE users DROP COLUMN register_type"
            ]
            
            for sql in drop_commands:
                try:
                    cursor.execute(sql)
                    logger.info(f"回滚成功: {sql}")
                except Exception as e:
                    logger.warning(f"回滚失败 (可能不存在): {sql}, 错误: {str(e)}")
            
            conn.commit()
            logger.info("微信字段回滚完成")
            
    except Exception as e:
        logger.error(f"数据库回滚失败: {str(e)}")
        raise

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'downgrade':
        downgrade()
    else:
        upgrade()
