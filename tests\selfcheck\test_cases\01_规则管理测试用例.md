# 规则管理模块测试用例

## 测试模块：规则管理
**测试负责人：** 测试工程师  
**测试时间：** 2025-06-08  
**测试版本：** V1.0  

## 1. 功能概述

规则管理模块负责自查规则的创建、编辑、删除、查询和状态管理等功能。

## 2. 测试用例

### TC001 - 规则列表查询
**测试目标：** 验证规则列表的正确显示和分页功能  
**前置条件：** 用户已登录，具有规则查看权限  
**测试步骤：**
1. 访问规则管理页面 `/selfcheck/rules`
2. 验证页面正常加载
3. 验证规则列表显示
4. 验证分页功能
5. 验证排序功能

**预期结果：**
- 页面正常加载，显示规则列表
- 分页控件正常工作
- 排序功能正常
- 数据显示完整（规则名称、类型、状态等）

**测试数据：**
- 至少10条测试规则
- 包含不同状态的规则（active、deleted）

---

### TC002 - 规则搜索功能
**测试目标：** 验证规则搜索和过滤功能  
**前置条件：** 规则列表中有测试数据  
**测试步骤：**
1. 在规则名称搜索框输入关键词
2. 点击搜索按钮
3. 验证搜索结果
4. 测试城市过滤
5. 测试规则类型过滤
6. 测试状态过滤
7. 测试组合搜索

**预期结果：**
- 搜索结果准确匹配关键词
- 过滤条件正确生效
- 组合搜索结果正确
- 无结果时显示友好提示

**测试数据：**
```json
{
  "search_keywords": ["测试规则", "重复收费", "住院"],
  "cities": ["北京", "上海", "广州"],
  "rule_types": ["重复收费", "频次上限", "金额"],
  "statuses": ["active", "deleted"]
}
```

---

### TC003 - 创建新规则
**测试目标：** 验证规则创建功能  
**前置条件：** 用户具有规则创建权限  
**测试步骤：**
1. 点击"新增规则"按钮
2. 填写规则基本信息
3. 填写规则内容和SQL语句
4. 选择规则类型和城市
5. 点击保存
6. 验证规则创建成功

**预期结果：**
- 创建表单正常显示
- 必填字段验证正确
- 规则保存成功
- 返回规则列表并显示新规则

**测试数据：**
```json
{
  "rule_name": "测试规则001",
  "rule_code": "TEST_RULE_001",
  "rule_description": "这是一个测试规则",
  "rule_type": "重复收费",
  "city": "北京",
  "sql_content": "SELECT * FROM test_table WHERE condition = 1"
}
```

---

### TC004 - 编辑规则
**测试目标：** 验证规则编辑功能  
**前置条件：** 存在可编辑的规则  
**测试步骤：**
1. 在规则列表中点击"编辑"按钮
2. 修改规则信息
3. 点击保存
4. 验证修改成功

**预期结果：**
- 编辑表单正确显示原有数据
- 修改保存成功
- 规则信息更新正确

---

### TC005 - 删除规则（软删除）
**测试目标：** 验证规则软删除功能  
**前置条件：** 存在可删除的规则  
**测试步骤：**
1. 选择要删除的规则
2. 点击"删除"按钮
3. 确认删除操作
4. 验证规则状态变为deleted
5. 验证规则在列表中的显示

**预期结果：**
- 删除确认对话框正常显示
- 规则状态正确更新为deleted
- 规则在列表中显示为已删除状态

---

### TC006 - 恢复已删除规则
**测试目标：** 验证规则恢复功能  
**前置条件：** 存在已删除的规则  
**测试步骤：**
1. 在规则列表中找到已删除的规则
2. 点击"恢复"按钮
3. 确认恢复操作
4. 验证规则状态恢复为active

**预期结果：**
- 恢复按钮正确显示
- 规则状态正确恢复
- 规则功能正常可用

---

### TC007 - 批量操作
**测试目标：** 验证规则批量删除功能  
**前置条件：** 存在多个可操作的规则  
**测试步骤：**
1. 选择多个规则（使用复选框）
2. 点击"批量删除"按钮
3. 确认批量删除操作
4. 验证所有选中规则状态更新

**预期结果：**
- 批量选择功能正常
- 批量删除操作成功
- 所有选中规则状态正确更新

---

### TC008 - 权限控制
**测试目标：** 验证规则管理的权限控制  
**前置条件：** 不同权限的测试用户  
**测试步骤：**
1. 使用只有查看权限的用户登录
2. 验证只能查看，不能编辑/删除
3. 使用有编辑权限的用户登录
4. 验证可以编辑但不能删除
5. 使用管理员用户登录
6. 验证所有操作权限

**预期结果：**
- 权限控制正确生效
- 无权限操作被正确阻止
- 权限提示信息友好

---

### TC009 - SQL语句编辑和测试
**测试目标：** 验证SQL编辑器和测试功能  
**前置条件：** 规则包含SQL语句  
**测试步骤：**
1. 打开规则编辑页面
2. 在SQL编辑器中输入SQL语句
3. 点击"测试SQL"按钮
4. 验证SQL语法检查
5. 保存SQL语句

**预期结果：**
- SQL编辑器正常工作
- 语法检查功能正确
- 危险SQL被正确拦截
- SQL保存成功

---

### TC010 - 数据导出
**测试目标：** 验证规则数据导出功能  
**前置条件：** 规则列表有数据  
**测试步骤：**
1. 在规则列表页面点击"导出"按钮
2. 选择导出格式（Excel/CSV）
3. 确认导出操作
4. 验证导出文件

**预期结果：**
- 导出功能正常工作
- 导出文件格式正确
- 导出数据完整准确

## 3. 性能测试用例

### TC011 - 大数据量加载性能
**测试目标：** 验证大量规则数据的加载性能  
**测试数据：** 1000+条规则记录  
**性能指标：** 页面加载时间 < 3秒

### TC012 - 搜索性能测试
**测试目标：** 验证复杂搜索条件的响应性能  
**性能指标：** 搜索响应时间 < 2秒

## 4. 异常测试用例

### TC013 - 网络异常处理
**测试目标：** 验证网络中断时的处理  
**测试步骤：** 模拟网络中断，验证错误提示和重试机制

### TC014 - 数据库异常处理
**测试目标：** 验证数据库连接异常的处理  
**测试步骤：** 模拟数据库异常，验证错误处理

## 5. 兼容性测试用例

### TC015 - 浏览器兼容性
**测试目标：** 验证不同浏览器的兼容性  
**测试范围：** Chrome、Firefox、Edge、Safari

### TC016 - 移动端适配
**测试目标：** 验证移动端显示效果
**测试设备：** 手机、平板

## 6. 自动化测试脚本

对应的自动化测试脚本：`test_scripts/test_rules_management.py`

## 7. 测试数据

测试数据文件：`test_data/sample_rules.json`
