#!/usr/bin/env python3
"""
测试增强AI解析
"""

import sys
sys.path.append('.')

def test_enhanced_ai():
    """测试增强AI解析"""
    try:
        from app.rules.services import ConverterService
        
        # 模拟文件内容
        test_content = [
            {'content': '违法违规使用医保基金负面清单（第一批）', 'type': 'title'},
            {'content': '国家医保局', 'type': 'header'},
            {'content': '1. 严禁重复开具同一检查项目，违反医保基金管理规定', 'type': 'text'},
            {'content': '2. 不得虚假住院治疗，骗取医保基金', 'type': 'text'},
            {'content': '3. 开展心电监护时，重复收取动态血压监测或动态心电图监测费用', 'type': 'text'},
        ]

        print('=== 测试增强AI解析 ===')
        
        # 测试预处理
        processed = ConverterService._preprocess_content(test_content)
        print(f'预处理后内容数量: {len(processed)}')
        for i, content in enumerate(processed):
            print(f'{i+1}. {content}')
        
        print('\n=== 测试分段 ===')
        segments = ConverterService._segment_rules(processed)
        print(f'分段数量: {len(segments)}')
        for i, segment in enumerate(segments):
            print(f'{i+1}. {segment["text"][:50]}...')
        
        print('\n=== 测试完整解析 ===')
        rules = ConverterService._enhanced_ai_extract_rules(test_content, '测试来源', {'test': True})

        print(f'识别到 {len(rules)} 条规则:')
        for i, rule in enumerate(rules, 1):
            print(f'{i}. {rule.get("rule_name", "")}')
            print(f'   科室: {rule.get("department", "")}')
            print(f'   违规类型: {rule.get("violation_type", "")}')
            print(f'   置信度: {rule.get("confidence", 0):.2f}')
            print()
            
    except Exception as e:
        print(f'测试失败: {str(e)}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_ai()
