-- 创建表头配置管理表

-- 1. 创建表格式配置表
CREATE TABLE selfcheck_table_formats (
    id NUMBER PRIMARY KEY,
    table_name VARCHAR2(100) NOT NULL UNIQUE,
    headers CLOB NOT NULL,                    -- JSON格式存储表头字段
    patterns CLOB,                            -- JSON格式存储文件名匹配模式
    description VARCHAR2(500),                -- 表格式描述
    is_active NUMBER(1) DEFAULT 1,            -- 是否启用
    created_by NUMBER,                        -- 创建人
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建序列
CREATE SEQUENCE selfcheck_table_formats_seq START WITH 1 INCREMENT BY 1;

-- 创建索引
CREATE INDEX idx_table_formats_name ON selfcheck_table_formats(table_name);
CREATE INDEX idx_table_formats_active ON selfcheck_table_formats(is_active);

-- 插入默认表格式配置
INSERT INTO selfcheck_table_formats (id, table_name, headers, patterns, description) VALUES (
    selfcheck_table_formats_seq.NEXTVAL, 
    'default', 
    '["患者姓名", "病案号", "身份证号", "结算单据号", "医疗机构编码", "医疗机构名称", "入院科室", "出院科室", "主诊医师姓名", "患者性别", "患者年龄", "险种类型", "医保等级", "入院日期", "出院日期", "项目使用日期", "结算日期", "入院诊断编码", "入院诊断名称", "出院诊断编码", "出院诊断名称", "主手术及操作编码", "主手术及操作名称", "其他手术及操作编码", "其他手术及操作名称", "医院项目编码", "医院项目名称", "医保项目编码", "医保项目名称", "规格", "单价", "数量", "金额", "医保范围内金额", "医疗总费用", "基本统筹支付", "个人自付", "个人自费", "符合基本医疗保险的费用", "报销比例", "自付比例", "费用类别", "支付类别", "开单科室名称", "执行科室名称", "开单医师姓名"]',
    '["医保数据", "费用明细", "medical_data", "cost_detail", "default"]',
    '默认医保数据格式'
);

COMMIT;
