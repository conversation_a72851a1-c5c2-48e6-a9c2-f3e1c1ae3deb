#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试应用导入问题
"""

import sys
import os

print("=== 调试应用导入 ===")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python路径: {sys.path[:3]}...")

try:
    print("1. 测试基础导入...")
    import logging
    print("   logging导入成功")
    
    print("2. 测试Flask导入...")
    from flask import Flask
    print("   Flask导入成功")
    
    print("3. 测试config导入...")
    from config import config
    print("   config导入成功")
    
    print("4. 测试app模块导入...")
    from app import create_app
    print("   app模块导入成功")
    
    print("5. 测试应用创建...")
    app = create_app('development')
    print("   应用创建成功")
    
    print("6. 测试应用配置...")
    print(f"   DEBUG: {app.debug}")
    print(f"   SECRET_KEY存在: {'SECRET_KEY' in app.config}")
    
    print("=== 所有测试通过 ===")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
