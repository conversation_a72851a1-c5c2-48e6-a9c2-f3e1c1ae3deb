#!/usr/bin/env python3
"""
测试智能转换工具功能
"""

import sys
sys.path.append('.')

from app.rules.services import ConverterService
from app.selfcheck.database import get_db_manager

def test_database_tables():
    """测试数据库表是否存在"""
    print("=== 测试数据库表 ===")
    
    db_manager = get_db_manager()
    
    # 检查表是否存在
    tables_to_check = ['converter_uploads', 'converter_rules']
    
    for table in tables_to_check:
        try:
            result = db_manager.execute_query(f"SELECT COUNT(*) FROM {table}")
            print(f"✅ 表 {table} 存在，记录数: {result}")
        except Exception as e:
            print(f"❌ 表 {table} 不存在或有问题: {str(e)}")

def test_upload_list():
    """测试获取上传列表"""
    print("\n=== 测试获取上传列表 ===")
    
    try:
        result = ConverterService.get_upload_list(user_id=1, page=1, per_page=10)
        print(f"✅ 获取上传列表成功: {result}")
    except Exception as e:
        print(f"❌ 获取上传列表失败: {str(e)}")

def test_rule_extraction():
    """测试规则提取功能"""
    print("\n=== 测试规则提取功能 ===")
    
    # 模拟文件内容
    test_content = [
        {
            'content': '严禁重复开具同一检查项目，违反医保基金管理规定',
            'type': 'text_line',
            'page': 1
        },
        {
            'content': '不得虚假住院治疗，骗取医保基金',
            'type': 'text_line', 
            'page': 1
        },
        {
            'content': '普通文本内容，不包含规则特征',
            'type': 'text_line',
            'page': 1
        }
    ]
    
    try:
        rules = ConverterService._extract_rules(test_content)
        print(f"✅ 规则提取成功，识别到 {len(rules)} 条规则:")
        for i, rule in enumerate(rules, 1):
            print(f"  规则 {i}: {rule['rule_name']} (置信度: {rule['confidence']:.2f})")
    except Exception as e:
        print(f"❌ 规则提取失败: {str(e)}")

if __name__ == "__main__":
    test_database_tables()
    test_upload_list()
    test_rule_extraction()
    print("\n=== 测试完成 ===")
