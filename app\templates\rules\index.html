{% extends "base.html" %}

{% block title %}规则管理 - MICRA飞检数据处理工具箱{% endblock %}



{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>
                        规则管理模块
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 飞检规则知识库 -->
                        {% if current_user.has_permission('rules.knowledge_base') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-book fa-3x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">飞检规则知识库</h5>
                                    <p class="card-text">管理飞检规则知识库，支持规则查询、编辑和维护</p>
                                    <a href="{{ url_for('rules.knowledge_base') }}" class="btn btn-primary">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        进入管理
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 规则SQL生成器 -->
                        {% if current_user.has_permission('rules.sql_generator') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-code fa-3x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">规则SQL生成器</h5>
                                    <p class="card-text">根据飞检规则自动生成SQL查询语句</p>
                                    <a href="{{ url_for('rules.sql_generator') }}" class="btn btn-warning">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        生成SQL
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 系统规则语句 -->
                        {% if current_user.has_permission('rules.system_rules') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-cogs fa-3x text-success"></i>
                                    </div>
                                    <h5 class="card-title">系统规则语句</h5>
                                    <p class="card-text">管理系统内置规则语句，支持规则配置和优化</p>
                                    <a href="{{ url_for('rules.system_rules') }}" class="btn btn-success">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        规则配置
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- 功能说明 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>
                                        功能说明
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <h6 class="text-primary">飞检规则知识库</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 查看和管理飞检规则</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 支持规则分类和搜索</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 规则版本管理和历史记录</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-warning">规则SQL生成器</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 根据规则自动生成SQL</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 支持多种数据库类型</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 门诊住院模板切换</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-success">系统规则语句</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 系统内置规则配置</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 规则语句优化和调试</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 规则执行性能监控</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
