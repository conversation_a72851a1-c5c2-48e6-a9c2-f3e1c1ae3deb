#!/usr/bin/env python3
"""
检查数据库中的用户信息
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.user import User

def check_users():
    """检查用户信息"""
    app = create_app()
    
    with app.app_context():
        users = User.query.all()
        print(f"数据库中共有 {len(users)} 个用户:")
        
        for user in users:
            print(f"\n用户ID: {user.id}")
            print(f"用户名: {user.username}")
            print(f"邮箱: {user.email}")
            print(f"是否激活: {user.is_active}")
            print(f"创建时间: {user.created_at}")
            print(f"角色: {[role.name for role in user.roles]}")
            
            # 测试密码验证
            test_passwords = ['admin123', 'admin', '123456', 'password']
            for pwd in test_passwords:
                if user.check_password(pwd):
                    print(f"✅ 密码 '{pwd}' 正确")
                    break
            else:
                print("❌ 测试的密码都不正确")

if __name__ == '__main__':
    check_users()
