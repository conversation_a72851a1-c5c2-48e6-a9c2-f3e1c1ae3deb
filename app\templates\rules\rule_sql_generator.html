{% extends "base.html" %}

{% block title %}规则SQL生成器 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-code"></i> 规则SQL生成器</h4>
{% endblock %}

{% block content %}
<!-- 规则检索区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-search"></i> 规则检索
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-md-2">
                        <label class="form-label">就诊类型</label>
                        <select class="form-select" id="visitType">
                            <option value="住院">住院</option>
                            <option value="门诊">门诊</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">城市</label>
                        <select class="form-select" id="cityType">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">规则名称</label>
                        <input type="text" class="form-control" id="ruleName" placeholder="输入规则名称">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">类型</label>
                        <select class="form-select" id="type">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">规则类型</label>
                        <select class="form-select" id="ruletype">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">规则来源</label>
                        <select class="form-select" id="ruleSource">
                            <option value="">全部</option>
                        </select>
                    </div>
                </div>
                <div class="row g-2 mt-2">
                    <div class="col-md-2">
                        <label class="form-label">行为认定</label>
                        <select class="form-select" id="behaviorType">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" onclick="searchRules()">
                            <i class="fas fa-search"></i> 搜索规则
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SQL生成区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-code"></i> SQL生成器
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label">数据库类型</label>
                        <select class="form-select" id="dbType">
                            <option value="oracle">Oracle</option>
                            <option value="pg">PostgreSQL</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">规则类别</label>
                        <select class="form-select" id="ruleType">
                            <option value="name">项目名称</option>
                            <option value="code">项目编码</option>
                            <option value="national_code">国家编码</option>
                            <option value="national_bureau">国家局</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">就诊类型</label>
                        <div class="mt-2">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="visitTypeRadio" id="inpatient" value="住院" checked>
                                <label class="form-check-label" for="inpatient">住院</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="visitTypeRadio" id="outpatient" value="门诊">
                                <label class="form-check-label" for="outpatient">门诊</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-success w-100" onclick="generateSelectedSQL()">
                            <i class="fas fa-code"></i> 生成选中SQL
                        </button>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">生成的SQL</label>
                    <pre id="generatedSQL" class="bg-light p-3 border rounded" style="min-height: 200px; max-height: 400px; overflow-y: auto;"></pre>
                </div>

                <div class="d-flex gap-2">
                    <button class="btn btn-primary" onclick="copySQL()">
                        <i class="fas fa-copy"></i> 复制SQL
                    </button>
                    <button class="btn btn-warning" onclick="executeSQL()">
                        <i class="fas fa-play"></i> 执行查询
                    </button>
                    <button class="btn btn-info" onclick="clearSQL()">
                        <i class="fas fa-eraser"></i> 清空
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 规则列表区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-list"></i> 规则列表 (共 <span id="ruleCount">0</span> 条)
                    </h6>
                    <div>
                        <button class="btn btn-outline-primary btn-sm" onclick="selectAllRules()">
                            <i class="fas fa-check-square"></i> 全选
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                            <i class="fas fa-square"></i> 清空选择
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="rulesTable">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                                </th>
                                <th width="60">序号</th>
                                <th width="10%">行为认定</th>
                                <th width="15%">适用范围</th>
                                <th width="20%">规则名称</th>
                                <th width="8%">城市</th>
                                <th width="12%">规则来源</th>
                                <th width="35%">规则内涵</th>
                            </tr>
                        </thead>
                        <tbody id="rulesTableBody">
                            <!-- 规则数据将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let rulesData = [];

// 页面加载完成后初始化
$(document).ready(function() {
    initializePage();

    // 为搜索输入框添加回车键事件
    $('#ruleName').on('keypress', function(e) {
        if (e.which === 13) {
            searchRules();
        }
    });
});

// 初始化页面
function initializePage() {
    loadBehaviorTypes();
    loadCityTypes();
    loadRuleSources();
    loadTypeTypes();
    loadRuleTypeTypes();
    loadRules();
}

// 加载行为认定选项
function loadBehaviorTypes() {
    fetch('/rules/api/behavior_types')
        .then(response => response.json())
        .then(data => {
            if (data.success && Array.isArray(data.types)) {
                const select = document.getElementById('behaviorType');
                select.innerHTML = '<option value="">全部</option>';
                data.types.forEach(type => {
                    select.innerHTML += `<option value="${type}">${type}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('加载行为认定失败:', error);
            showAlert('加载行为认定失败', 'danger');
        });
}

// 加载城市类型选项
function loadCityTypes() {
    fetch('/rules/api/city_types')
        .then(response => response.json())
        .then(data => {
            if (data.success && Array.isArray(data.types)) {
                const select = document.getElementById('cityType');
                select.innerHTML = '<option value="">全部</option>';
                data.types.forEach(type => {
                    select.innerHTML += `<option value="${type}">${type}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('加载城市类型失败:', error);
        });
}

// 加载规则来源选项
function loadRuleSources() {
    fetch('/rules/api/rule_sources')
        .then(response => response.json())
        .then(data => {
            if (data.success && Array.isArray(data.types)) {
                const select = document.getElementById('ruleSource');
                select.innerHTML = '<option value="">全部</option>';
                data.types.forEach(type => {
                    select.innerHTML += `<option value="${type}">${type}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('加载规则来源失败:', error);
        });
}

// 加载类型选项
function loadTypeTypes() {
    fetch('/rules/api/type_types')
        .then(response => response.json())
        .then(data => {
            if (data.success && Array.isArray(data.types)) {
                const select = document.getElementById('type');
                select.innerHTML = '<option value="">全部</option>';
                data.types.forEach(type => {
                    select.innerHTML += `<option value="${type}">${type}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('加载类型失败:', error);
        });
}

// 加载规则类型选项
function loadRuleTypeTypes() {
    fetch('/rules/api/rule_type_types')
        .then(response => response.json())
        .then(data => {
            if (data.success && Array.isArray(data.types)) {
                const select = document.getElementById('ruletype');
                select.innerHTML = '<option value="">全部</option>';
                data.types.forEach(type => {
                    select.innerHTML += `<option value="${type}">${type}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('加载规则类型失败:', error);
        });
}

// 搜索规则
function searchRules() {
    loadRules();
}

// 加载规则列表
function loadRules() {
    const params = new URLSearchParams({
        behavior_type: document.getElementById('behaviorType').value,
        city: document.getElementById('cityType').value,
        rule_source: document.getElementById('ruleSource').value,
        rule_name: document.getElementById('ruleName').value,
        type: document.getElementById('type').value,
        rule_type: document.getElementById('ruletype').value,
        visit_type: document.getElementById('visitType').value,
    });

    showLoading();

    fetch(`/rules/api/search?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                rulesData = data.rules || [];
                document.getElementById('ruleCount').textContent = rulesData.length;
                displayRules(rulesData);
            } else {
                showAlert('加载规则列表失败：' + (data.error || '未知错误'), 'danger');
            }
        })
        .catch(error => {
            console.error('加载规则失败:', error);
            showAlert('加载规则失败：' + error.message, 'danger');
        })
        .finally(() => {
            hideLoading();
        });
}

// 显示规则列表
function displayRules(rules) {
    const tbody = document.getElementById('rulesTableBody');
    tbody.innerHTML = '';

    if (rules.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }

    rules.forEach((rule, index) => {
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="rule-checkbox" value="${rule.id || index}" onchange="handleCheckboxChange()">
                </td>
                <td>${index + 1}</td>
                <td>${rule.行为认定 || '-'}</td>
                <td title="${rule.适用范围 || ''}">${truncateText(rule.适用范围 || '-', 50)}</td>
                <td title="${rule.规则名称 || ''}">${truncateText(rule.规则名称 || '-', 60)}</td>
                <td>${rule.城市 || '-'}</td>
                <td>${rule.规则来源 || '-'}</td>
                <td title="${rule.规则内涵 || ''}">${truncateText(rule.规则内涵 || '-', 100)}</td>
            </tr>
        `;
        tbody.insertAdjacentHTML('beforeend', row);
    });

    document.getElementById('selectAll').checked = false;
}

// 截断文本
function truncateText(text, maxLength) {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// 全选/取消全选
function toggleSelectAll() {
    const checkAll = document.getElementById('selectAll').checked;
    const checkboxes = document.getElementsByClassName('rule-checkbox');
    for (let i = 0; i < checkboxes.length; i++) {
        checkboxes[i].checked = checkAll;
    }
    handleCheckboxChange();
}

// 选择所有规则
function selectAllRules() {
    document.getElementById('selectAll').checked = true;
    toggleSelectAll();
}

// 清空选择
function clearSelection() {
    document.getElementById('selectAll').checked = false;
    toggleSelectAll();
}

// 处理复选框变化
function handleCheckboxChange() {
    const checkboxes = document.getElementsByClassName('rule-checkbox');
    let checkedCount = 0;
    for (let i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].checked) {
            checkedCount++;
        }
    }
    document.getElementById('selectAll').checked = (checkedCount > 0 && checkedCount === checkboxes.length);
}

// 获取选中的规则ID
function getSelectedRuleIds() {
    const checkboxes = document.getElementsByClassName('rule-checkbox');
    const selectedIds = [];
    for (let i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].checked) {
            selectedIds.push(checkboxes[i].value);
        }
    }
    return selectedIds;
}

// 生成选中规则的SQL
function generateSelectedSQL() {
    const selectedRules = getSelectedRuleIds();
    if (selectedRules.length === 0) {
        showAlert('请至少选择一条规则', 'warning');
        return;
    }

    const selectedDbType = document.getElementById('dbType').value;
    const selectedVisitType = document.querySelector('input[name="visitTypeRadio"]:checked');
    const selectedRuleType = document.getElementById('ruleType').value;

    if (!selectedVisitType) {
        showAlert('请选择就诊类型（门诊/住院）', 'warning');
        return;
    }

    // 根据数据库类型、就诊类型和规则类别确定模板路径
    const visitType = selectedVisitType.value === '门诊' ? 'outpatient' : 'inpatient';
    const templatePath = `rule_${selectedDbType}_${selectedRuleType}_${visitType}`;

    showLoading();

    const requestData = {
        rule_ids: selectedRules,
        template_path: templatePath,
        visit_type: selectedVisitType.value
    };

    fetch('/rules/api/generate_sql', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            document.getElementById('generatedSQL').textContent = data.sql;
            showAlert(`已成功生成 ${selectedRules.length} 条规则的SQL语句`, 'success');
        } else {
            showAlert('生成SQL失败：' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('生成SQL失败:', error);
        showAlert('生成SQL失败: ' + error.message, 'danger');
    });
}

// 复制SQL
function copySQL() {
    const sql = document.getElementById('generatedSQL').textContent;
    if (!sql.trim()) {
        showAlert('没有可复制的SQL', 'warning');
        return;
    }

    navigator.clipboard.writeText(sql)
        .then(() => {
            showAlert('SQL已复制到剪贴板', 'success');
        })
        .catch(err => {
            console.error('复制失败:', err);
            showAlert('复制失败', 'danger');
        });
}

// 执行SQL
function executeSQL() {
    const sql = document.getElementById('generatedSQL').textContent;
    if (!sql.trim()) {
        showAlert('没有可执行的SQL', 'warning');
        return;
    }

    if (!confirm('确定要执行这个SQL语句吗？')) {
        return;
    }

    showLoading();

    fetch('/rules/api/execute_sql', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            sql: sql
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert('SQL执行成功，共影响 ' + (data.affected_rows || 0) + ' 条记录', 'success');
        } else {
            showAlert('SQL执行失败：' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('SQL执行失败:', error);
        showAlert('SQL执行失败: ' + error.message, 'danger');
    });
}

// 清空SQL
function clearSQL() {
    document.getElementById('generatedSQL').textContent = '';
}

// 显示加载指示器
function showLoading() {
    // 创建加载指示器
    if (!document.getElementById('loadingIndicator')) {
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loadingIndicator';
        loadingDiv.className = 'position-fixed top-50 start-50 translate-middle';
        loadingDiv.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        `;
        document.body.appendChild(loadingDiv);
    }
    document.getElementById('loadingIndicator').style.display = 'block';
}

// 隐藏加载指示器
function hideLoading() {
    const loadingDiv = document.getElementById('loadingIndicator');
    if (loadingDiv) {
        loadingDiv.style.display = 'none';
    }
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}