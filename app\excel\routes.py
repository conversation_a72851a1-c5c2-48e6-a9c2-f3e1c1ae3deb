from flask import render_template, request, jsonify, flash, redirect, url_for, send_file
from flask_login import login_required, current_user
from app.excel import bp
from app.auth.decorators import permission_required
from app.models.audit import AuditLog
from app.excel.services import ExcelSplitService, ExcelCompareService, ExcelMergeService, ExcelConvertService, ExcelValidationService
from app.utils.database import handle_db_error
from app.utils.file_manager import FileManager, allowed_file
from app.utils.breadcrumb import generate_breadcrumb
from werkzeug.utils import secure_filename
from datetime import datetime
import os
import tempfile
import zipfile

@bp.route('/')
@login_required
@permission_required('excel')
def index():
    """Excel工具模块首页"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='excel_index',
        details='访问Excel工具模块首页'
    )
    breadcrumb_items = generate_breadcrumb('excel')
    return render_template('excel/index.html', breadcrumb_items=breadcrumb_items)

@bp.route('/splitter')
@login_required
@permission_required('excel.splitter')
def splitter():
    """Excel文件拆分"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='excel_splitter',
        details='访问Excel文件拆分'
    )
    breadcrumb_items = generate_breadcrumb('excel', 'splitter')
    return render_template('excel/splitter.html', breadcrumb_items=breadcrumb_items)

@bp.route('/delete')
@login_required
@permission_required('excel.delete')
def delete():
    """Excel内容删除"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='excel_delete',
        details='访问Excel内容删除'
    )
    breadcrumb_items = generate_breadcrumb('excel', 'delete')
    return render_template('excel/delete.html', breadcrumb_items=breadcrumb_items)

@bp.route('/compare')
@login_required
@permission_required('excel.compare')
def compare():
    """Excel比对工具"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='excel_compare',
        details='访问Excel比对工具'
    )
    breadcrumb_items = generate_breadcrumb('excel', 'compare')
    return render_template('excel/compare.html', breadcrumb_items=breadcrumb_items)

@bp.route('/to_sql')
@login_required
@permission_required('excel.to_sql')
def to_sql():
    """Excel转SQL工具"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='excel_to_sql',
        details='访问Excel转SQL工具'
    )
    breadcrumb_items = generate_breadcrumb('excel', 'to_sql')
    return render_template('excel/to_sql.html', breadcrumb_items=breadcrumb_items)

# API路由
@bp.route('/api/upload', methods=['POST'])
@login_required
@handle_db_error
def api_upload_file():
    """文件上传API"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        if not allowed_file(file.filename, {'xlsx', 'xls', 'csv'}):
            return jsonify({'success': False, 'error': '不支持的文件格式'}), 400

        # 保存文件到临时目录
        filename = secure_filename(file.filename)
        temp_path = os.path.join(tempfile.gettempdir(), f"upload_{current_user.id}_{filename}")
        file.save(temp_path)

        # 记录上传日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='upload',
            resource='excel_file',
            details=f'上传文件: {filename}'
        )

        return jsonify({
            'success': True,
            'file_path': temp_path,
            'filename': filename,
            'file_size': os.path.getsize(temp_path)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/split', methods=['POST'])
@login_required
@permission_required('excel.splitter')
@handle_db_error
def api_split_excel():
    """Excel拆分API"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        split_type = data.get('split_type', 'column')
        column_name = data.get('column_name', '')
        rows_per_file = data.get('rows_per_file', 1000)

        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 400

        if split_type == 'column':
            if not column_name:
                return jsonify({'success': False, 'error': '请指定拆分列名'}), 400
            results, output_dir = ExcelSplitService.split_excel_by_column(file_path, column_name)
        elif split_type == 'rows':
            results, output_dir = ExcelSplitService.split_excel_by_rows(file_path, int(rows_per_file))
        else:
            return jsonify({'success': False, 'error': '不支持的拆分类型'}), 400

        # 创建ZIP文件
        zip_filename = f"excel_split_{current_user.id}_{int(datetime.now().timestamp())}.zip"
        zip_path = os.path.join(tempfile.gettempdir(), zip_filename)

        with zipfile.ZipFile(zip_path, 'w') as zipf:
            for result in results:
                if result['status'] == 'success':
                    zipf.write(result['file_path'], result['filename'])

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='split_excel',
            resource='excel_file',
            details=f'拆分Excel文件，生成{len(results)}个文件'
        )

        return jsonify({
            'success': True,
            'results': results,
            'download_url': f'/excel/api/download/{zip_filename}',
            'total_files': len(results)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/compare', methods=['POST'])
@login_required
@permission_required('excel.compare')
@handle_db_error
def api_compare_excel():
    """Excel比对API"""
    try:
        data = request.get_json()
        file1_path = data.get('file1_path')
        file2_path = data.get('file2_path')
        key_columns = data.get('key_columns', [])

        if not file1_path or not os.path.exists(file1_path):
            return jsonify({'success': False, 'error': '文件1不存在'}), 400

        if not file2_path or not os.path.exists(file2_path):
            return jsonify({'success': False, 'error': '文件2不存在'}), 400

        result = ExcelCompareService.compare_excel_files(file1_path, file2_path, key_columns)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='compare_excel',
            resource='excel_file',
            details='比对Excel文件'
        )

        return jsonify({
            'success': True,
            'comparison_result': result
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/download/<filename>')
@login_required
def api_download_file(filename):
    """文件下载API"""
    try:
        # 安全检查文件名
        if '..' in filename or '/' in filename or '\\' in filename:
            return jsonify({'error': '非法文件名'}), 400

        file_path = os.path.join(tempfile.gettempdir(), filename)

        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404

        # 记录下载日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='download',
            resource='excel_file',
            resource_id=filename,
            details=f'下载文件: {filename}'
        )

        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/delete', methods=['POST'])
@login_required
@permission_required('excel.delete')
@handle_db_error
def api_delete_excel():
    """Excel内容删除API"""
    try:
        data = request.get_json()
        source_file = data.get('source_file')
        delete_file = data.get('delete_file')
        source_column = data.get('source_column')
        delete_column = data.get('delete_column')
        match_mode = data.get('match_mode', 'exact')
        case_sensitive = data.get('case_sensitive', False)
        create_backup = data.get('create_backup', True)

        if not source_file or not os.path.exists(source_file):
            return jsonify({'success': False, 'error': '源文件不存在'}), 400

        if not delete_file or not os.path.exists(delete_file):
            return jsonify({'success': False, 'error': '删除条件文件不存在'}), 400

        # 执行删除操作
        result = ExcelValidationService.delete_excel_content(
            source_file, delete_file, source_column, delete_column,
            match_mode, case_sensitive, create_backup
        )

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='delete_excel_content',
            resource='excel_file',
            details=f'删除Excel内容，删除{result.get("deleted_count", 0)}行'
        )

        return jsonify({
            'success': True,
            'delete_result': result
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/to_sql', methods=['POST'])
@login_required
@permission_required('excel.to_sql')
@handle_db_error
def api_excel_to_sql():
    """Excel转SQL API"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        name_column = data.get('name_column')
        sql_column = data.get('sql_column')
        desc_column = data.get('desc_column', '')
        policy_column = data.get('policy_column', '')

        options = {
            'include_description': data.get('include_description', True),
            'include_policy': data.get('include_policy', True),
            'create_separate_files': data.get('create_separate_files', True),
            'create_combined_file': data.get('create_combined_file', False),
            'zip_output': data.get('zip_output', True),
            'format_sql': data.get('format_sql', True),
            'add_timestamp': data.get('add_timestamp', True)
        }

        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 400

        if not name_column or not sql_column:
            return jsonify({'success': False, 'error': '请指定规则名称列和SQL列'}), 400

        # 执行转换
        result = ExcelConvertService.excel_to_sql(
            file_path, name_column, sql_column, desc_column, policy_column, options
        )

        if result.get('success'):
            # 生成下载链接
            output_filename = f"sql_files_{current_user.id}_{int(datetime.now().timestamp())}.zip"
            result['download_url'] = f'/excel/api/download/{output_filename}'

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='excel_to_sql',
            resource='excel_file',
            details=f'Excel转SQL，生成{result.get("file_count", 0)}个SQL文件'
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
