package com.ruoyi.selfcheck.mapper;

import java.util.List;
import com.ruoyi.selfcheck.domain.SelfCheckRule;

/**
 * 自查规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface SelfCheckRuleMapper 
{
    /**
     * 查询自查规则
     * 
     * @param ruleId 自查规则主键
     * @return 自查规则
     */
    public SelfCheckRule selectSelfCheckRuleByRuleId(Long ruleId);

    /**
     * 查询自查规则列表
     * 
     * @param selfCheckRule 自查规则
     * @return 自查规则集合
     */
    public List<SelfCheckRule> selectSelfCheckRuleList(SelfCheckRule selfCheckRule);

    /**
     * 新增自查规则
     * 
     * @param selfCheckRule 自查规则
     * @return 结果
     */
    public int insertSelfCheckRule(SelfCheckRule selfCheckRule);

    /**
     * 修改自查规则
     * 
     * @param selfCheckRule 自查规则
     * @return 结果
     */
    public int updateSelfCheckRule(SelfCheckRule selfCheckRule);

    /**
     * 删除自查规则
     * 
     * @param ruleId 自查规则主键
     * @return 结果
     */
    public int deleteSelfCheckRuleByRuleId(Long ruleId);

    /**
     * 批量删除自查规则
     * 
     * @param ruleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSelfCheckRuleByRuleIds(Long[] ruleIds);

    /**
     * 根据规则编码查询规则
     * 
     * @param ruleCode 规则编码
     * @return 自查规则
     */
    public SelfCheckRule selectSelfCheckRuleByRuleCode(String ruleCode);

    /**
     * 查询启用的规则列表
     * 
     * @return 启用的规则列表
     */
    public List<SelfCheckRule> selectEnabledRuleList();

    /**
     * 根据规则类型查询规则列表
     *
     * @param ruleType 规则类型
     * @return 规则列表
     */
    public List<SelfCheckRule> selectRuleListByType(String ruleType);

    /**
     * 校验规则编码唯一性
     *
     * @param ruleCode 规则编码
     * @return 自查规则
     */
    public SelfCheckRule checkRuleCodeUnique(String ruleCode);
}
