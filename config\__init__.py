import os
import configparser
from dotenv import load_dotenv

basedir = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(basedir, '.env'))

# 读取config.ini配置
config_ini = configparser.ConfigParser()
config_ini.read(os.path.join(basedir, '..', 'config.ini'), encoding='utf-8')

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # 数据库配置 - 使用Oracle数据库
    @staticmethod
    def get_oracle_uri():
        """获取Oracle数据库连接URI"""
        try:
            username = config_ini.get('datachange', 'username')
            password = config_ini.get('datachange', 'password')
            dsn = config_ini.get('datachange', 'dsn')
            # 使用标准Oracle连接字符串
            return f'oracle+oracledb://{username}:{password}@{dsn}'
        except:
            # 如果config.ini读取失败，使用默认配置
            return 'oracle+oracledb://datachange:drgs2019@127.0.0.1/orcl'

    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or get_oracle_uri()
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(basedir, '..', 'uploads')
    OUTPUT_FOLDER = os.path.join(basedir, '..', 'outputs')
    MAX_CONTENT_LENGTH = 1500 * 1024 * 1024  # 500MB max request size (for multiple files)
    
    # 安全配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1小时
    
    # 登录配置
    MAX_LOGIN_ATTEMPTS = 5
    ACCOUNT_LOCK_DURATION = 30  # 分钟
    
    # 缓存配置 - 使用简单缓存避免Redis依赖
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 日志配置
    LOG_TO_STDOUT = os.environ.get('LOG_TO_STDOUT')
    
    @staticmethod
    def init_app(app):
        pass

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or Config.get_oracle_uri()

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or Config.get_oracle_uri()
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """生产环境配置"""
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or Config.get_oracle_uri()
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # 邮件错误日志
        import logging
        from logging.handlers import SMTPHandler
        if app.config['MAIL_SERVER']:
            auth = None
            if app.config['MAIL_USERNAME'] or app.config['MAIL_PASSWORD']:
                auth = (app.config['MAIL_USERNAME'], app.config['MAIL_PASSWORD'])
            secure = None
            if app.config['MAIL_USE_TLS']:
                secure = ()
            mail_handler = SMTPHandler(
                mailhost=(app.config['MAIL_SERVER'], app.config['MAIL_PORT']),
                fromaddr='no-reply@' + app.config['MAIL_SERVER'],
                toaddrs=app.config['ADMINS'], subject='MICRA Application Failure',
                credentials=auth, secure=secure)
            mail_handler.setLevel(logging.ERROR)
            app.logger.addHandler(mail_handler)

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
