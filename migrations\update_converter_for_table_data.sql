-- 更新智能转换工具数据库表结构，支持表格数据存储
-- 2025-06-16

-- 1. 更新converter_uploads表，添加表格相关字段
ALTER TABLE converter_uploads ADD (
    table_headers CLOB,                    -- 表头信息（JSON格式）
    table_metadata CLOB                    -- 表格元数据（JSON格式）
);

-- 2. 创建表格行数据表
CREATE TABLE converter_table_rows (
    id NUMBER PRIMARY KEY,
    upload_id NUMBER NOT NULL,
    row_number NUMBER NOT NULL,
    cells CLOB NOT NULL,                   -- 单元格数据（JSON格式）
    original_content CLOB NOT NULL,        -- 原始行内容
    status VARCHAR2(20) DEFAULT 'pending', -- pending, processing, converted, confirmed
    converted_data CLOB,                   -- 转换后的数据（JSON格式）
    confidence NUMBER(3,2),                -- 转换置信度
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_table_rows_upload FOREIGN KEY (upload_id) REFERENCES converter_uploads(id) ON DELETE CASCADE
);

-- 创建序列
CREATE SEQUENCE converter_table_rows_seq START WITH 1 INCREMENT BY 1;

-- 创建索引
CREATE INDEX idx_table_rows_upload ON converter_table_rows(upload_id);
CREATE INDEX idx_table_rows_status ON converter_table_rows(status);
CREATE INDEX idx_table_rows_number ON converter_table_rows(upload_id, row_number);

-- 3. 创建转换任务表（用于跟踪逐行转换进度）
CREATE TABLE converter_conversion_tasks (
    id NUMBER PRIMARY KEY,
    upload_id NUMBER NOT NULL,
    total_rows NUMBER NOT NULL,
    processed_rows NUMBER DEFAULT 0,
    success_rows NUMBER DEFAULT 0,
    failed_rows NUMBER DEFAULT 0,
    status VARCHAR2(20) DEFAULT 'pending',  -- pending, processing, completed, failed
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message CLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_conversion_tasks_upload FOREIGN KEY (upload_id) REFERENCES converter_uploads(id) ON DELETE CASCADE
);

-- 创建序列
CREATE SEQUENCE converter_conversion_tasks_seq START WITH 1 INCREMENT BY 1;

-- 创建索引
CREATE INDEX idx_conversion_tasks_upload ON converter_conversion_tasks(upload_id);
CREATE INDEX idx_conversion_tasks_status ON converter_conversion_tasks(status);

COMMIT;
