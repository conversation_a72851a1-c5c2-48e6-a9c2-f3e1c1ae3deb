{% extends "base.html" %}

{% block title %}批量SQL查询 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-layer-group"></i> 批量SQL查询</h4>
{% endblock %}

{% block content %}
<!-- 查询配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 批量查询配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">查询模式</label>
                            <select class="form-select" id="queryMode" onchange="toggleQueryMode()">
                                <option value="manual">手动输入SQL</option>
                                <option value="template">使用模板</option>
                                <option value="file">从文件导入</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">输出格式</label>
                            <select class="form-select" id="outputFormat">
                                <option value="excel">Excel文件</option>
                                <option value="csv">CSV文件</option>
                                <option value="json">JSON文件</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="separateFiles" checked>
                            <label class="form-check-label" for="separateFiles">
                                每个查询生成单独文件
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeMetadata">
                            <label class="form-check-label" for="includeMetadata">
                                包含查询元数据
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SQL输入区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-code"></i> SQL查询列表
                </h6>
                <div>
                    <button type="button" class="btn btn-success btn-sm" onclick="addQuery()">
                        <i class="fas fa-plus"></i> 添加查询
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="loadTemplate()">
                        <i class="fas fa-template"></i> 加载模板
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="clearAllQueries()">
                        <i class="fas fa-trash"></i> 清空所有
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 手动输入模式 -->
                <div id="manualQueryMode">
                    <div id="queriesContainer">
                        <!-- 查询项将动态添加 -->
                    </div>
                </div>
                
                <!-- 模板模式 -->
                <div id="templateQueryMode" class="d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">选择模板</label>
                            <select class="form-select" id="batchTemplateSelect">
                                <option value="">请选择模板</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">参数配置</label>
                            <button type="button" class="btn btn-primary" onclick="configureTemplateParams()">
                                <i class="fas fa-cogs"></i> 配置参数
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 文件导入模式 -->
                <div id="fileQueryMode" class="d-none">
                    <div class="mb-3">
                        <label for="sqlFileInput" class="form-label">选择SQL文件</label>
                        <input type="file" class="form-control" id="sqlFileInput" accept=".sql,.txt" onchange="loadSQLFile()">
                        <div class="form-text">支持 .sql 和 .txt 格式文件，每个SQL语句用分号分隔</div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="executeBatchQueries()">
                        <i class="fas fa-play"></i> 执行批量查询
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="validateQueries()">
                        <i class="fas fa-check"></i> 验证SQL
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 执行进度区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks"></i> 执行进度
                </h6>
            </div>
            <div class="card-body">
                <div id="progressContainer" class="d-none">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>总体进度</span>
                            <span id="progressText">0/0</span>
                        </div>
                        <div class="progress">
                            <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div id="currentQueryInfo" class="alert alert-info">
                        <strong>当前查询：</strong><span id="currentQueryName">-</span>
                    </div>
                </div>
                
                <div id="noProgress" class="text-center text-muted py-4">
                    <i class="fas fa-clock fa-3x mb-3"></i>
                    <p>配置查询后点击"执行批量查询"开始处理</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 执行结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list-alt"></i> 执行结果
                </h6>
                <div id="resultActions" class="d-none">
                    <button type="button" class="btn btn-success btn-sm" onclick="downloadAllResults()">
                        <i class="fas fa-download"></i> 下载所有结果
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="resultsContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-file-excel fa-3x mb-3"></i>
                        <p>执行批量查询后结果将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 查询编辑模态框 -->
<div class="modal fade" id="queryEditModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> 编辑查询
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">查询名称</label>
                    <input type="text" class="form-control" id="editQueryName" placeholder="输入查询名称">
                </div>
                <div class="mb-3">
                    <label class="form-label">SQL语句</label>
                    <textarea class="form-control" id="editQuerySQL" rows="8" placeholder="输入SQL查询语句"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">描述</label>
                    <input type="text" class="form-control" id="editQueryDesc" placeholder="查询描述（可选）">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveQuery()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let queries = [];
let currentEditIndex = -1;
let batchResults = [];

// 页面加载完成后初始化
$(document).ready(function() {
    addQuery(); // 默认添加一个查询
});

// 切换查询模式
function toggleQueryMode() {
    const mode = $('#queryMode').val();

    $('#manualQueryMode').addClass('d-none');
    $('#templateQueryMode').addClass('d-none');
    $('#fileQueryMode').addClass('d-none');

    $(`#${mode}QueryMode`).removeClass('d-none');
}

// 添加查询
function addQuery() {
    const queryIndex = queries.length;
    const queryItem = {
        name: `查询${queryIndex + 1}`,
        sql: '',
        description: ''
    };

    queries.push(queryItem);

    const queryHtml = `
        <div class="card mb-3" id="query_${queryIndex}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-database"></i> ${queryItem.name}
                </h6>
                <div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="editQuery(${queryIndex})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeQuery(${queryIndex})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">SQL预览:</small>
                </div>
                <pre class="bg-light p-2 rounded" style="max-height: 100px; overflow-y: auto;">
                    <code id="sql_preview_${queryIndex}">-- 请点击编辑按钮添加SQL语句</code>
                </pre>
            </div>
        </div>
    `;

    $('#queriesContainer').append(queryHtml);
}

// 编辑查询
function editQuery(index) {
    currentEditIndex = index;
    const query = queries[index];

    $('#editQueryName').val(query.name);
    $('#editQuerySQL').val(query.sql);
    $('#editQueryDesc').val(query.description);

    $('#queryEditModal').modal('show');
}

// 保存查询
function saveQuery() {
    if (currentEditIndex === -1) return;

    const query = queries[currentEditIndex];
    query.name = $('#editQueryName').val() || `查询${currentEditIndex + 1}`;
    query.sql = $('#editQuerySQL').val();
    query.description = $('#editQueryDesc').val();

    // 更新显示
    $(`#query_${currentEditIndex} h6`).html(`<i class="fas fa-database"></i> ${query.name}`);
    $(`#sql_preview_${currentEditIndex}`).text(query.sql || '-- 请添加SQL语句');

    $('#queryEditModal').modal('hide');
    showAlert('查询已保存', 'success');
}

// 删除查询
function removeQuery(index) {
    if (confirm('确定要删除这个查询吗？')) {
        queries.splice(index, 1);
        refreshQueriesDisplay();
        showAlert('查询已删除', 'info');
    }
}

// 刷新查询显示
function refreshQueriesDisplay() {
    $('#queriesContainer').empty();
    queries.forEach((query, index) => {
        const queryHtml = `
            <div class="card mb-3" id="query_${index}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-database"></i> ${query.name}
                    </h6>
                    <div>
                        <button type="button" class="btn btn-sm btn-primary" onclick="editQuery(${index})">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeQuery(${index})">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">SQL预览:</small>
                    </div>
                    <pre class="bg-light p-2 rounded" style="max-height: 100px; overflow-y: auto;">
                        <code id="sql_preview_${index}">${query.sql || '-- 请点击编辑按钮添加SQL语句'}</code>
                    </pre>
                </div>
            </div>
        `;
        $('#queriesContainer').append(queryHtml);
    });
}

// 清空所有查询
function clearAllQueries() {
    if (confirm('确定要清空所有查询吗？')) {
        queries = [];
        $('#queriesContainer').empty();
        showAlert('所有查询已清空', 'info');
    }
}

// 验证查询
function validateQueries() {
    if (queries.length === 0) {
        showAlert('请先添加查询', 'warning');
        return;
    }

    let validCount = 0;
    let invalidQueries = [];

    queries.forEach((query, index) => {
        if (query.sql && query.sql.trim()) {
            const sql = query.sql.trim().toUpperCase();
            if (sql.startsWith('SELECT')) {
                validCount++;
            } else {
                invalidQueries.push(`${query.name}: 只允许SELECT查询`);
            }
        } else {
            invalidQueries.push(`${query.name}: SQL语句为空`);
        }
    });

    if (invalidQueries.length > 0) {
        showAlert(`验证失败：${invalidQueries.join('; ')}`, 'danger');
    } else {
        showAlert(`验证通过：${validCount}个查询都有效`, 'success');
    }
}

// 执行批量查询
function executeBatchQueries() {
    if (queries.length === 0) {
        showAlert('请先添加查询', 'warning');
        return;
    }

    const validQueries = queries.filter(q => q.sql && q.sql.trim());
    if (validQueries.length === 0) {
        showAlert('没有有效的查询语句', 'warning');
        return;
    }

    // 显示进度
    $('#progressContainer').removeClass('d-none');
    $('#noProgress').addClass('d-none');
    $('#progressBar').css('width', '0%');
    $('#progressText').text(`0/${validQueries.length}`);

    const queryData = validQueries.map(q => ({
        name: q.name,
        query: q.sql
    }));

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/database/api/batch_execute',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            queries: queryData
        }),
        success: function(response) {
            if (response.success) {
                batchResults = response.results;
                displayBatchResults(response);
                showAlert(`批量查询完成：${response.successful_queries}/${response.total_queries}个成功`, 'success');
            } else {
                showAlert('批量查询失败: ' + response.error, 'danger');
            }
            $('#progressContainer').addClass('d-none');
            $('#noProgress').removeClass('d-none');
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'danger');
            $('#progressContainer').addClass('d-none');
            $('#noProgress').removeClass('d-none');
        }
    });
}

// 显示批量查询结果
function displayBatchResults(response) {
    const { results, download_url } = response;

    let resultsHtml = `
        <div class="mb-3">
            <span class="badge bg-primary">总查询数: ${results.length}</span>
            <span class="badge bg-success">成功: ${results.filter(r => r.status === 'success').length}</span>
            <span class="badge bg-danger">失败: ${results.filter(r => r.status === 'error').length}</span>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>查询名称</th>
                        <th>状态</th>
                        <th>结果行数</th>
                        <th>文件名</th>
                        <th>错误信息</th>
                    </tr>
                </thead>
                <tbody>
    `;

    results.forEach(result => {
        const statusBadge = result.status === 'success' ?
            '<span class="badge bg-success">成功</span>' :
            '<span class="badge bg-danger">失败</span>';

        resultsHtml += `
            <tr>
                <td>${result.name}</td>
                <td>${statusBadge}</td>
                <td>${result.rows || 0}</td>
                <td>${result.filename || '-'}</td>
                <td>${result.error || '-'}</td>
            </tr>
        `;
    });

    resultsHtml += `
                </tbody>
            </table>
        </div>
    `;

    if (download_url) {
        resultsHtml += `
            <div class="mt-3">
                <a href="${download_url}" class="btn btn-success">
                    <i class="fas fa-download"></i> 下载所有结果文件
                </a>
            </div>
        `;
        $('#resultActions').removeClass('d-none');
    }

    $('#resultsContainer').html(resultsHtml);
}

// 其他功能函数
function downloadAllResults() {
    showAlert('请使用上方的下载链接', 'info');
}

function loadTemplate() {
    showAlert('模板功能开发中...', 'info');
}

function configureTemplateParams() {
    showAlert('模板参数配置功能开发中...', 'info');
}

function loadSQLFile() {
    const fileInput = document.getElementById('sqlFileInput');
    const file = fileInput.files[0];

    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        const content = e.target.result;
        const sqlStatements = content.split(';').filter(sql => sql.trim());

        queries = [];
        $('#queriesContainer').empty();

        sqlStatements.forEach((sql, index) => {
            const query = {
                name: `文件查询${index + 1}`,
                sql: sql.trim(),
                description: `从文件导入的查询 ${index + 1}`
            };
            queries.push(query);
        });

        refreshQueriesDisplay();
        showAlert(`成功导入${sqlStatements.length}个查询`, 'success');
    };

    reader.readAsText(file);
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
