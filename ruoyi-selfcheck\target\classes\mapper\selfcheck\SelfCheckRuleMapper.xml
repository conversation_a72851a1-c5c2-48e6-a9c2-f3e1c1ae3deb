<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.selfcheck.mapper.SelfCheckRuleMapper">
    
    <resultMap type="SelfCheckRule" id="SelfCheckRuleResult">
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="ruleDescription"    column="rule_description"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="ruleVersion"    column="rule_version"    />
        <result property="ruleContent"    column="rule_content"    />
        <result property="status"    column="status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSelfCheckRuleVo">
        select rule_id, rule_name, rule_code, rule_description, rule_type, rule_version, rule_content, status, sort_order, create_by, create_time, update_by, update_time, remark from biz_selfcheck_rule
    </sql>

    <select id="selectSelfCheckRuleList" parameterType="SelfCheckRule" resultMap="SelfCheckRuleResult">
        <include refid="selectSelfCheckRuleVo"/>
        <where>  
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleCode != null  and ruleCode != ''"> and rule_code = #{ruleCode}</if>
            <if test="ruleType != null  and ruleType != ''"> and rule_type = #{ruleType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order, create_time desc
    </select>
    
    <select id="selectSelfCheckRuleByRuleId" parameterType="Long" resultMap="SelfCheckRuleResult">
        <include refid="selectSelfCheckRuleVo"/>
        where rule_id = #{ruleId}
    </select>
        
    <insert id="insertSelfCheckRule" parameterType="SelfCheckRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into biz_selfcheck_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="ruleCode != null and ruleCode != ''">rule_code,</if>
            <if test="ruleDescription != null">rule_description,</if>
            <if test="ruleType != null and ruleType != ''">rule_type,</if>
            <if test="ruleVersion != null">rule_version,</if>
            <if test="ruleContent != null and ruleContent != ''">rule_content,</if>
            <if test="status != null">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="ruleCode != null and ruleCode != ''">#{ruleCode},</if>
            <if test="ruleDescription != null">#{ruleDescription},</if>
            <if test="ruleType != null and ruleType != ''">#{ruleType},</if>
            <if test="ruleVersion != null">#{ruleVersion},</if>
            <if test="ruleContent != null and ruleContent != ''">#{ruleContent},</if>
            <if test="status != null">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSelfCheckRule" parameterType="SelfCheckRule">
        update biz_selfcheck_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="ruleCode != null and ruleCode != ''">rule_code = #{ruleCode},</if>
            <if test="ruleDescription != null">rule_description = #{ruleDescription},</if>
            <if test="ruleType != null and ruleType != ''">rule_type = #{ruleType},</if>
            <if test="ruleVersion != null">rule_version = #{ruleVersion},</if>
            <if test="ruleContent != null and ruleContent != ''">rule_content = #{ruleContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where rule_id = #{ruleId}
    </update>

    <delete id="deleteSelfCheckRuleByRuleId" parameterType="Long">
        delete from biz_selfcheck_rule where rule_id = #{ruleId}
    </delete>

    <delete id="deleteSelfCheckRuleByRuleIds" parameterType="String">
        delete from biz_selfcheck_rule where rule_id in 
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>

    <select id="checkRuleCodeUnique" parameterType="String" resultMap="SelfCheckRuleResult">
        <include refid="selectSelfCheckRuleVo"/>
        where rule_code = #{ruleCode} limit 1
    </select>

    <select id="selectSelfCheckRuleByRuleCode" parameterType="String" resultMap="SelfCheckRuleResult">
        <include refid="selectSelfCheckRuleVo"/>
        where rule_code = #{ruleCode}
    </select>

    <select id="selectEnabledRuleList" resultMap="SelfCheckRuleResult">
        <include refid="selectSelfCheckRuleVo"/>
        where status = '0'
        order by sort_order, create_time desc
    </select>

    <select id="selectRuleListByType" parameterType="String" resultMap="SelfCheckRuleResult">
        <include refid="selectSelfCheckRuleVo"/>
        where rule_type = #{ruleType} and status = '0'
        order by sort_order, create_time desc
    </select>

</mapper>
