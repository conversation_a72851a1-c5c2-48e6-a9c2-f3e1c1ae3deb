{% extends "base.html" %}

{% block title %}查找重复文件 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-file-alt"></i> 查找重复文件</h4>
{% endblock %}

{% block content %}
<!-- 扫描配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 扫描配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label class="form-label">扫描目录</label>
                            <div id="directoriesContainer">
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" placeholder="输入要扫描的目录路径" id="directory_0">
                                    <button type="button" class="btn btn-outline-secondary" onclick="browseDirectory(0)">
                                        <i class="fas fa-folder-open"></i> 浏览
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="removeDirectory(0)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-success" onclick="addDirectory()">
                                <i class="fas fa-plus"></i> 添加目录
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">文件类型过滤</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="filterByExtension">
                                <label class="form-check-label" for="filterByExtension">
                                    启用文件类型过滤
                                </label>
                            </div>
                            <div id="extensionFilter" class="mt-2 d-none">
                                <input type="text" class="form-control" id="fileExtensions" 
                                       placeholder="例如: .jpg,.png,.pdf" 
                                       title="用逗号分隔多个扩展名">
                                <div class="form-text">用逗号分隔多个扩展名</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="checkContent" checked>
                                <label class="form-check-label" for="checkContent">
                                    基于文件内容检查
                                </label>
                            </div>
                            <div class="form-text">更准确但速度较慢</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="startScan()">
                            <i class="fas fa-search"></i> 开始扫描
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="resetForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 扫描进度区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks"></i> 扫描进度
                </h6>
            </div>
            <div class="card-body">
                <div id="scanProgress" class="d-none">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>扫描进度</span>
                            <span id="scanStatus">准备中...</span>
                        </div>
                        <div class="progress">
                            <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div id="scanInfo" class="alert alert-info">
                        <strong>当前状态：</strong><span id="currentStatus">初始化扫描...</span>
                    </div>
                </div>
                
                <div id="noScanProgress" class="text-center text-muted py-4">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <p>配置扫描目录后点击"开始扫描"开始查找重复文件</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 扫描结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list"></i> 重复文件列表
                </h6>
                <div id="resultActions" class="d-none">
                    <button type="button" class="btn btn-warning btn-sm" onclick="selectAllDuplicates()">
                        <i class="fas fa-check-square"></i> 全选
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteSelectedFiles()">
                        <i class="fas fa-trash"></i> 删除选中
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="exportResults()">
                        <i class="fas fa-download"></i> 导出结果
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="scanResultsContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-copy fa-3x mb-3"></i>
                        <p>扫描完成后重复文件将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文件详情模态框 -->
<div class="modal fade" id="fileDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> 文件详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="fileDetailContent">
                <!-- 文件详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let directoryCount = 1;
let scanResults = null;

// 页面加载完成后初始化
$(document).ready(function() {
    // 绑定文件类型过滤复选框事件
    $('#filterByExtension').change(function() {
        $('#extensionFilter').toggleClass('d-none', !this.checked);
    });
});

// 添加目录输入框
function addDirectory() {
    const container = $('#directoriesContainer');
    const newDirectoryHtml = `
        <div class="input-group mb-2">
            <input type="text" class="form-control" placeholder="输入要扫描的目录路径" id="directory_${directoryCount}">
            <button type="button" class="btn btn-outline-secondary" onclick="browseDirectory(${directoryCount})">
                <i class="fas fa-folder-open"></i> 浏览
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="removeDirectory(${directoryCount})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.append(newDirectoryHtml);
    directoryCount++;
}

// 删除目录输入框
function removeDirectory(index) {
    $(`#directory_${index}`).closest('.input-group').remove();
}

// 浏览目录（这里只是示例，实际需要文件选择器）
function browseDirectory(index) {
    // 在实际应用中，这里应该打开文件选择器
    showAlert('请手动输入目录路径', 'info');
}

// 开始扫描
function startScan() {
    // 收集所有目录路径
    const directories = [];
    $('[id^="directory_"]').each(function() {
        const path = $(this).val().trim();
        if (path) {
            directories.push(path);
        }
    });
    
    if (directories.length === 0) {
        showAlert('请至少输入一个扫描目录', 'warning');
        return;
    }
    
    // 收集扫描配置
    const checkContent = $('#checkContent').is(':checked');
    const filterByExtension = $('#filterByExtension').is(':checked');
    let fileExtensions = [];
    
    if (filterByExtension) {
        const extensionsText = $('#fileExtensions').val().trim();
        if (extensionsText) {
            fileExtensions = extensionsText.split(',').map(ext => ext.trim().toLowerCase());
        }
    }
    
    // 显示进度
    $('#scanProgress').removeClass('d-none');
    $('#noScanProgress').addClass('d-none');
    $('#progressBar').css('width', '0%');
    $('#scanStatus').text('扫描中...');
    $('#currentStatus').text('正在扫描文件...');
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    // 发送扫描请求
    $.ajax({
        url: '/data/api/find_duplicates',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            directories: directories,
            check_content: checkContent,
            file_extensions: fileExtensions
        }),
        success: function(response) {
            $('#scanProgress').addClass('d-none');
            $('#noScanProgress').removeClass('d-none');
            
            if (response.success) {
                scanResults = response;
                displayScanResults(response);
                showAlert(`扫描完成：找到${response.duplicate_groups}组重复文件`, 'success');
            } else {
                showAlert('扫描失败: ' + response.error, 'danger');
            }
        },
        error: function() {
            $('#scanProgress').addClass('d-none');
            $('#noScanProgress').removeClass('d-none');
            showAlert('网络错误，请稍后重试', 'danger');
        }
    });
}

// 显示扫描结果
function displayScanResults(results) {
    const { total_files, duplicate_files, duplicate_groups, space_savings, duplicates } = results;
    
    let resultsHtml = `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5>${total_files}</h5>
                        <p class="mb-0">总文件数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h5>${duplicate_files}</h5>
                        <p class="mb-0">重复文件数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h5>${duplicate_groups}</h5>
                        <p class="mb-0">重复组数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5>${formatFileSize(space_savings)}</h5>
                        <p class="mb-0">可节省空间</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    if (Object.keys(duplicates).length === 0) {
        resultsHtml += `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                恭喜！没有找到重复文件。
            </div>
        `;
    } else {
        resultsHtml += '<div class="accordion" id="duplicatesAccordion">';
        
        let groupIndex = 0;
        for (const [key, group] of Object.entries(duplicates)) {
            resultsHtml += `
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading${groupIndex}">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse${groupIndex}">
                            <strong>重复组 ${groupIndex + 1}</strong>
                            <span class="badge bg-warning ms-2">${group.length} 个文件</span>
                            <span class="badge bg-info ms-2">${formatFileSize(group[0].size)}</span>
                        </button>
                    </h2>
                    <div id="collapse${groupIndex}" class="accordion-collapse collapse" 
                         data-bs-parent="#duplicatesAccordion">
                        <div class="accordion-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th width="5%">
                                                <input type="checkbox" class="form-check-input" 
                                                       onchange="toggleGroupSelection(${groupIndex}, this.checked)">
                                            </th>
                                            <th>文件名</th>
                                            <th>路径</th>
                                            <th>大小</th>
                                            <th>修改时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
            `;
            
            group.forEach((file, fileIndex) => {
                resultsHtml += `
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input file-checkbox" 
                                   data-file-path="${file.path}" data-group="${groupIndex}">
                        </td>
                        <td>${file.name}</td>
                        <td title="${file.path}">${truncatePath(file.path, 50)}</td>
                        <td>${formatFileSize(file.size)}</td>
                        <td>${new Date(file.modified_time).toLocaleString()}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="showFileDetail('${file.path}')">
                                <i class="fas fa-info"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteFile('${file.path}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            resultsHtml += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            groupIndex++;
        }
        
        resultsHtml += '</div>';
    }
    
    $('#scanResultsContainer').html(resultsHtml);
    
    if (Object.keys(duplicates).length > 0) {
        $('#resultActions').removeClass('d-none');
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 截断路径
function truncatePath(path, maxLength) {
    if (path.length <= maxLength) return path;
    return '...' + path.substring(path.length - maxLength + 3);
}

// 切换组选择
function toggleGroupSelection(groupIndex, checked) {
    $(`.file-checkbox[data-group="${groupIndex}"]`).prop('checked', checked);
}

// 全选重复文件
function selectAllDuplicates() {
    $('.file-checkbox').prop('checked', true);
}

// 删除选中文件
function deleteSelectedFiles() {
    const selectedFiles = [];
    $('.file-checkbox:checked').each(function() {
        selectedFiles.push($(this).data('file-path'));
    });
    
    if (selectedFiles.length === 0) {
        showAlert('请选择要删除的文件', 'warning');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedFiles.length} 个文件吗？此操作不可恢复！`)) {
        return;
    }
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/data/api/delete_duplicates',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            file_paths: selectedFiles
        }),
        success: function(response) {
            if (response.success) {
                showAlert(`成功删除 ${response.deleted_count} 个文件`, 'success');
                if (response.failed_count > 0) {
                    showAlert(`${response.failed_count} 个文件删除失败`, 'warning');
                }
                // 重新扫描或更新显示
                startScan();
            } else {
                showAlert('删除失败: ' + response.error, 'danger');
            }
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'danger');
        }
    });
}

// 显示文件详情
function showFileDetail(filePath) {
    // 这里可以显示文件的详细信息
    $('#fileDetailContent').html(`
        <p><strong>文件路径：</strong>${filePath}</p>
        <p>更多详细信息...</p>
    `);
    $('#fileDetailModal').modal('show');
}

// 删除单个文件
function deleteFile(filePath) {
    if (!confirm('确定要删除这个文件吗？此操作不可恢复！')) {
        return;
    }
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/data/api/delete_duplicates',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            file_paths: [filePath]
        }),
        success: function(response) {
            if (response.success) {
                showAlert('文件删除成功', 'success');
                startScan(); // 重新扫描
            } else {
                showAlert('删除失败: ' + response.error, 'danger');
            }
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'danger');
        }
    });
}

// 导出结果
function exportResults() {
    if (!scanResults) {
        showAlert('没有可导出的结果', 'warning');
        return;
    }
    
    showAlert('导出功能开发中...', 'info');
}

// 重置表单
function resetForm() {
    // 清空所有目录输入框
    $('[id^="directory_"]').val('');
    
    // 重置复选框
    $('#filterByExtension').prop('checked', false);
    $('#checkContent').prop('checked', true);
    $('#extensionFilter').addClass('d-none');
    $('#fileExtensions').val('');
    
    // 隐藏结果
    $('#scanResultsContainer').html(`
        <div class="text-center text-muted py-5">
            <i class="fas fa-copy fa-3x mb-3"></i>
            <p>扫描完成后重复文件将显示在这里</p>
        </div>
    `);
    $('#resultActions').addClass('d-none');
    
    scanResults = null;
    showAlert('表单已重置', 'info');
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
