package com.ruoyi.selfcheck.service.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.selfcheck.mapper.SelfCheckTaskMapper;
import com.ruoyi.selfcheck.domain.SelfCheckTask;
import com.ruoyi.selfcheck.service.ISelfCheckTaskService;

/**
 * 自查任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@Service
public class SelfCheckTaskServiceImpl implements ISelfCheckTaskService 
{
    @Autowired
    private SelfCheckTaskMapper selfCheckTaskMapper;

    /**
     * 查询自查任务
     * 
     * @param taskId 自查任务主键
     * @return 自查任务
     */
    @Override
    public SelfCheckTask selectSelfCheckTaskByTaskId(Long taskId)
    {
        return selfCheckTaskMapper.selectSelfCheckTaskByTaskId(taskId);
    }

    /**
     * 查询自查任务列表
     * 
     * @param selfCheckTask 自查任务
     * @return 自查任务
     */
    @Override
    public List<SelfCheckTask> selectSelfCheckTaskList(SelfCheckTask selfCheckTask)
    {
        return selfCheckTaskMapper.selectSelfCheckTaskList(selfCheckTask);
    }

    /**
     * 新增自查任务
     * 
     * @param selfCheckTask 自查任务
     * @return 结果
     */
    @Override
    public int insertSelfCheckTask(SelfCheckTask selfCheckTask)
    {
        selfCheckTask.setCreateTime(new Date());
        return selfCheckTaskMapper.insertSelfCheckTask(selfCheckTask);
    }

    /**
     * 修改自查任务
     * 
     * @param selfCheckTask 自查任务
     * @return 结果
     */
    @Override
    public int updateSelfCheckTask(SelfCheckTask selfCheckTask)
    {
        selfCheckTask.setUpdateTime(new Date());
        return selfCheckTaskMapper.updateSelfCheckTask(selfCheckTask);
    }

    /**
     * 批量删除自查任务
     * 
     * @param taskIds 需要删除的自查任务主键
     * @return 结果
     */
    @Override
    public int deleteSelfCheckTaskByTaskIds(Long[] taskIds)
    {
        return selfCheckTaskMapper.deleteSelfCheckTaskByTaskIds(taskIds);
    }

    /**
     * 删除自查任务信息
     *
     * @param taskId 自查任务主键
     * @return 结果
     */
    @Override
    public int deleteSelfCheckTaskByTaskId(Long taskId)
    {
        return selfCheckTaskMapper.deleteSelfCheckTaskByTaskId(taskId);
    }

    /**
     * 查询自查任务列表（包含关联信息）
     *
     * @param selfCheckTask 自查任务
     * @return 自查任务集合
     */
    @Override
    public List<SelfCheckTask> selectSelfCheckTaskListWithDetails(SelfCheckTask selfCheckTask)
    {
        return selfCheckTaskMapper.selectSelfCheckTaskList(selfCheckTask);
    }

    /**
     * 创建自查任务
     *
     * @param uploadId 上传数据ID
     * @param ruleId 规则ID
     * @param userId 用户ID
     * @param taskName 任务名称
     * @return 任务ID
     */
    @Override
    public Long createTask(Long uploadId, Long ruleId, Long userId, String taskName)
    {
        SelfCheckTask task = new SelfCheckTask();
        task.setTaskName(taskName);
        task.setUploadId(uploadId);
        task.setRuleId(ruleId);
        task.setUserId(userId);
        task.setStatus("0"); // 待执行
        task.setProgress(0);
        task.setCreateTime(new Date());

        selfCheckTaskMapper.insertSelfCheckTask(task);
        return task.getTaskId();
    }

    /**
     * 执行自查任务
     *
     * @param taskId 任务ID
     * @return 执行结果
     */
    @Override
    public boolean executeTask(Long taskId)
    {
        // TODO: 实现任务执行逻辑
        SelfCheckTask task = new SelfCheckTask();
        task.setTaskId(taskId);
        task.setStatus("1"); // 执行中
        task.setStartTime(new Date());
        task.setUpdateTime(new Date());

        return selfCheckTaskMapper.updateSelfCheckTask(task) > 0;
    }

    /**
     * 停止自查任务
     *
     * @param taskId 任务ID
     * @return 停止结果
     */
    @Override
    public boolean stopTask(Long taskId)
    {
        SelfCheckTask task = new SelfCheckTask();
        task.setTaskId(taskId);
        task.setStatus("3"); // 已停止
        task.setEndTime(new Date());
        task.setUpdateTime(new Date());

        return selfCheckTaskMapper.updateSelfCheckTask(task) > 0;
    }

    /**
     * 查询执行中的任务列表
     *
     * @return 执行中的任务列表
     */
    @Override
    public List<SelfCheckTask> selectRunningTaskList()
    {
        SelfCheckTask task = new SelfCheckTask();
        task.setStatus("1"); // 执行中
        return selfCheckTaskMapper.selectSelfCheckTaskList(task);
    }

    /**
     * 查询已完成的任务列表（用于结果查看）
     *
     * @param selfCheckTask 查询条件
     * @return 已完成的任务列表
     */
    @Override
    public List<SelfCheckTask> selectCompletedTaskList(SelfCheckTask selfCheckTask)
    {
        selfCheckTask.setStatus("2"); // 已完成
        return selfCheckTaskMapper.selectSelfCheckTaskList(selfCheckTask);
    }

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param progress 进度
     */
    @Override
    public void updateTaskProgress(Long taskId, Integer progress)
    {
        SelfCheckTask task = new SelfCheckTask();
        task.setTaskId(taskId);
        task.setProgress(progress);
        task.setUpdateTime(new Date());

        selfCheckTaskMapper.updateSelfCheckTask(task);
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param resultSummary 结果摘要
     * @param resultDetail 详细结果
     * @param errorMessage 错误信息
     */
    @Override
    public void updateTaskStatus(Long taskId, String status, String resultSummary, String resultDetail, String errorMessage)
    {
        SelfCheckTask task = new SelfCheckTask();
        task.setTaskId(taskId);
        task.setStatus(status);
        task.setResultSummary(resultSummary);
        task.setResultDetail(resultDetail);
        task.setErrorMessage(errorMessage);
        task.setUpdateTime(new Date());

        if ("2".equals(status) || "4".equals(status)) { // 已完成或失败
            task.setEndTime(new Date());
        }

        selfCheckTaskMapper.updateSelfCheckTask(task);
    }
}
