#!/usr/bin/env python3
"""
表头配置管理服务
用于管理不同表格式的表头配置
"""

import json
import logging
from typing import Dict, List, Any
from .database import execute_query, execute_update

logger = logging.getLogger(__name__)


class TableConfigService:
    """表头配置管理服务"""
    
    @staticmethod
    def add_table_format(table_name: str, headers: List[str], patterns: List[str] = None) -> Dict[str, Any]:
        """添加新的表格式配置"""
        try:
            logger.info(f"添加表格式配置: {table_name}")
            
            # 检查表格式是否已存在
            check_query = "SELECT COUNT(*) as count FROM selfcheck_table_formats WHERE table_name = :table_name"
            result = execute_query(check_query, {'table_name': table_name})
            
            if result and result[0]['count'] > 0:
                return {'success': False, 'error': f'表格式 {table_name} 已存在'}
            
            # 插入表格式配置
            insert_query = """
            INSERT INTO selfcheck_table_formats (
                id, table_name, headers, patterns, is_active, created_at
            ) VALUES (
                selfcheck_table_formats_seq.NEXTVAL, :table_name, :headers, :patterns, 1, CURRENT_TIMESTAMP
            )
            """
            
            params = {
                'table_name': table_name,
                'headers': json.dumps(headers, ensure_ascii=False),
                'patterns': json.dumps(patterns or [], ensure_ascii=False)
            }
            
            execute_update(insert_query, params)
            
            logger.info(f"表格式配置添加成功: {table_name}")
            return {'success': True, 'message': f'表格式 {table_name} 添加成功'}
            
        except Exception as e:
            error_msg = f"添加表格式配置失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}
    
    @staticmethod
    def update_table_format(table_name: str, headers: List[str], patterns: List[str] = None) -> Dict[str, Any]:
        """更新表格式配置"""
        try:
            logger.info(f"更新表格式配置: {table_name}")
            
            update_query = """
            UPDATE selfcheck_table_formats 
            SET headers = :headers, patterns = :patterns, updated_at = CURRENT_TIMESTAMP
            WHERE table_name = :table_name
            """
            
            params = {
                'table_name': table_name,
                'headers': json.dumps(headers, ensure_ascii=False),
                'patterns': json.dumps(patterns or [], ensure_ascii=False)
            }
            
            execute_update(update_query, params)
            
            logger.info(f"表格式配置更新成功: {table_name}")
            return {'success': True, 'message': f'表格式 {table_name} 更新成功'}
            
        except Exception as e:
            error_msg = f"更新表格式配置失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}
    
    @staticmethod
    def get_table_formats() -> Dict[str, Any]:
        """获取所有表格式配置"""
        try:
            query = """
            SELECT table_name, headers, patterns, is_active, created_at, updated_at
            FROM selfcheck_table_formats 
            WHERE is_active = 1
            ORDER BY table_name
            """
            
            result = execute_query(query)
            
            formats = {}
            patterns = {}
            
            for row in result:
                table_name = row['table_name']
                headers = json.loads(row['headers']) if row['headers'] else []
                table_patterns = json.loads(row['patterns']) if row['patterns'] else []
                
                formats[table_name] = headers
                patterns[table_name] = table_patterns
            
            return {
                'success': True,
                'formats': formats,
                'patterns': patterns,
                'count': len(formats)
            }
            
        except Exception as e:
            error_msg = f"获取表格式配置失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}
    
    @staticmethod
    def get_table_format(table_name: str) -> Dict[str, Any]:
        """获取指定表格式配置"""
        try:
            query = """
            SELECT table_name, headers, patterns, is_active, created_at, updated_at
            FROM selfcheck_table_formats 
            WHERE table_name = :table_name AND is_active = 1
            """
            
            result = execute_query(query, {'table_name': table_name})
            
            if not result:
                return {'success': False, 'error': f'表格式 {table_name} 不存在'}
            
            row = result[0]
            headers = json.loads(row['headers']) if row['headers'] else []
            patterns = json.loads(row['patterns']) if row['patterns'] else []
            
            return {
                'success': True,
                'table_name': table_name,
                'headers': headers,
                'patterns': patterns,
                'created_at': row['created_at'],
                'updated_at': row['updated_at']
            }
            
        except Exception as e:
            error_msg = f"获取表格式配置失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}
    
    @staticmethod
    def delete_table_format(table_name: str) -> Dict[str, Any]:
        """删除表格式配置"""
        try:
            logger.info(f"删除表格式配置: {table_name}")
            
            # 软删除（设置为不活跃）
            update_query = """
            UPDATE selfcheck_table_formats 
            SET is_active = 0, updated_at = CURRENT_TIMESTAMP
            WHERE table_name = :table_name
            """
            
            execute_update(update_query, {'table_name': table_name})
            
            logger.info(f"表格式配置删除成功: {table_name}")
            return {'success': True, 'message': f'表格式 {table_name} 删除成功'}
            
        except Exception as e:
            error_msg = f"删除表格式配置失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}
    
    @staticmethod
    def import_from_excel(excel_file_path: str) -> Dict[str, Any]:
        """从Excel文件导入表格式配置"""
        try:
            import pandas as pd
            
            logger.info(f"从Excel文件导入表格式配置: {excel_file_path}")
            
            # 读取Excel文件的所有工作表
            excel_file = pd.ExcelFile(excel_file_path)
            sheet_names = excel_file.sheet_names
            
            imported_count = 0
            errors = []
            
            for sheet_name in sheet_names:
                try:
                    # 读取工作表的表头（第一行）
                    df = pd.read_excel(excel_file_path, sheet_name=sheet_name, nrows=0)
                    headers = list(df.columns)
                    
                    if headers:
                        # 生成文件名模式
                        patterns = [sheet_name.lower(), sheet_name]
                        
                        # 添加表格式配置
                        result = TableConfigService.add_table_format(sheet_name, headers, patterns)
                        
                        if result['success']:
                            imported_count += 1
                            logger.info(f"成功导入工作表: {sheet_name}, 字段数: {len(headers)}")
                        else:
                            errors.append(f"工作表 {sheet_name}: {result['error']}")
                    else:
                        errors.append(f"工作表 {sheet_name}: 没有找到表头")
                        
                except Exception as e:
                    errors.append(f"工作表 {sheet_name}: {str(e)}")
            
            if imported_count > 0:
                message = f"成功导入 {imported_count} 个表格式配置"
                if errors:
                    message += f"，{len(errors)} 个失败"
                
                return {
                    'success': True,
                    'message': message,
                    'imported_count': imported_count,
                    'errors': errors
                }
            else:
                return {
                    'success': False,
                    'error': '没有成功导入任何表格式配置',
                    'errors': errors
                }
                
        except Exception as e:
            error_msg = f"从Excel导入表格式配置失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}
    
    @staticmethod
    def export_to_excel(output_path: str) -> Dict[str, Any]:
        """导出表格式配置到Excel文件"""
        try:
            import pandas as pd
            
            logger.info(f"导出表格式配置到Excel: {output_path}")
            
            # 获取所有表格式配置
            formats_result = TableConfigService.get_table_formats()
            
            if not formats_result['success']:
                return formats_result
            
            formats = formats_result['formats']
            
            # 创建Excel写入器
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                for table_name, headers in formats.items():
                    # 创建空的DataFrame，只包含表头
                    df = pd.DataFrame(columns=headers)
                    
                    # 写入工作表
                    df.to_excel(writer, sheet_name=table_name, index=False)
            
            logger.info(f"表格式配置导出成功: {output_path}")
            return {
                'success': True,
                'message': f'成功导出 {len(formats)} 个表格式配置到 {output_path}',
                'exported_count': len(formats)
            }
            
        except Exception as e:
            error_msg = f"导出表格式配置失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}
