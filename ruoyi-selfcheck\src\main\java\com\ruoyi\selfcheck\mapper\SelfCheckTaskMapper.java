package com.ruoyi.selfcheck.mapper;

import java.util.List;
import com.ruoyi.selfcheck.domain.SelfCheckTask;

/**
 * 自查任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface SelfCheckTaskMapper 
{
    /**
     * 查询自查任务
     * 
     * @param taskId 自查任务主键
     * @return 自查任务
     */
    public SelfCheckTask selectSelfCheckTaskByTaskId(Long taskId);

    /**
     * 查询自查任务列表
     * 
     * @param selfCheckTask 自查任务
     * @return 自查任务集合
     */
    public List<SelfCheckTask> selectSelfCheckTaskList(SelfCheckTask selfCheckTask);

    /**
     * 查询自查任务列表（包含关联信息）
     * 
     * @param selfCheckTask 自查任务
     * @return 自查任务集合
     */
    public List<SelfCheckTask> selectSelfCheckTaskListWithDetails(SelfCheckTask selfCheckTask);

    /**
     * 新增自查任务
     * 
     * @param selfCheckTask 自查任务
     * @return 结果
     */
    public int insertSelfCheckTask(SelfCheckTask selfCheckTask);

    /**
     * 修改自查任务
     * 
     * @param selfCheckTask 自查任务
     * @return 结果
     */
    public int updateSelfCheckTask(SelfCheckTask selfCheckTask);

    /**
     * 删除自查任务
     * 
     * @param taskId 自查任务主键
     * @return 结果
     */
    public int deleteSelfCheckTaskByTaskId(Long taskId);

    /**
     * 批量删除自查任务
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSelfCheckTaskByTaskIds(Long[] taskIds);

    /**
     * 根据用户ID查询任务列表
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    public List<SelfCheckTask> selectTaskListByUserId(Long userId);

    /**
     * 根据状态查询任务列表
     * 
     * @param status 状态
     * @return 任务列表
     */
    public List<SelfCheckTask> selectTaskListByStatus(String status);

    /**
     * 查询执行中的任务列表
     * 
     * @return 执行中的任务列表
     */
    public List<SelfCheckTask> selectRunningTaskList();

    /**
     * 查询已完成的任务列表（用于结果查看）
     * 
     * @param selfCheckTask 查询条件
     * @return 已完成的任务列表
     */
    public List<SelfCheckTask> selectCompletedTaskList(SelfCheckTask selfCheckTask);
}
