package com.ruoyi.selfcheck.service;

import java.util.List;
import com.ruoyi.selfcheck.domain.SelfCheckTask;

/**
 * 自查任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ISelfCheckTaskService 
{
    /**
     * 查询自查任务
     * 
     * @param taskId 自查任务主键
     * @return 自查任务
     */
    public SelfCheckTask selectSelfCheckTaskByTaskId(Long taskId);

    /**
     * 查询自查任务列表
     * 
     * @param selfCheckTask 自查任务
     * @return 自查任务集合
     */
    public List<SelfCheckTask> selectSelfCheckTaskList(SelfCheckTask selfCheckTask);

    /**
     * 查询自查任务列表（包含关联信息）
     * 
     * @param selfCheckTask 自查任务
     * @return 自查任务集合
     */
    public List<SelfCheckTask> selectSelfCheckTaskListWithDetails(SelfCheckTask selfCheckTask);

    /**
     * 新增自查任务
     * 
     * @param selfCheckTask 自查任务
     * @return 结果
     */
    public int insertSelfCheckTask(SelfCheckTask selfCheckTask);

    /**
     * 修改自查任务
     * 
     * @param selfCheckTask 自查任务
     * @return 结果
     */
    public int updateSelfCheckTask(SelfCheckTask selfCheckTask);

    /**
     * 批量删除自查任务
     * 
     * @param taskIds 需要删除的自查任务主键集合
     * @return 结果
     */
    public int deleteSelfCheckTaskByTaskIds(Long[] taskIds);

    /**
     * 删除自查任务信息
     * 
     * @param taskId 自查任务主键
     * @return 结果
     */
    public int deleteSelfCheckTaskByTaskId(Long taskId);

    /**
     * 创建自查任务
     * 
     * @param uploadId 上传数据ID
     * @param ruleId 规则ID
     * @param userId 用户ID
     * @param taskName 任务名称
     * @return 任务ID
     */
    public Long createTask(Long uploadId, Long ruleId, Long userId, String taskName);

    /**
     * 执行自查任务
     * 
     * @param taskId 任务ID
     * @return 执行结果
     */
    public boolean executeTask(Long taskId);

    /**
     * 停止自查任务
     * 
     * @param taskId 任务ID
     * @return 停止结果
     */
    public boolean stopTask(Long taskId);

    /**
     * 查询执行中的任务列表
     * 
     * @return 执行中的任务列表
     */
    public List<SelfCheckTask> selectRunningTaskList();

    /**
     * 查询已完成的任务列表（用于结果查看）
     * 
     * @param selfCheckTask 查询条件
     * @return 已完成的任务列表
     */
    public List<SelfCheckTask> selectCompletedTaskList(SelfCheckTask selfCheckTask);

    /**
     * 更新任务进度
     * 
     * @param taskId 任务ID
     * @param progress 进度
     */
    public void updateTaskProgress(Long taskId, Integer progress);

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 状态
     * @param resultSummary 结果摘要
     * @param resultDetail 详细结果
     * @param errorMessage 错误信息
     */
    public void updateTaskStatus(Long taskId, String status, String resultSummary, String resultDetail, String errorMessage);
}
