from flask import render_template, request, jsonify, flash, redirect, url_for, send_file
from flask_login import login_required, current_user
from app.data import bp
from app.auth.decorators import permission_required
from app.models.audit import AuditLog
from app.data.services import DuplicateFileService, DataValidationService, DataStandardizationService
from app.utils.database import handle_db_error
from app.utils.file_manager import FileManager, allowed_file
from app.utils.breadcrumb import generate_breadcrumb
from werkzeug.utils import secure_filename
import os
import tempfile
import json

@bp.route('/')
@login_required
@permission_required('data')
def index():
    """数据处理模块首页"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='data_index',
        details='访问数据处理模块首页'
    )
    breadcrumb_items = generate_breadcrumb('data')
    return render_template('data/index.html', breadcrumb_items=breadcrumb_items)

@bp.route('/find_duplicates')
@login_required
@permission_required('data.find_duplicates')
def find_duplicates():
    """查找重复文件"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='data_find_duplicates',
        details='访问查找重复文件'
    )
    breadcrumb_items = generate_breadcrumb('data', 'find_duplicates')
    return render_template('data/find_duplicates.html', breadcrumb_items=breadcrumb_items)

@bp.route('/validator')
@login_required
@permission_required('data.validator')
def validator():
    """数据校验"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='data_validator',
        details='访问数据校验'
    )
    breadcrumb_items = generate_breadcrumb('data', 'validator')
    return render_template('data/validator.html', breadcrumb_items=breadcrumb_items)

@bp.route('/standardization')
@login_required
@permission_required('data.standardization')
def standardization():
    """数据标准化"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='data_standardization',
        details='访问数据标准化'
    )
    breadcrumb_items = generate_breadcrumb('data', 'standardization')
    return render_template('data/standardization.html', breadcrumb_items=breadcrumb_items)

# API路由
@bp.route('/api/upload', methods=['POST'])
@login_required
@handle_db_error
def api_upload_file():
    """文件上传API"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        if not allowed_file(file.filename, {'xlsx', 'xls', 'csv'}):
            return jsonify({'success': False, 'error': '不支持的文件格式'}), 400

        # 保存文件到临时目录
        filename = secure_filename(file.filename)
        temp_path = os.path.join(tempfile.gettempdir(), f"data_upload_{current_user.id}_{filename}")
        file.save(temp_path)

        # 记录上传日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='upload',
            resource='data_file',
            details=f'上传文件: {filename}'
        )

        return jsonify({
            'success': True,
            'file_path': temp_path,
            'filename': filename,
            'file_size': os.path.getsize(temp_path)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/find_duplicates', methods=['POST'])
@login_required
@permission_required('data.find_duplicates')
@handle_db_error
def api_find_duplicates():
    """查找重复文件API"""
    try:
        data = request.get_json()
        directory_paths = data.get('directories', [])
        check_content = data.get('check_content', True)
        file_extensions = data.get('file_extensions', [])

        if not directory_paths:
            return jsonify({'success': False, 'error': '请指定要扫描的目录'}), 400

        # 验证目录是否存在
        valid_directories = []
        for directory in directory_paths:
            if os.path.exists(directory) and os.path.isdir(directory):
                valid_directories.append(directory)

        if not valid_directories:
            return jsonify({'success': False, 'error': '没有有效的目录'}), 400

        # 查找重复文件
        result = DuplicateFileService.find_duplicate_files(
            valid_directories,
            check_content,
            file_extensions
        )

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='find_duplicates',
            resource='file_system',
            details=f'扫描目录: {", ".join(valid_directories)}'
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/delete_duplicates', methods=['POST'])
@login_required
@permission_required('data.find_duplicates')
@handle_db_error
def api_delete_duplicates():
    """删除重复文件API"""
    try:
        data = request.get_json()
        file_paths = data.get('file_paths', [])

        if not file_paths:
            return jsonify({'success': False, 'error': '没有指定要删除的文件'}), 400

        # 删除文件
        result = DuplicateFileService.delete_duplicate_files(file_paths)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='delete_duplicates',
            resource='file_system',
            details=f'删除{result.get("deleted_count", 0)}个重复文件'
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/validate_data', methods=['POST'])
@login_required
@permission_required('data.validator')
@handle_db_error
def api_validate_data():
    """数据验证API"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        validation_rules = data.get('validation_rules', {})

        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 400

        # 执行数据验证
        result = DataValidationService.validate_excel_data(file_path, validation_rules)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='validate_data',
            resource='data_file',
            details=f'验证数据文件: {os.path.basename(file_path)}'
        )

        return jsonify({
            'success': True,
            'validation_result': result
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/standardize_data', methods=['POST'])
@login_required
@permission_required('data.standardization')
@handle_db_error
def api_standardize_data():
    """数据标准化API"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        standardization_rules = data.get('standardization_rules', {})

        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 400

        # 执行数据标准化
        result = DataStandardizationService.standardize_excel_data(file_path, standardization_rules)

        if result.get('success'):
            # 生成下载链接
            output_filename = f"standardized_{os.path.basename(file_path)}"
            result['download_url'] = f'/data/api/download/{os.path.basename(result["output_file"])}'
            result['output_filename'] = output_filename

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='standardize_data',
            resource='data_file',
            details=f'标准化数据文件: {os.path.basename(file_path)}'
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/download/<filename>')
@login_required
def api_download_file(filename):
    """文件下载API"""
    try:
        # 安全检查文件名
        if '..' in filename or '/' in filename or '\\' in filename:
            return jsonify({'error': '非法文件名'}), 400

        file_path = os.path.join(tempfile.gettempdir(), filename)

        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404

        # 记录下载日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='download',
            resource='data_file',
            resource_id=filename,
            details=f'下载文件: {filename}'
        )

        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        return jsonify({'error': str(e)}), 500
