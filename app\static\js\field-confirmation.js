/**
 * 字段确认功能 - 修复版本
 */

class FieldConfirmation {
    constructor() {
        this.currentRuleData = null;
        this.currentUploadId = null;
        this.pendingRules = [];
        this.currentRuleIndex = 0;
        
        this.initEventListeners();
    }
    
    initEventListeners() {
        // 确认字段按钮
        document.getElementById('confirmFieldsBtn').addEventListener('click', () => {
            this.confirmCurrentRule();
        });
        
        // 保存规则按钮
        document.getElementById('saveRuleBtn').addEventListener('click', () => {
            this.saveCurrentRule();
        });
        
        // 模态框关闭事件
        document.getElementById('fieldConfirmationModal').addEventListener('hidden.bs.modal', () => {
            this.resetModal();
        });
    }
    
    /**
     * 开始字段确认流程
     */
    startFieldConfirmation(uploadId, rules) {
        this.currentUploadId = uploadId;
        this.pendingRules = rules.filter(rule => rule.confidence < 0.8); // 只确认低置信度的规则
        this.currentRuleIndex = 0;
        
        if (this.pendingRules.length === 0) {
            alert('所有规则的置信度都很高，无需手动确认！');
            return;
        }
        
        this.showNextRule();
    }
    
    /**
     * 显示下一个需要确认的规则
     */
    showNextRule() {
        if (this.currentRuleIndex >= this.pendingRules.length) {
            alert('所有规则确认完成！');
            this.closeModal();
            return;
        }
        
        this.currentRuleData = this.pendingRules[this.currentRuleIndex];
        this.populateModal(this.currentRuleData);
        this.showModal();
    }
    
    /**
     * 填充模态框数据
     */
    populateModal(ruleData) {
        // 显示原始内容
        document.getElementById('originalContent').textContent = ruleData.original_content || '';
        
        // 填充表单字段
        document.getElementById('ruleSource').value = ruleData.rule_source || '';
        document.getElementById('city').value = ruleData.city || '';
        document.getElementById('sequenceNumber').value = ruleData.sequence_number || '';
        document.getElementById('department').value = ruleData.department || '';
        document.getElementById('violationType').value = ruleData.violation_type || '';
        document.getElementById('ruleName').value = ruleData.rule_name || '';
        document.getElementById('ruleContent').value = ruleData.rule_content || '';
        document.getElementById('medicalName1').value = ruleData.medical_name1 || '';
        document.getElementById('medicalName2').value = ruleData.medical_name2 || '';
        document.getElementById('timeType').value = ruleData.time_type || '';
        document.getElementById('violationCount').value = ruleData.violation_count || '';
        document.getElementById('confidence').value = ruleData.confidence || 0.5;
        
        // 更新模态框标题
        document.getElementById('fieldConfirmationModalLabel').textContent = 
            `规则字段确认 (${this.currentRuleIndex + 1}/${this.pendingRules.length})`;
    }
    
    /**
     * 确认当前规则
     */
    confirmCurrentRule() {
        // 获取表单数据
        const formData = new FormData(document.getElementById('fieldConfirmationForm'));
        const ruleData = {};
        
        for (let [key, value] of formData.entries()) {
            ruleData[key] = value;
        }
        
        // 更新当前规则数据
        Object.assign(this.currentRuleData, ruleData);
        this.currentRuleData.confidence = Math.max(parseFloat(ruleData.confidence) || 0.8, 0.8);
        this.currentRuleData.status = 'confirmed';
        
        // 显示下一个规则
        this.currentRuleIndex++;
        this.showNextRule();
    }
    
    /**
     * 保存当前规则
     */
    saveCurrentRule() {
        // 获取表单数据
        const formData = new FormData(document.getElementById('fieldConfirmationForm'));
        const ruleData = {};
        
        for (let [key, value] of formData.entries()) {
            ruleData[key] = value;
        }
        
        // 发送保存请求
        fetch(`/rules/api/converter/rules/${this.currentRuleData.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(ruleData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('规则保存成功！');
                this.currentRuleIndex++;
                this.showNextRule();
            } else {
                alert('保存失败：' + data.error);
            }
        })
        .catch(error => {
            console.error('保存规则失败:', error);
            alert('保存失败，请重试');
        });
    }
    
    /**
     * 显示模态框
     */
    showModal() {
        const modal = new bootstrap.Modal(document.getElementById('fieldConfirmationModal'));
        modal.show();
    }
    
    /**
     * 关闭模态框
     */
    closeModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('fieldConfirmationModal'));
        if (modal) {
            modal.hide();
        }
    }
    
    /**
     * 重置模态框
     */
    resetModal() {
        document.getElementById('fieldConfirmationForm').reset();
        document.getElementById('originalContent').textContent = '';
        this.currentRuleData = null;
    }
    
    /**
     * 批量确认所有规则
     */
    batchConfirmRules(uploadId) {
        if (!confirm('确定要批量确认所有规则吗？这将使用AI提取的字段值。')) {
            return;
        }
        
        fetch(`/rules/api/converter/batch-confirm/${uploadId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('批量确认成功！');
                // 刷新规则列表
                if (typeof loadRules === 'function') {
                    loadRules(uploadId);
                }
            } else {
                alert('批量确认失败：' + data.error);
            }
        })
        .catch(error => {
            console.error('批量确认失败:', error);
            alert('批量确认失败，请重试');
        });
    }
}

// 全局实例
const fieldConfirmation = new FieldConfirmation();

/**
 * 启动字段确认流程
 */
function startFieldConfirmation(uploadId, rules) {
    fieldConfirmation.startFieldConfirmation(uploadId, rules);
}

/**
 * 批量确认规则
 */
function batchConfirmRules(uploadId) {
    fieldConfirmation.batchConfirmRules(uploadId);
}
