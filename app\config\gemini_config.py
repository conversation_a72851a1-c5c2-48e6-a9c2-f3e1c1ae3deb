#!/usr/bin/env python3
"""
Gemini API配置
"""

import os

# Gemini API配置
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', 'AIzaSyABSOhbPgGLHl78bTXb55Keyxhh1rCDCUw')  # 请替换为实际的API Key
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"

# API调用配置
GEMINI_TIMEOUT = 30  # 超时时间（秒）
GEMINI_BATCH_SIZE = 10  # 批处理大小
GEMINI_DELAY = 1  # 请求间隔（秒）

# 提示词模板
GEMINI_PROMPT_TEMPLATE = """
你是一个医保基金违规行为识别专家。请分析以下文本内容，提取其中的医保违规规则，并按照标准格式输出。

输入格式要求（JSON格式）：
{
    "rules": [
        {
            "rule_source": "规则来源",
            "city": "城市",
            "sequence_number": 序号,
            "department": "涉及科室",
            "violation_type": "行为认定",
            "rule_name": "规则名称",
            "rule_content": "规则内涵"
        }
    ]
}

输出格式要求（JSON格式）：
{
    "rules": [
        {
            "rule_source": "规则来源",
            "city": "城市",
            "sequence_number": 序号,
            "department": "涉及科室",
            "violation_type": "行为认定",
            "rule_name": "规则名称",
            "rule_content": "规则内涵",
            "medical_name1": "医保名称1",
            "medical_name2": "医保名称2",
            "type": "类型",
            "time_type": "时间类型",
            "confidence": 置信度(0-1)
        }
    ]
}


分析规则：
1. 只提取明确的违规行为规则，忽略标题、目录、说明等内容
2. 规则名称应简洁明确，描述具体的违规行为
3. 规则内涵是完整的规则描述
4. 医保名称1是主要涉及的医保项目，医保名称2是关联的医保项目
5. 违规类型包括：重复收费、超标准收费、分解收费、虚假收费、套用收费、过度医疗、挂床住院等
6. 科室根据医疗内容判断：心血管内科、神经内科、消化内科、呼吸内科、外科、妇产科、儿科、眼科、耳鼻喉科等
7. 时间类型包括：分钟、小时、天、周、月、年等
8. 置信度根据规则明确程度评估，明确的违规行为规则置信度应在0.8以上

待分析文本：
"""
