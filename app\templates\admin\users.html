{% extends "base.html" %}

{% block title %}用户管理 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-users"></i> 用户管理</h4>
{% endblock %}

{% block content %}
<!-- 操作工具栏 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-search"></i> 用户搜索
                        </h6>
                    </div>
                    <div class="col-md-6 text-end">
                        {% if current_user.has_permission('system.user.create') %}
                        <button type="button" class="btn btn-primary" onclick="showCreateUserModal()">
                            <i class="fas fa-plus"></i> 新建用户
                        </button>
                        {% endif %}
                        <button type="button" class="btn btn-info" onclick="refreshUserList()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="searchUsername" placeholder="用户名">
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="searchRealName" placeholder="真实姓名">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="searchStatus">
                            <option value="">所有状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">禁用</option>
                            <option value="locked">锁定</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-outline-primary" onclick="searchUsers()">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list"></i> 用户列表
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="usersTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>真实姓名</th>
                                <th>邮箱</th>
                                <th>电话</th>
                                <th>部门</th>
                                <th>角色</th>
                                <th>微信绑定</th>
                                <th>状态</th>
                                <th>最后登录</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- 用户数据将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="用户列表分页">
                    <ul class="pagination justify-content-center" id="usersPagination">
                        <!-- 分页将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 创建用户模态框 -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i> 创建新用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="createUsername" class="form-label">用户名 *</label>
                                <input type="text" class="form-control" id="createUsername" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="createRealName" class="form-label">真实姓名 *</label>
                                <input type="text" class="form-control" id="createRealName" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="createEmail" class="form-label">邮箱 *</label>
                                <input type="email" class="form-control" id="createEmail" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="createPhone" class="form-label">电话</label>
                                <input type="tel" class="form-control" id="createPhone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="createDepartment" class="form-label">部门</label>
                                <input type="text" class="form-control" id="createDepartment">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="createRole" class="form-label">角色</label>
                                <select class="form-select" id="createRole">
                                    <option value="">选择角色</option>
                                    <!-- 角色选项将通过AJAX加载 -->
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="createPassword" class="form-label">密码 *</label>
                                <input type="password" class="form-control" id="createPassword" required>
                                <div class="form-text text-muted">
                                    <small>密码要求：至少8个字符，包含大写字母、小写字母、数字和特殊字符</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">确认密码 *</label>
                                <input type="password" class="form-control" id="confirmPassword" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="createIsAdmin">
                                <label class="form-check-label" for="createIsAdmin">
                                    管理员权限
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createUser()">创建用户</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit"></i> 编辑用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editUsername" class="form-label">用户名 *</label>
                                <input type="text" class="form-control" id="editUsername" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editRealName" class="form-label">真实姓名 *</label>
                                <input type="text" class="form-control" id="editRealName" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editEmail" class="form-label">邮箱 *</label>
                                <input type="email" class="form-control" id="editEmail" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPhone" class="form-label">电话</label>
                                <input type="tel" class="form-control" id="editPhone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editDepartment" class="form-label">部门</label>
                                <input type="text" class="form-control" id="editDepartment">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editRole" class="form-label">角色</label>
                                <select class="form-select" id="editRole">
                                    <option value="">选择角色</option>
                                    <!-- 角色选项将通过AJAX加载 -->
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editIsActive">
                                <label class="form-check-label" for="editIsActive">
                                    账户激活
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editIsAdmin">
                                <label class="form-check-label" for="editIsAdmin">
                                    管理员权限
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">更新用户</button>
            </div>
        </div>
    </div>
</div>

<!-- 重置密码模态框 -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key"></i> 重置密码
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="resetPasswordForm">
                    <input type="hidden" id="resetUserId">
                    <div class="mb-3">
                        <label for="resetUserInfo" class="form-label">用户信息</label>
                        <input type="text" class="form-control" id="resetUserInfo" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">新密码 *</label>
                        <input type="password" class="form-control" id="newPassword" required>
                        <div class="form-text text-muted">
                            <small>密码要求：至少8个字符，包含大写字母、小写字母、数字和特殊字符</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmNewPassword" class="form-label">确认新密码 *</label>
                        <input type="password" class="form-control" id="confirmNewPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="resetUserPassword()">重置密码</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let totalPages = 1;

// 页面加载完成后初始化
$(document).ready(function() {
    loadUserList();
    loadRoles();
});

// 加载用户列表
function loadUserList(page = 1) {
    currentPage = page;
    
    const searchParams = {
        page: page,
        per_page: 10,
        username: $('#searchUsername').val(),
        real_name: $('#searchRealName').val(),
        status: $('#searchStatus').val()
    };
    
    $.get('/admin/api/users', searchParams)
        .done(function(data) {
            displayUsers(data.users);
            updatePagination(data.current_page, data.pages, data.total);
        })
        .fail(function() {
            showAlert('加载用户列表失败', 'danger');
        });
}

// 显示用户列表
function displayUsers(users) {
    const tbody = $('#usersTableBody');
    tbody.empty();
    
    users.forEach(user => {
        const statusBadge = getStatusBadge(user.is_active, user.is_locked);
        const roleBadge = user.is_admin ? '<span class="badge bg-danger">管理员</span>' : '<span class="badge bg-info">普通用户</span>';
        const wechatBadge = getWechatBadge(user.wechat_bound, user.wechat_nickname);

        const row = `
            <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.real_name || '-'}</td>
                <td>${user.email}</td>
                <td>${user.phone || '-'}</td>
                <td>${user.department || '-'}</td>
                <td>${roleBadge}</td>
                <td>${wechatBadge}</td>
                <td>${statusBadge}</td>
                <td>${user.last_login ? new Date(user.last_login).toLocaleString() : '从未登录'}</td>
                <td>${new Date(user.created_at).toLocaleString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        {% if current_user.has_permission('system.user.edit') %}
                        <button class="btn btn-outline-primary" onclick="showEditUserModal(${user.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        {% endif %}
                        {% if current_user.has_permission('system.user.reset_password') %}
                        <button class="btn btn-outline-warning" onclick="showResetPasswordModal(${user.id}, '${user.username}')" title="重置密码">
                            <i class="fas fa-key"></i>
                        </button>
                        {% endif %}
                        {% if current_user.has_permission('system.user.delete') %}
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id}, '${user.username}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                        {% endif %}
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 获取状态徽章
function getStatusBadge(isActive, isLocked) {
    if (isLocked) {
        return '<span class="badge bg-danger">锁定</span>';
    } else if (isActive) {
        return '<span class="badge bg-success">活跃</span>';
    } else {
        return '<span class="badge bg-secondary">禁用</span>';
    }
}

// 获取微信绑定徽章
function getWechatBadge(isBound, nickname) {
    if (isBound) {
        const displayName = nickname ? nickname.substring(0, 8) + (nickname.length > 8 ? '...' : '') : '已绑定';
        return `<span class="badge bg-success" title="微信昵称: ${nickname || '未知'}">
                    <i class="fab fa-weixin"></i> ${displayName}
                </span>`;
    } else {
        return '<span class="badge bg-secondary">未绑定</span>';
    }
}

// 更新分页
function updatePagination(currentPage, totalPages, totalItems) {
    const pagination = $('#usersPagination');
    pagination.empty();
    
    // 上一页
    const prevDisabled = currentPage === 1 ? 'disabled' : '';
    pagination.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="loadUserList(${currentPage - 1})">上一页</a>
        </li>
    `);
    
    // 页码
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const active = i === currentPage ? 'active' : '';
        pagination.append(`
            <li class="page-item ${active}">
                <a class="page-link" href="#" onclick="loadUserList(${i})">${i}</a>
            </li>
        `);
    }
    
    // 下一页
    const nextDisabled = currentPage === totalPages ? 'disabled' : '';
    pagination.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="loadUserList(${currentPage + 1})">下一页</a>
        </li>
    `);
    
    // 显示总数信息
    pagination.append(`
        <li class="page-item disabled">
            <span class="page-link">共 ${totalItems} 条记录</span>
        </li>
    `);
}

// 加载角色列表
function loadRoles() {
    $.get('/admin/api/roles')
        .done(function(data) {
            const createRoleSelect = $('#createRole');
            const editRoleSelect = $('#editRole');
            
            createRoleSelect.empty().append('<option value="">选择角色</option>');
            editRoleSelect.empty().append('<option value="">选择角色</option>');
            
            data.roles.forEach(role => {
                const option = `<option value="${role.id}">${role.name}</option>`;
                createRoleSelect.append(option);
                editRoleSelect.append(option);
            });
        })
        .fail(function() {
            console.error('加载角色列表失败');
        });
}

// 搜索用户
function searchUsers() {
    loadUserList(1);
}

// 重置搜索
function resetSearch() {
    $('#searchUsername').val('');
    $('#searchRealName').val('');
    $('#searchStatus').val('');
    loadUserList(1);
}

// 刷新用户列表
function refreshUserList() {
    loadUserList(currentPage);
}

// 显示创建用户模态框
function showCreateUserModal() {
    $('#createUserForm')[0].reset();
    $('#createUserModal').modal('show');
}

// 创建用户
function createUser() {
    const password = $('#createPassword').val();
    const confirmPassword = $('#confirmPassword').val();

    if (password !== confirmPassword) {
        showAlert('两次输入的密码不一致', 'warning');
        return;
    }

    const userData = {
        username: $('#createUsername').val(),
        real_name: $('#createRealName').val(),
        email: $('#createEmail').val(),
        phone: $('#createPhone').val(),
        department: $('#createDepartment').val(),
        role_id: $('#createRole').val(),
        password: password,
        is_admin: $('#createIsAdmin').is(':checked')
    };

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/admin/api/users',
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(userData),
        dataType: 'json'
    })
    .done(function(data) {
        if (data.success) {
            $('#createUserModal').modal('hide');
            showAlert('用户创建成功', 'success');
            loadUserList(currentPage);
        } else {
            showAlert('创建失败: ' + data.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

// 显示编辑用户模态框
function showEditUserModal(userId) {
    $.get(`/admin/api/users/${userId}`)
        .done(function(user) {
            $('#editUserId').val(user.id);
            $('#editUsername').val(user.username);
            $('#editRealName').val(user.real_name);
            $('#editEmail').val(user.email);
            $('#editPhone').val(user.phone);
            $('#editDepartment').val(user.department);
            $('#editRole').val(user.role_id);
            $('#editIsActive').prop('checked', user.is_active);
            $('#editIsAdmin').prop('checked', user.is_admin);
            
            $('#editUserModal').modal('show');
        })
        .fail(function() {
            showAlert('加载用户信息失败', 'danger');
        });
}

// 更新用户
function updateUser() {
    const userId = $('#editUserId').val();
    const userData = {
        username: $('#editUsername').val(),
        real_name: $('#editRealName').val(),
        email: $('#editEmail').val(),
        phone: $('#editPhone').val(),
        department: $('#editDepartment').val(),
        role_id: $('#editRole').val(),
        is_active: $('#editIsActive').is(':checked'),
        is_admin: $('#editIsAdmin').is(':checked')
    };
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: `/admin/api/users/${userId}`,
        method: 'PUT',
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(userData),
        dataType: 'json'
    })
    .done(function(data) {
        if (data.success) {
            $('#editUserModal').modal('hide');
            showAlert('用户信息更新成功', 'success');
            loadUserList(currentPage);
        } else {
            showAlert('更新失败: ' + data.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

// 显示重置密码模态框
function showResetPasswordModal(userId, username) {
    $('#resetUserId').val(userId);
    $('#resetUserInfo').val(`${username} (ID: ${userId})`);
    $('#newPassword').val('');
    $('#confirmNewPassword').val('');
    $('#resetPasswordModal').modal('show');
}

// 重置用户密码
function resetUserPassword() {
    const userId = $('#resetUserId').val();
    const newPassword = $('#newPassword').val();
    const confirmNewPassword = $('#confirmNewPassword').val();
    
    if (newPassword !== confirmNewPassword) {
        showAlert('两次输入的密码不一致', 'warning');
        return;
    }
    
    if (!confirm('确定要重置该用户的密码吗？')) {
        return;
    }
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: `/admin/api/users/${userId}/reset_password`,
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({ password: newPassword }),
        dataType: 'json'
    })
    .done(function(data) {
        if (data.success) {
            $('#resetPasswordModal').modal('hide');
            showAlert('密码重置成功', 'success');
        } else {
            showAlert('重置失败: ' + data.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

// 删除用户
function deleteUser(userId, username) {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) {
        return;
    }
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: `/admin/api/users/${userId}`,
        method: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(data) {
        if (data.success) {
            showAlert('用户删除成功', 'success');
            loadUserList(currentPage);
        } else {
            showAlert('删除失败: ' + data.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

// 显示提示信息 - 使用右上角悬浮显示，避免遮挡用户菜单
function showAlert(message, type = 'info') {
    // 移除已存在的alert
    $('.alert.position-fixed').remove();

    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('body').append(alertHtml);

    // 自动关闭
    setTimeout(function() {
        $('.alert.position-fixed').fadeOut(500, function() {
            $(this).remove();
        });
    }, 4000);
}
</script>
{% endblock %}
