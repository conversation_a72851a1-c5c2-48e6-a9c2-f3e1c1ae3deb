# 自查自纠模块专业测试套件

## 概述

这是一个专业的自查自纠模块测试套件，包含完整的测试用例、自动化测试脚本、测试工具和数据生成器。测试套件覆盖了规则管理、规则导入、方案管理、数据上传、任务管理等核心功能。

## 测试目录结构

```
tests/selfcheck/
├── README.md                    # 测试说明文档
├── test_config.py              # 测试配置文件
├── quick_test.py               # 快速冒烟测试
├── run_all_tests.py            # 全量测试运行器
├── test_cases/                  # 测试用例文档
│   ├── 01_规则管理测试用例.md
│   ├── 02_规则导入测试用例.md
│   ├── 03_方案管理测试用例.md
│   ├── 04_数据上传测试用例.md
│   ├── 05_任务管理测试用例.md
│   └── 06_集成测试用例.md
├── test_scripts/                # 自动化测试脚本
│   ├── test_rules_management.py
│   ├── test_rules_import.py
│   ├── test_scheme_management.py
│   ├── test_data_upload.py
│   ├── test_task_management.py
│   └── test_integration.py
├── test_data/                   # 测试数据
│   ├── sample_data.csv
│   ├── sample_rules.json
│   └── test_schemes.json
├── utils/                       # 测试工具
│   ├── __init__.py
│   ├── test_base.py            # 测试基础类
│   ├── api_client.py           # API客户端
│   └── data_generator.py       # 数据生成器
└── reports/                     # 测试报告
    ├── test_results.html
    └── coverage_report.html
```

## 快速开始

### 1. 环境检查
```bash
# 检查测试环境
cd tests/selfcheck
python test_config.py
```

### 2. 快速冒烟测试
```bash
# 运行快速测试（约2-3分钟）
python quick_test.py
```

### 3. 全量测试
```bash
# 运行所有测试模块（约10-15分钟）
python run_all_tests.py
```

## 详细使用说明

### 测试环境要求

- **Python**: 3.8+
- **Flask应用**: 运行在 localhost:5002
- **数据库**: Oracle数据库连接正常
- **权限**: 测试用户具有完整的模块权限

### 测试用户配置

在 `test_config.py` 中配置测试用户：

```python
'ADMIN_USER': {
    'username': 'admin',
    'password': 'Admin123!'
},
'TEST_USER': {
    'username': 'test_user',
    'password': 'Test123!'
}
```

### 运行特定测试模块

```bash
# 规则管理模块测试
python test_scripts/test_rules_management.py

# 规则导入模块测试
python test_scripts/test_rules_import.py

# 方案管理模块测试
python test_scripts/test_scheme_management.py
```

### 性能测试

测试套件包含性能测试，验证以下指标：
- 页面加载时间 < 3秒
- 搜索响应时间 < 2秒
- 规则导入速度 > 1规则/秒
- 批量导入时间 < 30秒

### 测试数据管理

#### 自动生成测试数据
```python
from utils.data_generator import TestDataGenerator

generator = TestDataGenerator()
# 生成规则数据
rules = generator.generate_rule_data(count=50)
# 生成方案数据
schemes = generator.generate_scheme_data(count=20)
# 生成CSV文件
generator.generate_csv_file('test_data.csv', row_count=1000)
```

#### 使用预定义测试数据
- `test_data/sample_rules.json` - 示例规则数据
- `test_data/test_schemes.json` - 示例方案数据
- `test_data/sample_data.csv` - 示例CSV数据

## 测试报告

### HTML报告
测试完成后会自动生成HTML格式的详细报告：
- 测试概览和统计
- 各模块测试结果
- 失败和错误详情
- 性能指标分析

### 报告位置
- `reports/test_report_YYYYMMDD_HHMMSS.html`
- `reports/coverage_report.html`

## 测试用例覆盖

### 功能测试
- ✅ 规则CRUD操作
- ✅ 规则搜索和过滤
- ✅ 规则导入功能
- ✅ 方案管理
- ✅ 数据上传
- ✅ 任务管理

### 性能测试
- ✅ 大数据量加载
- ✅ 搜索响应时间
- ✅ 批量导入性能
- ✅ 并发操作

### 异常测试
- ✅ 网络异常处理
- ✅ 数据库异常处理
- ✅ 输入验证
- ✅ 权限控制

### 兼容性测试
- ✅ 浏览器兼容性
- ✅ 数据格式兼容性
- ✅ 版本兼容性

## 最佳实践

### 1. 测试前准备
- 确保应用正常运行
- 备份重要数据
- 检查测试环境配置

### 2. 测试执行
- 先运行快速测试验证基本功能
- 再运行全量测试进行完整验证
- 关注测试报告中的失败和错误

### 3. 测试后处理
- 查看测试报告
- 分析失败原因
- 清理测试数据

## 故障排除

### 常见问题

1. **应用连接失败**
   ```
   解决方案：检查应用是否在5002端口运行
   ```

2. **登录失败**
   ```
   解决方案：检查test_config.py中的用户凭据
   ```

3. **数据库连接异常**
   ```
   解决方案：检查Oracle数据库连接配置
   ```

4. **权限不足**
   ```
   解决方案：确保测试用户具有完整的模块权限
   ```

### 调试模式

启用详细日志输出：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展测试

### 添加新测试用例
1. 在对应的测试脚本中添加测试方法
2. 遵循命名规范：`test_TCXXX_功能描述`
3. 使用测试基础类提供的工具方法
4. 添加适当的断言和错误处理

### 自定义测试数据
1. 修改 `data_generator.py` 中的生成逻辑
2. 或在 `test_data/` 目录添加自定义数据文件
3. 在测试脚本中引用自定义数据

## 贡献指南

1. 遵循现有的代码风格和命名规范
2. 为新功能添加相应的测试用例
3. 更新相关文档
4. 确保所有测试通过

## 联系方式

如有问题或建议，请联系测试团队。
