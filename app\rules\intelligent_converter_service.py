#!/usr/bin/env python3
"""
医保基金负面清单智能转换服务
支持Excel、PDF、Word格式文件的智能解析和规则提取
"""

import os
import json
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
import re

from .database import get_db_manager, execute_query, execute_update

logger = logging.getLogger(__name__)


class IntelligentConverterService:
    """智能转换服务类"""
    
    # 支持的文件格式
    SUPPORTED_FORMATS = ['xlsx', 'xls', 'pdf', 'doc', 'docx']
    
    # 规则条目识别模式
    RULE_PATTERNS = [
        r'不得|禁止|严禁|不允许|不可',  # 禁止性词汇
        r'超出|超过|违反|不符合',      # 违规性词汇
        r'重复|多次|频繁',            # 频次性词汇
        r'虚假|伪造|冒用',            # 欺诈性词汇
    ]
    
    # 医保相关关键词
    MEDICAL_KEYWORDS = [
        '医保', '医疗保险', '基金', '报销', '结算', '费用',
        '诊疗', '药品', '耗材', '检查', '治疗', '手术',
        '住院', '门诊', '急诊', '康复', '护理'
    ]
    
    @staticmethod
    def upload_file(file, user_id: int) -> Dict[str, Any]:
        """上传文件"""
        try:
            if not file or not file.filename:
                return {'success': False, 'error': '没有选择文件'}
            
            # 检查文件格式
            file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
            if file_ext not in IntelligentConverterService.SUPPORTED_FORMATS:
                return {'success': False, 'error': f'不支持的文件格式，支持格式：{", ".join(IntelligentConverterService.SUPPORTED_FORMATS)}'}
            
            # 创建上传目录
            upload_dir = os.path.join('uploads', 'converter', str(user_id))
            os.makedirs(upload_dir, exist_ok=True)
            
            # 生成唯一文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{file.filename}"
            file_path = os.path.join(upload_dir, filename)
            
            # 保存文件
            file.save(file_path)
            
            # 记录到数据库
            insert_sql = """
            INSERT INTO converter_uploads (
                id, user_id, original_filename, file_path, file_size, file_type, 
                status, upload_time
            ) VALUES (
                converter_uploads_seq.NEXTVAL, :user_id, :original_filename, :file_path, 
                :file_size, :file_type, 'uploaded', CURRENT_TIMESTAMP
            )
            """
            
            file_size = os.path.getsize(file_path)
            params = {
                'user_id': user_id,
                'original_filename': file.filename,
                'file_path': file_path,
                'file_size': file_size,
                'file_type': file_ext
            }
            
            execute_update(insert_sql, params)
            
            # 获取插入的记录ID
            id_query = "SELECT converter_uploads_seq.CURRVAL as id FROM dual"
            id_result = execute_query(id_query)
            upload_id = id_result[0]['id'] if id_result else None
            
            logger.info(f"文件上传成功: {file.filename}, ID: {upload_id}")
            
            return {
                'success': True,
                'upload_id': upload_id,
                'filename': file.filename,
                'file_size': file_size,
                'file_type': file_ext,
                'message': '文件上传成功'
            }
            
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'文件上传失败: {str(e)}'}
    
    @staticmethod
    def parse_file(upload_id: int) -> Dict[str, Any]:
        """解析文件内容"""
        try:
            # 获取上传记录
            upload_query = "SELECT * FROM converter_uploads WHERE id = :upload_id"
            upload_result = execute_query(upload_query, {'upload_id': upload_id})
            
            if not upload_result:
                return {'success': False, 'error': '上传记录不存在'}
            
            upload = upload_result[0]
            file_path = upload['file_path']
            file_type = upload['file_type']
            
            # 更新状态为解析中
            IntelligentConverterService._update_status(upload_id, 'parsing', '正在解析文件...')
            
            # 根据文件类型选择解析方法
            if file_type in ['xlsx', 'xls']:
                result = IntelligentConverterService._parse_excel(file_path)
            elif file_type == 'pdf':
                result = IntelligentConverterService._parse_pdf(file_path)
            elif file_type in ['doc', 'docx']:
                result = IntelligentConverterService._parse_word(file_path)
            else:
                return {'success': False, 'error': f'不支持的文件类型: {file_type}'}
            
            if result['success']:
                # 智能识别规则条目
                rules = IntelligentConverterService._extract_rules(result['content'])
                
                # 保存解析结果
                save_result = IntelligentConverterService._save_parse_result(upload_id, rules)
                
                if save_result['success']:
                    IntelligentConverterService._update_status(upload_id, 'parsed', f'解析完成，识别到 {len(rules)} 条规则')
                    
                    return {
                        'success': True,
                        'rules_count': len(rules),
                        'rules': rules[:10],  # 返回前10条预览
                        'message': f'文件解析成功，识别到 {len(rules)} 条规则'
                    }
                else:
                    IntelligentConverterService._update_status(upload_id, 'failed', f'保存解析结果失败: {save_result["error"]}')
                    return save_result
            else:
                IntelligentConverterService._update_status(upload_id, 'failed', f'文件解析失败: {result["error"]}')
                return result
                
        except Exception as e:
            error_msg = f"文件解析失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            IntelligentConverterService._update_status(upload_id, 'failed', error_msg)
            return {'success': False, 'error': error_msg}
    
    @staticmethod
    def _parse_excel(file_path: str) -> Dict[str, Any]:
        """解析Excel文件"""
        try:
            logger.info(f"开始解析Excel文件: {file_path}")
            
            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            all_content = []
            
            for sheet_name in excel_file.sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    # 转换为文本内容
                    for _, row in df.iterrows():
                        row_text = ' '.join([str(cell) for cell in row.values if pd.notna(cell)])
                        if row_text.strip():
                            all_content.append({
                                'sheet': sheet_name,
                                'content': row_text.strip(),
                                'type': 'table_row'
                            })
                            
                except Exception as e:
                    logger.warning(f"解析工作表 {sheet_name} 失败: {str(e)}")
                    continue
            
            logger.info(f"Excel解析完成，提取 {len(all_content)} 行内容")
            return {'success': True, 'content': all_content}
            
        except Exception as e:
            logger.error(f"Excel解析失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'Excel解析失败: {str(e)}'}
    
    @staticmethod
    def _parse_pdf(file_path: str) -> Dict[str, Any]:
        """解析PDF文件"""
        try:
            logger.info(f"开始解析PDF文件: {file_path}")
            
            # 这里需要安装PyPDF2或pdfplumber库
            try:
                import PyPDF2
                
                all_content = []
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    
                    for page_num, page in enumerate(pdf_reader.pages):
                        try:
                            text = page.extract_text()
                            if text.strip():
                                # 按行分割
                                lines = text.split('\n')
                                for line in lines:
                                    if line.strip():
                                        all_content.append({
                                            'page': page_num + 1,
                                            'content': line.strip(),
                                            'type': 'text_line'
                                        })
                        except Exception as e:
                            logger.warning(f"解析PDF第{page_num + 1}页失败: {str(e)}")
                            continue
                
                logger.info(f"PDF解析完成，提取 {len(all_content)} 行内容")
                return {'success': True, 'content': all_content}
                
            except ImportError:
                return {'success': False, 'error': 'PDF解析功能需要安装PyPDF2库'}
                
        except Exception as e:
            logger.error(f"PDF解析失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'PDF解析失败: {str(e)}'}
    
    @staticmethod
    def _parse_word(file_path: str) -> Dict[str, Any]:
        """解析Word文件"""
        try:
            logger.info(f"开始解析Word文件: {file_path}")
            
            # 这里需要安装python-docx库
            try:
                from docx import Document
                
                doc = Document(file_path)
                all_content = []
                
                # 解析段落
                for para_num, paragraph in enumerate(doc.paragraphs):
                    if paragraph.text.strip():
                        all_content.append({
                            'paragraph': para_num + 1,
                            'content': paragraph.text.strip(),
                            'type': 'paragraph'
                        })
                
                # 解析表格
                for table_num, table in enumerate(doc.tables):
                    for row_num, row in enumerate(table.rows):
                        row_text = ' '.join([cell.text.strip() for cell in row.cells if cell.text.strip()])
                        if row_text:
                            all_content.append({
                                'table': table_num + 1,
                                'row': row_num + 1,
                                'content': row_text,
                                'type': 'table_row'
                            })
                
                logger.info(f"Word解析完成，提取 {len(all_content)} 项内容")
                return {'success': True, 'content': all_content}
                
            except ImportError:
                return {'success': False, 'error': 'Word解析功能需要安装python-docx库'}
                
        except Exception as e:
            logger.error(f"Word解析失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'Word解析失败: {str(e)}'}
    
    @staticmethod
    def _extract_rules(content_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """从内容中智能提取规则条目"""
        try:
            logger.info("开始智能提取规则条目")
            
            rules = []
            rule_id = 1
            
            for item in content_list:
                content = item['content']
                
                # 检查是否包含规则特征
                if IntelligentConverterService._is_rule_content(content):
                    rule = {
                        'id': rule_id,
                        'original_content': content,
                        'rule_name': IntelligentConverterService._extract_rule_name(content),
                        'rule_description': content,
                        'rule_type': IntelligentConverterService._classify_rule_type(content),
                        'medical_category': IntelligentConverterService._extract_medical_category(content),
                        'severity': IntelligentConverterService._assess_severity(content),
                        'source_info': {
                            'sheet': item.get('sheet'),
                            'page': item.get('page'),
                            'paragraph': item.get('paragraph'),
                            'table': item.get('table'),
                            'type': item.get('type')
                        },
                        'confidence': IntelligentConverterService._calculate_confidence(content),
                        'status': 'pending_review',
                        'created_at': datetime.now().isoformat()
                    }
                    
                    rules.append(rule)
                    rule_id += 1
            
            logger.info(f"规则提取完成，共识别 {len(rules)} 条规则")
            return rules
            
        except Exception as e:
            logger.error(f"规则提取失败: {str(e)}", exc_info=True)
            return []
    
    @staticmethod
    def _is_rule_content(content: str) -> bool:
        """判断内容是否为规则条目"""
        # 检查是否包含规则特征词汇
        for pattern in IntelligentConverterService.RULE_PATTERNS:
            if re.search(pattern, content):
                # 同时检查是否包含医保相关词汇
                for keyword in IntelligentConverterService.MEDICAL_KEYWORDS:
                    if keyword in content:
                        return True
        return False
    
    @staticmethod
    def _extract_rule_name(content: str) -> str:
        """提取规则名称"""
        # 简单提取：取前50个字符作为规则名称
        name = content[:50].strip()
        if len(content) > 50:
            name += "..."
        return name
    
    @staticmethod
    def _classify_rule_type(content: str) -> str:
        """分类规则类型"""
        if re.search(r'不得|禁止|严禁', content):
            return '禁止性规则'
        elif re.search(r'超出|超过|违反', content):
            return '限制性规则'
        elif re.search(r'重复|多次|频繁', content):
            return '频次性规则'
        elif re.search(r'虚假|伪造|冒用', content):
            return '欺诈性规则'
        else:
            return '其他规则'
    
    @staticmethod
    def _extract_medical_category(content: str) -> str:
        """提取医疗类别"""
        categories = {
            '药品': ['药品', '药物', '处方', '用药'],
            '诊疗': ['诊疗', '诊断', '治疗', '手术'],
            '检查': ['检查', '检验', '化验', '影像'],
            '耗材': ['耗材', '器械', '设备'],
            '住院': ['住院', '病房', '床位'],
            '门诊': ['门诊', '急诊', '挂号']
        }
        
        for category, keywords in categories.items():
            for keyword in keywords:
                if keyword in content:
                    return category
        
        return '通用'
    
    @staticmethod
    def _assess_severity(content: str) -> str:
        """评估严重程度"""
        high_severity_words = ['严禁', '禁止', '违法', '欺诈', '虚假']
        medium_severity_words = ['不得', '不允许', '违反', '超出']
        
        for word in high_severity_words:
            if word in content:
                return '高'
        
        for word in medium_severity_words:
            if word in content:
                return '中'
        
        return '低'
    
    @staticmethod
    def _calculate_confidence(content: str) -> float:
        """计算识别置信度"""
        confidence = 0.0
        
        # 基础分数
        confidence += 0.3
        
        # 包含规则特征词汇
        for pattern in IntelligentConverterService.RULE_PATTERNS:
            if re.search(pattern, content):
                confidence += 0.2
                break
        
        # 包含医保关键词
        medical_count = sum(1 for keyword in IntelligentConverterService.MEDICAL_KEYWORDS if keyword in content)
        confidence += min(medical_count * 0.1, 0.3)
        
        # 内容长度合理性
        if 20 <= len(content) <= 200:
            confidence += 0.2
        
        return min(confidence, 1.0)
    
    @staticmethod
    def _save_parse_result(upload_id: int, rules: List[Dict[str, Any]]) -> Dict[str, Any]:
        """保存解析结果"""
        try:
            # 保存规则到数据库
            for rule in rules:
                insert_sql = """
                INSERT INTO converter_rules (
                    id, upload_id, rule_name, rule_description, rule_type, 
                    medical_category, severity, confidence, source_info, 
                    original_content, status, created_at
                ) VALUES (
                    converter_rules_seq.NEXTVAL, :upload_id, :rule_name, :rule_description, 
                    :rule_type, :medical_category, :severity, :confidence, :source_info, 
                    :original_content, :status, CURRENT_TIMESTAMP
                )
                """
                
                params = {
                    'upload_id': upload_id,
                    'rule_name': rule['rule_name'],
                    'rule_description': rule['rule_description'],
                    'rule_type': rule['rule_type'],
                    'medical_category': rule['medical_category'],
                    'severity': rule['severity'],
                    'confidence': rule['confidence'],
                    'source_info': json.dumps(rule['source_info'], ensure_ascii=False),
                    'original_content': rule['original_content'],
                    'status': rule['status']
                }
                
                execute_update(insert_sql, params)
            
            logger.info(f"保存解析结果成功，共 {len(rules)} 条规则")
            return {'success': True, 'message': f'保存 {len(rules)} 条规则成功'}
            
        except Exception as e:
            logger.error(f"保存解析结果失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'保存解析结果失败: {str(e)}'}
    
    @staticmethod
    def _update_status(upload_id: int, status: str, message: str = None):
        """更新上传记录状态"""
        try:
            update_sql = """
            UPDATE converter_uploads 
            SET status = :status, status_message = :message, updated_at = CURRENT_TIMESTAMP
            WHERE id = :upload_id
            """
            
            execute_update(update_sql, {
                'upload_id': upload_id,
                'status': status,
                'message': message
            })
            
        except Exception as e:
            logger.error(f"更新状态失败: {str(e)}")
    
    @staticmethod
    def get_upload_list(user_id: int, page: int = 1, per_page: int = 10) -> Dict[str, Any]:
        """获取上传列表"""
        try:
            offset = (page - 1) * per_page
            
            # 获取总数
            count_sql = "SELECT COUNT(*) as total FROM converter_uploads WHERE user_id = :user_id"
            count_result = execute_query(count_sql, {'user_id': user_id})
            total = count_result[0]['total'] if count_result else 0
            
            # 获取列表
            list_sql = """
            SELECT id, original_filename, file_size, file_type, status, status_message,
                   upload_time, updated_at,
                   (SELECT COUNT(*) FROM converter_rules WHERE upload_id = converter_uploads.id) as rules_count
            FROM converter_uploads 
            WHERE user_id = :user_id
            ORDER BY upload_time DESC
            OFFSET :offset ROWS FETCH NEXT :per_page ROWS ONLY
            """
            
            uploads = execute_query(list_sql, {
                'user_id': user_id,
                'offset': offset,
                'per_page': per_page
            })
            
            return {
                'success': True,
                'uploads': uploads,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page if total > 0 else 0
                }
            }
            
        except Exception as e:
            logger.error(f"获取上传列表失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'获取上传列表失败: {str(e)}'}
    
    @staticmethod
    def get_rules(upload_id: int, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """获取解析的规则列表"""
        try:
            offset = (page - 1) * per_page
            
            # 获取总数
            count_sql = "SELECT COUNT(*) as total FROM converter_rules WHERE upload_id = :upload_id"
            count_result = execute_query(count_sql, {'upload_id': upload_id})
            total = count_result[0]['total'] if count_result else 0
            
            # 获取规则列表
            rules_sql = """
            SELECT id, rule_name, rule_description, rule_type, medical_category, 
                   severity, confidence, status, created_at, updated_at
            FROM converter_rules 
            WHERE upload_id = :upload_id
            ORDER BY confidence DESC, created_at ASC
            OFFSET :offset ROWS FETCH NEXT :per_page ROWS ONLY
            """
            
            rules = execute_query(rules_sql, {
                'upload_id': upload_id,
                'offset': offset,
                'per_page': per_page
            })
            
            return {
                'success': True,
                'rules': rules,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page if total > 0 else 0
                }
            }
            
        except Exception as e:
            logger.error(f"获取规则列表失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'获取规则列表失败: {str(e)}'}
    
    @staticmethod
    def update_rule(rule_id: int, rule_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新规则"""
        try:
            update_sql = """
            UPDATE converter_rules 
            SET rule_name = :rule_name, rule_description = :rule_description,
                rule_type = :rule_type, medical_category = :medical_category,
                severity = :severity, status = :status, updated_at = CURRENT_TIMESTAMP
            WHERE id = :rule_id
            """
            
            params = {
                'rule_id': rule_id,
                'rule_name': rule_data.get('rule_name'),
                'rule_description': rule_data.get('rule_description'),
                'rule_type': rule_data.get('rule_type'),
                'medical_category': rule_data.get('medical_category'),
                'severity': rule_data.get('severity'),
                'status': rule_data.get('status', 'reviewed')
            }
            
            execute_update(update_sql, params)
            
            return {'success': True, 'message': '规则更新成功'}
            
        except Exception as e:
            logger.error(f"更新规则失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'更新规则失败: {str(e)}'}
    
    @staticmethod
    def delete_rule(rule_id: int) -> Dict[str, Any]:
        """删除规则"""
        try:
            delete_sql = "DELETE FROM converter_rules WHERE id = :rule_id"
            execute_update(delete_sql, {'rule_id': rule_id})
            
            return {'success': True, 'message': '规则删除成功'}
            
        except Exception as e:
            logger.error(f"删除规则失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'删除规则失败: {str(e)}'}
    
    @staticmethod
    def export_rules(upload_id: int) -> Dict[str, Any]:
        """导出规则为Excel文件"""
        try:
            # 获取所有规则
            rules_sql = """
            SELECT rule_name, rule_description, rule_type, medical_category, 
                   severity, confidence, status, created_at
            FROM converter_rules 
            WHERE upload_id = :upload_id AND status != 'deleted'
            ORDER BY confidence DESC, created_at ASC
            """
            
            rules = execute_query(rules_sql, {'upload_id': upload_id})
            
            if not rules:
                return {'success': False, 'error': '没有可导出的规则'}
            
            # 创建DataFrame
            df = pd.DataFrame(rules)
            
            # 重命名列
            df.columns = ['规则名称', '规则描述', '规则类型', '医疗类别', '严重程度', '置信度', '状态', '创建时间']
            
            # 生成文件路径
            export_dir = os.path.join('exports', 'converter')
            os.makedirs(export_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"负面清单规则_{upload_id}_{timestamp}.xlsx"
            file_path = os.path.join(export_dir, filename)
            
            # 导出Excel
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            logger.info(f"规则导出成功: {file_path}")
            
            return {
                'success': True,
                'file_path': file_path,
                'filename': filename,
                'rules_count': len(rules),
                'message': f'成功导出 {len(rules)} 条规则'
            }
            
        except Exception as e:
            logger.error(f"导出规则失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'导出规则失败: {str(e)}'}
