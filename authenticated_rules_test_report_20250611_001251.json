{"timestamp": "2025-06-11T00:12:51.440227", "test_type": "authenticated", "logged_in": true, "summary": {"total": 9, "passed": 9, "failed": 0, "errors": 0, "pass_rate": 100.0}, "results": [{"name": "规则列表API", "status": "PASS", "duration": 0.726525068283081, "details": "返回JSON数据，keys: ['page', 'pages', 'per_page', 'rules', 'success', 'total']", "error": null}, {"name": "规则详情API", "status": "PASS", "duration": 0.015365362167358398, "details": "返回JSON数据，keys: ['data', 'success']", "error": null}, {"name": "规则搜索API", "status": "PASS", "duration": 0.1393282413482666, "details": "返回JSON数据，keys: ['rules', 'success']", "error": null}, {"name": "规则参数搜索API", "status": "PASS", "duration": 0.014092445373535156, "details": "返回JSON数据，keys: ['rules', 'success']", "error": null}, {"name": "规则管理首页", "status": "PASS", "duration": 0.01587080955505371, "details": "返回HTML页面，长度: 38535", "error": null}, {"name": "飞检规则知识库", "status": "PASS", "duration": 0.017233610153198242, "details": "返回HTML页面，长度: 68726", "error": null}, {"name": "SQL生成器", "status": "PASS", "duration": 0.017184972763061523, "details": "返回HTML页面，长度: 52108", "error": null}, {"name": "系统规则", "status": "PASS", "duration": 0.017531394958496094, "details": "返回HTML页面，长度: 54957", "error": null}, {"name": "数据库查询", "status": "PASS", "duration": 0.04296684265136719, "details": "返回HTML页面，长度: 76116", "error": null}]}