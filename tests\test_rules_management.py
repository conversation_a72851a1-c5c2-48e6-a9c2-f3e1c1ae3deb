#!/usr/bin/env python3
"""
规则管理模块自动化测试脚本
"""

import requests
import json
import time
import sys
import os
from datetime import datetime

class RulesManagementTester:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def login(self, username="admin", password="admin123!"):
        """登录系统"""
        print("🔐 正在登录系统...")

        # 获取登录页面（获取CSRF token）
        login_page = self.session.get(f"{self.base_url}/auth/login")
        if login_page.status_code != 200:
            print(f"无法访问登录页面，状态码: {login_page.status_code}")
            return False

        # 提取CSRF token - 尝试多种模式
        import re
        csrf_patterns = [
            r'name="csrf_token" value="([^"]+)"',
            r'<input[^>]*name="csrf_token"[^>]*value="([^"]+)"',
            r'csrf_token["\']?\s*:\s*["\']([^"\']+)["\']'
        ]

        csrf_token = None
        for pattern in csrf_patterns:
            csrf_match = re.search(pattern, login_page.text)
            if csrf_match:
                csrf_token = csrf_match.group(1)
                break

        if not csrf_token:
            print("⚠️ 无法获取CSRF token，尝试无token登录")
            # 尝试不使用CSRF token的登录
            login_data = {
                'username': username,
                'password': password,
                'remember_me': False
            }
        else:
            # 使用CSRF token登录
            login_data = {
                'username': username,
                'password': password,
                'csrf_token': csrf_token,
                'remember_me': False
            }

        # 执行登录
        response = self.session.post(f"{self.base_url}/auth/login", data=login_data, allow_redirects=True)

        # 检查登录是否成功
        if response.status_code == 200:
            # 检查是否重定向到主页或包含成功信息
            if response.url.endswith('/') or "dashboard" in response.url or "main" in response.url:
                print("✅ 登录成功")
                return True
            elif "登录成功" in response.text:
                print("✅ 登录成功")
                return True

        print(f"❌ 登录失败，状态码: {response.status_code}, URL: {response.url}")
        return False
    
    def test_case(self, test_name, test_func):
        """执行单个测试用例"""
        print(f"\n🧪 执行测试: {test_name}")
        start_time = time.time()
        
        try:
            result = test_func()
            end_time = time.time()
            duration = round(end_time - start_time, 2)
            
            if result:
                print(f"✅ {test_name} - 通过 ({duration}s)")
                self.test_results.append({
                    'name': test_name,
                    'status': 'PASS',
                    'duration': duration,
                    'error': None
                })
            else:
                print(f"❌ {test_name} - 失败 ({duration}s)")
                self.test_results.append({
                    'name': test_name,
                    'status': 'FAIL',
                    'duration': duration,
                    'error': 'Test returned False'
                })
        except Exception as e:
            end_time = time.time()
            duration = round(end_time - start_time, 2)
            print(f"❌ {test_name} - 异常 ({duration}s): {str(e)}")
            self.test_results.append({
                'name': test_name,
                'status': 'ERROR',
                'duration': duration,
                'error': str(e)
            })
    
    def tc001_rules_index_access(self):
        """TC001 - 规则管理首页访问"""
        response = self.session.get(f"{self.base_url}/rules/")
        return response.status_code == 200 and "规则管理" in response.text
    
    def tc002_knowledge_base_page(self):
        """TC002 - 飞检规则知识库页面"""
        response = self.session.get(f"{self.base_url}/rules/knowledge_base")
        return response.status_code == 200 and "飞检规则知识库" in response.text
    
    def tc003_rules_search_api(self):
        """TC003 - 规则搜索API功能"""
        # 测试无参数搜索
        response = self.session.get(f"{self.base_url}/rules/api/search")
        if response.status_code != 200:
            return False
        
        data = response.json()
        if not data.get('success'):
            return False
        
        # 测试带参数搜索
        params = {'city': '北京', 'rule_type': '频次上限'}
        response = self.session.get(f"{self.base_url}/rules/api/search", params=params)
        if response.status_code != 200:
            return False
        
        data = response.json()
        return data.get('success', False)
    
    def tc004_rules_list_api(self):
        """TC004 - 规则列表API测试"""
        # 测试基本列表获取
        response = self.session.get(f"{self.base_url}/rules/api/rules")
        if response.status_code != 200:
            return False
        
        data = response.json()
        if not data.get('success'):
            return False
        
        # 测试分页参数
        params = {'page': 1, 'per_page': 10}
        response = self.session.get(f"{self.base_url}/rules/api/rules", params=params)
        if response.status_code != 200:
            return False
        
        data = response.json()
        return data.get('success', False)
    
    def tc005_rule_detail_api(self):
        """TC005 - 规则详情API测试"""
        # 首先获取规则列表，找到一个有效的规则ID
        response = self.session.get(f"{self.base_url}/rules/api/rules?per_page=1")
        if response.status_code != 200:
            return False
        
        data = response.json()
        if not data.get('success') or not data.get('rules'):
            print("⚠️ 没有找到规则数据，跳过详情测试")
            return True
        
        rule_id = data['rules'][0].get('ID')
        if not rule_id:
            return False
        
        # 测试规则详情获取
        response = self.session.get(f"{self.base_url}/rules/api/rules/{rule_id}")
        if response.status_code != 200:
            return False
        
        detail_data = response.json()
        return detail_data.get('success', False)
    
    def tc006_sql_generator_page(self):
        """TC006 - SQL生成器页面测试"""
        response = self.session.get(f"{self.base_url}/rules/rule_sql_generator")
        return response.status_code == 200 and "SQL生成器" in response.text
    
    def tc007_system_rules_page(self):
        """TC007 - 系统规则页面测试"""
        response = self.session.get(f"{self.base_url}/rules/system_rules")
        return response.status_code == 200 and "系统规则" in response.text
    
    def tc008_rule_create_api(self):
        """TC008 - 规则创建API测试"""
        # 获取CSRF token
        response = self.session.get(f"{self.base_url}/rules/knowledge_base")
        if response.status_code != 200:
            return False
        
        # 提取CSRF token
        import re
        csrf_match = re.search(r'name="csrf-token" content="([^"]+)"', response.text)
        if not csrf_match:
            print("⚠️ 无法获取CSRF token，跳过创建测试")
            return True
        
        csrf_token = csrf_match.group(1)
        
        # 准备测试数据
        test_rule = {
            "规则名称": f"测试规则_{int(time.time())}",
            "规则内涵": "这是一个自动化测试创建的规则",
            "类型": "频次上限",
            "规则类型": "检查项目",
            "行为认定": "违规行为",
            "违规数量": 1,
            "违规金额": 100.0
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token
        }
        
        response = self.session.post(
            f"{self.base_url}/rules/api/rules",
            json=test_rule,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get('success', False)
        else:
            print(f"⚠️ 创建请求失败，状态码: {response.status_code}")
            return False
    
    def tc009_performance_test(self):
        """TC009 - 性能测试"""
        start_time = time.time()
        
        # 测试规则列表加载性能
        response = self.session.get(f"{self.base_url}/rules/api/rules?per_page=100")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"📊 规则列表加载时间: {duration:.2f}s")
        
        # 性能要求：3秒内完成
        return response.status_code == 200 and duration < 3.0
    
    def tc010_error_handling(self):
        """TC010 - 错误处理测试"""
        # 测试无效规则ID
        response = self.session.get(f"{self.base_url}/rules/api/rules/99999")
        
        # 应该返回错误但不是500
        return response.status_code in [404, 400] or (
            response.status_code == 200 and not response.json().get('success')
        )
    
    def run_all_tests(self):
        """运行所有测试用例"""
        print("🚀 开始规则管理模块测试")
        print("=" * 60)
        
        # 登录系统
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        # 执行测试用例
        test_cases = [
            ("TC001 - 规则管理首页访问", self.tc001_rules_index_access),
            ("TC002 - 飞检规则知识库页面", self.tc002_knowledge_base_page),
            ("TC003 - 规则搜索API功能", self.tc003_rules_search_api),
            ("TC004 - 规则列表API测试", self.tc004_rules_list_api),
            ("TC005 - 规则详情API测试", self.tc005_rule_detail_api),
            ("TC006 - SQL生成器页面测试", self.tc006_sql_generator_page),
            ("TC007 - 系统规则页面测试", self.tc007_system_rules_page),
            ("TC008 - 规则创建API测试", self.tc008_rule_create_api),
            ("TC009 - 性能测试", self.tc009_performance_test),
            ("TC010 - 错误处理测试", self.tc010_error_handling),
        ]
        
        for test_name, test_func in test_cases:
            self.test_case(test_name, test_func)
            time.sleep(0.5)  # 避免请求过快
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        error_tests = len([r for r in self.test_results if r['status'] == 'ERROR'])
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"错误: {error_tests}")
        print(f"通过率: {(passed_tests/total_tests*100):.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASS' else "❌"
            print(f"{status_icon} {result['name']} - {result['status']} ({result['duration']}s)")
            if result['error']:
                print(f"   错误: {result['error']}")
        
        # 保存报告到文件
        report_file = f"rules_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total': total_tests,
                    'passed': passed_tests,
                    'failed': failed_tests,
                    'errors': error_tests,
                    'pass_rate': passed_tests/total_tests*100
                },
                'results': self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

    def tc011_database_connectivity(self):
        """TC011 - 数据库连接测试"""
        try:
            # 通过API测试数据库连接
            response = self.session.get(f"{self.base_url}/rules/api/rules?per_page=1")
            return response.status_code == 200
        except Exception as e:
            print(f"数据库连接测试失败: {e}")
            return False

    def tc012_data_integrity(self):
        """TC012 - 数据完整性测试"""
        # 获取规则数据并检查必要字段
        response = self.session.get(f"{self.base_url}/rules/api/rules?per_page=5")
        if response.status_code != 200:
            return False

        data = response.json()
        if not data.get('success') or not data.get('rules'):
            return True  # 没有数据也算正常

        # 检查数据结构完整性
        required_fields = ['ID', '规则名称']
        for rule in data['rules'][:3]:  # 检查前3条记录
            for field in required_fields:
                if field not in rule:
                    print(f"缺少必要字段: {field}")
                    return False

        return True

if __name__ == '__main__':
    # 检查应用是否运行
    try:
        response = requests.get("http://127.0.0.1:5002", timeout=5)
        if response.status_code != 200:
            print("❌ 应用未运行或无法访问，请先启动应用: python run.py")
            sys.exit(1)
    except requests.exceptions.RequestException:
        print("❌ 应用未运行或无法访问，请先启动应用: python run.py")
        sys.exit(1)

    # 运行测试
    tester = RulesManagementTester()

    # 添加新的测试用例到测试列表
    additional_tests = [
        ("TC011 - 数据库连接测试", tester.tc011_database_connectivity),
        ("TC012 - 数据完整性测试", tester.tc012_data_integrity),
    ]

    # 修改run_all_tests方法以包含新测试
    original_run = tester.run_all_tests

    def enhanced_run_all_tests():
        print("🚀 开始规则管理模块测试")
        print("=" * 60)

        # 登录系统
        if not tester.login():
            print("❌ 登录失败，无法继续测试")
            return

        # 执行测试用例
        test_cases = [
            ("TC001 - 规则管理首页访问", tester.tc001_rules_index_access),
            ("TC002 - 飞检规则知识库页面", tester.tc002_knowledge_base_page),
            ("TC003 - 规则搜索API功能", tester.tc003_rules_search_api),
            ("TC004 - 规则列表API测试", tester.tc004_rules_list_api),
            ("TC005 - 规则详情API测试", tester.tc005_rule_detail_api),
            ("TC006 - SQL生成器页面测试", tester.tc006_sql_generator_page),
            ("TC007 - 系统规则页面测试", tester.tc007_system_rules_page),
            ("TC008 - 规则创建API测试", tester.tc008_rule_create_api),
            ("TC009 - 性能测试", tester.tc009_performance_test),
            ("TC010 - 错误处理测试", tester.tc010_error_handling),
            ("TC011 - 数据库连接测试", tester.tc011_database_connectivity),
            ("TC012 - 数据完整性测试", tester.tc012_data_integrity),
        ]

        for test_name, test_func in test_cases:
            tester.test_case(test_name, test_func)
            time.sleep(0.5)  # 避免请求过快

        # 生成测试报告
        tester.generate_report()

    enhanced_run_all_tests()
