#!/usr/bin/env python3
"""测试中文文件名处理的脚本"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.selfcheck.services import UploadService
from werkzeug.datastructures import FileStorage
import io

def test_chinese_filename():
    """测试中文文件名处理"""
    app = create_app()
    
    with app.app_context():
        print("=== 测试中文文件名处理 ===")
        
        # 测试中文文件名
        chinese_filenames = [
            "测试数据.csv",
            "自查规则_2024.csv", 
            "北京市医保数据.csv",
            "飞检规则库_最新版本.csv",
            "数据上传_测试文件.csv"
        ]
        
        for filename in chinese_filenames:
            print(f"\n测试文件名: {filename}")
            
            # 创建测试文件
            csv_content = "rule_id,sql_content,city\n1,SELECT * FROM test,北京\n2,SELECT count(*) FROM users,上海".encode('utf-8')
            test_file = FileStorage(
                stream=io.BytesIO(csv_content),
                filename=filename,
                content_type="text/csv"
            )
            
            try:
                # 测试文件验证
                is_valid, message = UploadService.validate_file(test_file)
                print(f"  文件验证: {is_valid}, 消息: {message}")
                
                if is_valid:
                    # 测试文件保存
                    upload_record = UploadService.save_upload(test_file, 1)
                    print(f"  保存成功!")
                    print(f"  原始文件名: {upload_record['file_name']}")
                    print(f"  存储路径: {upload_record['file_path']}")
                    print(f"  文件类型: {upload_record['file_type']}")
                    
                    # 检查文件是否实际存在
                    if os.path.exists(upload_record['file_path']):
                        print(f"  ✅ 文件已成功保存到磁盘")
                    else:
                        print(f"  ❌ 文件未保存到磁盘")
                        
            except Exception as e:
                print(f"  ❌ 处理失败: {str(e)}")
                import traceback
                traceback.print_exc()

def test_file_extension_extraction():
    """测试文件扩展名提取"""
    print("\n=== 测试文件扩展名提取 ===")
    
    test_cases = [
        "测试.csv",
        "数据文件.CSV", 
        "规则.dmp",
        "备份.bak",
        "导出.dp",
        "无扩展名文件",
        "多个.点.的.文件.csv"
    ]
    
    for filename in test_cases:
        extension = ''
        if '.' in filename:
            extension = '.' + filename.rsplit('.', 1)[1].lower()
        
        print(f"文件名: {filename} -> 扩展名: {extension}")

if __name__ == '__main__':
    test_file_extension_extraction()
    test_chinese_filename()
