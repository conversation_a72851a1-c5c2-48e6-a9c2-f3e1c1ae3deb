#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试数据用于验证导出功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import db_manager
from datetime import datetime
import json

def create_test_upload():
    """创建测试上传记录"""
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                # 创建测试上传记录
                upload_sql = """
                INSERT INTO converter_uploads 
                (id, user_id, original_filename, file_path, file_size, file_type, status, status_message, upload_time, updated_at)
                VALUES (999, 1, '测试规则文件.xlsx', '/test/path.xlsx', 1024, 'xlsx', 'completed', '测试数据', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """
                
                try:
                    cursor.execute(upload_sql)
                    print("✅ 创建测试上传记录成功")
                except Exception as e:
                    if 'ORA-00001' in str(e):  # 主键冲突
                        print("⚠️ 测试上传记录已存在")
                    else:
                        raise e
                
                conn.commit()
                return 999
                
    except Exception as e:
        print(f"❌ 创建测试上传记录失败: {str(e)}")
        return None

def create_test_rules(upload_id):
    """创建测试规则数据"""
    try:
        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                # 先清理可能存在的测试数据
                cursor.execute("DELETE FROM converter_rules WHERE upload_id = :upload_id", {'upload_id': upload_id})
                
                # 创建测试规则数据
                test_rules = [
                    {
                        'rule_source': '国家飞检问题清单(2024版)',
                        'city': '合肥',
                        'sequence_number': '1',
                        'department': '心内科',
                        'violation_type': '重复收费',
                        'rule_name': '开展心电监护时，重复收取动态血压监测费用',
                        'rule_content': '在进行心电监护的同时，不应重复收取动态血压监测费用，这属于重复收费行为',
                        'medical_name1': '心电监护',
                        'medical_name2': '动态血压监测',
                        'violation_count': '',
                        'type': '重复收费',
                        'time_type': '同时',
                        'item_count': '',
                        'age': '',
                        'gender': '',
                        'exclude_diagnosis': '',
                        'exclude_department': '',
                        'include_diagnosis': '',
                        'other': '',
                        'confidence': 0.9,
                        'status': 'approved'
                    },
                    {
                        'rule_source': '国家飞检问题清单(2024版)',
                        'city': '合肥',
                        'sequence_number': '2',
                        'department': '骨科',
                        'violation_type': '串换项目',
                        'rule_name': '钢板项目串换收费',
                        'rule_content': '将普通钢板串换为高价钢板进行收费，违反医保收费规定',
                        'medical_name1': '钢板',
                        'medical_name2': '',
                        'violation_count': '',
                        'type': '串换项目',
                        'time_type': '',
                        'item_count': '',
                        'age': '',
                        'gender': '',
                        'exclude_diagnosis': '',
                        'exclude_department': '',
                        'include_diagnosis': '',
                        'other': '',
                        'confidence': 0.85,
                        'status': 'approved'
                    },
                    {
                        'rule_source': '国家飞检问题清单(2024版)',
                        'city': '合肥',
                        'sequence_number': '3',
                        'department': '普外科',
                        'violation_type': '超标准收费',
                        'rule_name': '手术费用超标准收费',
                        'rule_content': '手术费用超出医保标准进行收费，属于超标准收费违规行为',
                        'medical_name1': '手术费',
                        'medical_name2': '',
                        'violation_count': '',
                        'type': '超标准收费',
                        'time_type': '',
                        'item_count': '',
                        'age': '',
                        'gender': '',
                        'exclude_diagnosis': '',
                        'exclude_department': '',
                        'include_diagnosis': '',
                        'other': '',
                        'confidence': 0.8,
                        'status': 'pending_review'
                    }
                ]
                
                # 插入测试规则
                for i, rule in enumerate(test_rules, 1):
                    rule_sql = """
                    INSERT INTO converter_rules (
                        id, upload_id, rule_source, city, sequence_number, department, violation_type,
                        rule_name, rule_content, medical_name1, medical_name2, violation_count,
                        type, time_type, item_count, age, gender, exclude_diagnosis,
                        exclude_department, include_diagnosis, other, confidence, status,
                        created_at, updated_at
                    ) VALUES (
                        :id, :upload_id, :rule_source, :city, :sequence_number, :department, :violation_type,
                        :rule_name, :rule_content, :medical_name1, :medical_name2, :violation_count,
                        :type, :time_type, :item_count, :age, :gender, :exclude_diagnosis,
                        :exclude_department, :include_diagnosis, :other, :confidence, :status,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    )
                    """

                    # 准备参数，确保CLOB字段在最后
                    params = {
                        'id': 9990 + i,
                        'upload_id': upload_id,
                        'rule_source': rule['rule_source'],
                        'city': rule['city'],
                        'sequence_number': rule['sequence_number'],
                        'department': rule['department'],
                        'violation_type': rule['violation_type'],
                        'medical_name1': rule['medical_name1'],
                        'medical_name2': rule['medical_name2'],
                        'violation_count': rule['violation_count'],
                        'type': rule['type'],
                        'time_type': rule['time_type'],
                        'item_count': rule['item_count'],
                        'age': rule['age'],
                        'gender': rule['gender'],
                        'exclude_department': rule['exclude_department'],
                        'confidence': rule['confidence'],
                        'status': rule['status'],
                        # CLOB字段放在最后
                        'rule_name': rule['rule_name'],
                        'rule_content': rule['rule_content'],
                        'exclude_diagnosis': rule['exclude_diagnosis'],
                        'include_diagnosis': rule['include_diagnosis'],
                        'other': rule['other']
                    }

                    cursor.execute(rule_sql, params)
                
                conn.commit()
                print(f"✅ 创建 {len(test_rules)} 条测试规则成功")
                return len(test_rules)
                
    except Exception as e:
        print(f"❌ 创建测试规则失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0

def test_export_with_data():
    """测试有数据时的导出功能"""
    print("=== 测试Excel导出功能（有数据） ===\n")
    
    # 创建测试数据
    upload_id = create_test_upload()
    if not upload_id:
        return
    
    rules_count = create_test_rules(upload_id)
    if rules_count == 0:
        return
    
    # 测试导出
    try:
        from app.rules.services import ConverterService
        
        print(f"开始测试导出功能，upload_id: {upload_id}")
        result = ConverterService.export_rules(upload_id)
        
        print("导出结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        if result['success']:
            print(f"✅ 导出成功!")
            print(f"文件路径: {result['file_path']}")
            print(f"文件名: {result['filename']}")
            print(f"规则数量: {result['rules_count']}")
            
            # 检查文件是否存在
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"文件大小: {file_size} bytes")
                print("✅ Excel文件已成功创建")
                
                # 尝试读取Excel文件验证内容
                try:
                    import pandas as pd
                    df = pd.read_excel(result['file_path'])
                    print(f"Excel文件包含 {len(df)} 行数据")
                    print("前3行数据:")
                    print(df.head(3).to_string())
                except Exception as e:
                    print(f"⚠️ 读取Excel文件时出错: {str(e)}")
            else:
                print("❌ 文件未找到")
        else:
            print(f"❌ 导出失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_export_with_data()
