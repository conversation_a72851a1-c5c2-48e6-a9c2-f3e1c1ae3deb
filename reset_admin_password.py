#!/usr/bin/env python3
"""
重置admin用户密码
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.role import Role

def reset_admin_password():
    """重置admin用户密码"""
    app = create_app()
    
    with app.app_context():
        # 查找admin用户
        admin_user = User.query.filter_by(username='admin').first()
        
        if admin_user:
            # 重置密码（使用更强的密码）
            new_password = 'Admin123!'
            admin_user.set_password(new_password)
            
            # 确保admin用户有管理员角色
            admin_role = Role.query.filter_by(name='管理员').first()
            if admin_role and admin_role not in admin_user.roles:
                admin_user.roles.append(admin_role)
                print("✅ 为admin用户添加了管理员角色")
            
            db.session.commit()
            print(f"✅ admin用户密码已重置为 '{new_password}'")

            # 验证密码
            if admin_user.check_password(new_password):
                print("✅ 密码验证成功")
            else:
                print("❌ 密码验证失败")
                
        else:
            print("❌ 未找到admin用户")

if __name__ == '__main__':
    reset_admin_password()
