# MICRA项目重构 - 技术实现指南

## 一、环境准备和依赖管理

### 1.1 新增依赖包
```bash
# 认证和权限管理
pip install Flask-Login==0.6.3
pip install Flask-Principal==0.4.0
pip install Flask-WTF==1.2.1
pip install WTForms==3.1.1

# 数据库ORM
pip install Flask-SQLAlchemy==3.1.1
pip install Flask-Migrate==4.0.5

# 密码加密
pip install bcrypt==4.1.2

# 表单验证和安全
pip install email-validator==2.1.0

# 缓存
pip install Flask-Caching==2.1.0

# 配置管理
pip install python-dotenv==1.0.0
```

### 1.2 requirements.txt 更新
```txt
Flask==3.0.0
Flask-Login==0.6.3
Flask-Principal==0.4.0
Flask-WTF==1.2.1
WTForms==3.1.1
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.0.5
Flask-Caching==2.1.0
bcrypt==4.1.2
email-validator==2.1.0
python-dotenv==1.0.0
oracledb==2.0.1
psycopg2-binary==2.9.9
pymysql==1.1.0
pyodbc==5.0.1
pandas==2.1.4
openpyxl==3.1.2
redis==5.0.1
```

## 二、核心模型设计

### 2.1 用户模型 (app/models/user.py)
```python
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import re

db = SQLAlchemy()

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, index=True)
    email = db.Column(db.String(100), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    real_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    department = db.Column(db.String(100))
    is_active = db.Column(db.Boolean, default=True)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)
    
    # 关系
    roles = db.relationship('Role', secondary='user_roles', back_populates='users')
    audit_logs = db.relationship('AuditLog', back_populates='user')
    
    def set_password(self, password):
        """设置密码"""
        if not self.validate_password_strength(password):
            raise ValueError("密码强度不符合要求")
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    @staticmethod
    def validate_password_strength(password):
        """验证密码强度"""
        if len(password) < 8:
            return False
        if not re.search(r'[A-Z]', password):
            return False
        if not re.search(r'[a-z]', password):
            return False
        if not re.search(r'\d', password):
            return False
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False
        return True
    
    def has_permission(self, permission_code):
        """检查用户是否有指定权限"""
        if self.is_admin:
            return True
        
        for role in self.roles:
            if role.is_active and role.has_permission(permission_code):
                return True
        return False
    
    def get_permissions(self):
        """获取用户所有权限"""
        permissions = set()
        if self.is_admin:
            # 管理员拥有所有权限
            from .permission import Permission
            return set(Permission.query.filter_by(is_active=True).all())
        
        for role in self.roles:
            if role.is_active:
                permissions.update(role.permissions)
        return permissions
    
    def is_locked(self):
        """检查账户是否被锁定"""
        if self.locked_until and self.locked_until > datetime.utcnow():
            return True
        return False
    
    def lock_account(self, minutes=30):
        """锁定账户"""
        from datetime import timedelta
        self.locked_until = datetime.utcnow() + timedelta(minutes=minutes)
        self.login_attempts = 0
        db.session.commit()
    
    def unlock_account(self):
        """解锁账户"""
        self.locked_until = None
        self.login_attempts = 0
        db.session.commit()
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'phone': self.phone,
            'department': self.department,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'roles': [role.to_dict() for role in self.roles]
        }
```

### 2.2 角色模型 (app/models/role.py)
```python
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Role(db.Model):
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(200))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    users = db.relationship('User', secondary='user_roles', back_populates='roles')
    permissions = db.relationship('Permission', secondary='role_permissions', back_populates='roles')
    
    def has_permission(self, permission_code):
        """检查角色是否有指定权限"""
        for permission in self.permissions:
            if permission.is_active and permission.code == permission_code:
                return True
        return False
    
    def add_permission(self, permission):
        """添加权限"""
        if permission not in self.permissions:
            self.permissions.append(permission)
    
    def remove_permission(self, permission):
        """移除权限"""
        if permission in self.permissions:
            self.permissions.remove(permission)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'permissions': [perm.to_dict() for perm in self.permissions]
        }

# 用户角色关联表
user_roles = db.Table('user_roles',
    db.Column('user_id', db.Integer, db.ForeignKey('users.id'), primary_key=True),
    db.Column('role_id', db.Integer, db.ForeignKey('roles.id'), primary_key=True),
    db.Column('created_at', db.DateTime, default=datetime.utcnow)
)

# 角色权限关联表
role_permissions = db.Table('role_permissions',
    db.Column('role_id', db.Integer, db.ForeignKey('roles.id'), primary_key=True),
    db.Column('permission_id', db.Integer, db.ForeignKey('permissions.id'), primary_key=True),
    db.Column('created_at', db.DateTime, default=datetime.utcnow)
)
```

### 2.3 权限模型 (app/models/permission.py)
```python
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Permission(db.Model):
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(100), unique=True, nullable=False, index=True)
    description = db.Column(db.String(200))
    module = db.Column(db.String(50))
    resource_type = db.Column(db.String(20))  # menu, page, button, api
    parent_id = db.Column(db.Integer, db.ForeignKey('permissions.id'))
    sort_order = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    
    # 关系
    parent = db.relationship('Permission', remote_side=[id], backref='children')
    roles = db.relationship('Role', secondary='role_permissions', back_populates='permissions')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'module': self.module,
            'resource_type': self.resource_type,
            'parent_id': self.parent_id,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'children': [child.to_dict() for child in self.children] if self.children else []
        }
    
    @classmethod
    def get_menu_tree(cls):
        """获取菜单树结构"""
        menus = cls.query.filter_by(
            resource_type='menu', 
            parent_id=None, 
            is_active=True
        ).order_by(cls.sort_order).all()
        
        return [menu.to_dict() for menu in menus]
```

## 三、认证系统实现

### 3.1 认证装饰器 (app/auth/decorators.py)
```python
from functools import wraps
from flask import abort, redirect, url_for, request, flash
from flask_login import current_user
from app.models.audit import AuditLog

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

def permission_required(permission_code):
    """权限验证装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login'))
            
            if not current_user.has_permission(permission_code):
                flash('您没有权限访问此页面', 'error')
                abort(403)
            
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='access',
                resource=f.__name__,
                details=f'访问 {permission_code}'
            )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('需要管理员权限', 'error')
            abort(403)
        return f(*args, **kwargs)
    return decorated_function
```

### 3.2 认证表单 (app/auth/forms.py)
```python
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField, SelectField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError
from app.models.user import User

class LoginForm(FlaskForm):
    """登录表单"""
    username = StringField('用户名或邮箱', validators=[DataRequired(), Length(1, 64)])
    password = PasswordField('密码', validators=[DataRequired()])
    remember_me = BooleanField('记住我')
    submit = SubmitField('登录')

class CreateUserForm(FlaskForm):
    """创建用户表单"""
    username = StringField('用户名', validators=[
        DataRequired(),
        Length(3, 20, message='用户名长度必须在3-20个字符之间')
    ])
    email = StringField('邮箱', validators=[DataRequired(), Email()])
    real_name = StringField('真实姓名', validators=[DataRequired(), Length(1, 50)])
    phone = StringField('手机号码', validators=[Length(0, 20)])
    department = StringField('部门', validators=[Length(0, 100)])
    password = PasswordField('密码', validators=[
        DataRequired(),
        Length(8, 128, message='密码长度必须在8-128个字符之间')
    ])
    password2 = PasswordField('确认密码', validators=[
        DataRequired(),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    is_admin = BooleanField('管理员权限')
    submit = SubmitField('创建用户')

    def validate_username(self, field):
        if User.query.filter_by(username=field.data).first():
            raise ValidationError('用户名已存在')

    def validate_email(self, field):
        if User.query.filter_by(email=field.data).first():
            raise ValidationError('邮箱已被注册')

    def validate_password(self, field):
        if not User.validate_password_strength(field.data):
            raise ValidationError('密码必须包含大小写字母、数字和特殊字符')

class EditUserForm(FlaskForm):
    """编辑用户表单"""
    username = StringField('用户名', validators=[DataRequired(), Length(3, 20)])
    email = StringField('邮箱', validators=[DataRequired(), Email()])
    real_name = StringField('真实姓名', validators=[DataRequired(), Length(1, 50)])
    phone = StringField('手机号码', validators=[Length(0, 20)])
    department = StringField('部门', validators=[Length(0, 100)])
    is_active = BooleanField('账户状态')
    is_admin = BooleanField('管理员权限')
    submit = SubmitField('保存修改')

    def __init__(self, user, *args, **kwargs):
        super(EditUserForm, self).__init__(*args, **kwargs)
        self.user = user

    def validate_username(self, field):
        if field.data != self.user.username and \
           User.query.filter_by(username=field.data).first():
            raise ValidationError('用户名已存在')

    def validate_email(self, field):
        if field.data != self.user.email and \
           User.query.filter_by(email=field.data).first():
            raise ValidationError('邮箱已被注册')

class ChangePasswordForm(FlaskForm):
    """修改密码表单"""
    old_password = PasswordField('当前密码', validators=[DataRequired()])
    password = PasswordField('新密码', validators=[
        DataRequired(),
        Length(8, 128, message='密码长度必须在8-128个字符之间')
    ])
    password2 = PasswordField('确认新密码', validators=[
        DataRequired(),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('修改密码')

    def validate_password(self, field):
        if not User.validate_password_strength(field.data):
            raise ValidationError('密码必须包含大小写字母、数字和特殊字符')

class ResetPasswordForm(FlaskForm):
    """重置密码表单（管理员用）"""
    password = PasswordField('新密码', validators=[
        DataRequired(),
        Length(8, 128, message='密码长度必须在8-128个字符之间')
    ])
    password2 = PasswordField('确认新密码', validators=[
        DataRequired(),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('重置密码')

    def validate_password(self, field):
        if not User.validate_password_strength(field.data):
            raise ValidationError('密码必须包含大小写字母、数字和特殊字符')
```

### 3.3 认证路由 (app/auth/routes.py)
```python
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_user, logout_user, current_user, login_required
from app.auth.forms import LoginForm, ChangePasswordForm
from app.models.user import User
from app.models.audit import AuditLog
from app import db
from datetime import datetime, timedelta

auth = Blueprint('auth', __name__)

@auth.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))

    form = LoginForm()
    if form.validate_on_submit():
        # 支持用户名或邮箱登录
        user = User.query.filter(
            (User.username == form.username.data) |
            (User.email == form.username.data)
        ).first()

        if user is None:
            flash('用户名或密码错误', 'error')
            return render_template('auth/login.html', form=form)

        # 检查账户是否被锁定
        if user.is_locked():
            flash('账户已被锁定，请稍后再试', 'error')
            return render_template('auth/login.html', form=form)

        # 检查账户是否激活
        if not user.is_active:
            flash('账户已被禁用，请联系管理员', 'error')
            return render_template('auth/login.html', form=form)

        # 验证密码
        if user.check_password(form.password.data):
            # 登录成功
            user.last_login = datetime.utcnow()
            user.login_attempts = 0
            user.locked_until = None
            db.session.commit()

            login_user(user, remember=form.remember_me.data)

            # 记录登录日志
            AuditLog.log_action(
                user_id=user.id,
                action='login',
                resource='system',
                details='用户登录成功'
            )

            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('main.index')
            return redirect(next_page)
        else:
            # 登录失败
            user.login_attempts += 1
            if user.login_attempts >= current_app.config.get('MAX_LOGIN_ATTEMPTS', 5):
                user.lock_account(30)  # 锁定30分钟
                flash('登录失败次数过多，账户已被锁定30分钟', 'error')
            else:
                remaining = current_app.config.get('MAX_LOGIN_ATTEMPTS', 5) - user.login_attempts
                flash(f'用户名或密码错误，还有{remaining}次尝试机会', 'error')

            db.session.commit()

            # 记录失败日志
            AuditLog.log_action(
                user_id=user.id,
                action='login_failed',
                resource='system',
                details=f'登录失败，尝试次数: {user.login_attempts}'
            )

    return render_template('auth/login.html', form=form)

@auth.route('/logout')
@login_required
def logout():
    """用户登出"""
    # 记录登出日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='logout',
        resource='system',
        details='用户登出'
    )

    logout_user()
    flash('您已成功登出', 'info')
    return redirect(url_for('auth.login'))

@auth.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if current_user.check_password(form.old_password.data):
            current_user.set_password(form.password.data)
            db.session.commit()

            # 记录密码修改日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='change_password',
                resource='user',
                details='用户修改密码'
            )

            flash('密码修改成功', 'success')
            return redirect(url_for('main.index'))
        else:
            flash('当前密码错误', 'error')

    return render_template('auth/change_password.html', form=form)
```

### 3.4 应用工厂模式 (app/__init__.py)
```python
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect
from flask_caching import Cache
from config import config

db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()
csrf = CSRFProtect()
cache = Cache()

def create_app(config_name='development'):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    csrf.init_app(app)
    cache.init_app(app)

    # 配置登录管理器
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        from app.models.user import User
        return User.query.get(int(user_id))

    # 注册蓝图
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)

    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')

    from app.rules import bp as rules_bp
    app.register_blueprint(rules_bp, url_prefix='/rules')

    from app.database import bp as database_bp
    app.register_blueprint(database_bp, url_prefix='/database')

    from app.excel import bp as excel_bp
    app.register_blueprint(excel_bp, url_prefix='/excel')

    from app.data import bp as data_bp
    app.register_blueprint(data_bp, url_prefix='/data')

    return app
```

## 四、完整功能模块实现

### 4.1 规则管理模块 (app/rules/routes.py)
```python
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.auth.decorators import permission_required
from app.models.audit import AuditLog

rules = Blueprint('rules', __name__)

@rules.route('/knowledge_base')
@login_required
@permission_required('rules.knowledge_base')
def knowledge_base():
    """飞检规则知识库"""
    return render_template('rules/knowledge_base.html')

@rules.route('/sql_generator')
@login_required
@permission_required('rules.sql_generator')
def sql_generator():
    """规则SQL生成器"""
    return render_template('rules/sql_generator.html')

@rules.route('/system_rules')
@login_required
@permission_required('rules.system_rules')
def system_rules():
    """系统规则语句"""
    return render_template('rules/system_rules.html')
```

### 4.2 数据库工具模块 (app/database/routes.py)
```python
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.auth.decorators import permission_required
from app.models.audit import AuditLog

database = Blueprint('database', __name__)

@database.route('/sql_generator')
@login_required
@permission_required('database.sql_generator')
def sql_generator():
    """SQL生成器"""
    return render_template('database/sql_generator.html')

@database.route('/query')
@login_required
@permission_required('database.query')
def query():
    """数据库查询生成Excel"""
    return render_template('database/query.html')

@database.route('/batch_query')
@login_required
@permission_required('database.batch_query')
def batch_query():
    """批量SQL查询生成Excel"""
    return render_template('database/batch_query.html')

@database.route('/performance')
@login_required
@permission_required('database.performance')
def performance():
    """SQL性能测试"""
    return render_template('database/performance.html')
```

### 4.3 Excel工具模块 (app/excel/routes.py)
```python
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.auth.decorators import permission_required
from app.models.audit import AuditLog

excel = Blueprint('excel', __name__)

@excel.route('/splitter')
@login_required
@permission_required('excel.splitter')
def splitter():
    """Excel文件拆分"""
    return render_template('excel/splitter.html')

@excel.route('/delete')
@login_required
@permission_required('excel.delete')
def delete():
    """Excel内容删除"""
    return render_template('excel/delete.html')

@excel.route('/compare')
@login_required
@permission_required('excel.compare')
def compare():
    """Excel比对工具"""
    return render_template('excel/compare.html')

@excel.route('/to_sql')
@login_required
@permission_required('excel.to_sql')
def to_sql():
    """Excel转SQL工具"""
    return render_template('excel/to_sql.html')
```

### 4.4 数据处理模块 (app/data/routes.py)
```python
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.auth.decorators import permission_required
from app.models.audit import AuditLog

data = Blueprint('data', __name__)

@data.route('/find_duplicates')
@login_required
@permission_required('data.find_duplicates')
def find_duplicates():
    """查找重复文件"""
    return render_template('data/find_duplicates.html')

@data.route('/validator')
@login_required
@permission_required('data.validator')
def validator():
    """数据校验"""
    return render_template('data/validator.html')

@data.route('/standardization')
@login_required
@permission_required('data.standardization')
def standardization():
    """数据标准化"""
    return render_template('data/standardization.html')
```

### 4.5 审计日志模型 (app/models/audit.py)
```python
from flask_sqlalchemy import SQLAlchemy
from flask import request
from datetime import datetime
import json

db = SQLAlchemy()

class AuditLog(db.Model):
    __tablename__ = 'audit_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    action = db.Column(db.String(50), nullable=False)
    resource = db.Column(db.String(100), nullable=False)
    resource_id = db.Column(db.String(50))
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 关系
    user = db.relationship('User', back_populates='audit_logs')

    @classmethod
    def log_action(cls, user_id, action, resource, resource_id=None, details=None):
        """记录操作日志"""
        log = cls(
            user_id=user_id,
            action=action,
            resource=resource,
            resource_id=resource_id,
            details=details,
            ip_address=request.remote_addr if request else None,
            user_agent=request.headers.get('User-Agent') if request else None
        )
        db.session.add(log)
        db.session.commit()
        return log

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'action': self.action,
            'resource': self.resource,
            'resource_id': self.resource_id,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
```

这个更新后的技术实现指南现在包含了所有14个功能模块的完整实现方案，确保重构后的系统能够完全覆盖现有的所有功能。
```
