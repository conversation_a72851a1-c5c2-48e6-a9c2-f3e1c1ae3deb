from sqlalchemy import Sequence
from app import db

class Permission(db.Model):
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, Sequence('permissions_id_seq'), primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(100), unique=True, nullable=False, index=True)
    description = db.Column(db.String(200))
    module = db.Column(db.String(50))
    resource_type = db.Column(db.String(20))  # menu, page, button, api
    parent_id = db.Column(db.Integer, db.<PERSON>ey('permissions.id'))
    sort_order = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    
    # 关系
    parent = db.relationship('Permission', remote_side=[id], backref='children')
    roles = db.relationship('Role', secondary='role_permissions', back_populates='permissions')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'module': self.module,
            'resource_type': self.resource_type,
            'parent_id': self.parent_id,
            'sort_order': self.sort_order,
            'is_active': self.is_active,
            'children': [child.to_dict() for child in self.children] if self.children else []
        }
    
    @classmethod
    def get_menu_tree(cls):
        """获取菜单树结构"""
        menus = cls.query.filter_by(
            resource_type='menu', 
            parent_id=None, 
            is_active=True
        ).order_by(cls.sort_order).all()
        
        return [menu.to_dict() for menu in menus]
