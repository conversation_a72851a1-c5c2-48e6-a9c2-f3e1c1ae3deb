#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件
"""

import os

# 测试环境配置
TEST_CONFIG = {
    # 应用服务器配置
    'BASE_URL': 'http://localhost:5002',
    'TIMEOUT': 30,
    
    # 测试用户配置
    'ADMIN_USER': {
        'username': 'admin',
        'password': 'Admin123!'
    },
    'TEST_USER': {
        'username': 'test_user',
        'password': 'Test123!'
    },
    
    # 测试数据配置
    'TEST_DATA_DIR': os.path.join(os.path.dirname(__file__), 'test_data'),
    'REPORTS_DIR': os.path.join(os.path.dirname(__file__), 'reports'),
    
    # 性能测试阈值
    'PERFORMANCE_THRESHOLDS': {
        'page_load_time': 3.0,  # 页面加载时间（秒）
        'search_response_time': 2.0,  # 搜索响应时间（秒）
        'import_speed': 1.0,  # 导入速度（规则/秒）
        'batch_import_time': 30.0,  # 批量导入时间（秒）
    },
    
    # 测试数据量配置
    'TEST_DATA_SIZES': {
        'small': 10,   # 小数据量
        'medium': 50,  # 中等数据量
        'large': 100,  # 大数据量
        'huge': 500,   # 超大数据量
    },
    
    # 数据库配置
    'DATABASE': {
        'cleanup_after_test': True,  # 测试后清理数据
        'backup_before_test': False,  # 测试前备份数据
    },
    
    # 报告配置
    'REPORTS': {
        'generate_html': True,
        'generate_json': True,
        'include_screenshots': False,
        'detailed_logs': True,
    },
    
    # 并发测试配置
    'CONCURRENCY': {
        'max_concurrent_users': 5,
        'test_duration': 60,  # 秒
    },
    
    # 文件上传测试配置
    'FILE_UPLOAD': {
        'max_file_size': 10 * 1024 * 1024,  # 10MB
        'supported_formats': ['csv', 'dmp', 'dp', 'bak'],
        'test_files': {
            'small_csv': 'small_data.csv',
            'large_csv': 'large_data.csv',
            'invalid_format': 'test.txt',
        }
    },
    
    # 错误处理测试配置
    'ERROR_HANDLING': {
        'network_timeout': 5,
        'retry_attempts': 3,
        'expected_error_codes': [400, 401, 403, 404, 500],
    }
}

# 测试环境检查
def check_test_environment():
    """检查测试环境是否就绪"""
    import requests
    import time
    
    print("检查测试环境...")
    
    # 检查应用服务器
    try:
        response = requests.get(TEST_CONFIG['BASE_URL'], timeout=5)
        if response.status_code == 200:
            print("✅ 应用服务器连接正常")
        else:
            print(f"⚠️ 应用服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 应用服务器连接失败: {e}")
        return False
    
    # 检查测试数据目录
    if not os.path.exists(TEST_CONFIG['TEST_DATA_DIR']):
        print(f"⚠️ 测试数据目录不存在，将创建: {TEST_CONFIG['TEST_DATA_DIR']}")
        os.makedirs(TEST_CONFIG['TEST_DATA_DIR'], exist_ok=True)
    else:
        print("✅ 测试数据目录存在")
    
    # 检查报告目录
    if not os.path.exists(TEST_CONFIG['REPORTS_DIR']):
        print(f"⚠️ 报告目录不存在，将创建: {TEST_CONFIG['REPORTS_DIR']}")
        os.makedirs(TEST_CONFIG['REPORTS_DIR'], exist_ok=True)
    else:
        print("✅ 报告目录存在")
    
    print("✅ 测试环境检查完成")
    return True

# 获取配置值的便捷函数
def get_config(key, default=None):
    """获取配置值"""
    keys = key.split('.')
    value = TEST_CONFIG
    
    for k in keys:
        if isinstance(value, dict) and k in value:
            value = value[k]
        else:
            return default
    
    return value

# 测试用例标记
class TestTags:
    """测试用例标记"""
    SMOKE = 'smoke'          # 冒烟测试
    REGRESSION = 'regression' # 回归测试
    PERFORMANCE = 'performance' # 性能测试
    INTEGRATION = 'integration' # 集成测试
    UI = 'ui'                # UI测试
    API = 'api'              # API测试
    DATABASE = 'database'    # 数据库测试
    SECURITY = 'security'    # 安全测试
    
    # 优先级
    CRITICAL = 'critical'    # 关键
    HIGH = 'high'           # 高
    MEDIUM = 'medium'       # 中
    LOW = 'low'             # 低

# 测试数据模板
TEST_DATA_TEMPLATES = {
    'rule': {
        'rule_name': 'TEST_RULE_{timestamp}',
        'rule_code': 'TEST_CODE_{timestamp}',
        'rule_description': '测试规则描述',
        'rule_type': '重复收费',
        'city': '北京',
        'sql_content': 'SELECT * FROM test_table'
    },
    'scheme': {
        'scheme_name': 'TEST_SCHEME_{timestamp}',
        'scheme_code': 'TEST_SCHEME_CODE_{timestamp}',
        'description': '测试方案描述',
        'status': 'active'
    },
    'task': {
        'task_name': 'TEST_TASK_{timestamp}',
        'status': 'pending'
    }
}

if __name__ == '__main__':
    # 运行环境检查
    check_test_environment()
