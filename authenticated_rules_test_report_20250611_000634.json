{"timestamp": "2025-06-11T00:06:34.176042", "test_type": "authenticated", "logged_in": true, "summary": {"total": 9, "passed": 9, "failed": 0, "errors": 0, "pass_rate": 100.0}, "results": [{"name": "规则列表API", "status": "PASS", "duration": 0.7889745235443115, "details": "返回JSON数据，keys: ['page', 'pages', 'per_page', 'rules', 'success', 'total']", "error": null}, {"name": "规则详情API", "status": "PASS", "duration": 0.026443004608154297, "details": "返回JSON数据，keys: ['data', 'success']", "error": null}, {"name": "规则搜索API", "status": "PASS", "duration": 0.1613478660583496, "details": "返回JSON数据，keys: ['rules', 'success']", "error": null}, {"name": "规则参数搜索API", "status": "PASS", "duration": 0.018609046936035156, "details": "返回JSON数据，keys: ['rules', 'success']", "error": null}, {"name": "规则管理首页", "status": "PASS", "duration": 0.017508983612060547, "details": "返回HTML页面，长度: 38535", "error": null}, {"name": "飞检规则知识库", "status": "PASS", "duration": 0.024000167846679688, "details": "返回HTML页面，长度: 68725", "error": null}, {"name": "SQL生成器", "status": "PASS", "duration": 0.022002220153808594, "details": "返回HTML页面，长度: 52108", "error": null}, {"name": "系统规则", "status": "PASS", "duration": 0.017897844314575195, "details": "返回HTML页面，长度: 54957", "error": null}, {"name": "数据库查询", "status": "PASS", "duration": 0.23101568222045898, "details": "返回HTML页面，长度: 76116", "error": null}]}