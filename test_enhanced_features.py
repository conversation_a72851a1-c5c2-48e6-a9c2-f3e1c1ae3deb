#!/usr/bin/env python3
"""
测试增强的任务功能
"""

import requests
import json
from bs4 import BeautifulSoup

def test_enhanced_features():
    """测试增强功能"""
    
    # 创建会话
    session = requests.Session()
    
    print("=== 🚀 测试增强的任务功能 ===")
    
    # 1. 登录
    print("\n1. 🔐 登录...")
    login_url = 'http://127.0.0.1:5002/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }
    
    try:
        login_page = session.get(login_url, timeout=10)
        soup = BeautifulSoup(login_page.text, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        if csrf_token:
            login_data['csrf_token'] = csrf_token['value']
        
        login_response = session.post(login_url, data=login_data, allow_redirects=True, timeout=10)
        
        if '/index' not in login_response.url:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录过程出错: {str(e)}")
        return False
    
    # 2. 获取现有任务列表
    print("\n2. 📋 获取任务列表...")
    try:
        tasks_list_url = 'http://127.0.0.1:5002/selfcheck/api/tasks?page=1&per_page=10'
        tasks_list_response = session.get(tasks_list_url, timeout=10)
        
        if tasks_list_response.status_code == 200:
            tasks_list_data = tasks_list_response.json()
            if tasks_list_data.get('success'):
                tasks = tasks_list_data['tasks']
                print(f"✅ 获取到 {len(tasks)} 个任务")
                
                if tasks:
                    test_task_id = tasks[0]['id']
                    test_task_name = tasks[0]['task_name']
                    print(f"📋 将使用任务进行测试: {test_task_name} (ID: {test_task_id})")

                    # 查找已完成的任务用于报告测试
                    completed_task_id = None
                    for task in tasks:
                        if task.get('status') == 'completed':
                            completed_task_id = task['id']
                            print(f"📋 找到已完成任务用于报告测试: {task['task_name']} (ID: {completed_task_id})")
                            break

                    if not completed_task_id:
                        print("📋 没有已完成的任务，将使用任务ID 1 进行报告测试")
                        completed_task_id = 1
                else:
                    print("❌ 没有可用的任务进行测试")
                    return False
            else:
                print(f"❌ 任务列表API返回错误: {tasks_list_data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 任务列表API失败: {tasks_list_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取任务列表出错: {str(e)}")
        return False
    
    # 3. 测试任务详情API
    print("\n3. 🔍 测试任务详情API...")
    try:
        detail_url = f'http://127.0.0.1:5002/selfcheck/api/tasks/{test_task_id}/detail'
        detail_response = session.get(detail_url, timeout=10)
        
        print(f"状态码: {detail_response.status_code}")
        
        if detail_response.status_code == 200:
            detail_data = detail_response.json()
            if detail_data.get('success'):
                task = detail_data['task']
                rules = detail_data.get('rules', [])
                
                print("✅ 任务详情获取成功")
                print(f"📋 任务信息:")
                print(f"  - 任务名称: {task['task_name']}")
                print(f"  - 数据源: {task.get('data_source', 'N/A')}")
                print(f"  - 方案: {task.get('scheme_name', 'N/A')}")
                print(f"  - 状态: {task.get('status', 'N/A')}")
                print(f"  - 规则数量: {len(rules)}")
                
                if rules:
                    print("📋 规则执行状态:")
                    for rule in rules[:3]:  # 只显示前3个
                        print(f"  - {rule['rule_name']}: {rule.get('status', 'N/A')} ({rule.get('progress', 0)}%)")
                
            else:
                print(f"❌ 任务详情API返回错误: {detail_data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 任务详情API失败: {detail_response.status_code}")
            print(f"响应内容: {detail_response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 任务详情API测试出错: {str(e)}")
        return False
    
    # 4. 测试报告预览API
    print("\n4. 📄 测试报告预览API...")
    try:
        preview_url = f'http://127.0.0.1:5002/selfcheck/api/tasks/{completed_task_id}/report/preview'
        preview_response = session.get(preview_url, timeout=10)
        
        print(f"状态码: {preview_response.status_code}")
        
        if preview_response.status_code == 200:
            preview_content = preview_response.text
            if 'DOCTYPE html' in preview_content and '自查自纠报告' in preview_content:
                print("✅ 报告预览生成成功")
                print(f"📄 报告内容长度: {len(preview_content)} 字符")
                
                # 检查报告内容
                if test_task_name in preview_content:
                    print("✅ 报告包含任务名称")
                if '规则执行详情' in preview_content:
                    print("✅ 报告包含规则执行详情")
                if 'table' in preview_content:
                    print("✅ 报告包含表格结构")
            else:
                print("❌ 报告预览内容格式不正确")
                return False
        else:
            print(f"❌ 报告预览API失败: {preview_response.status_code}")
            print(f"响应内容: {preview_response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 报告预览API测试出错: {str(e)}")
        return False
    
    # 5. 测试删除功能（创建一个测试任务然后删除）
    print("\n5. 🗑️ 测试删除功能...")
    try:
        # 先创建一个测试任务
        tasks_page = session.get('http://127.0.0.1:5002/selfcheck/tasks', timeout=10)
        soup = BeautifulSoup(tasks_page.text, 'html.parser')
        csrf_token = soup.find('meta', {'name': 'csrf-token'})
        csrf_token_value = csrf_token['content'] if csrf_token else None
        
        if not csrf_token_value:
            print("❌ 无法获取CSRF令牌")
            return False
        
        # 创建测试任务
        create_task_data = {
            'task_name': '删除测试任务',
            'data_source': 'pg_pool1',
            'scheme_id': 1
        }
        
        create_task_url = 'http://127.0.0.1:5002/selfcheck/api/tasks'
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token_value
        }
        
        create_response = session.post(
            create_task_url,
            headers=headers,
            data=json.dumps(create_task_data),
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            if create_result.get('success'):
                delete_task_id = create_result['task']['id']
                print(f"✅ 测试任务创建成功 (ID: {delete_task_id})")
                
                # 删除任务
                delete_url = f'http://127.0.0.1:5002/selfcheck/api/tasks/{delete_task_id}'
                delete_response = session.delete(
                    delete_url,
                    headers={'X-CSRFToken': csrf_token_value},
                    timeout=10
                )
                
                if delete_response.status_code == 200:
                    delete_result = delete_response.json()
                    if delete_result.get('success'):
                        print("✅ 任务删除成功")
                    else:
                        print(f"❌ 任务删除失败: {delete_result.get('message', '未知错误')}")
                        return False
                else:
                    print(f"❌ 删除API失败: {delete_response.status_code}")
                    return False
            else:
                print(f"❌ 创建测试任务失败: {create_result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 创建测试任务API失败: {create_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 删除功能测试出错: {str(e)}")
        return False
    
    print("\n🎉 所有增强功能测试通过！")
    print("\n📊 测试总结:")
    print("✅ 任务详情API正常工作")
    print("✅ 规则执行状态显示正常")
    print("✅ 报告预览功能正常")
    print("✅ 任务删除功能正常")
    print("\n🌟 任务管理功能已完全增强！")
    
    return True

if __name__ == '__main__':
    try:
        success = test_enhanced_features()
        if success:
            print("\n🎊 恭喜！所有增强功能验证成功！")
        else:
            print("\n❌ 验证失败，请检查相关问题")
    except Exception as e:
        print(f"验证过程出错: {e}")
        import traceback
        traceback.print_exc()
