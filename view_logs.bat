@echo off
chcp 65001 >nul
echo ========================================
echo MICRA应用日志查看工具
echo ========================================
echo.

if "%1"=="" (
    echo 使用方法:
    echo   view_logs.bat [选项]
    echo.
    echo 选项:
    echo   list          - 列出所有日志文件
    echo   today         - 查看今天的日志（最后50行）
    echo   errors        - 查看错误日志
    echo   follow        - 实时跟踪日志
    echo   stats         - 查看日志统计
    echo   search [关键词] - 搜索日志
    echo   clean         - 清理30天前的日志
    echo.
    goto :end
)

if "%1"=="list" (
    python log_manager.py list
    goto :end
)

if "%1"=="today" (
    python view_logs.py --lines 50
    goto :end
)

if "%1"=="errors" (
    python log_manager.py view --level error --lines 100
    goto :end
)

if "%1"=="follow" (
    echo 实时跟踪日志（按Ctrl+C退出）...
    python view_logs.py --follow
    goto :end
)

if "%1"=="stats" (
    python log_manager.py stats
    goto :end
)

if "%1"=="search" (
    if "%2"=="" (
        echo 请提供搜索关键词
        echo 例如: view_logs.bat search "错误"
        goto :end
    )
    python log_manager.py search "%2"
    goto :end
)

if "%1"=="clean" (
    echo 清理30天前的日志文件...
    python log_manager.py clean --days 30
    goto :end
)

echo 未知选项: %1
echo 使用 view_logs.bat 查看帮助

:end
pause
