package com.ruoyi.selfcheck.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 自查任务对象 biz_self_check_task
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class SelfCheckTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long taskId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 上传数据ID */
    @Excel(name = "上传数据ID")
    private Long uploadId;

    /** 规则ID */
    @Excel(name = "规则ID")
    private Long ruleId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 状态(0待执行 1执行中 2完成 3失败) */
    @Excel(name = "状态", readConverterExp = "0=待执行,1=执行中,2=完成,3=失败")
    private String status;

    /** 执行进度(0-100) */
    @Excel(name = "执行进度")
    private Integer progress;

    /** 结果摘要 */
    private String resultSummary;

    /** 详细结果JSON */
    private String resultDetail;

    /** 错误信息 */
    private String errorMessage;

    /** 关联信息 */
    private String userName;
    private String fileName;
    private String ruleName;
    private Integer errorCount;
    private Integer warningCount;

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }
    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUploadId(Long uploadId) 
    {
        this.uploadId = uploadId;
    }

    public Long getUploadId() 
    {
        return uploadId;
    }
    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setProgress(Integer progress) 
    {
        this.progress = progress;
    }

    public Integer getProgress() 
    {
        return progress;
    }
    public void setResultSummary(String resultSummary) 
    {
        this.resultSummary = resultSummary;
    }

    public String getResultSummary() 
    {
        return resultSummary;
    }
    public void setResultDetail(String resultDetail) 
    {
        this.resultDetail = resultDetail;
    }

    public String getResultDetail() 
    {
        return resultDetail;
    }
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    // 关联信息的getter和setter
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public Integer getWarningCount() {
        return warningCount;
    }

    public void setWarningCount(Integer warningCount) {
        this.warningCount = warningCount;
    }

    @Override
    public String toString() {
        return "SelfCheckTask{" +
                "taskId=" + taskId +
                ", taskName='" + taskName + '\'' +
                ", userId=" + userId +
                ", uploadId=" + uploadId +
                ", ruleId=" + ruleId +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", status='" + status + '\'' +
                ", progress=" + progress +
                ", resultSummary='" + resultSummary + '\'' +
                ", resultDetail='" + resultDetail + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
