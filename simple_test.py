#!/usr/bin/env python3
"""
简单测试新的任务创建功能
"""

import requests
import json
from bs4 import BeautifulSoup

def simple_test():
    """简单测试"""

    # 创建会话
    session = requests.Session()

    # 初始化变量
    first_data_source_id = None

    print("=== 简单测试新的任务创建功能 ===")
    
    # 1. 登录
    print("\n1. 登录...")
    login_url = 'http://127.0.0.1:5002/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }
    
    # 获取登录页面以获取CSRF令牌
    try:
        login_page = session.get(login_url, timeout=10)
        soup = BeautifulSoup(login_page.text, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        if csrf_token:
            login_data['csrf_token'] = csrf_token['value']
        
        # 执行登录
        login_response = session.post(login_url, data=login_data, allow_redirects=True, timeout=10)
        
        if '/index' not in login_response.url:
            print("❌ 登录失败")
            return
        
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录过程出错: {str(e)}")
        return
    
    # 2. 测试数据源API
    print("\n2. 测试数据源API...")
    try:
        data_sources_url = 'http://127.0.0.1:5002/selfcheck/api/data-sources'
        data_sources_response = session.get(data_sources_url, timeout=10)
        
        print(f"状态码: {data_sources_response.status_code}")
        
        if data_sources_response.status_code == 200:
            data_sources_data = data_sources_response.json()
            if data_sources_data.get('success'):
                data_sources = data_sources_data['data_sources']
                print(f"✅ 获取到 {len(data_sources)} 个数据源")
                for ds in data_sources[:3]:  # 只显示前3个
                    print(f"  - {ds['name']} ({ds['type']}) ID: {ds['id']}")

                # 保存第一个数据源ID供后续使用
                first_data_source_id = data_sources[0]['id'] if data_sources else None
            else:
                print(f"❌ 数据源API返回错误: {data_sources_data.get('message', '未知错误')}")
                first_data_source_id = None
        else:
            print(f"❌ 数据源API失败: {data_sources_response.status_code}")
            first_data_source_id = None
            
    except Exception as e:
        print(f"❌ 数据源API测试出错: {str(e)}")
    
    # 3. 测试授权方案API
    print("\n3. 测试授权方案API...")
    try:
        schemes_url = 'http://127.0.0.1:5002/selfcheck/api/authorized-schemes'
        schemes_response = session.get(schemes_url, timeout=10)
        
        print(f"状态码: {schemes_response.status_code}")
        
        if schemes_response.status_code == 200:
            schemes_data = schemes_response.json()
            if schemes_data.get('success'):
                schemes = schemes_data['schemes']
                print(f"✅ 获取到 {len(schemes)} 个授权方案")
                for scheme in schemes[:3]:  # 只显示前3个
                    rule_count = scheme.get('rule_count', 0)
                    print(f"  - {scheme['scheme_name']} ({rule_count}个规则)")
            else:
                print(f"❌ 授权方案API返回错误: {schemes_data.get('message', '未知错误')}")
        else:
            print(f"❌ 授权方案API失败: {schemes_response.status_code}")
            
    except Exception as e:
        print(f"❌ 授权方案API测试出错: {str(e)}")
    
    # 4. 测试创建任务API
    print("\n4. 测试创建任务API...")
    try:
        # 获取CSRF令牌
        tasks_page = session.get('http://127.0.0.1:5002/selfcheck/tasks', timeout=10)
        soup = BeautifulSoup(tasks_page.text, 'html.parser')
        csrf_token = soup.find('meta', {'name': 'csrf-token'})
        csrf_token_value = csrf_token['content'] if csrf_token else None
        
        if not csrf_token_value:
            print("❌ 无法获取CSRF令牌")
            return
        
        # 使用实际获取到的数据源ID
        if not first_data_source_id:
            print("❌ 没有可用的数据源")
            return

        create_task_data = {
            'task_name': '测试任务-新API',
            'data_source': first_data_source_id,
            'scheme_id': 1  # 使用第一个方案
        }
        
        create_task_url = 'http://127.0.0.1:5002/selfcheck/api/tasks'
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token_value
        }
        
        create_response = session.post(
            create_task_url,
            headers=headers,
            data=json.dumps(create_task_data),
            timeout=10
        )
        
        print(f"状态码: {create_response.status_code}")
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            if create_result.get('success'):
                print("✅ 任务创建成功!")
                task = create_result['task']
                print(f"  任务ID: {task['id']}")
                print(f"  任务名称: {task['task_name']}")
                print(f"  数据源: {task['data_source']}")
                print(f"  方案ID: {task['scheme_id']}")
                print(f"  状态: {task['status']}")
            else:
                print(f"❌ 任务创建失败: {create_result.get('message', '未知错误')}")
        else:
            print(f"❌ 任务创建API失败: {create_response.status_code}")
            print(f"响应内容: {create_response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 创建任务API测试出错: {str(e)}")
    
    print("\n🎉 测试完成！")

if __name__ == '__main__':
    simple_test()
