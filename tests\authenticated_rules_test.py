#!/usr/bin/env python3
"""
带认证的规则管理模块测试脚本
"""

import requests
import json
import time
import re
from datetime import datetime

class AuthenticatedRulesTest:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.logged_in = False
        
    def login(self, username="admin", password="Admin123!"):
        """登录系统"""
        print("🔐 正在登录系统...")
        
        try:
            # 获取登录页面
            login_page = self.session.get(f"{self.base_url}/auth/login")
            if login_page.status_code != 200:
                print(f"❌ 无法访问登录页面，状态码: {login_page.status_code}")
                return False
            
            # 使用正则表达式解析CSRF token
            csrf_patterns = [
                r'name="csrf_token" value="([^"]+)"',
                r'<input[^>]*name="csrf_token"[^>]*value="([^"]+)"',
                r'csrf_token["\']?\s*:\s*["\']([^"\']+)["\']'
            ]

            csrf_token = None
            for pattern in csrf_patterns:
                csrf_match = re.search(pattern, login_page.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
                    print(f"✅ 获取到CSRF token: {csrf_token[:10]}...")
                    break

            if not csrf_token:
                print("⚠️ 未找到CSRF token，尝试无token登录")
            
            # 准备登录数据
            login_data = {
                'username': username,
                'password': password,
                'remember_me': False
            }
            
            if csrf_token:
                login_data['csrf_token'] = csrf_token
            
            # 执行登录
            response = self.session.post(
                f"{self.base_url}/auth/login", 
                data=login_data, 
                allow_redirects=True
            )
            
            # 检查登录结果
            if response.status_code == 200:
                # 检查是否重定向到主页
                if '/auth/login' not in response.url:
                    print("✅ 登录成功")
                    self.logged_in = True
                    return True
                else:
                    print("❌ 登录失败，仍在登录页面")
                    return False
            else:
                print(f"❌ 登录请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False
    
    def test_api_with_auth(self, name, url, expected_json=True):
        """测试需要认证的API"""
        print(f"🧪 测试API: {name}")
        start_time = time.time()
        
        try:
            response = self.session.get(url)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                if expected_json:
                    try:
                        data = response.json()
                        print(f"✅ {name} - 通过 ({duration:.2f}s)")
                        self.test_results.append({
                            'name': name,
                            'status': 'PASS',
                            'duration': duration,
                            'details': f'返回JSON数据，keys: {list(data.keys()) if isinstance(data, dict) else "非字典"}',
                            'error': None
                        })
                        return data
                    except json.JSONDecodeError:
                        print(f"❌ {name} - JSON解析失败")
                        self.test_results.append({
                            'name': name,
                            'status': 'FAIL',
                            'duration': duration,
                            'details': '',
                            'error': 'JSON解析失败'
                        })
                        return None
                else:
                    print(f"✅ {name} - 通过 ({duration:.2f}s)")
                    self.test_results.append({
                        'name': name,
                        'status': 'PASS',
                        'duration': duration,
                        'details': f'返回HTML页面，长度: {len(response.text)}',
                        'error': None
                    })
                    return response.text
            else:
                print(f"❌ {name} - 状态码: {response.status_code}")
                self.test_results.append({
                    'name': name,
                    'status': 'FAIL',
                    'duration': duration,
                    'details': '',
                    'error': f'状态码: {response.status_code}'
                })
                return None
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {name} - 异常: {str(e)}")
            self.test_results.append({
                'name': name,
                'status': 'ERROR',
                'duration': duration,
                'details': '',
                'error': str(e)
            })
            return None
    
    def test_rules_functionality(self):
        """测试规则管理功能"""
        if not self.logged_in:
            print("❌ 未登录，跳过功能测试")
            return
        
        print("\n📋 测试规则管理功能...")
        
        # 测试规则列表API
        rules_data = self.test_api_with_auth(
            "规则列表API", 
            f"{self.base_url}/rules/api/rules?per_page=10"
        )
        
        if rules_data and isinstance(rules_data, dict):
            if rules_data.get('success'):
                rules = rules_data.get('rules', [])
                total = rules_data.get('total', 0)
                print(f"   📊 获取到 {len(rules)} 条规则，总计 {total} 条")
                
                # 如果有规则数据，测试规则详情
                if rules and len(rules) > 0:
                    first_rule = rules[0]
                    rule_id = first_rule.get('ID')
                    if rule_id:
                        self.test_api_with_auth(
                            "规则详情API",
                            f"{self.base_url}/rules/api/rules/{rule_id}"
                        )
            else:
                print(f"   ⚠️ 规则API返回success=false: {rules_data.get('error', '未知错误')}")
        
        # 测试搜索API
        self.test_api_with_auth(
            "规则搜索API",
            f"{self.base_url}/rules/api/search"
        )
        
        # 测试带参数的搜索
        self.test_api_with_auth(
            "规则参数搜索API",
            f"{self.base_url}/rules/api/search?city=北京&rule_type=频次上限"
        )
    
    def test_page_functionality(self):
        """测试页面功能"""
        if not self.logged_in:
            print("❌ 未登录，跳过页面测试")
            return
        
        print("\n📄 测试页面功能...")
        
        # 测试各个页面
        pages = [
            ("规则管理首页", "/rules/"),
            ("飞检规则知识库", "/rules/knowledge_base"),
            ("SQL生成器", "/rules/sql_generator"),
            ("系统规则", "/rules/system_rules"),
            ("数据库查询", "/database/query")
        ]
        
        for name, path in pages:
            self.test_api_with_auth(
                name,
                f"{self.base_url}{path}",
                expected_json=False
            )
    
    def run_authenticated_tests(self):
        """运行认证测试"""
        print("🚀 开始带认证的规则管理模块测试")
        print("=" * 60)
        
        # 先登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        # 测试功能
        self.test_rules_functionality()
        self.test_page_functionality()
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 认证测试报告")
        print("=" * 60)
        
        total = len(self.test_results)
        passed = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed = len([r for r in self.test_results if r['status'] == 'FAIL'])
        errors = len([r for r in self.test_results if r['status'] == 'ERROR'])
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        print(f"错误: {errors}")
        if total > 0:
            print(f"通过率: {(passed/total*100):.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASS' else "❌"
            print(f"{status_icon} {result['name']} - {result['status']} ({result['duration']:.2f}s)")
            if result['details']:
                print(f"   详情: {result['details']}")
            if result['error']:
                print(f"   错误: {result['error']}")
        
        # 保存报告
        report_file = f"authenticated_rules_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'test_type': 'authenticated',
                'logged_in': self.logged_in,
                'summary': {
                    'total': total,
                    'passed': passed,
                    'failed': failed,
                    'errors': errors,
                    'pass_rate': passed/total*100 if total > 0 else 0
                },
                'results': self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 认证测试报告已保存到: {report_file}")

if __name__ == '__main__':
    tester = AuthenticatedRulesTest()
    tester.run_authenticated_tests()
