#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text
from app.models.permission import Permission

def fix_sequence_issue():
    """修复序列问题"""
    app = create_app()
    
    with app.app_context():
        try:
            print('=== 检查和修复序列问题 ===')
            
            # 检查最大的权限ID
            max_id_result = db.session.execute(text('SELECT MAX(id) FROM permissions'))
            max_id = max_id_result.scalar()
            print(f'当前最大ID: {max_id}')
            
            # 获取序列当前值（通过查询序列的last_number）
            seq_info = db.session.execute(text("""
                SELECT last_number 
                FROM user_sequences 
                WHERE sequence_name = 'PERMISSIONS_ID_SEQ'
            """))
            current_seq = seq_info.scalar()
            print(f'序列当前值: {current_seq}')
            
            # 如果序列值小于等于最大ID，需要重置序列
            if current_seq <= max_id:
                print(f'⚠️ 序列值({current_seq})小于等于最大ID({max_id})，需要重置序列')
                
                # Oracle重置序列的方法
                new_seq_val = max_id + 10  # 留一些余量
                
                # 先删除序列
                try:
                    db.session.execute(text('DROP SEQUENCE permissions_id_seq'))
                    print('删除旧序列')
                except:
                    print('序列删除失败，可能不存在')
                
                # 重新创建序列
                db.session.execute(text(f'''
                    CREATE SEQUENCE permissions_id_seq 
                    START WITH {new_seq_val} 
                    INCREMENT BY 1 
                    NOCACHE
                '''))
                print(f'✅ 重新创建序列，起始值: {new_seq_val}')
                
                db.session.commit()
            else:
                print('✅ 序列值正常')
            
            # 现在尝试添加方案管理权限
            print('\n=== 添加方案管理权限 ===')
            
            # 获取自查自纠模块的父权限
            selfcheck_parent = Permission.query.filter_by(code='selfcheck').first()
            if not selfcheck_parent:
                print('错误：找不到自查自纠父权限')
                return
            
            # 检查是否已存在方案管理权限
            schemes_perm = Permission.query.filter_by(code='selfcheck.schemes').first()
            if not schemes_perm:
                # 创建方案管理权限
                schemes_perm = Permission(
                    name='方案管理',
                    code='selfcheck.schemes',
                    module='selfcheck',
                    resource_type='menu',
                    parent_id=selfcheck_parent.id,
                    sort_order=1,
                    is_active=True
                )
                db.session.add(schemes_perm)
                db.session.flush()
                print(f'✅ 创建方案管理权限: {schemes_perm.name} (ID: {schemes_perm.id})')
            else:
                print(f'✅ 方案管理权限已存在: {schemes_perm.name} (ID: {schemes_perm.id})')
            
            # 创建方案管理的子权限
            schemes_children = [
                {'name': '查看方案', 'code': 'selfcheck.schemes.view', 'resource_type': 'page', 'sort_order': 1},
                {'name': '创建方案', 'code': 'selfcheck.schemes.create', 'resource_type': 'button', 'sort_order': 2},
                {'name': '编辑方案', 'code': 'selfcheck.schemes.edit', 'resource_type': 'button', 'sort_order': 3},
                {'name': '删除方案', 'code': 'selfcheck.schemes.delete', 'resource_type': 'button', 'sort_order': 4},
                {'name': '管理方案规则', 'code': 'selfcheck.schemes.manage_rules', 'resource_type': 'button', 'sort_order': 5},
                {'name': '查看统计', 'code': 'selfcheck.schemes.statistics', 'resource_type': 'button', 'sort_order': 6}
            ]
            
            for child_config in schemes_children:
                existing_child = Permission.query.filter_by(code=child_config['code']).first()
                if not existing_child:
                    child_perm = Permission(
                        name=child_config['name'],
                        code=child_config['code'],
                        module='selfcheck',
                        resource_type=child_config['resource_type'],
                        parent_id=schemes_perm.id,
                        sort_order=child_config['sort_order'],
                        is_active=True
                    )
                    db.session.add(child_perm)
                    print(f'✅ 创建子权限: {child_perm.name} ({child_perm.code})')
                else:
                    print(f'✅ 子权限已存在: {existing_child.name} ({existing_child.code})')
            
            # 更新其他权限的排序
            other_perms = Permission.query.filter_by(
                parent_id=selfcheck_parent.id,
                module='selfcheck'
            ).filter(Permission.code != 'selfcheck.schemes').all()
            
            for perm in other_perms:
                if perm.code == 'selfcheck.rules':
                    perm.sort_order = 2
                elif perm.code in ['selfcheck.upload', 'selfcheck.uploads']:
                    perm.sort_order = 3
                    # 统一使用 uploads
                    if perm.code == 'selfcheck.upload':
                        perm.code = 'selfcheck.uploads'
                        perm.name = '数据上传'
                elif perm.code == 'selfcheck.tasks':
                    perm.sort_order = 4
            
            db.session.commit()
            print('✅ 方案管理权限添加完成！')
            
        except Exception as e:
            print(f"修复失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    fix_sequence_issue()
