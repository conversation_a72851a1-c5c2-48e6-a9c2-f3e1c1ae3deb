#!/usr/bin/env python3
"""
测试规则识别逻辑
"""

import sys
sys.path.append('.')

from app.rules.services import ConverterService

def test_rule_recognition():
    """测试规则识别逻辑"""
    test_texts = [
        '严禁重复开具同一检查项目，违反医保基金管理规定',
        '不得虚假住院治疗，骗取医保基金',
        '开展心电监护时，重复收取动态血压监测或动态心电图监测费用',
        '违法违规使用医保基金负面清单（护理类）',
        '国家医保局下发医保基金使用负面清单',
        '1. 心血管内科 重复收费 开展心电监护时，重复收取动态血压监测或动态心电图监测费用',
        '目录',
        '第1页',
        '联系电话：123456789'
    ]

    print('=== 测试规则识别逻辑 ===')
    for i, text in enumerate(test_texts, 1):
        try:
            is_rule = ConverterService._is_potential_rule(text)
            print(f'{i}. {text[:50]}...')
            print(f'   长度: {len(text)}')
            print(f'   是否识别为规则: {is_rule}')
            
            if is_rule:
                # 测试AI分析
                rule_data = ConverterService._ai_analyze_text(text, '测试来源', i, {'type': 'test'})
                if rule_data:
                    print(f'   规则名称: {rule_data.get("rule_name", "")}')
                    print(f'   科室: {rule_data.get("department", "")}')
                    print(f'   违规类型: {rule_data.get("violation_type", "")}')
                    print(f'   置信度: {rule_data.get("confidence", 0):.2f}')
            print()
        except Exception as e:
            print(f'   错误: {str(e)}')
            print()

if __name__ == "__main__":
    test_rule_recognition()
