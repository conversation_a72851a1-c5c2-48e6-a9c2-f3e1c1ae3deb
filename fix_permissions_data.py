#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.permission import Permission

def fix_permissions_data():
    """修复权限数据问题"""
    app = create_app()
    
    with app.app_context():
        try:
            print('=== 修复权限数据 ===')
            
            # 修复 selfcheck 权限的 is_active 字段
            selfcheck_perm = Permission.query.filter_by(code='selfcheck').first()
            if selfcheck_perm:
                if selfcheck_perm.is_active is None:
                    selfcheck_perm.is_active = True
                    db.session.commit()
                    print(f'✅ 修复权限: {selfcheck_perm.name} - 设置 is_active = True')
                else:
                    print(f'✅ 权限正常: {selfcheck_perm.name} - is_active = {selfcheck_perm.is_active}')
            
            # 检查所有权限的 is_active 字段
            null_active_perms = Permission.query.filter(Permission.is_active.is_(None)).all()
            if null_active_perms:
                print(f'\n发现 {len(null_active_perms)} 个权限的 is_active 字段为 None:')
                for perm in null_active_perms:
                    print(f'  - {perm.name} ({perm.code})')
                    perm.is_active = True
                
                db.session.commit()
                print('✅ 已修复所有 is_active 为 None 的权限')
            else:
                print('✅ 所有权限的 is_active 字段都正常')
            
            # 检查权限层级关系
            print('\n=== 检查权限层级关系 ===')
            parent_perms = Permission.query.filter(Permission.parent_id.is_(None)).all()
            for parent in parent_perms:
                print(f'父权限: {parent.name} ({parent.code})')
                children = Permission.query.filter_by(parent_id=parent.id).all()
                for child in children:
                    print(f'  子权限: {child.name} ({child.code})')
                    
        except Exception as e:
            print(f"修复失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    fix_permissions_data()
