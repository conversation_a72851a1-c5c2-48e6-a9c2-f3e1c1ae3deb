-- 创建自查自纠模块相关表

-- 1. 创建自查规则表
CREATE TABLE selfcheck_rules (
    id NUMBER PRIMARY KEY,
    rule_name VARCHAR2(200) NOT NULL,
    rule_code VARCHAR2(100) UNIQUE NOT NULL,
    rule_description CLOB,
    rule_type VARCHAR2(50) NOT NULL,
    rule_content CLOB,
    rule_version VARCHAR2(20) DEFAULT '1.0',
    status VARCHAR2(10) DEFAULT 'active',
    sort_order NUMBER DEFAULT 0,
    created_by NUMBE<PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by NUMBER,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- 从rule_sql_history导入的字段
    sql_content CLOB,
    city VARCHAR2(10),
    rule_source VARCHAR2(200),
    medical_behavior VARCHAR2(150),
    template_name VARCHAR2(30),
    visit_type VARCHAR2(10),
    types VARCHAR2(10),
    original_rule_id NUMBER
);

-- 2. 创建数据上传表
CREATE TABLE selfcheck_uploads (
    id NUMBER PRIMARY KEY,
    user_id NUMBER NOT NULL,
    file_name VARCHAR2(500) NOT NULL,
    file_path VARCHAR2(1000) NOT NULL,
    file_size NUMBER,
    file_type VARCHAR2(50),
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR2(20) DEFAULT 'pending',
    validation_result CLOB,
    error_message CLOB,
    record_count NUMBER DEFAULT 0,
    CONSTRAINT fk_selfcheck_uploads_user FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 3. 创建自查任务表
CREATE TABLE selfcheck_tasks (
    id NUMBER PRIMARY KEY,
    task_name VARCHAR2(200) NOT NULL,
    user_id NUMBER NOT NULL,
    upload_id NUMBER NOT NULL,
    rule_id NUMBER NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR2(20) DEFAULT 'pending',
    progress NUMBER DEFAULT 0,
    result_summary CLOB,
    result_detail CLOB,
    error_message CLOB,
    error_count NUMBER DEFAULT 0,
    warning_count NUMBER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_selfcheck_tasks_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_selfcheck_tasks_upload FOREIGN KEY (upload_id) REFERENCES selfcheck_uploads(id),
    CONSTRAINT fk_selfcheck_tasks_rule FOREIGN KEY (rule_id) REFERENCES (id)
);

-- 4. 创建序列
CREATE SEQUENCE selfcheck_rules_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE selfcheck_uploads_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE selfcheck_tasks_seq START WITH 1 INCREMENT BY 1;

-- 5. 创建索引
CREATE INDEX idx_selfcheck_rules_code ON selfcheck_rules(rule_code);
CREATE INDEX idx_selfcheck_rules_type ON selfcheck_rules(rule_type);
CREATE INDEX idx_selfcheck_rules_status ON selfcheck_rules(status);
CREATE INDEX idx_selfcheck_uploads_user ON selfcheck_uploads(user_id);
CREATE INDEX idx_selfcheck_uploads_status ON selfcheck_uploads(status);
CREATE INDEX idx_selfcheck_tasks_user ON selfcheck_tasks(user_id);
CREATE INDEX idx_selfcheck_tasks_status ON selfcheck_tasks(status);
CREATE INDEX idx_selfcheck_tasks_upload ON selfcheck_tasks(upload_id);
CREATE INDEX idx_selfcheck_tasks_rule ON selfcheck_tasks(rule_id);

-- 6. 添加约束
ALTER TABLE  ADD CONSTRAINT chk_selfcheck_rules_status CHECK (status IN ('active', 'inactive'));
ALTER TABLE selfcheck_uploads ADD CONSTRAINT chk_selfcheck_uploads_status CHECK (status IN ('pending', 'validating', 'validated', 'failed'));
ALTER TABLE selfcheck_tasks ADD CONSTRAINT chk_selfcheck_tasks_status CHECK (status IN ('pending', 'running', 'completed', 'failed'));
ALTER TABLE selfcheck_tasks ADD CONSTRAINT chk_selfcheck_tasks_progress CHECK (progress >= 0 AND progress <= 100);

-- 7. 插入权限数据
INSERT INTO permissions (id, name, code, description, module, resource_type, parent_id, sort_order) VALUES
-- 主菜单
(60, '自查自纠', 'selfcheck', '在线自查自纠模块', 'selfcheck', 'menu', NULL, 6);

INSERT INTO permissions (id, name, code, description, module, resource_type, parent_id, sort_order) VALUES
-- 规则管理
(61, '规则管理', 'selfcheck.rules', '自查规则管理', 'selfcheck', 'menu', 60, 1),
(611, '查看规则', 'selfcheck.rules.view', '查看自查规则', 'selfcheck', 'page', 61, 1),
(612, '创建规则', 'selfcheck.rules.create', '创建自查规则', 'selfcheck', 'button', 61, 2),
(613, '编辑规则', 'selfcheck.rules.edit', '编辑自查规则', 'selfcheck', 'button', 61, 3),
(614, '删除规则', 'selfcheck.rules.delete', '删除自查规则', 'selfcheck', 'button', 61, 4),
(615, '导入规则', 'selfcheck.rules.import', '从历史规则导入', 'selfcheck', 'button', 61, 5);

INSERT INTO permissions (id, name, code, description, module, resource_type, parent_id, sort_order) VALUES
-- 数据上传
(62, '数据上传', 'selfcheck.upload', '数据文件上传', 'selfcheck', 'menu', 60, 2),
(621, '查看上传记录', 'selfcheck.upload.view', '查看上传记录', 'selfcheck', 'page', 62, 1),
(622, '上传文件', 'selfcheck.upload.create', '上传数据文件', 'selfcheck', 'button', 62, 2),
(623, '删除上传', 'selfcheck.upload.delete', '删除上传记录', 'selfcheck', 'button', 62, 3);

INSERT INTO permissions (id, name, code, description, module, resource_type, parent_id, sort_order) VALUES
-- 自查任务
(63, '自查任务', 'selfcheck.tasks', '自查任务管理', 'selfcheck', 'menu', 60, 3),
(631, '查看任务', 'selfcheck.tasks.view', '查看自查任务', 'selfcheck', 'page', 63, 1),
(632, '创建任务', 'selfcheck.tasks.create', '创建自查任务', 'selfcheck', 'button', 63, 2),
(633, '执行任务', 'selfcheck.tasks.execute', '执行自查任务', 'selfcheck', 'button', 63, 3),
(634, '查看结果', 'selfcheck.tasks.result', '查看任务结果', 'selfcheck', 'button', 63, 4),
(635, '导出结果', 'selfcheck.tasks.export', '导出任务结果', 'selfcheck', 'button', 63, 5);

COMMIT;
