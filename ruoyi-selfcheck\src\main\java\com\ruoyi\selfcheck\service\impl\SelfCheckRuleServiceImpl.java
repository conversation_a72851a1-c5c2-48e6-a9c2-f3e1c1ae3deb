package com.ruoyi.selfcheck.service.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.selfcheck.mapper.SelfCheckRuleMapper;
import com.ruoyi.selfcheck.domain.SelfCheckRule;
import com.ruoyi.selfcheck.service.ISelfCheckRuleService;
import com.ruoyi.common.utils.StringUtils;

/**
 * 自查规则Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@Service
public class SelfCheckRuleServiceImpl implements ISelfCheckRuleService 
{
    @Autowired
    private SelfCheckRuleMapper selfCheckRuleMapper;

    /**
     * 查询自查规则
     * 
     * @param ruleId 自查规则主键
     * @return 自查规则
     */
    @Override
    public SelfCheckRule selectSelfCheckRuleByRuleId(Long ruleId)
    {
        return selfCheckRuleMapper.selectSelfCheckRuleByRuleId(ruleId);
    }

    /**
     * 查询自查规则列表
     * 
     * @param selfCheckRule 自查规则
     * @return 自查规则
     */
    @Override
    public List<SelfCheckRule> selectSelfCheckRuleList(SelfCheckRule selfCheckRule)
    {
        return selfCheckRuleMapper.selectSelfCheckRuleList(selfCheckRule);
    }

    /**
     * 新增自查规则
     * 
     * @param selfCheckRule 自查规则
     * @return 结果
     */
    @Override
    public int insertSelfCheckRule(SelfCheckRule selfCheckRule)
    {
        selfCheckRule.setCreateTime(new Date());
        return selfCheckRuleMapper.insertSelfCheckRule(selfCheckRule);
    }

    /**
     * 修改自查规则
     * 
     * @param selfCheckRule 自查规则
     * @return 结果
     */
    @Override
    public int updateSelfCheckRule(SelfCheckRule selfCheckRule)
    {
        selfCheckRule.setUpdateTime(new Date());
        return selfCheckRuleMapper.updateSelfCheckRule(selfCheckRule);
    }

    /**
     * 批量删除自查规则
     * 
     * @param ruleIds 需要删除的自查规则主键
     * @return 结果
     */
    @Override
    public int deleteSelfCheckRuleByRuleIds(Long[] ruleIds)
    {
        return selfCheckRuleMapper.deleteSelfCheckRuleByRuleIds(ruleIds);
    }

    /**
     * 删除自查规则信息
     * 
     * @param ruleId 自查规则主键
     * @return 结果
     */
    @Override
    public int deleteSelfCheckRuleByRuleId(Long ruleId)
    {
        return selfCheckRuleMapper.deleteSelfCheckRuleByRuleId(ruleId);
    }

    /**
     * 校验规则编码是否唯一
     *
     * @param selfCheckRule 规则信息
     * @return 结果
     */
    @Override
    public String checkRuleCodeUnique(SelfCheckRule selfCheckRule)
    {
        Long ruleId = StringUtils.isNull(selfCheckRule.getRuleId()) ? -1L : selfCheckRule.getRuleId();
        SelfCheckRule info = selfCheckRuleMapper.checkRuleCodeUnique(selfCheckRule.getRuleCode());
        if (StringUtils.isNotNull(info) && info.getRuleId().longValue() != ruleId.longValue())
        {
            return "1";
        }
        return "0";
    }

    /**
     * 根据规则编码查询规则
     *
     * @param ruleCode 规则编码
     * @return 自查规则
     */
    @Override
    public SelfCheckRule selectSelfCheckRuleByRuleCode(String ruleCode)
    {
        return selfCheckRuleMapper.selectSelfCheckRuleByRuleCode(ruleCode);
    }

    /**
     * 查询启用的规则列表
     *
     * @return 启用的规则列表
     */
    @Override
    public List<SelfCheckRule> selectEnabledRuleList()
    {
        SelfCheckRule rule = new SelfCheckRule();
        rule.setStatus("0"); // 启用状态
        return selfCheckRuleMapper.selectSelfCheckRuleList(rule);
    }

    /**
     * 根据规则类型查询规则列表
     *
     * @param ruleType 规则类型
     * @return 规则列表
     */
    @Override
    public List<SelfCheckRule> selectRuleListByType(String ruleType)
    {
        SelfCheckRule rule = new SelfCheckRule();
        rule.setRuleType(ruleType);
        rule.setStatus("0"); // 只查询启用的规则
        return selfCheckRuleMapper.selectSelfCheckRuleList(rule);
    }
}
