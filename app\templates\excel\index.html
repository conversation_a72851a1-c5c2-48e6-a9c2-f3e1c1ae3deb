{% extends "base.html" %}

{% block title %}Excel工具 - MICRA飞检数据处理工具箱{% endblock %}



{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-file-excel me-2"></i>
                        Excel工具模块
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Excel文件拆分 -->
                        {% if current_user.has_permission('excel.splitter') %}
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="far fa-file-excel fa-3x text-primary"></i>
                                    </div>
                                    <h6 class="card-title">文件拆分</h6>
                                    <p class="card-text small">将大Excel文件拆分为多个小文件</p>
                                    <a href="{{ url_for('excel.splitter') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>进入
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Excel内容删除 -->
                        {% if current_user.has_permission('excel.delete') %}
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-danger">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-eraser fa-3x text-danger"></i>
                                    </div>
                                    <h6 class="card-title">内容删除</h6>
                                    <p class="card-text small">批量删除Excel文件中的指定内容</p>
                                    <a href="{{ url_for('excel.delete') }}" class="btn btn-danger btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>进入
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Excel比对工具 -->
                        {% if current_user.has_permission('excel.compare') %}
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-file-excel fa-3x text-success"></i>
                                    </div>
                                    <h6 class="card-title">文件比对</h6>
                                    <p class="card-text small">比较两个Excel文件的差异</p>
                                    <a href="{{ url_for('excel.compare') }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>进入
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Excel转SQL -->
                        {% if current_user.has_permission('excel.to_sql') %}
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-file-code fa-3x text-info"></i>
                                    </div>
                                    <h6 class="card-title">转SQL工具</h6>
                                    <p class="card-text small">将Excel数据转换为SQL语句</p>
                                    <a href="{{ url_for('excel.to_sql') }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>进入
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- 功能说明 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>
                                        功能说明
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <h6 class="text-primary">文件拆分</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 按行数拆分Excel文件</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 按工作表拆分</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 保持原始格式</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-danger">内容删除</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 批量删除指定行列</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 条件删除数据</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 安全备份机制</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-success">文件比对</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 智能差异检测</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 高亮显示差异</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 生成比对报告</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-info">转SQL工具</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 自动生成INSERT语句</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 支持多种数据库</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 数据类型自动识别</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
