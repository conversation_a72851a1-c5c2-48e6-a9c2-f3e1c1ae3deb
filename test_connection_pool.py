#!/usr/bin/env python3
"""
测试数据库连接池功能的脚本
"""

import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_connection_pool():
    """测试连接池功能"""
    print("=== 测试数据库连接池功能 ===")
    
    try:
        from app.selfcheck.database import get_db_manager
        
        # 获取数据库管理器
        db_manager = get_db_manager()
        
        # 检查连接池状态
        print("1. 检查连接池初始状态...")
        pool_status = db_manager.get_pool_status()
        if pool_status:
            print(f"连接池状态: 已打开={pool_status['opened']}, 繁忙={pool_status['busy']}, 最大={pool_status['max']}, 最小={pool_status['min']}")
        else:
            print("连接池不可用，使用直接连接")
        
        # 测试单个连接
        print("\n2. 测试单个连接...")
        start_time = time.time()
        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1 FROM DUAL")
                result = cursor.fetchone()
                cursor.close()
                print(f"单个连接测试成功，结果: {result}, 耗时: {time.time() - start_time:.3f}秒")
        except Exception as e:
            print(f"单个连接测试失败: {e}")
        
        # 测试并发连接
        print("\n3. 测试并发连接...")
        
        def test_concurrent_connection(thread_id):
            """并发连接测试函数"""
            try:
                start_time = time.time()
                with db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT :thread_id FROM DUAL", {'thread_id': thread_id})
                    result = cursor.fetchone()
                    cursor.close()
                    elapsed = time.time() - start_time
                    print(f"线程 {thread_id}: 连接成功，结果: {result}, 耗时: {elapsed:.3f}秒")
                    return True
            except Exception as e:
                print(f"线程 {thread_id}: 连接失败: {e}")
                return False
        
        # 使用5个并发线程测试
        concurrent_count = 5
        with ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            futures = [executor.submit(test_concurrent_connection, i) for i in range(concurrent_count)]
            results = [future.result() for future in futures]
        
        success_count = sum(results)
        print(f"并发连接测试完成: {success_count}/{concurrent_count} 成功")
        
        # 再次检查连接池状态
        print("\n4. 检查连接池最终状态...")
        pool_status = db_manager.get_pool_status()
        if pool_status:
            print(f"连接池状态: 已打开={pool_status['opened']}, 繁忙={pool_status['busy']}, 最大={pool_status['max']}, 最小={pool_status['min']}")
        
        print("\n✅ 连接池功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_simulation():
    """模拟规则导入过程"""
    print("\n=== 模拟规则导入过程 ===")
    
    try:
        from app.selfcheck.database import get_db_manager, execute_query
        
        # 获取一些测试数据
        print("1. 获取测试数据...")
        query = """
        SELECT rule_id, rule_name, compare_id, visit_type
        FROM rule_sql_history
        WHERE compare_id IS NOT NULL 
        AND visit_type IS NOT NULL
        AND ROWNUM <= 5
        """
        
        test_rules = execute_query(query)
        print(f"获取到 {len(test_rules)} 条测试规则")
        
        if not test_rules:
            print("没有找到测试数据")
            return True
        
        # 模拟并发导入
        print("\n2. 模拟并发导入...")
        
        def simulate_import(rule_data):
            """模拟导入单个规则"""
            rule_id = rule_data['rule_id']
            try:
                # 模拟查询操作
                query1 = "SELECT COUNT(*) as count FROM selfcheck_rules WHERE rule_name = :rule_name"
                result1 = execute_query(query1, {'rule_name': f"测试规则_{rule_id}"})

                # 模拟更新操作
                query2 = """
                SELECT rule_id, rule_name
                FROM rule_sql_history
                WHERE rule_id = :rule_id
                """
                result2 = execute_query(query2, {'rule_id': rule_id})
                
                print(f"规则 {rule_id} 模拟导入成功")
                return True
            except Exception as e:
                print(f"规则 {rule_id} 模拟导入失败: {e}")
                return False
        
        # 并发执行
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(simulate_import, rule) for rule in test_rules]
            results = [future.result() for future in futures]
        
        success_count = sum(results)
        print(f"模拟导入完成: {success_count}/{len(test_rules)} 成功")
        
        print("\n✅ 规则导入模拟测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_connection_recovery():
    """测试连接恢复机制"""
    print("\n=== 测试连接恢复机制 ===")
    
    try:
        from app.selfcheck.database import get_db_manager
        
        db_manager = get_db_manager()
        
        # 测试大量并发连接（可能触发连接池耗尽）
        print("1. 测试大量并发连接...")
        
        def stress_test_connection(thread_id):
            """压力测试连接"""
            try:
                for i in range(3):  # 每个线程执行3次查询
                    with db_manager.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT :value FROM DUAL", {'value': f"{thread_id}_{i}"})
                        result = cursor.fetchone()
                        cursor.close()
                        time.sleep(0.1)  # 短暂延迟
                print(f"线程 {thread_id}: 压力测试完成")
                return True
            except Exception as e:
                print(f"线程 {thread_id}: 压力测试失败: {e}")
                return False
        
        # 使用10个并发线程进行压力测试
        stress_count = 10
        with ThreadPoolExecutor(max_workers=stress_count) as executor:
            futures = [executor.submit(stress_test_connection, i) for i in range(stress_count)]
            results = [future.result() for future in futures]
        
        success_count = sum(results)
        print(f"压力测试完成: {success_count}/{stress_count} 成功")
        
        # 检查连接池是否正常
        print("\n2. 检查连接池恢复状态...")
        pool_status = db_manager.get_pool_status()
        if pool_status:
            print(f"连接池状态: 已打开={pool_status['opened']}, 繁忙={pool_status['busy']}")
            if pool_status['busy'] == 0:
                print("✅ 连接池已恢复正常")
            else:
                print("⚠️ 连接池仍有繁忙连接")
        
        print("\n✅ 连接恢复机制测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试数据库连接池功能...\n")
    
    # 运行所有测试
    tests = [
        test_connection_pool,
        test_import_simulation,
        test_connection_recovery
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "="*60)
    print("测试总结:")
    
    test_names = [
        "连接池功能测试",
        "规则导入模拟测试", 
        "连接恢复机制测试"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    passed_count = sum(results)
    total_count = len(results)
    
    if passed_count == total_count:
        print(f"\n🎉 所有测试通过！({passed_count}/{total_count})")
        print("\n连接池功能正常：")
        print("1. ✅ 连接池创建和管理正常")
        print("2. ✅ 并发连接处理正常")
        print("3. ✅ 连接恢复机制正常")
        print("4. ✅ 可以支持规则导入的并发需求")
    else:
        print(f"\n⚠️ 部分测试失败 ({passed_count}/{total_count})，请检查连接池配置。")
    
    print("="*60)
