"""数据库连接和查询工具类"""

import oracledb
import psycopg2
import pymysql
import pyodbc
import pandas as pd
import logging
from contextlib import contextmanager
from typing import Dict, Any, Optional, Union, Tuple
import configparser
import os
from app import db

# 读取配置文件
config = configparser.ConfigParser()
config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config.ini')
config.read(config_path, encoding='utf-8')

class DatabasePoolManager:
    """数据库连接池管理器"""
    def __init__(self):
        self.pools: Dict[str, Dict[str, any]] = {
            'oracle': {},
            'postgresql': {},
            'mysql': {},
            'sqlserver': {}
        }
        self.default_pool = None
        self.init_pools()

    def init_pools(self):
        """初始化所有配置的数据库连接池"""
        try:
            # 初始化Oracle客户端
            try:
                oracledb.init_oracle_client()
            except Exception:
                pass  # 可能已经初始化过了
            
            # 初始化默认连接池（Oracle）
            if 'datachange' in config.sections():
                default_pool = self.create_oracle_pool(
                    'default',
                    username=config.get('datachange', 'username'),
                    password=config.get('datachange', 'password'),
                    dsn=config.get('datachange', 'dsn')
                )
                self.default_pool = default_pool
                
        except Exception as e:
            logging.error(f"Failed to initialize database pools: {str(e)}")

    def create_oracle_pool(self, pool_name: str, **kwargs) -> oracledb.ConnectionPool:
        """创建 Oracle 连接池"""
        try:
            pool = oracledb.create_pool(
                user=kwargs.get('username'),
                password=kwargs.get('password'),
                dsn=kwargs.get('dsn'),
                min=kwargs.get('min', 2),
                max=kwargs.get('max', 5),
                increment=kwargs.get('increment', 1),
                getmode=oracledb.POOL_GETMODE_WAIT,
                wait_timeout=kwargs.get('wait_timeout', 10000),
                timeout=kwargs.get('timeout', 300),
                retry_count=kwargs.get('retry_count', 3),
                retry_delay=kwargs.get('retry_delay', 2),
                max_lifetime_session=kwargs.get('max_lifetime_session', 28800)
            )
            self.pools['oracle'][pool_name] = pool
            return pool
        except Exception as e:
            logging.error(f"Failed to create Oracle pool {pool_name}: {str(e)}")
            raise

    @contextmanager
    def get_connection(self, db_type: str = 'oracle', pool_name: str = 'default'):
        """获取数据库连接的上下文管理器"""
        pool = self.pools.get(db_type, {}).get(pool_name) or self.default_pool
        if not pool:
            raise ValueError(f"No pool found for {db_type}:{pool_name}")
            
        conn = pool.acquire()
        try:
            yield conn
        finally:
            pool.release(conn)

    def connect_to_oracle(self, username=None, password=None, dsn=None):
        """建立Oracle数据库连接"""
        try:
            # 如果没有提供参数，使用配置文件中的默认值
            if not username:
                username = config.get('datachange', 'username')
            if not password:
                password = config.get('datachange', 'password')
            if not dsn:
                dsn = config.get('datachange', 'dsn')
                
            connection = oracledb.connect(
                user=username,
                password=password,
                dsn=dsn
            )
            return connection
        except oracledb.Error as e:
            logging.error(f"Database connection error: {str(e)}")
            raise

def execute_rules_query(connection, query, params=None):
    """执行规则查询并返回DataFrame"""
    try:
        with connection.cursor() as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # 获取列名
            columns = [desc[0] for desc in cursor.description]

            # 获取所有数据
            rows = cursor.fetchall()

            # 构建DataFrame
            df = pd.DataFrame(rows, columns=columns)
            return df
    except Exception as e:
        logging.error(f"Query execution error: {str(e)}")
        raise

def execute_query_in_chunks(connection_params, query, chunk_size=50000):
    """分块执行大查询"""
    try:
        with oracledb.connect(**connection_params) as connection:
            cursor = connection.cursor()
            cursor.execute(query)
            columns = [col[0] for col in cursor.description]
            
            while True:
                rows = cursor.fetchmany(chunk_size)
                if not rows:
                    break
                yield pd.DataFrame(rows, columns=columns)
    except Exception as e:
        raise Exception(f"执行查询时出错: {str(e)}")

def handle_db_error(func):
    """数据库错误处理装饰器"""
    import functools

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logging.error(f"Database error in {func.__name__}: {str(e)}")
            from flask import jsonify
            return jsonify({'success': False, 'error': str(e)}), 500
    return wrapper

# 创建全局连接池管理器实例
db_manager = DatabasePoolManager()

def get_connection(pool=None):
    """获取数据库连接（兼容原有代码）"""
    if pool:
        return db_manager.get_connection()
    else:
        return db_manager.get_connection()

def init_db_pool():
    """初始化数据库连接池（兼容原有代码）"""
    return db_manager.default_pool, None
