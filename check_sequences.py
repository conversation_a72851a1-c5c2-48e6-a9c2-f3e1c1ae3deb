#!/usr/bin/env python3
"""
检查数据库序列
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def check_sequences():
    """检查数据库序列"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            # 查看所有序列
            query = "SELECT sequence_name FROM user_sequences"
            sequences = db_manager.execute_query(query)
            
            print("所有序列:")
            for seq in sequences:
                seq_name = seq.get('sequence_name', '')
                print(f"  - {seq_name}")
            
            # 查看权限表的最大ID
            max_id_query = "SELECT MAX(id) as max_id FROM permissions"
            max_id_result = db_manager.execute_query(max_id_query)
            max_id = max_id_result[0].get('max_id', 0) if max_id_result else 0
            print(f"\n权限表最大ID: {max_id}")
            
            # 手动插入权限（使用最大ID+1）
            print("\n=== 手动添加缺失权限 ===")
            
            # 检查是否存在删除任务权限
            check_query = "SELECT id FROM permissions WHERE code = 'selfcheck.tasks.delete'"
            existing = db_manager.execute_query(check_query)
            
            if existing:
                print("✅ selfcheck.tasks.delete 权限已存在")
            else:
                # 获取父权限ID
                parent_query = "SELECT id FROM permissions WHERE code = 'selfcheck.tasks'"
                parent_result = db_manager.execute_query(parent_query)
                
                if parent_result:
                    parent_id = parent_result[0]['id']
                    new_id = max_id + 1
                    
                    insert_query = """
                    INSERT INTO permissions (id, name, code, description, module, resource_type, parent_id, is_active)
                    VALUES (:id, :name, :code, :description, 'selfcheck', 'button', :parent_id, 1)
                    """
                    
                    db_manager.execute_update(insert_query, {
                        'id': new_id,
                        'name': '删除任务',
                        'code': 'selfcheck.tasks.delete',
                        'description': '删除自查任务',
                        'parent_id': parent_id
                    })
                    
                    print(f"✅ 已添加权限: selfcheck.tasks.delete (ID: {new_id})")
                else:
                    print("❌ 无法找到父权限 selfcheck.tasks")
            
        except Exception as e:
            print(f"检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_sequences()
