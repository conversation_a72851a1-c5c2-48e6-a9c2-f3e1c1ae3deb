from flask import render_template, request, jsonify, flash, redirect, url_for, send_file
from flask_login import login_required, current_user
from app.database import bp
from app.auth.decorators import permission_required
from app.models.audit import AuditLog
from app.database.services import DatabaseQueryService, SQLGeneratorService, DatabaseConnectionService
from app.utils.database import handle_db_error
from app.utils.breadcrumb import generate_breadcrumb
from datetime import datetime
import os
import tempfile
import zipfile
import logging

# 添加日志记录器
logger = logging.getLogger(__name__)

# 第一个sql_generator函数已删除，避免重复定义

@bp.route('/')
@login_required
@permission_required('database')
def index():
    """数据库工具模块首页"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='database_index',
        details='访问数据库工具模块首页'
    )
    breadcrumb_items = generate_breadcrumb('database')
    return render_template('database/index.html', breadcrumb_items=breadcrumb_items)

@bp.route('/query')
@login_required
@permission_required('database.query')
def query():
    """数据库查询生成Excel"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='database_query',
        details='访问数据库查询生成Excel'
    )

    # 获取表列表
    tables = DatabaseConnectionService.get_table_list()
    breadcrumb_items = generate_breadcrumb('database', 'query')

    return render_template('database/query.html', tables=tables, breadcrumb_items=breadcrumb_items)

@bp.route('/batch_query')
@login_required
@permission_required('database.batch_query')
def batch_query():
    """批量SQL查询生成Excel"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='database_batch_query',
        details='访问批量SQL查询生成Excel'
    )
    breadcrumb_items = generate_breadcrumb('database', 'batch_query')
    return render_template('database/batch_query.html', breadcrumb_items=breadcrumb_items)

@bp.route('/performance')
@login_required
@permission_required('database.performance')
def performance():
    """数据库性能监控"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='database_performance',
        details='访问数据库性能监控'
    )
    breadcrumb_items = generate_breadcrumb('database', 'performance')
    return render_template('database/performance_test.html', breadcrumb_items=breadcrumb_items)

@bp.route('/sql_generator')
@login_required
@permission_required('database.sql_generator')
def sql_generator():
    """SQL生成器"""
    # 获取数据库表列表
    try:
        from app.utils.database import db_manager
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT table_name FROM user_tables ORDER BY table_name")
            tables = [row[0] for row in cursor.fetchall()]
    except Exception as e:
        tables = []

    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='sql_generator',
        details='访问SQL生成器'
    )

    breadcrumb_items = generate_breadcrumb('database', 'sql_generator')
    return render_template('database/sql_generator.html', tables=tables, breadcrumb_items=breadcrumb_items)

@bp.route('/performance_test')
@login_required
@permission_required('database.performance_test')
def performance_test():
    """SQL性能测试"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='performance_test',
        details='访问SQL性能测试'
    )
    breadcrumb_items = generate_breadcrumb('database', 'performance_test')
    return render_template('database/performance_test.html', breadcrumb_items=breadcrumb_items)

# API路由
@bp.route('/api/execute_query', methods=['POST'])
@login_required
@permission_required('database.query')
@handle_db_error
def api_execute_query():
    """执行SQL查询API"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        export_format = data.get('export_format', 'json')
        db_type = data.get('db_type', 'Oracle')
        db_host = data.get('db_host', '')
        db_schema = data.get('db_schema', '')
        limit_rows = data.get('limit_rows', 1000)  # 默认限制1000行，前端可以指定10行

        if not query:
            return jsonify({'success': False, 'error': '查询语句不能为空'}), 400

        # 验证SQL
        is_valid, message = SQLGeneratorService.validate_sql(query)
        if not is_valid:
            return jsonify({'success': False, 'error': message}), 400

        # 记录操作日志（包含数据库配置信息）
        AuditLog.log_action(
            user_id=current_user.id,
            action='execute_query',
            resource='database',
            details=f'执行SQL查询: {query[:100]}... | 数据库: {db_type} | 主机: {db_host} | Schema: {db_schema}'
        )

        if export_format == 'excel':
            # 导出到Excel（使用指定的数据库配置）
            file_path, filename = DatabaseQueryService.execute_query_to_excel_with_config(
                query, db_type, db_host, db_schema
            )
            if file_path:
                return jsonify({
                    'success': True,
                    'export_url': f'/database/api/download/{os.path.basename(file_path)}',
                    'filename': filename
                })
            else:
                return jsonify({'success': False, 'error': '查询结果为空'})
        elif export_format == 'csv':
            # 导出到CSV（使用指定的数据库配置）
            file_path, filename = DatabaseQueryService.execute_query_to_csv_with_config(
                query, db_type, db_host, db_schema
            )
            if file_path:
                return jsonify({
                    'success': True,
                    'export_url': f'/database/api/download/{os.path.basename(file_path)}',
                    'filename': filename
                })
            else:
                return jsonify({'success': False, 'error': '查询结果为空'})
        else:
            # 返回JSON数据（使用指定的数据库配置）
            result = DatabaseQueryService.execute_query_with_config(
                query, db_type, db_host, db_schema, limit_rows
            )

            if not result['success']:
                # 记录详细的错误日志
                logger.error(f"数据库查询失败 - 用户: {current_user.username}, 数据库: {db_type}, 主机: {db_host}, Schema: {db_schema}, 错误: {result['error']}")

                # 记录审计日志
                AuditLog.log_action(
                    user_id=current_user.id,
                    action='query_error',
                    resource='database',
                    details=f'查询执行失败: {db_type} | 主机: {db_host} | Schema: {db_schema} | 错误: {result["error"]}'
                )

                return jsonify(result), 500

            return jsonify({
                'success': True,
                'data': result['data'],
                'columns': result['columns'],
                'total': result['total'],
                'displayed': result['displayed'],
                'truncated': result['truncated'],
                'db_config': {
                    'db_type': db_type,
                    'db_host': db_host,
                    'db_schema': db_schema
                }
            })

    except Exception as e:
        error_msg = f'数据库查询请求处理失败: {str(e)}'
        logger.error(f"数据库查询请求异常 - 用户: {current_user.username}, 错误: {str(e)}")

        # 记录审计日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='query_request_error',
            resource='database',
            details=f'查询请求处理失败: {str(e)}'
        )

        return jsonify({'success': False, 'error': error_msg}), 500

@bp.route('/api/sql_templates', methods=['GET'])
@login_required
@permission_required('database.sql_generator')
def api_get_sql_templates():
    """获取SQL模板API"""
    try:
        templates = SQLGeneratorService.get_sql_templates()
        return jsonify({'success': True, 'templates': templates})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/render_template', methods=['POST'])
@login_required
@permission_required('database.sql_generator')
def api_render_template():
    """渲染SQL模板API"""
    try:
        data = request.get_json()
        template = data.get('template', '')
        variables = data.get('variables', {})

        rendered_sql = SQLGeneratorService.render_template(template, variables)

        return jsonify({
            'success': True,
            'sql': rendered_sql
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/table_columns/<table_name>', methods=['GET'])
@login_required
@permission_required('database.query')
@handle_db_error
def api_get_table_columns(table_name):
    """获取表字段API"""
    try:
        columns = DatabaseConnectionService.get_table_columns(table_name)
        return jsonify({'success': True, 'columns': columns})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/database_schemas', methods=['POST'])
@login_required
@permission_required('database.query')
@handle_db_error
def api_get_database_schemas():
    """获取指定数据库实例中的所有schema"""
    try:
        data = request.get_json()
        database = data.get('database', 'Oracle')
        host = data.get('host', '')

        if not host:
            return jsonify({'success': False, 'error': '数据库主机IP不能为空'}), 400

        schemas = []

        if database.lower() == 'oracle':
            # 获取Oracle实例中的所有schema
            try:
                import oracledb
                # 使用指定主机的Oracle连接
                dsn = f"{host}:1521/orcl"  # 默认端口和服务名
                with oracledb.connect(
                    user="datachange",  # 默认用户名
                    password="drgs2019",  # 默认密码
                    dsn=dsn
                ) as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("""
                            SELECT username FROM all_users
                            WHERE username NOT IN ('ANONYMOUS','APPQOSSYS','AUDSYS','CTXSYS','DBSFWUSER','DBSNMP','DIP','DVF','DVSYS','ENGINE','GGSYS','GSMADMIN_INTERNAL','GSMCATUSER','GSMROOTUSER','GSMUSER','HR','LBACSYS','MDDATA','MDSYS','OJVMSYS','OLAPSYS','ORACLE_OCM','ORDDATA','ORDPLUGINS','ORDSYS','OUTLN','REMOTE_SCHEDULER_AGENT','SI_INFORMTN_SCHEMA','SYS','SYS$UMF','SYSBACKUP','SYSDG','SYSKM','SYSRAC','SYSTEM','WMSYS','XDB','XS$NULL')
                            ORDER BY username
                        """)
                        rows = cursor.fetchall()
                        schemas = [row[0] for row in rows]
            except Exception as e:
                return jsonify({'success': False, 'error': f'连接Oracle主机 {host} 失败: {str(e)}'}), 500

        elif database.lower() == 'postgresql':
            # 获取PostgreSQL实例中的所有schema
            try:
                try:
                    # 尝试使用psycopg2
                    import psycopg2
                    conn = psycopg2.connect(
                        host=host,
                        port=5432,  # 默认端口
                        database="databasetools",  # 默认数据库名
                        user="postgres",  # 默认用户名
                        password="P@ssw0rd"  # 默认密码
                    )
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1') ORDER BY schema_name")
                        rows = cursor.fetchall()
                        schemas = [row[0] for row in rows]
                    conn.close()
                except ImportError:
                    # 如果psycopg2不可用，尝试使用psycopg3
                    import psycopg
                    with psycopg.connect(
                        host=host,
                        port=5432,  # 默认端口
                        dbname="databasetools",  # 默认数据库名
                        user="postgres",  # 默认用户名
                        password="P@ssw0rd"  # 默认密码
                    ) as conn:
                        with conn.cursor() as cursor:
                            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1') ORDER BY schema_name")
                            rows = cursor.fetchall()
                            schemas = [row[0] for row in rows]
            except Exception as e:
                return jsonify({'success': False, 'error': f'连接PostgreSQL主机 {host} 失败: {str(e)}'}), 500

        elif database.lower() == 'mysql':
            # 获取MySQL实例中的所有数据库（相当于schema）
            try:
                import mysql.connector
                with mysql.connector.connect(
                    host=host,
                    port=3306,  # 默认端口
                    user="root",  # 默认用户名
                    password="password"  # 默认密码
                ) as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("SHOW DATABASES")
                        rows = cursor.fetchall()
                        schemas = [row[0] for row in rows if row[0] not in ('information_schema', 'performance_schema', 'mysql', 'sys')]
            except Exception as e:
                return jsonify({'success': False, 'error': f'连接MySQL主机 {host} 失败: {str(e)}'}), 500

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='get_schemas',
            resource='database',
            details=f'获取数据库Schema: {database} | 主机: {host} | 找到{len(schemas)}个Schema'
        )

        return jsonify({'success': True, 'schemas': schemas})

    except Exception as e:
        logger.error(f"获取数据库schema列表失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/database_tables', methods=['POST'])
@login_required
@permission_required('database.query')
@handle_db_error
def api_get_database_tables():
    """获取指定数据库Schema中的所有表"""
    try:
        data = request.get_json()
        database = data.get('database', 'Oracle')
        host = data.get('host', '')
        schema = data.get('schema', '')

        if not host:
            return jsonify({'success': False, 'error': '数据库主机IP不能为空'}), 400

        if not schema:
            return jsonify({'success': False, 'error': 'Schema不能为空'}), 400

        tables = []

        if database.lower() == 'oracle':
            # 获取Oracle Schema中的所有表
            try:
                import oracledb
                dsn = f"{host}:1521/orcl"
                with oracledb.connect(
                    user="datachange",
                    password="drgs2019",
                    dsn=dsn
                ) as conn:
                    with conn.cursor() as cursor:
                        # 获取指定schema的所有表（不需要切换schema）
                        cursor.execute("""
                            SELECT table_name, comments
                            FROM all_tab_comments
                            WHERE owner = :schema_name
                              AND table_type = 'TABLE'
                            ORDER BY table_name
                        """, {'schema_name': schema.upper()})
                        rows = cursor.fetchall()
                        tables = [{'name': row[0], 'comment': row[1] or ''} for row in rows]
            except Exception as e:
                return jsonify({'success': False, 'error': f'连接Oracle主机 {host} 失败: {str(e)}'}), 500

        elif database.lower() == 'postgresql':
            # 获取PostgreSQL Schema中的所有表
            try:
                try:
                    # 尝试使用psycopg2
                    import psycopg2
                    conn = psycopg2.connect(
                        host=host,
                        port=5432,
                        database="databasetools",
                        user="postgres",
                        password="P@ssw0rd"
                    )
                    with conn.cursor() as cursor:
                        # 设置search_path到指定schema
                        cursor.execute(f'SET search_path TO "{schema}", public')

                        # 获取当前schema的所有表（简化查询）
                        cursor.execute("""
                            SELECT table_name, '' as comment
                            FROM information_schema.tables
                            WHERE table_schema = %s
                              AND table_type = 'BASE TABLE'
                            ORDER BY table_name
                        """, (schema,))
                        rows = cursor.fetchall()
                        tables = [{'name': row[0], 'comment': row[1] or ''} for row in rows]
                    conn.close()
                except ImportError:
                    # 如果psycopg2不可用，尝试使用psycopg3
                    import psycopg
                    with psycopg.connect(
                        host=host,
                        port=5432,
                        dbname="databasetools",
                        user="postgres",
                        password="P@ssw0rd"
                    ) as conn:
                        with conn.cursor() as cursor:
                            # 设置search_path到指定schema
                            cursor.execute(f'SET search_path TO "{schema}", public')

                            # 获取当前schema的所有表（简化查询）
                            cursor.execute("""
                                SELECT table_name, '' as comment
                                FROM information_schema.tables
                                WHERE table_schema = %s
                                  AND table_type = 'BASE TABLE'
                                ORDER BY table_name
                            """, (schema,))
                            rows = cursor.fetchall()
                            tables = [{'name': row[0], 'comment': row[1] or ''} for row in rows]
            except Exception as e:
                return jsonify({'success': False, 'error': f'连接PostgreSQL主机 {host} 失败: {str(e)}'}), 500

        elif database.lower() == 'mysql':
            # 获取MySQL数据库中的所有表
            try:
                import mysql.connector
                with mysql.connector.connect(
                    host=host,
                    port=3306,
                    database=schema,  # MySQL中schema就是database
                    user="root",
                    password="password"
                ) as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("""
                            SELECT table_name, table_comment
                            FROM information_schema.tables
                            WHERE table_schema = %s
                              AND table_type = 'BASE TABLE'
                            ORDER BY table_name
                        """, (schema,))
                        rows = cursor.fetchall()
                        tables = [{'name': row[0], 'comment': row[1] or ''} for row in rows]
            except Exception as e:
                return jsonify({'success': False, 'error': f'连接MySQL主机 {host} 失败: {str(e)}'}), 500

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='get_tables',
            resource='database',
            details=f'获取数据库表列表: {database} | 主机: {host} | Schema: {schema} | 找到{len(tables)}个表'
        )

        return jsonify({'success': True, 'tables': tables})

    except Exception as e:
        logger.error(f"获取数据库表列表失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/table_columns', methods=['POST'])
@login_required
@permission_required('database.query')
@handle_db_error
def api_get_table_columns_with_config():
    """获取指定表的字段信息（支持数据库配置）"""
    try:
        data = request.get_json()
        database = data.get('database', 'Oracle')
        host = data.get('host', '')
        schema = data.get('schema', '')
        table_name = data.get('table_name', '')

        if not host:
            return jsonify({'success': False, 'error': '数据库主机IP不能为空'}), 400

        if not schema:
            return jsonify({'success': False, 'error': 'Schema不能为空'}), 400

        if not table_name:
            return jsonify({'success': False, 'error': '表名不能为空'}), 400

        columns = []

        if database.lower() == 'oracle':
            # 获取Oracle表的字段信息
            try:
                import oracledb
                dsn = f"{host}:1521/orcl"
                with oracledb.connect(
                    user="datachange",
                    password="drgs2019",
                    dsn=dsn
                ) as conn:
                    with conn.cursor() as cursor:
                        # 获取指定schema的表字段信息（不需要切换schema）
                        cursor.execute("""
                            SELECT
                                c.column_name,
                                c.data_type,
                                c.data_length,
                                c.data_precision,
                                c.data_scale,
                                c.nullable,
                                c.column_id,
                                COALESCE(cc.comments, '') as comments
                            FROM all_tab_columns c
                            LEFT JOIN all_col_comments cc ON c.owner = cc.owner
                                                           AND c.table_name = cc.table_name
                                                           AND c.column_name = cc.column_name
                            WHERE c.owner = :schema_name
                              AND c.table_name = :table_name
                            ORDER BY c.column_id
                        """, {'schema_name': schema.upper(), 'table_name': table_name.upper()})
                        rows = cursor.fetchall()

                        for row in rows:
                            column_info = {
                                'name': row[0],
                                'type': row[1],
                                'length': row[2],
                                'precision': row[3],
                                'scale': row[4],
                                'nullable': row[5] == 'Y',
                                'position': row[6],
                                'comment': row[7] or ''
                            }
                            columns.append(column_info)
            except Exception as e:
                return jsonify({'success': False, 'error': f'连接Oracle主机 {host} 失败: {str(e)}'}), 500

        elif database.lower() == 'postgresql':
            # 获取PostgreSQL表的字段信息
            try:
                try:
                    # 尝试使用psycopg2
                    import psycopg2
                    conn = psycopg2.connect(
                        host=host,
                        port=5432,
                        database="databasetools",
                        user="postgres",
                        password="P@ssw0rd"
                    )
                    with conn.cursor() as cursor:
                        # 获取表字段信息（简化查询）
                        cursor.execute("""
                            SELECT
                                column_name,
                                data_type,
                                character_maximum_length,
                                numeric_precision,
                                numeric_scale,
                                is_nullable,
                                ordinal_position,
                                '' as comment
                            FROM information_schema.columns
                            WHERE table_schema = %s
                              AND table_name = %s
                            ORDER BY ordinal_position
                        """, (schema, table_name))
                        rows = cursor.fetchall()

                        for row in rows:
                            column_info = {
                                'name': row[0],
                                'type': row[1],
                                'length': row[2],
                                'precision': row[3],
                                'scale': row[4],
                                'nullable': row[5] == 'YES',
                                'position': row[6],
                                'comment': row[7] or ''
                            }
                            columns.append(column_info)
                    conn.close()
                except ImportError:
                    # 如果psycopg2不可用，尝试使用psycopg3
                    import psycopg
                    with psycopg.connect(
                        host=host,
                        port=5432,
                        dbname="postgres",
                        user="postgres",
                        password="postgres"
                    ) as conn:
                        with conn.cursor() as cursor:
                            # 获取表字段信息（简化查询）
                            cursor.execute("""
                                SELECT
                                    column_name,
                                    data_type,
                                    character_maximum_length,
                                    numeric_precision,
                                    numeric_scale,
                                    is_nullable,
                                    ordinal_position,
                                    '' as comment
                                FROM information_schema.columns
                                WHERE table_schema = %s
                                  AND table_name = %s
                                ORDER BY ordinal_position
                            """, (schema, table_name))
                            rows = cursor.fetchall()

                            for row in rows:
                                column_info = {
                                    'name': row[0],
                                    'type': row[1],
                                    'length': row[2],
                                    'precision': row[3],
                                    'scale': row[4],
                                    'nullable': row[5] == 'YES',
                                    'position': row[6],
                                    'comment': row[7] or ''
                                }
                                columns.append(column_info)
            except Exception as e:
                return jsonify({'success': False, 'error': f'连接PostgreSQL主机 {host} 失败: {str(e)}'}), 500

        elif database.lower() == 'mysql':
            # 获取MySQL表的字段信息
            try:
                import mysql.connector
                with mysql.connector.connect(
                    host=host,
                    port=3306,
                    database=schema,
                    user="root",
                    password="password"
                ) as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("""
                            SELECT
                                column_name,
                                data_type,
                                character_maximum_length,
                                numeric_precision,
                                numeric_scale,
                                is_nullable,
                                ordinal_position,
                                COALESCE(column_comment, '') as comment
                            FROM information_schema.columns
                            WHERE table_schema = %s
                              AND table_name = %s
                            ORDER BY ordinal_position
                        """, (schema, table_name))
                        rows = cursor.fetchall()

                        for row in rows:
                            column_info = {
                                'name': row[0],
                                'type': row[1],
                                'length': row[2],
                                'precision': row[3],
                                'scale': row[4],
                                'nullable': row[5] == 'YES',
                                'position': row[6],
                                'comment': row[7] or ''
                            }
                            columns.append(column_info)
            except Exception as e:
                return jsonify({'success': False, 'error': f'连接MySQL主机 {host} 失败: {str(e)}'}), 500

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='get_columns',
            resource='database',
            details=f'获取表字段信息: {database} | 主机: {host} | Schema: {schema} | 表: {table_name} | 找到{len(columns)}个字段'
        )

        return jsonify({'success': True, 'columns': columns})

    except Exception as e:
        logger.error(f"获取表字段信息失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/download/<filename>')
@login_required
def api_download_file(filename):
    """下载文件API"""
    try:
        # 安全检查文件名
        if '..' in filename or '/' in filename or '\\' in filename:
            return jsonify({'error': '非法文件名'}), 400

        file_path = os.path.join(tempfile.gettempdir(), filename)

        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404

        # 记录下载日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='download',
            resource='file',
            resource_id=filename,
            details=f'下载文件: {filename}'
        )

        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/batch_execute', methods=['POST'])
@login_required
@permission_required('database.batch_query')
@handle_db_error
def api_batch_execute():
    """批量执行SQL查询API"""
    try:
        data = request.get_json()
        queries = data.get('queries', [])

        if not queries:
            return jsonify({'success': False, 'error': '没有查询语句'}), 400

        results = []
        successful_queries = 0

        for i, query_item in enumerate(queries):
            query_name = query_item.get('name', f'查询{i+1}')
            query_sql = query_item.get('query', '').strip()

            if not query_sql:
                results.append({
                    'name': query_name,
                    'status': 'error',
                    'error': 'SQL语句为空'
                })
                continue

            # 验证SQL
            is_valid, message = SQLGeneratorService.validate_sql(query_sql)
            if not is_valid:
                results.append({
                    'name': query_name,
                    'status': 'error',
                    'error': message
                })
                continue

            try:
                # 执行查询并导出到Excel
                file_path, filename = DatabaseQueryService.execute_query_to_excel(query_sql)

                if file_path:
                    # 获取行数
                    from app.utils.database import db_manager, execute_rules_query
                    with db_manager.get_connection() as conn:
                        df = execute_rules_query(conn, query_sql)
                        row_count = len(df)

                    results.append({
                        'name': query_name,
                        'status': 'success',
                        'rows': row_count,
                        'filename': filename,
                        'download_url': f'/database/api/download/{os.path.basename(file_path)}'
                    })
                    successful_queries += 1
                else:
                    results.append({
                        'name': query_name,
                        'status': 'error',
                        'error': '查询结果为空'
                    })

            except Exception as e:
                results.append({
                    'name': query_name,
                    'status': 'error',
                    'error': str(e)
                })

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='batch_execute',
            resource='database',
            details=f'批量执行{len(queries)}个查询，成功{successful_queries}个'
        )

        return jsonify({
            'success': True,
            'results': results,
            'total_queries': len(queries),
            'successful_queries': successful_queries
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/performance_test', methods=['POST'])
@login_required
@permission_required('database.performance_test')
@handle_db_error
def api_performance_test():
    """SQL性能测试API"""
    try:
        data = request.get_json()
        tests = data.get('tests', [])
        mode = data.get('mode', 'single')
        execution_count = data.get('execution_count', 1)

        if not tests:
            return jsonify({'success': False, 'error': '没有测试SQL'}), 400

        results = []
        total_time = 0
        successful_tests = 0

        for test in tests:
            test_name = test.get('name', '未命名测试')
            test_sql = test.get('sql', '').strip()

            if not test_sql:
                results.append({
                    'name': test_name,
                    'status': 'error',
                    'error': 'SQL语句为空'
                })
                continue

            # 验证SQL
            is_valid, message = SQLGeneratorService.validate_sql(test_sql)
            if not is_valid:
                results.append({
                    'name': test_name,
                    'status': 'error',
                    'error': message
                })
                continue

            try:
                # 执行性能测试
                import time
                from app.utils.database import db_manager, execute_rules_query

                execution_times = []
                row_count = 0

                for _ in range(execution_count):
                    start_time = time.time()

                    with db_manager.get_connection() as conn:
                        df = execute_rules_query(conn, test_sql)
                        row_count = len(df)

                    end_time = time.time()
                    execution_times.append((end_time - start_time) * 1000)  # 转换为毫秒

                # 计算统计信息
                avg_time = sum(execution_times) / len(execution_times)
                min_time = min(execution_times)
                max_time = max(execution_times)

                results.append({
                    'name': test_name,
                    'status': 'success',
                    'execution_count': execution_count,
                    'avg_time': round(avg_time, 2),
                    'min_time': round(min_time, 2),
                    'max_time': round(max_time, 2),
                    'rows': row_count
                })

                total_time += avg_time
                successful_tests += 1

            except Exception as e:
                results.append({
                    'name': test_name,
                    'status': 'error',
                    'error': str(e)
                })

        # 计算汇总信息
        avg_execution_time = total_time / successful_tests if successful_tests > 0 else 0

        summary = {
            'total_tests': len(tests),
            'successful_tests': successful_tests,
            'total_execution_time': round(total_time, 2),
            'avg_execution_time': round(avg_execution_time, 2)
        }

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='performance_test',
            resource='database',
            details=f'性能测试{len(tests)}个SQL，成功{successful_tests}个'
        )

        return jsonify({
            'success': True,
            'results': results,
            'summary': summary
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
