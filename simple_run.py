#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Flask应用启动脚本，用于调试
"""

import os
import sys
import logging

# 强制刷新输出
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

print("=== 开始启动应用 ===")
sys.stdout.flush()

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
    ],
    force=True
)

print("日志配置完成")
sys.stdout.flush()

try:
    print("正在导入应用模块...")
    sys.stdout.flush()

    from app import create_app
    print("应用模块导入成功")
    sys.stdout.flush()

    print("正在创建应用实例...")
    sys.stdout.flush()

    app = create_app('development')
    print("应用创建成功")
    sys.stdout.flush()

    # 测试日志
    print("测试应用日志...")
    sys.stdout.flush()

    app.logger.info("应用日志测试")
    print("应用日志测试完成")
    sys.stdout.flush()

    print("=== 启动Flask开发服务器 ===")
    sys.stdout.flush()

    # 启动服务器
    app.run(host='0.0.0.0', port=5003, debug=True, use_reloader=False)

except Exception as e:
    print(f"启动失败: {e}")
    sys.stdout.flush()
    import traceback
    traceback.print_exc()
    sys.stdout.flush()
