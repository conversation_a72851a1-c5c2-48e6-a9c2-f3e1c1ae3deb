#!/usr/bin/env python3
"""测试CSRF保护的脚本"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
import requests
from flask import url_for

def test_csrf_protection():
    """测试CSRF保护"""
    app = create_app()
    
    with app.test_client() as client:
        print("=== 测试CSRF保护 ===")
        
        # 测试没有CSRF token的POST请求应该被拒绝
        print("\n1. 测试没有CSRF token的请求:")
        
        # 先登录获取session
        login_response = client.post('/auth/login', data={
            'username': 'admin',
            'password': 'admin123',
            'csrf_token': 'invalid_token'  # 故意使用无效token
        }, follow_redirects=False)
        
        print(f"   登录响应状态码: {login_response.status_code}")
        
        # 测试API请求没有CSRF token
        api_tests = [
            ('/admin/api/users', 'POST', {'username': 'test', 'email': '<EMAIL>'}),
            ('/selfcheck/api/uploads', 'POST', {}),
            ('/database/api/execute_query', 'POST', {'query': 'SELECT 1 FROM dual'}),
        ]
        
        for url, method, data in api_tests:
            if method == 'POST':
                response = client.post(url, json=data)
            elif method == 'PUT':
                response = client.put(url, json=data)
            elif method == 'DELETE':
                response = client.delete(url)
            
            print(f"   {method} {url}: 状态码 {response.status_code}")
            if response.status_code == 400:
                print(f"      ✅ CSRF保护生效")
            else:
                print(f"      ❌ CSRF保护可能未生效")

def check_template_csrf_tokens():
    """检查模板文件中的CSRF token"""
    print("\n=== 检查模板文件中的CSRF token ===")
    
    template_dir = "app/templates"
    csrf_patterns = ['csrf_token()', 'csrf-token', 'X-CSRFToken']
    
    # 需要检查的模板文件
    template_files = [
        "base.html",
        "admin/users.html",
        "admin/roles.html", 
        "data/find_duplicates.html",
        "data/validator.html",
        "database/query.html",
        "database/batch_query.html",
        "excel/splitter.html",
        "excel/compare.html",
        "rules/knowledge_base.html",
        "selfcheck/tasks.html",
        "selfcheck/uploads.html",
        "selfcheck/rules.html"
    ]
    
    for template_file in template_files:
        file_path = os.path.join(template_dir, template_file)
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            has_csrf = any(pattern in content for pattern in csrf_patterns)
            status = "✅" if has_csrf else "❌"
            print(f"{status} {template_file}: {'有CSRF保护' if has_csrf else '缺少CSRF保护'}")
        else:
            print(f"❓ {template_file}: 文件不存在")

def check_route_csrf_protection():
    """检查路由的CSRF保护"""
    print("\n=== 检查路由的CSRF保护配置 ===")
    
    app = create_app()
    
    with app.app_context():
        # 检查CSRF是否启用
        csrf_enabled = app.config.get('WTF_CSRF_ENABLED', False)
        print(f"CSRF全局启用状态: {'✅ 启用' if csrf_enabled else '❌ 未启用'}")
        
        csrf_time_limit = app.config.get('WTF_CSRF_TIME_LIMIT', 3600)
        print(f"CSRF令牌有效期: {csrf_time_limit}秒")
        
        # 检查是否有CSRFProtect实例
        from app import csrf
        print(f"CSRFProtect实例: {'✅ 已配置' if csrf else '❌ 未配置'}")

if __name__ == '__main__':
    check_route_csrf_protection()
    check_template_csrf_tokens()
    test_csrf_protection()
