#!/usr/bin/env python3
"""
检查页面是否有错误
"""

import requests
import re

def check_page_errors():
    base_url = "http://127.0.0.1:5002"
    session = requests.Session()
    
    # 先登录
    print("🔐 正在登录...")
    login_page = session.get(f"{base_url}/auth/login")
    
    csrf_patterns = [
        r'name="csrf_token" value="([^"]+)"',
        r'<input[^>]*name="csrf_token"[^>]*value="([^"]+)"',
    ]
    
    csrf_token = None
    for pattern in csrf_patterns:
        csrf_match = re.search(pattern, login_page.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            break
    
    login_data = {
        'username': 'admin',
        'password': 'Admin123!',
        'remember_me': False
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    response = session.post(f"{base_url}/auth/login", data=login_data, allow_redirects=True)
    
    if response.status_code == 200 and '/auth/login' not in response.url:
        print("✅ 登录成功")
    else:
        print("❌ 登录失败")
        return
    
    # 检查页面
    pages = [
        ('/rules/sql_generator', 'SQL生成器'),
        ('/rules/system_rules', '系统规则语句')
    ]
    
    for url, name in pages:
        print(f"\n🔍 检查页面: {name}")
        try:
            response = session.get(f"{base_url}{url}")
            if response.status_code == 200:
                content = response.text
                
                # 检查是否有错误信息
                error_patterns = [
                    r'Traceback',
                    r'Exception',
                    r'Error:',
                    r'500 Internal Server Error',
                    r'NameError',
                    r'AttributeError',
                    r'TypeError'
                ]
                
                errors_found = []
                for pattern in error_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        errors_found.append(pattern)
                
                if errors_found:
                    print(f"❌ 发现错误: {', '.join(errors_found)}")
                    # 提取错误上下文
                    for pattern in errors_found:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            start = max(0, match.start() - 100)
                            end = min(len(content), match.end() + 100)
                            context = content[start:end]
                            print(f"   错误上下文: ...{context}...")
                            break  # 只显示第一个匹配
                        break
                else:
                    print(f"✅ 页面正常加载，无明显错误")
                    
                    # 检查页面是否包含预期内容
                    if 'sql_generator' in url:
                        if '规则SQL生成器' in content:
                            print("   ✅ 包含预期标题")
                        else:
                            print("   ⚠️ 缺少预期标题")
                    elif 'system_rules' in url:
                        if '系统规则语句' in content:
                            print("   ✅ 包含预期标题")
                        else:
                            print("   ⚠️ 缺少预期标题")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 异常: {str(e)}")

if __name__ == '__main__':
    check_page_errors()
