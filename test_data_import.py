#!/usr/bin/env python3
"""
测试数据导入功能
"""

import sys
sys.path.append('.')

import pandas as pd
import os
from app.selfcheck.data_import_service import DataImportService

def create_test_csv():
    """创建测试CSV文件"""
    # 使用标准表头创建测试数据
    data = {
        '患者姓名': ['张三', '李四', '王五'],
        '病案号': ['BH001', 'BH002', 'BH003'],
        '身份证号': ['110101199001011234', '110101199002021234', '110101199003031234'],
        '结算单据号': ['JS001', 'JS002', 'JS003'],
        '医疗机构编码': ['YY001', 'YY001', 'YY001'],
        '医疗机构名称': ['测试医院', '测试医院', '测试医院'],
        '入院科室': ['内科', '外科', '儿科'],
        '出院科室': ['内科', '外科', '儿科'],
        '主诊医师姓名': ['医生A', '医生B', '医生C'],
        '患者性别': ['男', '女', '男'],
        '患者年龄': [30, 25, 35],
        '险种类型': ['职工医保', '居民医保', '职工医保'],
        '医保等级': ['一级', '二级', '一级'],
        '入院日期': ['2024-01-01', '2024-01-02', '2024-01-03'],
        '出院日期': ['2024-01-05', '2024-01-06', '2024-01-07'],
        '项目使用日期': ['2024-01-02', '2024-01-03', '2024-01-04'],
        '结算日期': ['2024-01-05', '2024-01-06', '2024-01-07'],
        '入院诊断编码': ['ICD001', 'ICD002', 'ICD003'],
        '入院诊断名称': ['诊断1', '诊断2', '诊断3'],
        '出院诊断编码': ['ICD001', 'ICD002', 'ICD003'],
        '出院诊断名称': ['诊断1', '诊断2', '诊断3'],
        '主手术及操作编码': ['OP001', 'OP002', 'OP003'],
        '主手术及操作名称': ['手术1', '手术2', '手术3'],
        '其他手术及操作编码': ['', '', ''],
        '其他手术及操作名称': ['', '', ''],
        '医院项目编码': ['HI001', 'HI002', 'HI003'],
        '医院项目名称': ['项目1', '项目2', '项目3'],
        '医保项目编码': ['MI001', 'MI002', 'MI003'],
        '医保项目名称': ['医保项目1', '医保项目2', '医保项目3'],
        '规格': ['规格1', '规格2', '规格3'],
        '单价': [100.00, 200.00, 150.00],
        '数量': [1, 2, 1],
        '金额': [100.00, 400.00, 150.00],
        '医保范围内金额': [80.00, 320.00, 120.00],
        '医疗总费用': [100.00, 400.00, 150.00],
        '基本统筹支付': [60.00, 240.00, 90.00],
        '个人自付': [20.00, 80.00, 30.00],
        '个人自费': [20.00, 80.00, 30.00],
        '符合基本医疗保险的费用': [80.00, 320.00, 120.00],
        '报销比例': [75.0, 75.0, 75.0],
        '自付比例': [25.0, 25.0, 25.0],
        '费用类别': ['药品费', '检查费', '治疗费'],
        '支付类别': ['甲类', '乙类', '甲类'],
        '开单科室名称': ['内科', '外科', '儿科'],
        '执行科室名称': ['药房', '检验科', '治疗室'],
        '开单医师姓名': ['医生A', '医生B', '医生C']
    }
    
    df = pd.DataFrame(data)
    test_file = 'test_medical_data.csv'
    df.to_csv(test_file, index=False, encoding='utf-8')
    print(f"✅ 创建测试CSV文件: {test_file}")
    return test_file

def test_csv_header_validation():
    """测试CSV表头校验"""
    print("\n🧪 测试CSV表头校验...")
    
    # 创建测试文件
    test_file = create_test_csv()
    
    try:
        # 测试表头校验
        result = DataImportService._validate_csv_headers(test_file)
        
        if result['success']:
            print("✅ CSV表头校验通过")
            print(f"   文件表头数量: {result['total_headers']}")
            print(f"   匹配字段数量: {result['matched_headers']}")
            if result['extra_headers']:
                print(f"   额外字段: {result['extra_headers']}")
        else:
            print("❌ CSV表头校验失败")
            print(f"   错误信息: {result['error']}")
            if result.get('missing_headers'):
                print(f"   缺少字段: {result['missing_headers']}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ 表头校验异常: {str(e)}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

def test_user_schema_creation():
    """测试用户Schema创建"""
    print("\n🧪 测试用户Schema创建...")
    
    test_username = 'test_user_schema'
    
    try:
        # 测试Schema创建
        result = DataImportService._ensure_user_schema(test_username)
        
        if result['success']:
            print("✅ 用户Schema创建/检查成功")
            print(f"   消息: {result['message']}")
            
            # 检查数据表是否存在
            table_exists = DataImportService._check_user_data_table(test_username)
            if table_exists:
                print("✅ 用户数据表存在")
            else:
                print("❌ 用户数据表不存在")
                
            return True
        else:
            print("❌ 用户Schema创建失败")
            print(f"   错误信息: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Schema创建异常: {str(e)}")
        return False

def test_data_cleaning():
    """测试数据清洗"""
    print("\n🧪 测试数据清洗...")
    
    # 创建测试文件
    test_file = create_test_csv()
    
    try:
        # 读取CSV文件
        df = pd.read_csv(test_file, encoding='utf-8')
        
        # 测试数据清洗
        cleaned_data = DataImportService._clean_csv_data(df)
        
        if cleaned_data and len(cleaned_data) > 0:
            print("✅ 数据清洗成功")
            print(f"   清洗后记录数: {len(cleaned_data)}")
            print(f"   第一条记录字段数: {len(cleaned_data[0])}")
            
            # 检查数据质量
            quality_result = DataImportService._validate_csv_data_quality(cleaned_data)
            print(f"   质量问题数: {quality_result['total_issues']}")
            print(f"   错误数: {quality_result['error_count']}")
            print(f"   警告数: {quality_result['warning_count']}")
            
            return True
        else:
            print("❌ 数据清洗失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据清洗异常: {str(e)}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

def main():
    """主测试函数"""
    print("🚀 开始测试数据导入功能...")
    
    tests = [
        ("CSV表头校验", test_csv_header_validation),
        ("用户Schema创建", test_user_schema_creation),
        ("数据清洗", test_data_cleaning)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！数据导入功能基本正常。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")

if __name__ == '__main__':
    main()
