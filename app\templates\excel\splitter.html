{% extends "base.html" %}

{% block title %}Excel文件拆分 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-cut"></i> Excel文件拆分</h4>
{% endblock %}

{% block content %}
<!-- 文件上传区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-upload"></i> 文件上传
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="fileInput" class="form-label">选择Excel文件</label>
                            <input type="file" class="form-control" id="fileInput" accept=".xlsx,.xls" onchange="uploadFile()">
                            <div class="form-text">支持 .xlsx 和 .xls 格式文件</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mt-4">
                            <div id="uploadStatus" class="alert alert-info d-none">
                                <i class="fas fa-info-circle"></i>
                                <span id="uploadMessage">请选择文件</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件信息显示 -->
                <div id="fileInfo" class="mt-3 d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr><th width="30%">文件名:</th><td id="fileName"></td></tr>
                                <tr><th>文件大小:</th><td id="fileSize"></td></tr>
                                <tr><th>上传时间:</th><td id="uploadTime"></td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div id="filePreview">
                                <!-- 文件预览信息 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 拆分配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 拆分配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">拆分方式</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="splitType" id="splitByColumn" value="column" checked onchange="toggleSplitOptions()">
                            <label class="form-check-label" for="splitByColumn">
                                按列拆分
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="splitType" id="splitByRows" value="rows" onchange="toggleSplitOptions()">
                            <label class="form-check-label" for="splitByRows">
                                按行数拆分
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <!-- 按列拆分选项 -->
                        <div id="columnSplitOptions">
                            <label class="form-label">选择拆分列</label>
                            <select class="form-select" id="splitColumn">
                                <option value="">请先上传文件</option>
                            </select>
                            <div class="form-text">根据选择的列的不同值拆分为多个文件</div>
                        </div>
                        
                        <!-- 按行数拆分选项 -->
                        <div id="rowsSplitOptions" class="d-none">
                            <label class="form-label">每个文件的行数</label>
                            <input type="number" class="form-control" id="rowsPerFile" value="1000" min="1">
                            <div class="form-text">指定每个拆分文件包含的数据行数</div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="splitExcel()" id="splitButton" disabled>
                            <i class="fas fa-cut"></i> 开始拆分
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="resetForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 拆分结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list"></i> 拆分结果
                </h6>
                <div id="resultActions" class="d-none">
                    <button type="button" class="btn btn-success btn-sm" onclick="downloadResults()">
                        <i class="fas fa-download"></i> 下载所有文件
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="splitProgress" class="d-none">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">拆分中...</span>
                        </div>
                        <p class="mt-2">正在拆分文件，请稍候...</p>
                    </div>
                </div>
                
                <div id="splitResults" class="d-none">
                    <div class="mb-3">
                        <span id="resultSummary" class="badge bg-success"></span>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>文件名</th>
                                    <th>拆分值/范围</th>
                                    <th>行数</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div id="noResults" class="text-center text-muted py-5">
                    <i class="fas fa-file-excel fa-3x mb-3"></i>
                    <p>上传文件并配置拆分选项后，点击"开始拆分"查看结果</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentFilePath = '';
let currentFileName = '';
let downloadUrl = '';

// 切换拆分选项
function toggleSplitOptions() {
    const splitType = document.querySelector('input[name="splitType"]:checked').value;
    
    if (splitType === 'column') {
        document.getElementById('columnSplitOptions').classList.remove('d-none');
        document.getElementById('rowsSplitOptions').classList.add('d-none');
    } else {
        document.getElementById('columnSplitOptions').classList.add('d-none');
        document.getElementById('rowsSplitOptions').classList.remove('d-none');
    }
}

// 上传文件
function uploadFile() {
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];
    
    if (!file) {
        return;
    }
    
    // 检查文件类型
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
        showUploadStatus('error', '不支持的文件格式，请选择 .xlsx 或 .xls 文件');
        return;
    }
    
    // 显示上传状态
    showUploadStatus('info', '正在上传文件...');
    
    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);
    
    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // 上传文件
    fetch('/excel/api/upload', {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentFilePath = data.file_path;
            currentFileName = data.filename;
            
            showUploadStatus('success', '文件上传成功');
            displayFileInfo(data);
            loadFileColumns();
            document.getElementById('splitButton').disabled = false;
        } else {
            showUploadStatus('error', '上传失败: ' + data.error);
        }
    })
    .catch(error => {
        showUploadStatus('error', '网络错误: ' + error.message);
    });
}

// 显示上传状态
function showUploadStatus(type, message) {
    const statusDiv = document.getElementById('uploadStatus');
    const messageSpan = document.getElementById('uploadMessage');
    
    statusDiv.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
    statusDiv.classList.remove('d-none');
    messageSpan.textContent = message;
}

// 显示文件信息
function displayFileInfo(data) {
    document.getElementById('fileName').textContent = data.filename;
    document.getElementById('fileSize').textContent = formatFileSize(data.file_size);
    document.getElementById('uploadTime').textContent = new Date().toLocaleString();
    document.getElementById('fileInfo').classList.remove('d-none');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 加载文件列信息
function loadFileColumns() {
    // 这里应该调用API获取Excel文件的列信息
    // 暂时使用示例数据
    const sampleColumns = ['科室名称', '医生姓名', '患者姓名', '费用类型', '金额', '日期'];
    
    const select = document.getElementById('splitColumn');
    select.innerHTML = '<option value="">请选择拆分列</option>';
    
    sampleColumns.forEach(column => {
        const option = document.createElement('option');
        option.value = column;
        option.textContent = column;
        select.appendChild(option);
    });
}

// 拆分Excel文件
function splitExcel() {
    if (!currentFilePath) {
        showAlert('请先上传文件', 'warning');
        return;
    }
    
    const splitType = document.querySelector('input[name="splitType"]:checked').value;
    let requestData = {
        file_path: currentFilePath,
        split_type: splitType
    };
    
    if (splitType === 'column') {
        const columnName = document.getElementById('splitColumn').value;
        if (!columnName) {
            showAlert('请选择拆分列', 'warning');
            return;
        }
        requestData.column_name = columnName;
    } else {
        const rowsPerFile = document.getElementById('rowsPerFile').value;
        if (!rowsPerFile || rowsPerFile <= 0) {
            showAlert('请输入有效的行数', 'warning');
            return;
        }
        requestData.rows_per_file = parseInt(rowsPerFile);
    }
    
    // 显示进度
    document.getElementById('splitProgress').classList.remove('d-none');
    document.getElementById('splitResults').classList.add('d-none');
    document.getElementById('noResults').classList.add('d-none');
    
    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // 发送拆分请求
    fetch('/excel/api/split', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('splitProgress').classList.add('d-none');
        
        if (data.success) {
            displaySplitResults(data);
            downloadUrl = data.download_url;
            showAlert('文件拆分成功', 'success');
        } else {
            showAlert('拆分失败: ' + data.error, 'danger');
            document.getElementById('noResults').classList.remove('d-none');
        }
    })
    .catch(error => {
        document.getElementById('splitProgress').classList.add('d-none');
        showAlert('网络错误: ' + error.message, 'danger');
        document.getElementById('noResults').classList.remove('d-none');
    });
}

// 显示拆分结果
function displaySplitResults(data) {
    const { results, total_files } = data;
    
    // 显示摘要
    document.getElementById('resultSummary').textContent = `成功拆分为 ${total_files} 个文件`;
    
    // 显示结果表格
    const tbody = document.getElementById('resultsTableBody');
    tbody.innerHTML = '';
    
    results.forEach((result, index) => {
        const row = document.createElement('tr');
        
        let splitValue = '';
        if (result.value) {
            splitValue = result.value;
        } else if (result.start_row && result.end_row) {
            splitValue = `第${result.start_row}-${result.end_row}行`;
        }
        
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${result.filename}</td>
            <td>${splitValue}</td>
            <td>${result.rows}</td>
            <td>
                <span class="badge bg-${result.status === 'success' ? 'success' : 'danger'}">
                    ${result.status === 'success' ? '成功' : '失败'}
                </span>
            </td>
        `;
        
        tbody.appendChild(row);
    });
    
    // 显示结果区域
    document.getElementById('splitResults').classList.remove('d-none');
    document.getElementById('resultActions').classList.remove('d-none');
}

// 下载结果文件
function downloadResults() {
    if (downloadUrl) {
        window.location.href = downloadUrl;
    } else {
        showAlert('没有可下载的文件', 'warning');
    }
}

// 重置表单
function resetForm() {
    // 重置文件输入
    document.getElementById('fileInput').value = '';
    
    // 重置变量
    currentFilePath = '';
    currentFileName = '';
    downloadUrl = '';
    
    // 隐藏信息区域
    document.getElementById('fileInfo').classList.add('d-none');
    document.getElementById('uploadStatus').classList.add('d-none');
    document.getElementById('splitResults').classList.add('d-none');
    document.getElementById('resultActions').classList.add('d-none');
    document.getElementById('noResults').classList.remove('d-none');
    
    // 重置选项
    document.getElementById('splitColumn').innerHTML = '<option value="">请先上传文件</option>';
    document.getElementById('rowsPerFile').value = '1000';
    document.getElementById('splitByColumn').checked = true;
    toggleSplitOptions();
    
    // 禁用拆分按钮
    document.getElementById('splitButton').disabled = true;
    
    showAlert('表单已重置', 'info');
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
