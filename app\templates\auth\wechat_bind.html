{% extends "base.html" %}

{% block title %}微信绑定 - MICRA飞检数据处理工具箱{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fab fa-weixin me-2"></i>
                        微信账号绑定
                    </h5>
                </div>
                <div class="card-body">
                    {% if current_user.is_wechat_bound() %}
                        <!-- 已绑定状态 -->
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            您已成功绑定微信账号
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3 text-center">
                                {% if current_user.wechat_avatar %}
                                    <img src="{{ current_user.wechat_avatar }}" 
                                         alt="微信头像" 
                                         class="rounded-circle" 
                                         style="width: 80px; height: 80px;">
                                {% else %}
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 80px; height: 80px;">
                                        <i class="fab fa-weixin fa-2x text-success"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-9">
                                <h6>微信信息</h6>
                                <p class="mb-1">
                                    <strong>昵称：</strong>
                                    {{ current_user.wechat_nickname or '未设置' }}
                                </p>
                                <p class="mb-1">
                                    <strong>性别：</strong>
                                    {% if current_user.wechat_sex == 1 %}
                                        男
                                    {% elif current_user.wechat_sex == 2 %}
                                        女
                                    {% else %}
                                        未知
                                    {% endif %}
                                </p>
                                <p class="mb-3">
                                    <strong>绑定时间：</strong>
                                    {{ current_user.updated_at.strftime('%Y-%m-%d %H:%M') if current_user.updated_at else '未知' }}
                                </p>
                                
                                <button type="button" 
                                        class="btn btn-outline-danger" 
                                        onclick="unbindWechat()">
                                    <i class="fas fa-unlink me-2"></i>
                                    解绑微信
                                </button>
                            </div>
                        </div>
                    {% else %}
                        <!-- 未绑定状态 -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            您尚未绑定微信账号，绑定后可以使用微信快速登录
                        </div>
                        
                        <div class="text-center">
                            <div class="mb-4">
                                <i class="fab fa-weixin fa-5x text-success"></i>
                            </div>
                            <h6 class="mb-3">绑定微信账号的好处：</h6>
                            <ul class="list-unstyled text-start">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    快速登录，无需输入密码
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    账号安全性更高
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    支持多种登录方式
                                </li>
                            </ul>
                            
                            <a href="{{ url_for('auth.wechat_bind') }}" 
                               class="btn btn-success btn-lg mt-3">
                                <i class="fab fa-weixin me-2"></i>
                                立即绑定微信
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function unbindWechat() {
    if (confirm('确定要解绑微信账号吗？解绑后将无法使用微信登录。')) {
        fetch('{{ url_for("auth.wechat_unbind") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('微信解绑成功！');
                location.reload();
            } else {
                alert('解绑失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('解绑失败，请稍后重试');
        });
    }
}
</script>
{% endblock %}
