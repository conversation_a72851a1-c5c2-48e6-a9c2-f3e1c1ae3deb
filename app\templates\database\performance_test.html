{% extends "base.html" %}

{% block title %}SQL性能测试 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-tachometer-alt"></i> SQL性能测试</h4>
{% endblock %}

{% block content %}
<!-- 测试配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 性能测试配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">测试模式</label>
                            <select class="form-select" id="testMode" onchange="toggleTestMode()">
                                <option value="single">单次执行测试</option>
                                <option value="multiple">多次执行测试</option>
                                <option value="concurrent">并发执行测试</option>
                                <option value="comparison">SQL对比测试</option>
                            </select>
                        </div>
                        
                        <div class="mb-3" id="executionCountGroup">
                            <label class="form-label">执行次数</label>
                            <input type="number" class="form-control" id="executionCount" value="5" min="1" max="100">
                            <div class="form-text">多次执行可以获得更准确的平均性能数据</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3" id="concurrentGroup" class="d-none">
                            <label class="form-label">并发线程数</label>
                            <input type="number" class="form-control" id="concurrentThreads" value="3" min="1" max="10">
                            <div class="form-text">同时执行的查询线程数量</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">超时时间（秒）</label>
                            <input type="number" class="form-control" id="timeoutSeconds" value="300" min="10" max="3600">
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableProfiling">
                            <label class="form-check-label" for="enableProfiling">
                                启用详细性能分析
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SQL输入区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-code"></i> SQL语句
                </h6>
                <div>
                    <button type="button" class="btn btn-success btn-sm" onclick="addSQLTest()">
                        <i class="fas fa-plus"></i> 添加SQL
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="loadSampleSQL()">
                        <i class="fas fa-file-code"></i> 加载示例
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="sqlTestsContainer">
                    <!-- SQL测试项将动态添加 -->
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="startPerformanceTest()">
                        <i class="fas fa-play"></i> 开始性能测试
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="validateSQL()">
                        <i class="fas fa-check"></i> 验证SQL
                    </button>
                    <button type="button" class="btn btn-warning ms-2" onclick="clearAllTests()">
                        <i class="fas fa-trash"></i> 清空所有
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 测试进度区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks"></i> 测试进度
                </h6>
            </div>
            <div class="card-body">
                <div id="testProgressContainer" class="d-none">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>测试进度</span>
                            <span id="testProgressText">0/0</span>
                        </div>
                        <div class="progress">
                            <div id="testProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div id="currentTestInfo" class="alert alert-info">
                        <strong>当前测试：</strong><span id="currentTestName">-</span><br>
                        <strong>执行次数：</strong><span id="currentExecution">-</span>
                    </div>
                </div>
                
                <div id="noTestProgress" class="text-center text-muted py-4">
                    <i class="fas fa-stopwatch fa-3x mb-3"></i>
                    <p>配置SQL后点击"开始性能测试"开始测试</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 测试结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i> 性能测试结果
                </h6>
                <div id="resultActions" class="d-none">
                    <button type="button" class="btn btn-success btn-sm" onclick="exportResults()">
                        <i class="fas fa-download"></i> 导出报告
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="showDetailedReport()">
                        <i class="fas fa-chart-bar"></i> 详细报告
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="testResultsContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <p>执行性能测试后结果将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SQL编辑模态框 -->
<div class="modal fade" id="sqlEditModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> 编辑SQL测试
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">测试名称</label>
                    <input type="text" class="form-control" id="editTestName" placeholder="输入测试名称">
                </div>
                <div class="mb-3">
                    <label class="form-label">SQL语句</label>
                    <textarea class="form-control" id="editTestSQL" rows="10" placeholder="输入SQL查询语句"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">测试描述</label>
                    <input type="text" class="form-control" id="editTestDesc" placeholder="测试描述（可选）">
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="editTestEnabled" checked>
                        <label class="form-check-label" for="editTestEnabled">
                            启用此测试
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveSQLTest()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 详细报告模态框 -->
<div class="modal fade" id="detailedReportModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar"></i> 详细性能报告
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="detailedReportContent">
                    <!-- 详细报告内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportDetailedReport()">导出详细报告</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let sqlTests = [];
let currentEditIndex = -1;
let testResults = [];

// 页面加载完成后初始化
$(document).ready(function() {
    addSQLTest(); // 默认添加一个测试
});

// 切换测试模式
function toggleTestMode() {
    const mode = $('#testMode').val();
    
    $('#executionCountGroup').toggleClass('d-none', mode === 'single');
    $('#concurrentGroup').toggleClass('d-none', mode !== 'concurrent');
}

// 添加SQL测试
function addSQLTest() {
    const testIndex = sqlTests.length;
    const testItem = {
        name: `性能测试${testIndex + 1}`,
        sql: '',
        description: '',
        enabled: true
    };
    
    sqlTests.push(testItem);
    
    const testHtml = `
        <div class="card mb-3" id="sqltest_${testIndex}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-flask"></i> ${testItem.name}
                    <span class="badge bg-success ms-2" id="status_${testIndex}">启用</span>
                </h6>
                <div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="editSQLTest(${testIndex})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="toggleSQLTest(${testIndex})">
                        <i class="fas fa-power-off"></i> 禁用
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="removeSQLTest(${testIndex})">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">SQL预览:</small>
                </div>
                <pre class="bg-light p-2 rounded" style="max-height: 120px; overflow-y: auto;">
                    <code id="sql_preview_${testIndex}">-- 请点击编辑按钮添加SQL语句</code>
                </pre>
            </div>
        </div>
    `;
    
    $('#sqlTestsContainer').append(testHtml);
}

// 编辑SQL测试
function editSQLTest(index) {
    currentEditIndex = index;
    const test = sqlTests[index];
    
    $('#editTestName').val(test.name);
    $('#editTestSQL').val(test.sql);
    $('#editTestDesc').val(test.description);
    $('#editTestEnabled').prop('checked', test.enabled);
    
    $('#sqlEditModal').modal('show');
}

// 保存SQL测试
function saveSQLTest() {
    if (currentEditIndex === -1) return;
    
    const test = sqlTests[currentEditIndex];
    test.name = $('#editTestName').val() || `性能测试${currentEditIndex + 1}`;
    test.sql = $('#editTestSQL').val();
    test.description = $('#editTestDesc').val();
    test.enabled = $('#editTestEnabled').prop('checked');
    
    // 更新显示
    $(`#sqltest_${currentEditIndex} h6`).html(`
        <i class="fas fa-flask"></i> ${test.name}
        <span class="badge bg-${test.enabled ? 'success' : 'secondary'} ms-2" id="status_${currentEditIndex}">
            ${test.enabled ? '启用' : '禁用'}
        </span>
    `);
    $(`#sql_preview_${currentEditIndex}`).text(test.sql || '-- 请添加SQL语句');
    
    // 更新按钮
    const toggleBtn = $(`#sqltest_${currentEditIndex} .btn-warning`);
    toggleBtn.html(`<i class="fas fa-power-off"></i> ${test.enabled ? '禁用' : '启用'}`);
    
    $('#sqlEditModal').modal('hide');
    showAlert('测试已保存', 'success');
}

// 切换SQL测试状态
function toggleSQLTest(index) {
    const test = sqlTests[index];
    test.enabled = !test.enabled;
    
    // 更新状态显示
    $(`#status_${index}`).removeClass('bg-success bg-secondary')
                         .addClass(test.enabled ? 'bg-success' : 'bg-secondary')
                         .text(test.enabled ? '启用' : '禁用');
    
    // 更新按钮
    const toggleBtn = $(`#sqltest_${index} .btn-warning`);
    toggleBtn.html(`<i class="fas fa-power-off"></i> ${test.enabled ? '禁用' : '启用'}`);
    
    showAlert(`测试已${test.enabled ? '启用' : '禁用'}`, 'info');
}

// 删除SQL测试
function removeSQLTest(index) {
    if (confirm('确定要删除这个测试吗？')) {
        sqlTests.splice(index, 1);
        refreshTestsDisplay();
        showAlert('测试已删除', 'info');
    }
}

// 刷新测试显示
function refreshTestsDisplay() {
    $('#sqlTestsContainer').empty();
    sqlTests.forEach((test, index) => {
        const testHtml = `
            <div class="card mb-3" id="sqltest_${index}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-flask"></i> ${test.name}
                        <span class="badge bg-${test.enabled ? 'success' : 'secondary'} ms-2" id="status_${index}">
                            ${test.enabled ? '启用' : '禁用'}
                        </span>
                    </h6>
                    <div>
                        <button type="button" class="btn btn-sm btn-primary" onclick="editSQLTest(${index})">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="toggleSQLTest(${index})">
                            <i class="fas fa-power-off"></i> ${test.enabled ? '禁用' : '启用'}
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeSQLTest(${index})">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">SQL预览:</small>
                    </div>
                    <pre class="bg-light p-2 rounded" style="max-height: 120px; overflow-y: auto;">
                        <code id="sql_preview_${index}">${test.sql || '-- 请点击编辑按钮添加SQL语句'}</code>
                    </pre>
                </div>
            </div>
        `;
        $('#sqlTestsContainer').append(testHtml);
    });
}

// 验证SQL
function validateSQL() {
    if (sqlTests.length === 0) {
        showAlert('请先添加测试', 'warning');
        return;
    }
    
    const enabledTests = sqlTests.filter(t => t.enabled);
    if (enabledTests.length === 0) {
        showAlert('没有启用的测试', 'warning');
        return;
    }
    
    let validCount = 0;
    let invalidTests = [];
    
    enabledTests.forEach(test => {
        if (test.sql && test.sql.trim()) {
            const sql = test.sql.trim().toUpperCase();
            if (sql.startsWith('SELECT')) {
                validCount++;
            } else {
                invalidTests.push(`${test.name}: 只允许SELECT查询`);
            }
        } else {
            invalidTests.push(`${test.name}: SQL语句为空`);
        }
    });
    
    if (invalidTests.length > 0) {
        showAlert(`验证失败：${invalidTests.join('; ')}`, 'danger');
    } else {
        showAlert(`验证通过：${validCount}个测试都有效`, 'success');
    }
}

// 开始性能测试
function startPerformanceTest() {
    const enabledTests = sqlTests.filter(t => t.enabled && t.sql && t.sql.trim());
    
    if (enabledTests.length === 0) {
        showAlert('没有有效的测试', 'warning');
        return;
    }
    
    const testMode = $('#testMode').val();
    const executionCount = parseInt($('#executionCount').val()) || 1;
    const concurrentThreads = parseInt($('#concurrentThreads').val()) || 1;
    const timeoutSeconds = parseInt($('#timeoutSeconds').val()) || 300;
    const enableProfiling = $('#enableProfiling').prop('checked');
    
    // 显示进度
    $('#testProgressContainer').removeClass('d-none');
    $('#noTestProgress').addClass('d-none');
    $('#testProgressBar').css('width', '0%');
    $('#testProgressText').text(`0/${enabledTests.length}`);
    
    const testData = {
        tests: enabledTests.map(t => ({
            name: t.name,
            sql: t.sql
        })),
        mode: testMode,
        execution_count: executionCount,
        concurrent_threads: concurrentThreads,
        timeout_seconds: timeoutSeconds,
        enable_profiling: enableProfiling
    };
    
    $.ajax({
        url: '/database/api/performance_test',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(testData),
        success: function(response) {
            if (response.success) {
                testResults = response.results;
                displayTestResults(response);
                showAlert('性能测试完成', 'success');
            } else {
                showAlert('性能测试失败: ' + response.error, 'danger');
            }
            
            $('#testProgressContainer').addClass('d-none');
            $('#noTestProgress').removeClass('d-none');
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'danger');
            $('#testProgressContainer').addClass('d-none');
            $('#noTestProgress').removeClass('d-none');
        }
    });
}

// 显示测试结果
function displayTestResults(response) {
    const { results, summary } = response;
    
    let resultsHtml = `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5>${summary.total_tests}</h5>
                        <p class="mb-0">总测试数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5>${summary.successful_tests}</h5>
                        <p class="mb-0">成功测试</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h5>${summary.avg_execution_time}ms</h5>
                        <p class="mb-0">平均执行时间</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h5>${summary.total_execution_time}ms</h5>
                        <p class="mb-0">总执行时间</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>测试名称</th>
                        <th>状态</th>
                        <th>执行次数</th>
                        <th>平均时间(ms)</th>
                        <th>最小时间(ms)</th>
                        <th>最大时间(ms)</th>
                        <th>结果行数</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    results.forEach(result => {
        const statusBadge = result.status === 'success' ? 
            '<span class="badge bg-success">成功</span>' : 
            '<span class="badge bg-danger">失败</span>';
        
        resultsHtml += `
            <tr>
                <td>${result.name}</td>
                <td>${statusBadge}</td>
                <td>${result.execution_count || 1}</td>
                <td>${result.avg_time || 0}</td>
                <td>${result.min_time || 0}</td>
                <td>${result.max_time || 0}</td>
                <td>${result.rows || 0}</td>
            </tr>
        `;
    });
    
    resultsHtml += `
                </tbody>
            </table>
        </div>
    `;
    
    $('#testResultsContainer').html(resultsHtml);
    $('#resultActions').removeClass('d-none');
}

// 其他功能函数
function clearAllTests() {
    if (confirm('确定要清空所有测试吗？')) {
        sqlTests = [];
        $('#sqlTestsContainer').empty();
        showAlert('所有测试已清空', 'info');
    }
}

function loadSampleSQL() {
    // 添加示例SQL
    const sampleTests = [
        {
            name: '基础查询测试',
            sql: 'SELECT COUNT(*) FROM 费用明细表 WHERE 就诊日期 >= \'2024-01-01\'',
            description: '测试基础计数查询性能',
            enabled: true
        },
        {
            name: '复杂查询测试',
            sql: 'SELECT 科室名称, COUNT(*) as 患者数, SUM(费用金额) as 总费用 FROM 费用明细表 WHERE 就诊日期 BETWEEN \'2024-01-01\' AND \'2024-12-31\' GROUP BY 科室名称 ORDER BY 总费用 DESC',
            description: '测试分组聚合查询性能',
            enabled: true
        }
    ];
    
    sqlTests = [...sqlTests, ...sampleTests];
    refreshTestsDisplay();
    showAlert('示例SQL已加载', 'success');
}

function exportResults() {
    if (testResults.length === 0) {
        showAlert('没有可导出的结果', 'warning');
        return;
    }
    showAlert('导出功能开发中...', 'info');
}

function showDetailedReport() {
    if (testResults.length === 0) {
        showAlert('没有可显示的详细报告', 'warning');
        return;
    }
    
    // 生成详细报告内容
    let reportHtml = '<h6>性能测试详细报告</h6>';
    // 这里可以添加更详细的图表和分析
    
    $('#detailedReportContent').html(reportHtml);
    $('#detailedReportModal').modal('show');
}

function exportDetailedReport() {
    showAlert('详细报告导出功能开发中...', 'info');
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
