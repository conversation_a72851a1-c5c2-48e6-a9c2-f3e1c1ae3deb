from functools import wraps
from flask import abort, redirect, url_for, request, flash
from flask_login import current_user
from app.models.audit import AuditLog

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

def permission_required(permission_code):
    """权限验证装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('auth.login'))
            
            if not current_user.has_permission(permission_code):
                flash('您没有权限访问此页面', 'error')
                abort(403)
            
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='access',
                resource=f.__name__,
                details=f'访问 {permission_code}'
            )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('需要管理员权限', 'error')
            abort(403)
        return f(*args, **kwargs)
    return decorated_function
