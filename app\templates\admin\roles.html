{% extends "base.html" %}

{% block title %}角色管理 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-user-tag"></i> 角色管理</h4>
{% endblock %}

{% block content %}
<!-- 操作工具栏 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-search"></i> 角色搜索
                        </h6>
                    </div>
                    <div class="col-md-6 text-end">
                        {% if current_user.has_permission('system.role.create') %}
                        <button type="button" class="btn btn-primary" onclick="showCreateRoleModal()">
                            <i class="fas fa-plus"></i> 新建角色
                        </button>
                        {% endif %}
                        <button type="button" class="btn btn-info" onclick="refreshRoleList()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="searchRoleName" placeholder="角色名称">
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="searchDescription" placeholder="角色描述">
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-primary" onclick="searchRoles()">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 角色列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list"></i> 角色列表
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="rolesTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>角色名称</th>
                                <th>角色描述</th>
                                <th>权限数量</th>
                                <th>用户数量</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="rolesTableBody">
                            <!-- 角色数据将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="角色列表分页">
                    <ul class="pagination justify-content-center" id="rolesPagination">
                        <!-- 分页将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 创建角色模态框 -->
<div class="modal fade" id="createRoleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-tag"></i> 创建新角色
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 选项卡导航 -->
                <ul class="nav nav-tabs" id="createRoleTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="create-basic-tab" data-bs-toggle="tab" data-bs-target="#create-basic" type="button" role="tab">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="create-permissions-tab" data-bs-toggle="tab" data-bs-target="#create-permissions" type="button" role="tab">
                            <i class="fas fa-key"></i> 权限分配
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="create-upload-tab" data-bs-toggle="tab" data-bs-target="#create-upload" type="button" role="tab">
                            <i class="fas fa-upload"></i> 上传配置
                        </button>
                    </li>
                </ul>

                <!-- 选项卡内容 -->
                <div class="tab-content mt-3" id="createRoleTabContent">
                    <!-- 基本信息选项卡 -->
                    <div class="tab-pane fade show active" id="create-basic" role="tabpanel">
                        <form id="createRoleForm">
                            <div class="mb-3">
                                <label for="createRoleName" class="form-label">角色名称 *</label>
                                <input type="text" class="form-control" id="createRoleName" required>
                            </div>
                            <div class="mb-3">
                                <label for="createRoleDescription" class="form-label">角色描述</label>
                                <textarea class="form-control" id="createRoleDescription" rows="3"></textarea>
                            </div>
                        </form>
                    </div>

                    <!-- 权限分配选项卡 -->
                    <div class="tab-pane fade" id="create-permissions" role="tabpanel">
                        <div class="mb-3">
                            <label class="form-label">权限分配</label>
                            <div id="createPermissionsList" class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                                <!-- 权限列表将通过AJAX加载 -->
                            </div>
                        </div>
                    </div>

                    <!-- 上传配置选项卡 -->
                    <div class="tab-pane fade" id="create-upload" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createUploadMaxSize" class="form-label">总大小限制 (MB)</label>
                                    <input type="number" class="form-control" id="createUploadMaxSize" value="350" min="-1">
                                    <div class="form-text">-1 表示无限制</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createUploadMaxCount" class="form-label">文件数量限制</label>
                                    <input type="number" class="form-control" id="createUploadMaxCount" value="100" min="-1">
                                    <div class="form-text">-1 表示无限制</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createUploadSingleFileMax" class="form-label">单文件大小限制 (MB)</label>
                                    <input type="number" class="form-control" id="createUploadSingleFileMax" value="10" min="-1">
                                    <div class="form-text">-1 表示无限制</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createUploadAllowedTypes" class="form-label">允许的文件类型</label>
                                    <input type="text" class="form-control" id="createUploadAllowedTypes" value="csv,dmp,dp,bak">
                                    <div class="form-text">用逗号分隔，如：csv,dmp,dp,bak</div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>配置说明：</strong>
                            <ul class="mb-0 mt-2">
                                <li>总大小限制：用户文件夹的总大小限制</li>
                                <li>文件数量限制：用户可上传的最大文件数量</li>
                                <li>单文件大小限制：单个文件的最大大小</li>
                                <li>文件类型：支持的文件扩展名，不区分大小写</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createRole()">创建角色</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑角色模态框 -->
<div class="modal fade" id="editRoleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> 编辑角色
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 选项卡导航 -->
                <ul class="nav nav-tabs" id="editRoleTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="edit-basic-tab" data-bs-toggle="tab" data-bs-target="#edit-basic" type="button" role="tab">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="edit-permissions-tab" data-bs-toggle="tab" data-bs-target="#edit-permissions" type="button" role="tab">
                            <i class="fas fa-key"></i> 权限分配
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="edit-upload-tab" data-bs-toggle="tab" data-bs-target="#edit-upload" type="button" role="tab">
                            <i class="fas fa-upload"></i> 上传配置
                        </button>
                    </li>
                </ul>

                <!-- 选项卡内容 -->
                <div class="tab-content mt-3" id="editRoleTabContent">
                    <!-- 基本信息选项卡 -->
                    <div class="tab-pane fade show active" id="edit-basic" role="tabpanel">
                        <form id="editRoleForm">
                            <input type="hidden" id="editRoleId">
                            <div class="mb-3">
                                <label for="editRoleName" class="form-label">角色名称 *</label>
                                <input type="text" class="form-control" id="editRoleName" required>
                            </div>
                            <div class="mb-3">
                                <label for="editRoleDescription" class="form-label">角色描述</label>
                                <textarea class="form-control" id="editRoleDescription" rows="3"></textarea>
                            </div>
                        </form>
                    </div>

                    <!-- 权限分配选项卡 -->
                    <div class="tab-pane fade" id="edit-permissions" role="tabpanel">
                        <div class="mb-3">
                            <label class="form-label">权限分配</label>
                            <div id="editPermissionsList" class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                                <!-- 权限列表将通过AJAX加载 -->
                            </div>
                        </div>
                    </div>

                    <!-- 上传配置选项卡 -->
                    <div class="tab-pane fade" id="edit-upload" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editUploadMaxSize" class="form-label">总大小限制 (MB)</label>
                                    <input type="number" class="form-control" id="editUploadMaxSize" value="350" min="-1">
                                    <div class="form-text">-1 表示无限制</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editUploadMaxCount" class="form-label">文件数量限制</label>
                                    <input type="number" class="form-control" id="editUploadMaxCount" value="100" min="-1">
                                    <div class="form-text">-1 表示无限制</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editUploadSingleFileMax" class="form-label">单文件大小限制 (MB)</label>
                                    <input type="number" class="form-control" id="editUploadSingleFileMax" value="10" min="-1">
                                    <div class="form-text">-1 表示无限制</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editUploadAllowedTypes" class="form-label">允许的文件类型</label>
                                    <input type="text" class="form-control" id="editUploadAllowedTypes" value="csv,dmp,dp,bak">
                                    <div class="form-text">用逗号分隔，如：csv,dmp,dp,bak</div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>配置说明：</strong>
                            <ul class="mb-0 mt-2">
                                <li>总大小限制：用户文件夹的总大小限制</li>
                                <li>文件数量限制：用户可上传的最大文件数量</li>
                                <li>单文件大小限制：单个文件的最大大小</li>
                                <li>文件类型：支持的文件扩展名，不区分大小写</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateRole()">更新角色</button>
            </div>
        </div>
    </div>
</div>

<!-- 角色详情模态框 -->
<div class="modal fade" id="roleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> 角色详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="roleDetailContent">
                <!-- 角色详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.permission-tree {
    font-size: 14px;
}

.permission-module {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

.module-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
}

.module-header:hover {
    background-color: #e9ecef !important;
}

.permission-item {
    border-left: 2px solid #e9ecef;
    padding-left: 0.5rem;
}

.permission-item:hover {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}

.permission-toggle, .module-toggle {
    border: none !important;
    color: #6c757d;
    font-size: 12px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.permission-toggle:hover, .module-toggle:hover {
    color: #495057;
    background-color: #e9ecef;
    border-radius: 50%;
}

.permission-name {
    font-weight: 500;
}

.form-check-input:indeterminate {
    background-color: #6c757d;
    border-color: #6c757d;
}

.badge {
    font-size: 0.7em;
}

/* 不同层级的权限项样式 */
.permission-item[style*="margin-left: 0px"] .permission-name {
    font-weight: 600;
    color: #495057;
}

.permission-item[style*="margin-left: 20px"] .permission-name {
    font-weight: 500;
    color: #6c757d;
}

.permission-item[style*="margin-left: 40px"] .permission-name {
    font-weight: 400;
    color: #868e96;
}

/* 添加连接线样式 */
.permission-item[style*="margin-left: 20px"]:before {
    content: '';
    position: absolute;
    left: 10px;
    top: 50%;
    width: 10px;
    height: 1px;
    background-color: #dee2e6;
}

.permission-item[style*="margin-left: 40px"]:before {
    content: '';
    position: absolute;
    left: 30px;
    top: 50%;
    width: 10px;
    height: 1px;
    background-color: #dee2e6;
}

/* 权限类型标签样式 */
.badge.bg-primary {
    background-color: #0d6efd !important;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
}

/* indeterminate状态样式 */
.form-check-input:indeterminate {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
</style>

<script>
let currentPage = 1;
let totalPages = 1;
let allPermissions = [];

// 页面加载完成后初始化
$(document).ready(function() {
    loadRoleList();
    loadPermissions();
});

// 加载角色列表
function loadRoleList(page = 1) {
    currentPage = page;
    
    const searchParams = {
        page: page,
        per_page: 10,
        name: $('#searchRoleName').val(),
        description: $('#searchDescription').val()
    };
    
    $.get('/admin/api/roles', searchParams)
        .done(function(data) {
            displayRoles(data.roles);
            updatePagination(data.current_page, data.pages, data.total);
        })
        .fail(function() {
            showAlert('加载角色列表失败', 'danger');
        });
}

// 显示角色列表
function displayRoles(roles) {
    const tbody = $('#rolesTableBody');
    tbody.empty();
    
    roles.forEach(role => {
        const row = `
            <tr>
                <td>${role.id}</td>
                <td>${role.name}</td>
                <td>${role.description || '-'}</td>
                <td><span class="badge bg-info">${role.permission_count || 0}</span></td>
                <td><span class="badge bg-secondary">${role.user_count || 0}</span></td>
                <td>${new Date(role.created_at).toLocaleString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="showRoleDetail(${role.id})" title="详情">
                            <i class="fas fa-info"></i>
                        </button>
                        {% if current_user.has_permission('system.role.edit') %}
                        <button class="btn btn-outline-primary" onclick="showEditRoleModal(${role.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        {% endif %}
                        {% if current_user.has_permission('system.role.delete') %}
                        <button class="btn btn-outline-danger" onclick="deleteRole(${role.id}, '${role.name}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                        {% endif %}
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 更新分页
function updatePagination(currentPage, totalPages, totalItems) {
    const pagination = $('#rolesPagination');
    pagination.empty();
    
    // 上一页
    const prevDisabled = currentPage === 1 ? 'disabled' : '';
    pagination.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="loadRoleList(${currentPage - 1})">上一页</a>
        </li>
    `);
    
    // 页码
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const active = i === currentPage ? 'active' : '';
        pagination.append(`
            <li class="page-item ${active}">
                <a class="page-link" href="#" onclick="loadRoleList(${i})">${i}</a>
            </li>
        `);
    }
    
    // 下一页
    const nextDisabled = currentPage === totalPages ? 'disabled' : '';
    pagination.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="loadRoleList(${currentPage + 1})">下一页</a>
        </li>
    `);
    
    // 显示总数信息
    pagination.append(`
        <li class="page-item disabled">
            <span class="page-link">共 ${totalItems} 条记录</span>
        </li>
    `);
}

// 加载权限列表
function loadPermissions() {
    return $.get('/admin/api/permissions')
        .done(function(data) {
            allPermissions = data.permissions;
            console.log('权限数据加载成功:', allPermissions.length, '个权限');
            renderPermissionsList('createPermissionsList', []);
        })
        .fail(function() {
            console.error('加载权限列表失败');
        });
}

// 渲染权限列表（树状结构）
function renderPermissionsList(containerId, selectedPermissions = []) {
    const container = $(`#${containerId}`);
    container.empty();

    console.log('渲染权限列表:', containerId, '权限数量:', allPermissions.length, '已选权限:', selectedPermissions);

    // 构建权限树
    const permissionTree = buildPermissionTree(allPermissions);
    console.log('权限树构建完成:', Object.keys(permissionTree));

    // 渲染权限树
    const treeHtml = renderPermissionTree(permissionTree, selectedPermissions, containerId);
    container.html(treeHtml);

    // 绑定事件
    bindPermissionTreeEvents(containerId);
}

// 构建权限树结构
function buildPermissionTree(permissions) {
    const tree = {};
    const permissionMap = {};

    // 创建权限映射表
    permissions.forEach(permission => {
        permissionMap[permission.id] = {
            ...permission,
            children: []
        };
    });

    // 按模块分组并建立层级关系
    permissions.forEach(permission => {
        const module = permission.module || 'other';

        // 初始化模块
        if (!tree[module]) {
            tree[module] = {
                name: getModuleName(module),
                code: module,
                permissions: []
            };
        }

        // 建立父子关系
        if (permission.parent_id && permissionMap[permission.parent_id]) {
            // 有父权限，添加到父权限的children中
            permissionMap[permission.parent_id].children.push(permissionMap[permission.id]);
        } else {
            // 顶级权限，添加到模块的permissions中
            tree[module].permissions.push(permissionMap[permission.id]);
        }
    });

    // 按sort_order排序
    Object.keys(tree).forEach(moduleKey => {
        tree[moduleKey].permissions.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
        sortPermissionChildren(tree[moduleKey].permissions);
    });

    return tree;
}

// 递归排序权限子项
function sortPermissionChildren(permissions) {
    permissions.forEach(permission => {
        if (permission.children && permission.children.length > 0) {
            permission.children.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
            sortPermissionChildren(permission.children);
        }
    });
}

// 渲染权限树
function renderPermissionTree(tree, selectedPermissions, containerId) {
    let html = '<div class="permission-tree">';

    Object.keys(tree).forEach(moduleKey => {
        const module = tree[moduleKey];
        const moduleId = `${containerId}_module_${moduleKey}`;

        html += `
            <div class="permission-module mb-3">
                <div class="module-header d-flex align-items-center p-2 bg-light rounded">
                    <button type="button" class="btn btn-sm btn-link p-0 me-2 module-toggle" data-target="${moduleId}">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="form-check me-2">
                        <input class="form-check-input module-checkbox" type="checkbox" id="${moduleId}_checkbox" data-module="${moduleKey}">
                        <label class="form-check-label fw-bold" for="${moduleId}_checkbox">
                            ${module.name}
                        </label>
                    </div>
                </div>
                <div class="module-content" id="${moduleId}" style="display: block;">
                    <div class="ps-4 pt-2">
                        ${renderPermissionItems(module.permissions, selectedPermissions, 0)}
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    return html;
}

// 渲染权限项目
function renderPermissionItems(permissions, selectedPermissions, level) {
    let html = '';

    permissions.forEach(permission => {
        const checked = selectedPermissions.includes(permission.id) ? 'checked' : '';
        const indent = level * 20;
        const hasChildren = permission.children && permission.children.length > 0;

        // 根据层级设置不同的样式
        let itemClass = 'permission-item mb-2';
        let badgeClass = 'badge ms-1';

        switch(level) {
            case 0: // 第二级：页面权限
                badgeClass += ' bg-primary';
                break;
            case 1: // 第三级：按钮权限
                badgeClass += ' bg-success';
                break;
            default:
                badgeClass += ' bg-info';
        }

        html += `
            <div class="${itemClass}" style="margin-left: ${indent}px;">
                <div class="d-flex align-items-center">
                    ${hasChildren ? `
                        <button type="button" class="btn btn-sm btn-link p-0 me-2 permission-toggle" data-target="perm_${permission.id}">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    ` : '<span class="me-4"></span>'}
                    <div class="form-check">
                        <input class="form-check-input permission-checkbox" type="checkbox" value="${permission.id}" ${checked}
                               data-parent="${permission.parent_id || ''}" data-level="${level}" data-code="${permission.code}">
                        <label class="form-check-label">
                            <span class="permission-name">${permission.name}</span>
                            ${permission.description && permission.description !== permission.name ? `<small class="text-muted ms-1">(${permission.description})</small>` : ''}
                            <span class="${badgeClass}">${getResourceTypeText(permission.resource_type)}</span>
                        </label>
                    </div>
                </div>
                ${hasChildren ? `
                    <div class="permission-children" id="perm_${permission.id}" style="display: block;">
                        ${renderPermissionItems(permission.children, selectedPermissions, level + 1)}
                    </div>
                ` : ''}
            </div>
        `;
    });

    return html;
}

// 绑定权限树事件
function bindPermissionTreeEvents(containerId) {
    const container = $(`#${containerId}`);

    // 模块折叠/展开
    container.on('click', '.module-toggle', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const target = $(this).data('target');
        const content = $(`#${target}`);
        const icon = $(this).find('i');

        console.log('模块折叠/展开:', target, content.length, content.is(':visible'));

        if (content.length === 0) {
            console.error('找不到目标元素:', target);
            return;
        }

        if (content.is(':visible')) {
            content.slideUp(200);
            icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
        } else {
            content.slideDown(200);
            icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
        }
    });

    // 权限项折叠/展开
    container.on('click', '.permission-toggle', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const target = $(this).data('target');
        const content = $(`#${target}`);
        const icon = $(this).find('i');

        console.log('权限项折叠/展开:', target, content.length, content.is(':visible'));

        if (content.length === 0) {
            console.error('找不到目标元素:', target);
            return;
        }

        if (content.is(':visible')) {
            content.slideUp(200);
            icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
        } else {
            content.slideDown(200);
            icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
        }
    });

    // 模块复选框变化
    container.on('change', '.module-checkbox', function() {
        const isChecked = $(this).is(':checked');
        const moduleKey = $(this).data('module');

        // 选中/取消选中该模块下的所有权限
        const moduleContent = $(this).closest('.permission-module').find('.permission-checkbox');
        moduleContent.prop('checked', isChecked);

        // 触发权限复选框变化事件
        moduleContent.trigger('change');
    });

    // 权限复选框变化
    container.on('change', '.permission-checkbox', function() {
        const isChecked = $(this).is(':checked');
        const permissionId = $(this).val();

        console.log('权限复选框变化:', permissionId, isChecked);

        // 如果选中，则选中所有子权限
        if (isChecked) {
            selectChildPermissions($(this), true);
        } else {
            selectChildPermissions($(this), false);
        }

        // 更新父权限状态
        updateParentPermissionStatus($(this));

        // 更新模块复选框状态
        updateModuleCheckboxStatus($(this));
    });
}

// 选中/取消选中子权限
function selectChildPermissions(checkbox, isChecked) {
    const permissionId = checkbox.val();
    const childrenContainer = $(`#perm_${permissionId}`);

    console.log('选择子权限:', permissionId, isChecked, '子权限容器:', childrenContainer.length);

    if (childrenContainer.length > 0) {
        const childCheckboxes = childrenContainer.find('.permission-checkbox');
        console.log('找到子权限数量:', childCheckboxes.length);

        childCheckboxes.each(function() {
            $(this).prop('checked', isChecked);
            // 递归选择孙权限
            selectChildPermissions($(this), isChecked);
        });
    }
}

// 更新父权限状态
function updateParentPermissionStatus(checkbox) {
    const parentId = checkbox.data('parent');
    if (parentId && parentId !== '') {
        const parentCheckbox = $(`.permission-checkbox[value="${parentId}"]`);
        if (parentCheckbox.length > 0) {
            // 查找所有直接子权限
            const childrenContainer = $(`#perm_${parentId}`);
            const directChildren = childrenContainer.find('> .permission-item > .d-flex > .form-check > .permission-checkbox');
            const checkedChildren = directChildren.filter(':checked');

            // 清除indeterminate状态
            parentCheckbox.prop('indeterminate', false);

            if (checkedChildren.length === directChildren.length && directChildren.length > 0) {
                // 所有子权限都选中，选中父权限
                parentCheckbox.prop('checked', true);
            } else if (checkedChildren.length === 0) {
                // 所有子权限都未选中，取消选中父权限
                parentCheckbox.prop('checked', false);
            } else {
                // 部分子权限选中，设置为indeterminate状态
                parentCheckbox.prop('checked', false);
                parentCheckbox.prop('indeterminate', true);
            }

            // 递归更新上级父权限
            updateParentPermissionStatus(parentCheckbox);
        }
    }
}

// 更新模块复选框状态
function updateModuleCheckboxStatus(checkbox) {
    const moduleCheckbox = checkbox.closest('.permission-module').find('.module-checkbox');
    const allPermissions = checkbox.closest('.permission-module').find('.permission-checkbox');
    const checkedPermissions = allPermissions.filter(':checked');

    if (checkedPermissions.length === allPermissions.length) {
        moduleCheckbox.prop('checked', true).prop('indeterminate', false);
    } else if (checkedPermissions.length === 0) {
        moduleCheckbox.prop('checked', false).prop('indeterminate', false);
    } else {
        moduleCheckbox.prop('checked', false).prop('indeterminate', true);
    }
}

// 获取模块名称
function getModuleName(module) {
    const moduleNames = {
        'system': '系统管理',
        'data': '数据处理',
        'excel': 'Excel工具',
        'database': '数据库工具',
        'rules': '规则管理',
        'selfcheck': '自查自纠'
    };
    return moduleNames[module] || module;
}

// 获取资源类型文本
function getResourceTypeText(resourceType) {
    const typeNames = {
        'menu': '菜单',
        'page': '页面',
        'button': '按钮',
        'api': 'API'
    };
    return typeNames[resourceType] || resourceType || '其他';
}

// 搜索角色
function searchRoles() {
    loadRoleList(1);
}

// 重置搜索
function resetSearch() {
    $('#searchRoleName').val('');
    $('#searchDescription').val('');
    loadRoleList(1);
}

// 刷新角色列表
function refreshRoleList() {
    loadRoleList(currentPage);
}

// 显示创建角色模态框
function showCreateRoleModal() {
    // 确保权限数据已加载
    if (allPermissions.length === 0) {
        loadPermissions().then(() => {
            showCreateRoleModal();
        });
        return;
    }

    $('#createRoleForm')[0].reset();
    renderPermissionsList('createPermissionsList', []);

    // 重置上传配置为默认值
    resetUploadConfig('create');

    // 重置选项卡到第一个
    $('#create-basic-tab').tab('show');

    $('#createRoleModal').modal('show');
}

// 创建角色
function createRole() {
    const selectedPermissions = [];
    $('#createPermissionsList input[type="checkbox"]:checked').each(function() {
        selectedPermissions.push(parseInt($(this).val()));
    });

    // 获取上传配置
    const uploadConfig = getUploadConfig('create');

    const roleData = {
        name: $('#createRoleName').val(),
        description: $('#createRoleDescription').val(),
        permission_ids: selectedPermissions,
        upload_config: uploadConfig
    };

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/admin/api/roles',
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(roleData),
        dataType: 'json'
    })
    .done(function(data) {
        if (data.success) {
            $('#createRoleModal').modal('hide');
            showAlert('角色创建成功', 'success');
            loadRoleList(currentPage);
        } else {
            showAlert('创建失败: ' + data.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

// 显示编辑角色模态框
function showEditRoleModal(roleId) {
    // 确保权限数据已加载
    if (allPermissions.length === 0) {
        loadPermissions().then(() => {
            showEditRoleModal(roleId);
        });
        return;
    }

    $.get(`/admin/api/roles/${roleId}`)
        .done(function(role) {
            $('#editRoleId').val(role.id);
            $('#editRoleName').val(role.name);
            $('#editRoleDescription').val(role.description);

            const selectedPermissions = role.permissions.map(p => p.id);
            renderPermissionsList('editPermissionsList', selectedPermissions);

            // 加载上传配置
            loadUploadConfig('edit', roleId);

            // 重置选项卡到第一个
            $('#edit-basic-tab').tab('show');

            $('#editRoleModal').modal('show');
        })
        .fail(function() {
            showAlert('加载角色信息失败', 'danger');
        });
}

// 更新角色
function updateRole() {
    const roleId = $('#editRoleId').val();
    const selectedPermissions = [];
    $('#editPermissionsList input[type="checkbox"]:checked').each(function() {
        selectedPermissions.push(parseInt($(this).val()));
    });

    // 获取上传配置
    const uploadConfig = getUploadConfig('edit');

    const roleData = {
        name: $('#editRoleName').val(),
        description: $('#editRoleDescription').val(),
        permission_ids: selectedPermissions,
        upload_config: uploadConfig
    };

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: `/admin/api/roles/${roleId}`,
        method: 'PUT',
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(roleData),
        dataType: 'json'
    })
    .done(function(data) {
        if (data.success) {
            $('#editRoleModal').modal('hide');
            showAlert('角色信息更新成功', 'success');
            loadRoleList(currentPage);
        } else {
            showAlert('更新失败: ' + data.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

// 显示角色详情
function showRoleDetail(roleId) {
    $.get(`/admin/api/roles/${roleId}`)
        .done(function(role) {
            const detailHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr><th width="30%">角色ID:</th><td>${role.id}</td></tr>
                            <tr><th>角色名称:</th><td>${role.name}</td></tr>
                            <tr><th>角色描述:</th><td>${role.description || '-'}</td></tr>
                            <tr><th>创建时间:</th><td>${new Date(role.created_at).toLocaleString()}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>拥有权限 (${role.permissions.length}个):</h6>
                        <div style="max-height: 200px; overflow-y: auto;">
                            ${role.permissions.map(p => `<span class="badge bg-secondary me-1 mb-1">${p.description || p.name}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `;
            $('#roleDetailContent').html(detailHtml);
            $('#roleDetailModal').modal('show');
        })
        .fail(function() {
            showAlert('加载角色详情失败', 'danger');
        });
}

// 删除角色
function deleteRole(roleId, roleName) {
    if (!confirm(`确定要删除角色 "${roleName}" 吗？此操作不可恢复！`)) {
        return;
    }
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: `/admin/api/roles/${roleId}`,
        method: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(data) {
        if (data.success) {
            showAlert('角色删除成功', 'success');
            loadRoleList(currentPage);
        } else {
            showAlert('删除失败: ' + data.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

// 重置上传配置为默认值
function resetUploadConfig(prefix) {
    $(`#${prefix}UploadMaxSize`).val(350);
    $(`#${prefix}UploadMaxCount`).val(100);
    $(`#${prefix}UploadSingleFileMax`).val(10);
    $(`#${prefix}UploadAllowedTypes`).val('csv,dmp,dp,bak');
}

// 获取上传配置
function getUploadConfig(prefix) {
    return {
        upload_max_size_mb: parseInt($(`#${prefix}UploadMaxSize`).val()) || 350,
        upload_max_file_count: parseInt($(`#${prefix}UploadMaxCount`).val()) || 100,
        upload_single_file_max_mb: parseInt($(`#${prefix}UploadSingleFileMax`).val()) || 10,
        upload_allowed_types: $(`#${prefix}UploadAllowedTypes`).val() || 'csv,dmp,dp,bak'
    };
}

// 设置上传配置
function setUploadConfig(prefix, config) {
    $(`#${prefix}UploadMaxSize`).val(config.upload_max_size_mb || 350);
    $(`#${prefix}UploadMaxCount`).val(config.upload_max_file_count || 100);
    $(`#${prefix}UploadSingleFileMax`).val(config.upload_single_file_max_mb || 10);
    $(`#${prefix}UploadAllowedTypes`).val(config.upload_allowed_types || 'csv,dmp,dp,bak');
}

// 加载角色的上传配置
function loadUploadConfig(prefix, roleId) {
    $.get(`/admin/api/roles/${roleId}/upload-config`)
        .done(function(data) {
            if (data.success) {
                setUploadConfig(prefix, data.config);
            } else {
                // 如果没有配置，使用默认值
                resetUploadConfig(prefix);
            }
        })
        .fail(function() {
            // 加载失败，使用默认值
            resetUploadConfig(prefix);
        });
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
