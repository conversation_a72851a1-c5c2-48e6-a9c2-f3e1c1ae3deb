#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能转换功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.rules.services import ConverterService
import json

def test_intelligent_conversion():
    """测试智能转换功能"""
    
    # 测试数据 - 模拟真实的规则内容
    test_cases = [
        {
            "name": "心电监护重复收费",
            "content": "心内科|1|开展心电监护时，重复收取动态血压监测费用|在进行心电监护的同时，不应重复收取动态血压监测费用|重复收费",
            "expected_violation": "重复收费",
            "expected_medical1": "心电监护",
            "expected_medical2": "血压监测"
        },
        {
            "name": "冠脉造影导管重复使用",
            "content": "心内科|2|冠脉造影导管重复收费|同一次冠脉造影手术中，对同一根导管重复收费|重复收费",
            "expected_violation": "重复收费",
            "expected_medical1": "导管",
            "expected_medical2": "冠脉造影"
        },
        {
            "name": "骨科钢板串换",
            "content": "骨科|3|钢板项目串换收费|将普通钢板串换为高价钢板进行收费|串换项目",
            "expected_violation": "串换项目",
            "expected_medical1": "钢板",
            "expected_medical2": ""
        },
        {
            "name": "复杂文本描述",
            "content": "在心脏介入手术过程中，医院对患者使用的冠脉造影导管和导丝进行了重复收费，违反了医保收费标准",
            "expected_violation": "重复收费",
            "expected_medical1": "导管",
            "expected_medical2": "导丝"
        },
        {
            "name": "您提供的实际案例",
            "content": "列1: 心血管内科|列2: 2|列3: 模拟转换的规则名称|列4: 适用|列5: 重复收费",
            "expected_violation": "重复收费",
            "expected_medical1": "",
            "expected_medical2": ""
        }
    ]
    
    print("=== 智能转换功能测试 ===\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试案例 {i}: {test_case['name']}")
        print(f"原始内容: {test_case['content']}")
        print("-" * 80)
        
        try:
            # 调用智能解析
            result = ConverterService._parse_rule_content(test_case['content'])
            
            print("转换结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 验证关键字段
            print("\n验证结果:")
            print(f"✓ 规则名称: {result['rule_name']}")
            print(f"✓ 规则内容: {result['rule_content']}")
            print(f"✓ 违规类型: {result['violation_type']} (期望: {test_case['expected_violation']})")
            print(f"✓ 医保项目1: {result['medical_name1']} (期望: {test_case['expected_medical1']})")
            print(f"✓ 医保项目2: {result['medical_name2']} (期望: {test_case['expected_medical2']})")
            print(f"✓ 涉及科室: {result['department']}")
            
            # 检查是否符合预期
            success = True
            if test_case['expected_violation'] not in result['violation_type']:
                print(f"❌ 违规类型不匹配")
                success = False
            
            if test_case['expected_medical1'] and test_case['expected_medical1'] not in result['medical_name1']:
                print(f"❌ 医保项目1不匹配")
                success = False
                
            if success:
                print("✅ 测试通过")
            else:
                print("❌ 测试失败")
                
        except Exception as e:
            print(f"❌ 转换失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("=" * 80)
        print()

def test_conversion_prompt():
    """测试转换提示词生成"""
    print("=== 转换提示词测试 ===\n")
    
    test_content = "心内科|1|开展心电监护时，重复收取动态血压监测费用|在进行心电监护的同时，不应重复收取动态血压监测费用|重复收费"
    test_cells = ["心内科", "1", "开展心电监护时，重复收取动态血压监测费用", "在进行心电监护的同时，不应重复收取动态血压监测费用", "重复收费"]
    
    prompt = ConverterService._build_conversion_prompt(test_content, test_cells, 'intelligent')
    
    print("生成的提示词:")
    print(prompt)
    print()

if __name__ == "__main__":
    test_conversion_prompt()
    test_intelligent_conversion()
