"""文件管理工具类"""

import os
import tempfile
import shutil
import hashlib
import threading
import atexit
import logging
from pathlib import Path
from typing import Optional, Set
from werkzeug.datastructures import FileStorage
from werkzeug.utils import secure_filename

# 全局变量用于跟踪临时文件
temp_files: Set[str] = set()
temp_files_lock = threading.Lock()

def cleanup_temp_files():
    """清理所有临时文件"""
    with temp_files_lock:
        for file_path in temp_files:
            try:
                if os.path.exists(file_path):
                    if os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                    else:
                        os.remove(file_path)
            except Exception as e:
                logging.error(f"Error cleaning up temp file {file_path}: {str(e)}")
        temp_files.clear()

# 注册退出时的清理函数
atexit.register(cleanup_temp_files)

def create_temp_dir() -> str:
    """创建临时目录并跟踪它"""
    temp_dir = tempfile.mkdtemp()
    with temp_files_lock:
        temp_files.add(temp_dir)
    return temp_dir

def create_temp_file(suffix: Optional[str] = None) -> str:
    """创建临时文件并跟踪它"""
    fd, temp_path = tempfile.mkstemp(suffix=suffix)
    os.close(fd)
    with temp_files_lock:
        temp_files.add(temp_path)
    return temp_path

def safe_file_path(base_dir: str, filename: str) -> str:
    """安全地构建文件路径，防止目录遍历攻击"""
    try:
        base_path = Path(base_dir).resolve()
        file_path = (base_path / filename).resolve()
        if base_path in file_path.parents:
            return str(file_path)
        raise ValueError("Attempted path traversal")
    except Exception as e:
        logging.error(f"Error in safe_file_path: {str(e)}")
        raise ValueError("Invalid file path")

def process_large_file(file_path: str, chunk_size: int = 8192):
    """以块的方式处理大文件"""
    try:
        with open(file_path, 'rb') as f:
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                yield chunk
    except Exception as e:
        logging.error(f"Error processing large file {file_path}: {str(e)}")
        raise

def calculate_file_hash(file_path: str) -> str:
    """计算文件的SHA-256哈希值"""
    sha256_hash = hashlib.sha256()
    try:
        for chunk in process_large_file(file_path):
            sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    except Exception as e:
        logging.error(f"Error calculating file hash for {file_path}: {str(e)}")
        raise

def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不安全字符"""
    # 移除或替换不安全的字符
    unsafe_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
    for char in unsafe_chars:
        filename = filename.replace(char, '_')
    
    # 限制文件名长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename

def allowed_file(filename: str, allowed_extensions: Optional[Set[str]] = None) -> bool:
    """检查文件扩展名是否被允许"""
    if allowed_extensions is None:
        allowed_extensions = {
            'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 
            'xls', 'xlsx', 'csv', 'sql', 'zip', 'rar', '7z'
        }
    
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

class FileManager:
    """文件管理器类"""
    
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir).resolve()
        os.makedirs(self.base_dir, exist_ok=True)

    def safe_save_file(self, file: FileStorage, filename: str) -> str:
        """安全地保存上传的文件"""
        try:
            safe_filename_str = secure_filename(filename)
            file_path = safe_file_path(str(self.base_dir), safe_filename_str)
            file.save(file_path)
            return file_path
        except Exception as e:
            logging.error(f"Error saving file {filename}: {str(e)}")
            raise

    def safe_read_file(self, filename: str) -> bytes:
        """安全地读取文件"""
        try:
            file_path = safe_file_path(str(self.base_dir), filename)
            return Path(file_path).read_bytes()
        except Exception as e:
            logging.error(f"Error reading file {filename}: {str(e)}")
            raise

    def safe_delete_file(self, filename: str) -> bool:
        """安全地删除文件"""
        try:
            file_path = safe_file_path(str(self.base_dir), filename)
            os.remove(file_path)
            return True
        except Exception as e:
            logging.error(f"Error deleting file {filename}: {str(e)}")
            return False

    def list_files(self, pattern: str = "*") -> list:
        """列出目录中的文件"""
        try:
            return list(self.base_dir.glob(pattern))
        except Exception as e:
            logging.error(f"Error listing files: {str(e)}")
            return []

    def get_file_info(self, filename: str) -> dict:
        """获取文件信息"""
        try:
            file_path = safe_file_path(str(self.base_dir), filename)
            path_obj = Path(file_path)
            
            if not path_obj.exists():
                return {}
            
            stat = path_obj.stat()
            return {
                'name': path_obj.name,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'is_file': path_obj.is_file(),
                'is_dir': path_obj.is_dir(),
                'extension': path_obj.suffix.lower()
            }
        except Exception as e:
            logging.error(f"Error getting file info for {filename}: {str(e)}")
            return {}

def split_and_save_to_excel(df, group_columns, output_dir, prefix="", suffix=""):
    """将DataFrame按指定列分组并保存为Excel文件"""
    output_files = []
    unique_values = df[group_columns].drop_duplicates()
    
    for _, row in unique_values.iterrows():
        subset = df
        for column in group_columns:
            subset = subset[subset[column] == row[column]]
        
        filename_parts = [str(row[col]) for col in group_columns]
        filename = '_'.join(filter(None, [prefix] + filename_parts + [suffix])) + '.xlsx'
        filename = sanitize_filename(filename)

        output_file = os.path.join(output_dir, filename)
        subset.to_excel(output_file, index=False)
        output_files.append((output_file, filename))
        logging.info(f"数据已保存至文件: {filename}")
    
    return output_files
