#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基础类和工具函数
"""

import unittest
import requests
import json
import time
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SelfCheckTestBase(unittest.TestCase):
    """自查自纠模块测试基础类"""
    
    def setUp(self):
        """测试前置设置"""
        self.base_url = "http://localhost:5002"
        self.session = requests.Session()
        self.test_user = {
            "username": "test_user",
            "password": "Test123!"
        }
        self.admin_user = {
            "username": "admin", 
            "password": "Admin123!"
        }
        
        # 登录获取session
        self.login_as_admin()
        
        # 测试数据清理标记
        self.created_resources = {
            'rules': [],
            'schemes': [],
            'uploads': [],
            'tasks': []
        }
    
    def tearDown(self):
        """测试后清理"""
        self.cleanup_test_data()
        self.session.close()
    
    def login_as_admin(self):
        """以管理员身份登录"""
        return self.login(self.admin_user)
    
    def login_as_test_user(self):
        """以测试用户身份登录"""
        return self.login(self.test_user)
    
    def login(self, user_credentials: Dict[str, str]) -> bool:
        """用户登录"""
        try:
            # 获取登录页面和CSRF token
            login_page = self.session.get(f"{self.base_url}/auth/login")
            self.assertEqual(login_page.status_code, 200)
            
            # 提取CSRF token（简化处理）
            csrf_token = self.extract_csrf_token(login_page.text)
            
            # 执行登录
            login_data = {
                'username': user_credentials['username'],
                'password': user_credentials['password'],
                'csrf_token': csrf_token
            }
            
            response = self.session.post(
                f"{self.base_url}/auth/login",
                data=login_data,
                allow_redirects=False
            )
            
            # 检查登录是否成功（重定向到首页）
            success = response.status_code in [302, 200]
            if success:
                logger.info(f"用户 {user_credentials['username']} 登录成功")
            else:
                logger.error(f"用户 {user_credentials['username']} 登录失败")
            
            return success
            
        except Exception as e:
            logger.error(f"登录过程出错: {e}")
            return False
    
    def extract_csrf_token(self, html_content: str) -> str:
        """从HTML中提取CSRF token"""
        # 简化的CSRF token提取
        import re
        pattern = r'name="csrf_token".*?value="([^"]+)"'
        match = re.search(pattern, html_content)
        return match.group(1) if match else ""
    
    def api_request(self, method: str, endpoint: str, data: Dict = None, 
                   files: Dict = None, params: Dict = None) -> requests.Response:
        """发送API请求"""
        url = f"{self.base_url}{endpoint}"
        
        # 获取CSRF token
        csrf_token = self.get_csrf_token()
        
        headers = {
            'X-CSRFToken': csrf_token,
            'Content-Type': 'application/json' if data and not files else None
        }
        
        # 移除None值的headers
        headers = {k: v for k, v in headers.items() if v is not None}
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, headers=headers)
            elif method.upper() == 'POST':
                if files:
                    # 文件上传请求
                    response = self.session.post(url, files=files, data=data, headers={'X-CSRFToken': csrf_token})
                else:
                    response = self.session.post(url, json=data, headers=headers)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data, headers=headers)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, json=data, headers=headers)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            logger.info(f"{method} {url} - Status: {response.status_code}")
            return response
            
        except Exception as e:
            logger.error(f"API请求失败: {method} {url} - {e}")
            raise
    
    def get_csrf_token(self) -> str:
        """获取CSRF token"""
        try:
            # 从任意页面获取CSRF token
            response = self.session.get(f"{self.base_url}/selfcheck/")
            if response.status_code == 200:
                return self.extract_csrf_token(response.text)
            return ""
        except:
            return ""
    
    def assert_api_success(self, response: requests.Response, message: str = ""):
        """断言API请求成功"""
        self.assertEqual(response.status_code, 200, f"API请求失败: {message}")
        
        try:
            data = response.json()
            self.assertTrue(data.get('success', False), f"API返回失败: {data.get('message', '')} - {message}")
            return data
        except json.JSONDecodeError:
            self.fail(f"API返回非JSON格式数据: {message}")
    
    def assert_api_error(self, response: requests.Response, expected_status: int = 400, message: str = ""):
        """断言API请求失败"""
        self.assertEqual(response.status_code, expected_status, f"期望状态码 {expected_status}: {message}")
        
        try:
            data = response.json()
            self.assertFalse(data.get('success', True), f"期望API返回失败: {message}")
            return data
        except json.JSONDecodeError:
            # 某些错误可能不返回JSON
            pass
    
    def wait_for_condition(self, condition_func, timeout: int = 30, interval: float = 1.0) -> bool:
        """等待条件满足"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            if condition_func():
                return True
            time.sleep(interval)
        return False
    
    def create_test_rule(self, rule_data: Dict = None) -> Dict[str, Any]:
        """创建测试规则"""
        if rule_data is None:
            timestamp = int(time.time())
            rule_data = {
                "rule_name": f"测试规则_{timestamp}",
                "rule_code": f"TEST_RULE_{timestamp}",
                "rule_description": "自动化测试创建的规则",
                "rule_type": "重复收费",
                "city": "北京",
                "sql_content": "SELECT * FROM test_table WHERE id > 0"
            }
        
        response = self.api_request('POST', '/selfcheck/api/rules', data=rule_data)
        result = self.assert_api_success(response, "创建测试规则失败")
        
        rule_id = result.get('rule', {}).get('id')
        if rule_id:
            self.created_resources['rules'].append(rule_id)
        
        return result
    
    def create_test_scheme(self, scheme_data: Dict = None) -> Dict[str, Any]:
        """创建测试方案"""
        if scheme_data is None:
            timestamp = int(time.time())
            scheme_data = {
                "scheme_name": f"测试方案_{timestamp}",
                "scheme_code": f"TEST_SCHEME_{timestamp}",
                "description": "自动化测试创建的方案"
            }
        
        response = self.api_request('POST', '/selfcheck/api/schemes', data=scheme_data)
        result = self.assert_api_success(response, "创建测试方案失败")
        
        scheme_id = result.get('scheme', {}).get('id')
        if scheme_id:
            self.created_resources['schemes'].append(scheme_id)
        
        return result
    
    def cleanup_test_data(self):
        """清理测试数据"""
        logger.info("开始清理测试数据...")
        
        # 清理任务
        for task_id in self.created_resources['tasks']:
            try:
                self.api_request('DELETE', f'/selfcheck/api/tasks/{task_id}')
            except:
                pass
        
        # 清理上传记录
        for upload_id in self.created_resources['uploads']:
            try:
                self.api_request('DELETE', f'/selfcheck/api/uploads/{upload_id}')
            except:
                pass
        
        # 清理方案
        for scheme_id in self.created_resources['schemes']:
            try:
                self.api_request('DELETE', f'/selfcheck/api/schemes/{scheme_id}')
            except:
                pass
        
        # 清理规则
        for rule_id in self.created_resources['rules']:
            try:
                self.api_request('DELETE', f'/selfcheck/api/rules/{rule_id}')
            except:
                pass
        
        logger.info("测试数据清理完成")
    
    def generate_test_data(self, data_type: str, count: int = 1) -> List[Dict]:
        """生成测试数据"""
        timestamp = int(time.time())
        data_list = []
        
        for i in range(count):
            if data_type == 'rule':
                data = {
                    "rule_name": f"测试规则_{timestamp}_{i}",
                    "rule_code": f"TEST_RULE_{timestamp}_{i}",
                    "rule_description": f"测试规则描述_{i}",
                    "rule_type": ["重复收费", "频次上限", "金额限制"][i % 3],
                    "city": ["北京", "上海", "广州"][i % 3],
                    "sql_content": f"SELECT * FROM test_table WHERE id = {i}"
                }
            elif data_type == 'scheme':
                data = {
                    "scheme_name": f"测试方案_{timestamp}_{i}",
                    "scheme_code": f"TEST_SCHEME_{timestamp}_{i}",
                    "description": f"测试方案描述_{i}"
                }
            else:
                data = {}
            
            data_list.append(data)
        
        return data_list


class PerformanceTestMixin:
    """性能测试混入类"""
    
    def measure_response_time(self, func, *args, **kwargs):
        """测量响应时间"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        response_time = end_time - start_time
        
        logger.info(f"响应时间: {response_time:.2f}秒")
        return result, response_time
    
    def assert_response_time(self, func, max_time: float, *args, **kwargs):
        """断言响应时间"""
        result, response_time = self.measure_response_time(func, *args, **kwargs)
        self.assertLessEqual(response_time, max_time, f"响应时间超过限制: {response_time:.2f}s > {max_time}s")
        return result


class DataValidationMixin:
    """数据验证混入类"""
    
    def assert_required_fields(self, data: Dict, required_fields: List[str]):
        """断言必填字段存在"""
        for field in required_fields:
            self.assertIn(field, data, f"缺少必填字段: {field}")
            self.assertIsNotNone(data[field], f"必填字段不能为空: {field}")
    
    def assert_data_types(self, data: Dict, type_mapping: Dict[str, type]):
        """断言数据类型正确"""
        for field, expected_type in type_mapping.items():
            if field in data:
                self.assertIsInstance(data[field], expected_type, f"字段 {field} 类型错误")
    
    def assert_pagination_data(self, data: Dict):
        """断言分页数据格式正确"""
        required_fields = ['page', 'pages', 'per_page', 'total']
        self.assert_required_fields(data, required_fields)
        
        type_mapping = {
            'page': int,
            'pages': int, 
            'per_page': int,
            'total': int
        }
        self.assert_data_types(data, type_mapping)
