#!/usr/bin/env python3
"""
直接查看权限数据库，找出重复问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def check_permissions():
    """检查权限数据"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.admin.models import Permission
            
            # 获取所有权限
            permissions = Permission.query.order_by(Permission.module, Permission.sort_order, Permission.id).all()
            
            print('=== 权限结构分析 ===')
            print(f'总权限数: {len(permissions)}')
            
            # 按模块分组
            modules = {}
            for perm in permissions:
                module = perm.module or 'other'
                if module not in modules:
                    modules[module] = []
                modules[module].append(perm)
            
            print(f'模块数: {len(modules)}')
            
            # 分析每个模块的权限结构
            for module, perms in modules.items():
                print(f'\n=== {module} 模块 ({len(perms)}个权限) ===')
                
                # 分析层级结构
                top_level = [p for p in perms if not p.parent_id]
                has_parent = [p for p in perms if p.parent_id]
                
                print(f'  顶级权限: {len(top_level)}个')
                print(f'  子权限: {len(has_parent)}个')
                
                # 显示所有顶级权限
                for p in top_level:
                    print(f'    - {p.name} (ID: {p.id}, 类型: {p.resource_type}, 代码: {p.code})')
            
            # 检查重复问题
            print('\n=== 检查重复问题 ===')
            
            # 1. 检查是否有相同名称的权限
            name_count = {}
            for perm in permissions:
                name = perm.name
                if name not in name_count:
                    name_count[name] = []
                name_count[name].append(perm)
            
            duplicates = {name: perms for name, perms in name_count.items() if len(perms) > 1}
            if duplicates:
                print('发现重复名称的权限:')
                for name, perms in duplicates.items():
                    print(f'  权限名称: "{name}" ({len(perms)}个)')
                    for p in perms:
                        print(f'    - ID: {p.id}, 模块: {p.module}, 类型: {p.resource_type}, 代码: {p.code}, 父权限: {p.parent_id}')
            else:
                print('没有发现重复名称的权限')
            
            # 2. 检查是否有模块级权限和页面级权限重复
            print('\n=== 检查模块级权限问题 ===')
            module_permissions = [p for p in permissions if p.resource_type == 'menu' and not p.parent_id]
            print(f'模块级权限: {len(module_permissions)}个')
            
            for p in module_permissions:
                print(f'  - {p.name} (模块: {p.module}, 代码: {p.code})')
                
                # 检查是否有同名的页面权限
                same_name_pages = [pp for pp in permissions if pp.name == p.name and pp.resource_type == 'page']
                if same_name_pages:
                    print(f'    ⚠️  发现同名页面权限: {len(same_name_pages)}个')
                    for sp in same_name_pages:
                        print(f'      - ID: {sp.id}, 代码: {sp.code}, 父权限: {sp.parent_id}')
            
            # 3. 分析具体的重复情况
            print('\n=== 具体重复分析 ===')
            
            # 查找"数据处理"相关的权限
            data_permissions = [p for p in permissions if '数据处理' in p.name]
            if data_permissions:
                print(f'包含"数据处理"的权限: {len(data_permissions)}个')
                for p in data_permissions:
                    parent_info = f', 父权限: {p.parent_id}' if p.parent_id else ', 顶级权限'
                    print(f'  - {p.name} (ID: {p.id}, 模块: {p.module}, 类型: {p.resource_type}, 代码: {p.code}{parent_info})')
            
            # 查找所有模块的顶级权限
            print('\n=== 所有模块的顶级权限 ===')
            all_top_level = [p for p in permissions if not p.parent_id]
            for p in all_top_level:
                print(f'  - {p.name} (ID: {p.id}, 模块: {p.module}, 类型: {p.resource_type}, 代码: {p.code})')
                
        except Exception as e:
            print(f"检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_permissions()
