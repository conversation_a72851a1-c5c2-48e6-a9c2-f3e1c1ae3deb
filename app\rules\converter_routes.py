#!/usr/bin/env python3
"""
智能转换工具路由 - 遵循项目标准
"""

import os
import logging
from flask import render_template, request, jsonify, send_file, flash, redirect, url_for
from flask_login import login_required, current_user
from app.rules import bp
from app.auth.decorators import permission_required
from app.models.audit import AuditLog
from app.rules.services import ConverterService
from app.utils.database import handle_db_error
from app.utils.breadcrumb import generate_breadcrumb

logger = logging.getLogger(__name__)


# ==================== 页面路由 ====================

@bp.route('/intelligent-converter')
@login_required
@permission_required('rules.converter.view')
def intelligent_converter():
    """智能转换工具页面"""
    try:
        breadcrumb = generate_breadcrumb([
            {'name': '首页', 'url': url_for('main.index')},
            {'name': '规则管理', 'url': url_for('rules.index')},
            {'name': '智能转换工具', 'url': None}
        ])
        
        return render_template('rules/intelligent_converter.html', 
                             breadcrumb=breadcrumb)
    except Exception as e:
        logger.error(f"加载智能转换工具页面失败: {str(e)}", exc_info=True)
        flash('页面加载失败', 'error')
        return redirect(url_for('rules.index'))


# ==================== API接口 ====================

@bp.route('/api/converter/upload', methods=['POST'])
@login_required
@permission_required('rules.converter.upload')
def api_upload_file():
    """上传文件API"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'})
        
        file = request.files['file']
        result = ConverterService.upload_file(file, current_user.id)
        
        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='upload',
                resource='converter_file',
                resource_id=str(result['upload_id']),
                details=f'上传文件: {file.filename}'
            )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"文件上传API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/parse/<int:upload_id>', methods=['POST'])
@login_required
@permission_required('rules.converter.parse')
def api_parse_file(upload_id):
    """解析文件API"""
    try:
        result = ConverterService.parse_file(upload_id)
        
        if result['success']:
            # 记录操作日志
            rows_count = result.get('rows_count', result.get('rules_count', 0))
            AuditLog.log_action(
                user_id=current_user.id,
                action='parse',
                resource='converter_file',
                resource_id=str(upload_id),
                details=f'解析文件，提取 {rows_count} 行数据'
            )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"文件解析API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/uploads', methods=['GET'])
@login_required
@permission_required('rules.converter.view')
def api_get_uploads():
    """获取上传列表API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        result = ConverterService.get_upload_list(current_user.id, page, per_page)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"获取上传列表API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/uploads/<int:upload_id>', methods=['GET'])
@login_required
@permission_required('rules.converter.view')
def api_get_upload(upload_id):
    """获取上传详情API"""
    try:
        # 这里可以添加获取单个上传记录的逻辑
        return jsonify({'success': True, 'upload': {'id': upload_id}})
        
    except Exception as e:
        logger.error(f"获取上传详情API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/rules/<int:upload_id>', methods=['GET'])
@login_required
@permission_required('rules.converter.view')
def api_get_converter_rules(upload_id):
    """获取规则列表API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        result = ConverterService.get_rules(upload_id, page, per_page)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"获取规则列表API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/rules/<int:rule_id>', methods=['PUT'])
@login_required
@permission_required('rules.converter.edit')
def api_update_converter_rule(rule_id):
    """更新规则API"""
    try:
        rule_data = request.get_json()
        result = ConverterService.update_rule(rule_id, rule_data)
        
        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='update',
                resource='converter_rule',
                resource_id=str(rule_id),
                details=f'更新规则: {rule_data.get("rule_name", "")}'
            )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"更新规则API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/rules/<int:rule_id>', methods=['DELETE'])
@login_required
@permission_required('rules.converter.delete')
def api_delete_converter_rule(rule_id):
    """删除规则API"""
    try:
        result = ConverterService.delete_rule(rule_id)
        
        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='delete',
                resource='converter_rule',
                resource_id=str(rule_id),
                details='删除转换规则'
            )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"删除规则API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/rules/<int:rule_id>/approve', methods=['POST'])
@login_required
@permission_required('rules.converter.approve')
def api_approve_converter_rule(rule_id):
    """确认规则API"""
    try:
        rule_data = {'status': 'approved'}
        result = ConverterService.update_rule(rule_id, rule_data)
        
        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='approve',
                resource='converter_rule',
                resource_id=str(rule_id),
                details='确认转换规则'
            )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"确认规则API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/rules/<int:rule_id>/reject', methods=['POST'])
@login_required
@permission_required('rules.converter.approve')
def api_reject_converter_rule(rule_id):
    """拒绝规则API"""
    try:
        rule_data = {'status': 'rejected'}
        result = ConverterService.update_rule(rule_id, rule_data)
        
        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='reject',
                resource='converter_rule',
                resource_id=str(rule_id),
                details='拒绝转换规则'
            )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"拒绝规则API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/rules/batch', methods=['POST'])
@login_required
@permission_required('rules.converter.edit')
def api_batch_update_rules():
    """批量更新规则API"""
    try:
        data = request.get_json()
        rule_ids = data.get('rule_ids', [])
        action = data.get('action')  # approve, reject, delete
        
        if not rule_ids or not action:
            return jsonify({'success': False, 'error': '参数不完整'})
        
        success_count = 0
        error_count = 0
        
        for rule_id in rule_ids:
            try:
                if action == 'approve':
                    result = ConverterService.update_rule(rule_id, {'status': 'approved'})
                elif action == 'reject':
                    result = ConverterService.update_rule(rule_id, {'status': 'rejected'})
                elif action == 'delete':
                    result = ConverterService.delete_rule(rule_id)
                else:
                    continue
                
                if result['success']:
                    success_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                logger.error(f"批量操作规则 {rule_id} 失败: {str(e)}")
                error_count += 1
        
        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action=f'batch_{action}',
            resource='converter_rule',
            resource_id=','.join(map(str, rule_ids)),
            details=f'批量{action}规则，成功{success_count}个，失败{error_count}个'
        )
        
        return jsonify({
            'success': True,
            'message': f'批量操作完成，成功{success_count}个，失败{error_count}个',
            'success_count': success_count,
            'error_count': error_count
        })
        
    except Exception as e:
        logger.error(f"批量更新规则API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/export/<int:upload_id>', methods=['GET'])
@login_required
@permission_required('rules.converter.export')
def api_export_rules(upload_id):
    """导出规则API"""
    try:
        result = ConverterService.export_rules(upload_id)
        
        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='export',
                resource='converter_rules',
                resource_id=str(upload_id),
                details=f'导出规则，共{result["rules_count"]}条'
            )
            
            return send_file(
                result['file_path'],
                as_attachment=True,
                download_name=result['filename'],
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"导出规则API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/converter/uploads/<int:upload_id>', methods=['DELETE'])
@login_required
@permission_required('rules.converter.delete')
def api_delete_upload(upload_id):
    """删除上传记录API"""
    try:
        result = ConverterService.delete_upload(upload_id)

        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='delete',
                resource='converter_upload',
                resource_id=str(upload_id),
                details='删除上传记录及相关规则'
            )

        return jsonify(result)

    except Exception as e:
        logger.error(f"删除上传记录API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/converter/table/<int:upload_id>', methods=['GET'])
@login_required
@permission_required('rules.converter.view')
def api_get_table_data(upload_id):
    """获取原始表格数据API"""
    try:
        result = ConverterService.get_raw_table_data(upload_id)

        if result['success']:
            # 转换为Markdown格式
            markdown_content = ConverterService.convert_table_to_markdown(result['table_data'])
            result['markdown'] = markdown_content

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取表格数据API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/converter/convert/<int:upload_id>', methods=['POST'])
@login_required
@permission_required('rules.converter.convert')
def api_start_conversion(upload_id):
    """启动智能转换API"""
    try:
        data = request.get_json() or {}
        start_row = data.get('start_row', 2)
        mode = data.get('mode', 'intelligent')

        result = ConverterService.start_intelligent_conversion(upload_id, start_row, mode)

        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='convert',
                resource='converter_file',
                resource_id=str(upload_id),
                details=f'启动智能转换，模式: {mode}，起始行: {start_row}'
            )

        return jsonify(result)

    except Exception as e:
        logger.error(f"启动智能转换API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/converter/conversion-progress/<int:upload_id>', methods=['GET'])
@login_required
@permission_required('rules.converter.view')
def api_get_conversion_progress(upload_id):
    """获取转换进度API"""
    try:
        result = ConverterService.get_conversion_progress(upload_id)
        return jsonify(result)

    except Exception as e:
        logger.error(f"获取转换进度API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/converter/conversion-results/<int:upload_id>', methods=['GET'])
@login_required
@permission_required('rules.converter.view')
def api_get_conversion_results(upload_id):
    """获取转换结果API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        result = ConverterService.get_conversion_results(upload_id, page, per_page)

        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='view_results',
                resource='converter_file',
                resource_id=str(upload_id),
                details=f'查看转换结果，第{page}页'
            )

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取转换结果API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/converter/conversion-comparison/<int:upload_id>/<int:row_number>', methods=['GET'])
@login_required
@permission_required('rules.converter.view')
def api_get_conversion_comparison(upload_id, row_number):
    """获取单行转换对比API"""
    try:
        result = ConverterService.get_conversion_comparison(upload_id, row_number)

        if result['success']:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='view_comparison',
                resource='converter_file',
                resource_id=str(upload_id),
                details=f'查看第{row_number}行转换对比'
            )

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取转换对比API失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


# ==================== 错误处理 ====================

@bp.errorhandler(413)
def file_too_large(error):
    """文件过大错误处理"""
    return jsonify({'success': False, 'error': '文件过大，请选择小于10MB的文件'}), 413
