#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission

def check_permissions_data():
    """检查权限相关数据"""
    app = create_app()

    with app.app_context():
        try:
            print('=== 权限表数据 ===')
            permissions = Permission.query.order_by(Permission.module, Permission.sort_order).all()
            for perm in permissions:
                print(f'ID: {perm.id}, 名称: {perm.name}, 代码: {perm.code}, 模块: {perm.module}, 类型: {perm.resource_type}, 激活: {perm.is_active}')

            print('\n=== 角色表数据 ===')
            roles = Role.query.all()
            for role in roles:
                print(f'ID: {role.id}, 名称: {role.name}, 描述: {role.description}, 激活: {role.is_active}')

            print('\n=== 用户角色关联 ===')
            users = User.query.filter_by(is_active=True).all()
            for user in users:
                if user.roles:
                    for role in user.roles:
                        print(f'用户ID: {user.id}, 用户名: {user.username}, 角色ID: {role.id}, 角色名: {role.name}')

            print('\n=== 角色权限关联 ===')
            roles = Role.query.all()
            for role in roles:
                if role.permissions:
                    for perm in role.permissions:
                        print(f'角色: {role.name} -> 权限: {perm.name} ({perm.code})')

            # 检查特定用户的权限
            print('\n=== 用户权限检查 ===')
            users = User.query.filter_by(is_active=True).all()
            for user in users:
                print(f'\n用户: {user.username} (ID: {user.id}, 管理员: {user.is_admin})')

                if user.roles:
                    for role in user.roles:
                        print(f'  角色: {role.name} (ID: {role.id}, 激活: {role.is_active})')

                        if role.permissions:
                            for perm in role.permissions:
                                print(f'    权限: {perm.name} ({perm.code}) - 激活: {perm.is_active}')
                        else:
                            print('    无权限分配')
                else:
                    print('  无角色分配')

                # 测试权限检查方法
                print(f'  权限检查测试:')
                test_permissions = ['selfcheck', 'selfcheck.schemes.view', 'system.user.view']
                for test_perm in test_permissions:
                    has_perm = user.has_permission(test_perm)
                    print(f'    {test_perm}: {has_perm}')

        except Exception as e:
            print(f"检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_permissions_data()
