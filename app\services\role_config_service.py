"""
角色配置管理服务
"""
from typing import Dict, List, Any, Optional
from app.models.role_config import RoleConfig
from app.selfcheck.database import execute_query


class RoleConfigService:
    """角色配置管理服务"""

    # 预定义的配置项
    UPLOAD_CONFIG_KEYS = {
        'upload_max_size_mb': {
            'type': 'number',
            'description': '上传文件总大小限制(MB)，-1表示无限制',
            'default': 350
        },
        'upload_max_file_count': {
            'type': 'number', 
            'description': '最大文件数量限制，-1表示无限制',
            'default': 100
        },
        'upload_allowed_types': {
            'type': 'string',
            'description': '允许上传的文件类型，用逗号分隔',
            'default': 'csv,dmp,dp,bak'
        },
        'upload_single_file_max_mb': {
            'type': 'number',
            'description': '单个文件最大大小(MB)',
            'default': 10
        }
    }

    @classmethod
    def get_role_upload_config(cls, role_id: int) -> Dict[str, Any]:
        """获取角色的上传配置"""
        config = {}
        for key, meta in cls.UPLOAD_CONFIG_KEYS.items():
            value = RoleConfig.get_role_config(role_id, key, meta['default'])
            config[key] = value
        return config

    @classmethod
    def set_role_upload_config(cls, role_id: int, config_data: Dict[str, Any]) -> bool:
        """设置角色的上传配置"""
        try:
            for key, value in config_data.items():
                if key in cls.UPLOAD_CONFIG_KEYS:
                    meta = cls.UPLOAD_CONFIG_KEYS[key]
                    RoleConfig.set_role_config(
                        role_id=role_id,
                        config_key=key,
                        value=value,
                        config_type=meta['type'],
                        description=meta['description']
                    )
            return True
        except Exception as e:
            print(f"设置角色配置失败: {str(e)}")
            return False

    @classmethod
    def get_user_upload_limits(cls, user) -> Dict[str, Any]:
        """获取用户的上传限制"""
        return RoleConfig.get_upload_limits_for_user(user)

    @classmethod
    def init_default_role_configs(cls):
        """初始化默认角色配置"""
        try:
            # 获取所有角色
            roles_query = "SELECT id, name FROM roles WHERE is_active = 1"
            roles = execute_query(roles_query)

            # 预定义角色配置
            role_configs = {
                '超级管理员': {
                    'upload_max_size_mb': -1,
                    'upload_max_file_count': -1,
                    'upload_allowed_types': 'csv,dmp,dp,bak,xlsx,xls',
                    'upload_single_file_max_mb': -1
                },
                '系统管理员': {
                    'upload_max_size_mb': -1,
                    'upload_max_file_count': -1,
                    'upload_allowed_types': 'csv,dmp,dp,bak,xlsx,xls',
                    'upload_single_file_max_mb': 50
                },
                '规则管理员': {
                    'upload_max_size_mb': 1000,  # 1GB
                    'upload_max_file_count': 500,
                    'upload_allowed_types': 'csv,dmp,dp,bak',
                    'upload_single_file_max_mb': 50
                },
                '数据分析师': {
                    'upload_max_size_mb': 2000,  # 2GB
                    'upload_max_file_count': 1000,
                    'upload_allowed_types': 'csv,dmp,dp,bak,xlsx,xls',
                    'upload_single_file_max_mb': 100
                },
                'VIP用户': {
                    'upload_max_size_mb': 5000,  # 5GB
                    'upload_max_file_count': 2000,
                    'upload_allowed_types': 'csv,dmp,dp,bak,xlsx,xls',
                    'upload_single_file_max_mb': 200
                },
                '普通用户': {
                    'upload_max_size_mb': 350,
                    'upload_max_file_count': 100,
                    'upload_allowed_types': 'csv,dmp,dp,bak',
                    'upload_single_file_max_mb': 10
                }
            }

            for role in roles:
                if role['name'] in role_configs:
                    config_data = role_configs[role['name']]
                    cls.set_role_upload_config(role['id'], config_data)
                    print(f"初始化角色配置: {role['name']}")
        except Exception as e:
            print(f"初始化角色配置失败: {str(e)}")

    @classmethod
    def get_all_roles_config(cls) -> List[Dict[str, Any]]:
        """获取所有角色的配置"""
        try:
            roles_query = "SELECT * FROM roles WHERE is_active = 1"
            roles = execute_query(roles_query)
            result = []

            for role in roles:
                role_data = dict(role)
                role_data['upload_config'] = cls.get_role_upload_config(role['id'])
                result.append(role_data)

            return result
        except Exception as e:
            print(f"获取角色配置列表失败: {str(e)}")
            return []

    @classmethod
    def validate_user_upload(cls, user, file_size: int, current_total_size: int = 0) -> tuple[bool, str]:
        """验证用户上传权限"""
        limits = cls.get_user_upload_limits(user)
        
        # 检查单文件大小限制
        single_file_limit = limits.get('upload_single_file_max_mb', 10)
        if single_file_limit != -1 and file_size > single_file_limit * 1024 * 1024:
            return False, f"单个文件大小超过限制({single_file_limit}MB)"
        
        # 检查总大小限制
        max_size_mb = limits.get('max_size_mb', 350)
        if max_size_mb != -1:
            max_size_bytes = max_size_mb * 1024 * 1024
            if current_total_size + file_size > max_size_bytes:
                return False, f"上传总大小将超过限制({max_size_mb}MB)"
        
        return True, "验证通过"

    @classmethod
    def get_user_current_usage(cls, user) -> Dict[str, Any]:
        """获取用户当前的使用情况"""
        try:
            from app.models.user import User
            from app.selfcheck.database import execute_query
            import os
            from flask import current_app

            # 直接计算用户文件夹使用情况，避免循环依赖
            base_upload_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), 'selfcheck')
            user_upload_dir = os.path.join(base_upload_dir, user.username)

            current_size_mb = 0
            file_count = 0

            if os.path.exists(user_upload_dir):
                for root, dirs, files in os.walk(user_upload_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if os.path.exists(file_path):
                            file_size = os.path.getsize(file_path)
                            current_size_mb += file_size / 1024 / 1024
                            file_count += 1

            limits = cls.get_user_upload_limits(user)

            usage = {
                'current_size_mb': current_size_mb,
                'max_size_mb': limits.get('max_size_mb', 350),
                'current_file_count': file_count,
                'max_file_count': limits.get('max_file_count', 100),
                'allowed_types': limits.get('allowed_types', ['csv', 'dmp', 'dp', 'bak']),
                'usage_percentage': 0
            }

            # 计算使用百分比
            if usage['max_size_mb'] != -1 and usage['max_size_mb'] > 0:
                usage['usage_percentage'] = (usage['current_size_mb'] / usage['max_size_mb']) * 100

            return usage
        except Exception as e:
            print(f"获取用户使用情况失败: {str(e)}")
            return {
                'current_size_mb': 0,
                'max_size_mb': 350,
                'current_file_count': 0,
                'max_file_count': 100,
                'allowed_types': ['csv', 'dmp', 'dp', 'bak'],
                'usage_percentage': 0
            }
