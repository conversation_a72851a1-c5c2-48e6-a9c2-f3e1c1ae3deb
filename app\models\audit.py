from flask import request
from datetime import datetime
from sqlalchemy import Sequence
from app import db

class AuditLog(db.Model):
    __tablename__ = 'audit_logs'

    id = db.Column(db.Integer, Sequence('audit_logs_id_seq'), primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'))
    action = db.Column(db.String(50), nullable=False)
    resource = db.Column(db.String(100), nullable=False)
    resource_id = db.Column(db.String(50))
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    user = db.relationship('User', back_populates='audit_logs')
    
    @classmethod
    def log_action(cls, user_id, action, resource, resource_id=None, details=None):
        """记录操作日志"""
        log = cls(
            user_id=user_id,
            action=action,
            resource=resource,
            resource_id=resource_id,
            details=details,
            ip_address=request.remote_addr if request else None,
            user_agent=request.headers.get('User-Agent') if request else None
        )
        db.session.add(log)
        db.session.commit()
        return log
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'action': self.action,
            'resource': self.resource,
            'resource_id': self.resource_id,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'timestamp': self.created_at.isoformat() if self.created_at else None
        }

    @staticmethod
    def log_action(user_id=None, action=None, resource=None, resource_id=None,
                   details=None, ip_address=None, user_agent=None):
        """记录审计日志的便捷方法"""
        from flask import request, current_app
        from flask_login import current_user

        try:
            # 自动获取当前用户
            if user_id is None and hasattr(current_user, 'id'):
                user_id = current_user.id

            # 自动获取IP地址
            if ip_address is None and request:
                ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)

            # 自动获取用户代理
            if user_agent is None and request:
                user_agent = request.headers.get('User-Agent', '')

            # 创建审计日志
            audit_log = AuditLog(
                user_id=user_id,
                action=action,
                resource=resource,
                resource_id=resource_id,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent
            )

            db.session.add(audit_log)
            db.session.commit()

            current_app.logger.info(f"审计日志记录: {action} - {resource} - {details}")
            return audit_log

        except Exception as e:
            current_app.logger.error(f"记录审计日志失败: {e}")
            # 不要因为审计日志失败而影响主要功能
            try:
                db.session.rollback()
            except:
                pass
            return None
