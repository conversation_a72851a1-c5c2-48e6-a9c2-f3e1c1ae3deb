{% extends "base.html" %}

{% block title %}Excel转SQL工具 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-code"></i> Excel转SQL工具</h4>
{% endblock %}

{% block content %}
<!-- 文件上传区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-upload"></i> 文件上传
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="fileInput" class="form-label">选择Excel文件</label>
                            <input type="file" class="form-control" id="fileInput" accept=".xlsx,.xls" onchange="uploadFile()">
                            <div class="form-text">支持 Excel (.xlsx, .xls) 格式文件</div>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Excel文件格式要求：</h6>
                            <ul class="mb-0">
                                <li><strong>规则名称</strong>（必需）- 用作SQL文件名和注释</li>
                                <li><strong>SQL</strong> 或 <strong>sql</strong>（必需）- SQL语句内容</li>
                                <li><strong>规则内涵</strong>（可选）- 规则说明，将作为注释</li>
                                <li><strong>政策依据</strong>（可选）- 政策依据，将作为注释</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mt-4">
                            <div id="uploadStatus" class="alert alert-info d-none">
                                <i class="fas fa-info-circle"></i>
                                <span id="uploadMessage">请选择文件</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件信息显示 -->
                <div id="fileInfo" class="mt-3 d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr><th width="30%">文件名:</th><td id="fileName"></td></tr>
                                <tr><th>文件大小:</th><td id="fileSize"></td></tr>
                                <tr><th>上传时间:</th><td id="uploadTime"></td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div id="filePreview">
                                <h6>检测到的列:</h6>
                                <div id="columnsList" class="border rounded p-2" style="max-height: 120px; overflow-y: auto;">
                                    <!-- 列信息将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 转换配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 转换配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">列映射配置</label>
                            <div class="mb-2">
                                <label for="nameColumn" class="form-label">规则名称列</label>
                                <select class="form-select" id="nameColumn">
                                    <option value="">请先上传文件</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label for="sqlColumn" class="form-label">SQL语句列</label>
                                <select class="form-select" id="sqlColumn">
                                    <option value="">请先上传文件</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label for="descColumn" class="form-label">规则内涵列（可选）</label>
                                <select class="form-select" id="descColumn">
                                    <option value="">无</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label for="policyColumn" class="form-label">政策依据列（可选）</label>
                                <select class="form-select" id="policyColumn">
                                    <option value="">无</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">生成选项</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeDescription" checked>
                                <label class="form-check-label" for="includeDescription">
                                    包含规则内涵注释
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includePolicy" checked>
                                <label class="form-check-label" for="includePolicy">
                                    包含政策依据注释
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="createSeparateFiles" checked>
                                <label class="form-check-label" for="createSeparateFiles">
                                    为每个规则生成独立SQL文件
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="createCombinedFile">
                                <label class="form-check-label" for="createCombinedFile">
                                    生成合并的SQL文件
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="zipOutput" checked>
                                <label class="form-check-label" for="zipOutput">
                                    打包为ZIP文件
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">SQL格式化选项</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="formatSql" checked>
                                <label class="form-check-label" for="formatSql">
                                    格式化SQL语句
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="addTimestamp" checked>
                                <label class="form-check-label" for="addTimestamp">
                                    添加生成时间戳
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="startConversion()" disabled id="convertBtn">
                            <i class="fas fa-code"></i> 开始转换
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="previewConversion()" disabled id="previewBtn">
                            <i class="fas fa-eye"></i> 预览转换
                        </button>
                        <button type="button" class="btn btn-info ms-2" onclick="resetForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 转换进度区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks"></i> 转换进度
                </h6>
            </div>
            <div class="card-body">
                <div id="conversionProgress" class="d-none">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>转换进度</span>
                            <span id="conversionStatus">转换中...</span>
                        </div>
                        <div class="progress">
                            <div id="conversionProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div id="conversionInfo" class="alert alert-info">
                        <strong>当前状态：</strong><span id="currentConversionStatus">正在转换...</span>
                    </div>
                </div>
                
                <div id="noConversionProgress" class="text-center text-muted py-4">
                    <i class="fas fa-code fa-3x mb-3"></i>
                    <p>上传Excel文件并配置转换选项后，点击"开始转换"生成SQL文件</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 转换结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list-alt"></i> 转换结果
                </h6>
                <div id="resultActions" class="d-none">
                    <button type="button" class="btn btn-success btn-sm" onclick="downloadResult()">
                        <i class="fas fa-download"></i> 下载SQL文件
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="showConversionLog()">
                        <i class="fas fa-list"></i> 转换日志
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="conversionResultsContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-file-code fa-3x mb-3"></i>
                        <p>转换完成后结果将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye"></i> SQL转换预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewModalContent">
                <!-- 预览内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="confirmConversion()">确认转换</button>
            </div>
        </div>
    </div>
</div>

<!-- 转换日志模态框 -->
<div class="modal fade" id="conversionLogModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list"></i> 转换日志
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="conversionLogContent">
                <!-- 转换日志内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportConversionLog()">导出日志</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentFilePath = '';
let conversionResults = null;

// 上传文件
function uploadFile() {
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];
    
    if (!file) return;
    
    // 检查文件类型
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
        showUploadStatus('error', '不支持的文件格式，请选择 Excel 文件');
        return;
    }
    
    showUploadStatus('info', '正在上传文件...');
    
    const formData = new FormData();
    formData.append('file', file);
    
    fetch('/excel/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentFilePath = data.file_path;
            showUploadStatus('success', '文件上传成功');
            displayFileInfo(data);
            loadFileColumns();
        } else {
            showUploadStatus('error', '上传失败: ' + data.error);
        }
    })
    .catch(error => {
        showUploadStatus('error', '网络错误: ' + error.message);
    });
}

// 显示上传状态
function showUploadStatus(type, message) {
    const statusDiv = document.getElementById('uploadStatus');
    const messageSpan = document.getElementById('uploadMessage');
    
    statusDiv.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
    statusDiv.classList.remove('d-none');
    messageSpan.textContent = message;
}

// 显示文件信息
function displayFileInfo(data) {
    document.getElementById('fileName').textContent = data.filename;
    document.getElementById('fileSize').textContent = formatFileSize(data.file_size);
    document.getElementById('uploadTime').textContent = new Date().toLocaleString();
    document.getElementById('fileInfo').classList.remove('d-none');
}

// 加载文件列信息
function loadFileColumns() {
    // 这里应该调用API获取文件列信息
    // 暂时使用示例数据
    const sampleColumns = ['规则名称', 'SQL', '规则内涵', '政策依据', '规则编号', '规则类型'];
    
    const columnsList = document.getElementById('columnsList');
    columnsList.innerHTML = '';
    
    // 更新所有下拉框
    const selects = ['nameColumn', 'sqlColumn', 'descColumn', 'policyColumn'];
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        const currentValue = select.value;
        
        if (selectId === 'descColumn' || selectId === 'policyColumn') {
            select.innerHTML = '<option value="">无</option>';
        } else {
            select.innerHTML = '<option value="">请选择列</option>';
        }
        
        sampleColumns.forEach(column => {
            const option = document.createElement('option');
            option.value = column;
            option.textContent = column;
            select.appendChild(option);
        });
        
        // 自动匹配常见列名
        if (selectId === 'nameColumn' && sampleColumns.includes('规则名称')) {
            select.value = '规则名称';
        } else if (selectId === 'sqlColumn' && sampleColumns.includes('SQL')) {
            select.value = 'SQL';
        } else if (selectId === 'descColumn' && sampleColumns.includes('规则内涵')) {
            select.value = '规则内涵';
        } else if (selectId === 'policyColumn' && sampleColumns.includes('政策依据')) {
            select.value = '政策依据';
        }
    });
    
    // 显示列信息
    sampleColumns.forEach(column => {
        const columnDiv = document.createElement('div');
        columnDiv.className = 'badge bg-secondary me-1 mb-1';
        columnDiv.textContent = column;
        columnsList.appendChild(columnDiv);
    });
    
    checkFormComplete();
}

// 检查表单是否完整
function checkFormComplete() {
    const nameColumn = document.getElementById('nameColumn').value;
    const sqlColumn = document.getElementById('sqlColumn').value;
    
    const isComplete = currentFilePath && nameColumn && sqlColumn;
    
    document.getElementById('convertBtn').disabled = !isComplete;
    document.getElementById('previewBtn').disabled = !isComplete;
}

// 预览转换
function previewConversion() {
    const config = getConversionConfig();
    
    // 这里应该调用API进行预览
    showAlert('预览功能开发中...', 'info');
}

// 开始转换
function startConversion() {
    const config = getConversionConfig();
    
    // 显示进度
    document.getElementById('conversionProgress').classList.remove('d-none');
    document.getElementById('noConversionProgress').classList.add('d-none');
    
    // 这里应该调用API执行转换
    showAlert('转换功能开发中...', 'info');
}

// 获取转换配置
function getConversionConfig() {
    return {
        file_path: currentFilePath,
        name_column: document.getElementById('nameColumn').value,
        sql_column: document.getElementById('sqlColumn').value,
        desc_column: document.getElementById('descColumn').value,
        policy_column: document.getElementById('policyColumn').value,
        include_description: document.getElementById('includeDescription').checked,
        include_policy: document.getElementById('includePolicy').checked,
        create_separate_files: document.getElementById('createSeparateFiles').checked,
        create_combined_file: document.getElementById('createCombinedFile').checked,
        zip_output: document.getElementById('zipOutput').checked,
        format_sql: document.getElementById('formatSql').checked,
        add_timestamp: document.getElementById('addTimestamp').checked
    };
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 重置表单
function resetForm() {
    currentFilePath = '';
    
    document.getElementById('fileInput').value = '';
    document.getElementById('fileInfo').classList.add('d-none');
    
    const selects = ['nameColumn', 'sqlColumn', 'descColumn', 'policyColumn'];
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (selectId === 'descColumn' || selectId === 'policyColumn') {
            select.innerHTML = '<option value="">无</option>';
        } else {
            select.innerHTML = '<option value="">请先上传文件</option>';
        }
    });
    
    // 重置复选框
    document.getElementById('includeDescription').checked = true;
    document.getElementById('includePolicy').checked = true;
    document.getElementById('createSeparateFiles').checked = true;
    document.getElementById('createCombinedFile').checked = false;
    document.getElementById('zipOutput').checked = true;
    document.getElementById('formatSql').checked = true;
    document.getElementById('addTimestamp').checked = true;
    
    checkFormComplete();
    
    showAlert('表单已重置', 'info');
}

// 下载结果
function downloadResult() {
    if (!conversionResults) {
        showAlert('没有可下载的结果', 'warning');
        return;
    }
    
    showAlert('下载功能开发中...', 'info');
}

// 显示转换日志
function showConversionLog() {
    document.getElementById('conversionLogModal').querySelector('.modal').modal('show');
}

// 导出转换日志
function exportConversionLog() {
    showAlert('日志导出功能开发中...', 'info');
}

// 确认转换
function confirmConversion() {
    $('#previewModal').modal('hide');
    startConversion();
}

// 使用base.html中的统一showAlert函数

// 页面加载完成后绑定事件
$(document).ready(function() {
    // 绑定列选择变化事件
    $('#nameColumn, #sqlColumn').change(checkFormComplete);
});
</script>
{% endblock %}
