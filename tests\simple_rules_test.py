#!/usr/bin/env python3
"""
规则管理模块简化测试脚本
直接测试API端点，不依赖登录
"""

import requests
import json
import time
from datetime import datetime

class SimpleRulesTest:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def test_endpoint(self, name, url, method="GET", data=None, expected_status=200):
        """测试单个端点"""
        print(f"🧪 测试: {name}")
        start_time = time.time()
        
        try:
            if method == "GET":
                response = self.session.get(url, timeout=10)
            elif method == "POST":
                response = self.session.post(url, json=data, timeout=10)
            else:
                response = self.session.request(method, url, json=data, timeout=10)
            
            duration = time.time() - start_time
            
            # 检查状态码
            if response.status_code == expected_status:
                print(f"✅ {name} - 通过 ({duration:.2f}s)")
                self.test_results.append({
                    'name': name,
                    'status': 'PASS',
                    'duration': duration,
                    'status_code': response.status_code,
                    'error': None
                })
                return True
            else:
                print(f"❌ {name} - 状态码错误: {response.status_code} (期望: {expected_status})")
                self.test_results.append({
                    'name': name,
                    'status': 'FAIL',
                    'duration': duration,
                    'status_code': response.status_code,
                    'error': f'状态码错误: {response.status_code}'
                })
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {name} - 异常: {str(e)}")
            self.test_results.append({
                'name': name,
                'status': 'ERROR',
                'duration': duration,
                'status_code': None,
                'error': str(e)
            })
            return False
    
    def test_page_access(self, name, path, expected_content=None):
        """测试页面访问"""
        print(f"🌐 测试页面: {name}")
        start_time = time.time()
        
        try:
            response = self.session.get(f"{self.base_url}{path}", timeout=10)
            duration = time.time() - start_time
            
            # 检查状态码和内容
            success = response.status_code == 200
            if expected_content and success:
                success = expected_content in response.text
            
            if success:
                print(f"✅ {name} - 通过 ({duration:.2f}s)")
                self.test_results.append({
                    'name': name,
                    'status': 'PASS',
                    'duration': duration,
                    'status_code': response.status_code,
                    'error': None
                })
                return True
            else:
                error_msg = f"状态码: {response.status_code}"
                if expected_content and expected_content not in response.text:
                    error_msg += f", 缺少内容: {expected_content}"
                
                print(f"❌ {name} - 失败: {error_msg}")
                self.test_results.append({
                    'name': name,
                    'status': 'FAIL',
                    'duration': duration,
                    'status_code': response.status_code,
                    'error': error_msg
                })
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {name} - 异常: {str(e)}")
            self.test_results.append({
                'name': name,
                'status': 'ERROR',
                'duration': duration,
                'status_code': None,
                'error': str(e)
            })
            return False
    
    def run_basic_tests(self):
        """运行基础测试"""
        print("🚀 开始规则管理模块基础测试")
        print("=" * 60)
        
        # 测试应用基础连接
        print("\n📡 测试应用连接...")
        self.test_endpoint("应用根路径访问", f"{self.base_url}/")
        
        # 测试规则管理页面（可能需要登录，但我们测试是否能访问）
        print("\n📄 测试页面访问...")
        self.test_page_access("规则管理首页", "/rules/")
        self.test_page_access("飞检规则知识库", "/rules/knowledge_base")
        self.test_page_access("SQL生成器", "/rules/rule_sql_generator")
        self.test_page_access("系统规则", "/rules/system_rules")
        
        # 测试API端点（可能返回401未授权，但不应该是500错误）
        print("\n🔌 测试API端点...")
        self.test_endpoint("规则搜索API", f"{self.base_url}/rules/api/search", expected_status=[200, 401, 302])
        self.test_endpoint("规则列表API", f"{self.base_url}/rules/api/rules", expected_status=[200, 401, 302])
        
        # 测试数据库相关API
        print("\n💾 测试数据库相关功能...")
        self.test_endpoint("数据库查询页面", f"{self.base_url}/database/query", expected_status=[200, 401, 302])
        
        # 生成报告
        self.generate_simple_report()
    
    def test_endpoint_with_multiple_status(self, name, url, expected_statuses):
        """测试端点，允许多个状态码"""
        print(f"🧪 测试: {name}")
        start_time = time.time()
        
        try:
            response = self.session.get(url, timeout=10)
            duration = time.time() - start_time
            
            if response.status_code in expected_statuses:
                print(f"✅ {name} - 通过 ({duration:.2f}s) [状态码: {response.status_code}]")
                self.test_results.append({
                    'name': name,
                    'status': 'PASS',
                    'duration': duration,
                    'status_code': response.status_code,
                    'error': None
                })
                return True
            else:
                print(f"❌ {name} - 状态码错误: {response.status_code} (期望: {expected_statuses})")
                self.test_results.append({
                    'name': name,
                    'status': 'FAIL',
                    'duration': duration,
                    'status_code': response.status_code,
                    'error': f'状态码错误: {response.status_code}'
                })
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {name} - 异常: {str(e)}")
            self.test_results.append({
                'name': name,
                'status': 'ERROR',
                'duration': duration,
                'status_code': None,
                'error': str(e)
            })
            return False
    
    def run_comprehensive_tests(self):
        """运行全面测试"""
        print("🚀 开始规则管理模块全面测试")
        print("=" * 60)
        
        # 基础连接测试
        print("\n📡 测试应用连接...")
        self.test_endpoint("应用根路径访问", f"{self.base_url}/")
        
        # 页面访问测试
        print("\n📄 测试页面访问...")
        pages = [
            ("规则管理首页", "/rules/"),
            ("飞检规则知识库", "/rules/knowledge_base"),
            ("SQL生成器", "/rules/sql_generator"),
            ("系统规则", "/rules/system_rules"),
            ("登录页面", "/auth/login"),
            ("数据库查询", "/database/query")
        ]
        
        for name, path in pages:
            self.test_endpoint_with_multiple_status(name, f"{self.base_url}{path}", [200, 302, 401])
        
        # API端点测试
        print("\n🔌 测试API端点...")
        apis = [
            ("规则搜索API", "/rules/api/search"),
            ("规则列表API", "/rules/api/rules"),
            ("数据库Schema API", "/database/api/database_schemas"),
            ("数据库执行API", "/database/api/execute_query")
        ]
        
        for name, path in apis:
            self.test_endpoint_with_multiple_status(name, f"{self.base_url}{path}", [200, 302, 401, 405])
        
        # 静态资源测试
        print("\n🎨 测试静态资源...")
        static_resources = [
            ("Bootstrap CSS", "/static/css/bootstrap.min.css"),
            ("jQuery JS", "/static/js/jquery.min.js"),
            ("应用样式", "/static/css/app.css")
        ]
        
        for name, path in static_resources:
            self.test_endpoint_with_multiple_status(name, f"{self.base_url}{path}", [200, 404])
        
        # 生成报告
        self.generate_simple_report()
    
    def generate_simple_report(self):
        """生成简单测试报告"""
        print("\n" + "=" * 60)
        print("📋 测试报告")
        print("=" * 60)
        
        total = len(self.test_results)
        passed = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed = len([r for r in self.test_results if r['status'] == 'FAIL'])
        errors = len([r for r in self.test_results if r['status'] == 'ERROR'])
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        print(f"错误: {errors}")
        if total > 0:
            print(f"通过率: {(passed/total*100):.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASS' else "❌"
            status_code = f" [{result['status_code']}]" if result['status_code'] else ""
            print(f"{status_icon} {result['name']}{status_code} - {result['status']} ({result['duration']:.2f}s)")
            if result['error']:
                print(f"   错误: {result['error']}")
        
        # 保存报告
        report_file = f"simple_rules_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total': total,
                    'passed': passed,
                    'failed': failed,
                    'errors': errors,
                    'pass_rate': passed/total*100 if total > 0 else 0
                },
                'results': self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

if __name__ == '__main__':
    tester = SimpleRulesTest()
    tester.run_comprehensive_tests()
