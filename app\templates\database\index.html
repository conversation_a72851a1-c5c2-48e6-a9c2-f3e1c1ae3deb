{% extends "base.html" %}

{% block title %}数据库工具 - MICRA飞检数据处理工具箱{% endblock %}



{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        数据库工具模块
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- SQL生成器 -->
                        {% if current_user.has_permission('database.sql_generator') %}
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-database fa-3x text-primary"></i>
                                    </div>
                                    <h6 class="card-title">SQL生成器</h6>
                                    <p class="card-text small">智能SQL语句生成工具</p>
                                    <a href="{{ url_for('database.sql_generator') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>进入
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 数据库查询 -->
                        {% if current_user.has_permission('database.query') %}
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-table fa-3x text-success"></i>
                                    </div>
                                    <h6 class="card-title">数据库查询</h6>
                                    <p class="card-text small">查询数据并导出Excel</p>
                                    <a href="{{ url_for('database.query') }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>进入
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 批量查询 -->
                        {% if current_user.has_permission('database.batch_query') %}
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-tasks fa-3x text-warning"></i>
                                    </div>
                                    <h6 class="card-title">批量查询</h6>
                                    <p class="card-text small">批量SQL查询处理</p>
                                    <a href="{{ url_for('database.batch_query') }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>进入
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 性能测试 -->
                        {% if current_user.has_permission('database.performance_test') %}
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-tachometer-alt fa-3x text-info"></i>
                                    </div>
                                    <h6 class="card-title">性能测试</h6>
                                    <p class="card-text small">SQL性能测试分析</p>
                                    <a href="{{ url_for('database.performance_test') }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-arrow-right me-1"></i>进入
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- 功能说明 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>
                                        功能说明
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <h6 class="text-primary">SQL生成器</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 智能SQL语句生成</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 支持多种查询模板</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 语法检查和优化建议</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-success">数据库查询</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 在线SQL查询执行</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 结果实时预览</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 一键导出Excel文件</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-warning">批量查询</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 批量SQL脚本执行</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 任务队列管理</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 执行进度监控</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-info">性能测试</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> SQL执行性能分析</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 执行计划查看</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 性能优化建议</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
