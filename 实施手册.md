# MICRA自查自纠模块实施手册

**文档版本：** V1.0  
**编写日期：** 2025年1月7日  
**项目名称：** MICRA工具箱 - 自查自纠模块  
**文档类型：** 实施手册  

## 1. 实施概述

### 1.1 实施目标
本手册指导MICRA自查自纠模块在医疗机构的完整实施过程，包括需求调研、系统配置、数据迁移、用户培训和上线运行，确保系统成功落地并发挥预期效果。

### 1.2 实施范围
- **功能范围：** 数据上传、规则管理、方案配置、任务执行、结果分析
- **用户范围：** 医疗机构数据管理人员、质控人员、系统管理员
- **数据范围：** 医疗保险数据、检查规则、历史记录

### 1.3 实施原则
- **分阶段实施：** 按模块逐步上线，降低风险
- **用户参与：** 全程用户参与，确保需求满足
- **数据安全：** 严格保护数据安全和隐私
- **可回退性：** 每个阶段都有回退方案

## 2. 实施计划

### 2.1 实施阶段划分

#### 阶段一：准备阶段（1-2周）
- 需求调研和确认
- 环境准备和基础配置
- 团队组建和培训

#### 阶段二：基础实施（2-3周）
- 系统安装和配置
- 基础数据导入
- 核心功能测试

#### 阶段三：业务配置（2-3周）
- 规则配置和导入
- 方案设计和测试
- 权限配置和用户管理

#### 阶段四：试运行（1-2周）
- 小范围试运行
- 问题修复和优化
- 用户培训和反馈

#### 阶段五：正式上线（1周）
- 全面上线运行
- 监控和支持
- 文档交付

### 2.2 实施时间表

| 阶段 | 任务 | 负责人 | 开始时间 | 结束时间 | 里程碑 |
|------|------|--------|----------|----------|--------|
| 准备 | 需求调研 | 业务分析师 | 第1周 | 第1周 | 需求确认 |
| 准备 | 环境准备 | 系统工程师 | 第1周 | 第2周 | 环境就绪 |
| 基础 | 系统安装 | 技术工程师 | 第3周 | 第4周 | 系统可用 |
| 基础 | 功能测试 | 测试工程师 | 第4周 | 第5周 | 功能验证 |
| 业务 | 规则配置 | 业务专家 | 第6周 | 第7周 | 规则就绪 |
| 业务 | 方案设计 | 业务专家 | 第7周 | 第8周 | 方案完成 |
| 试运行 | 试点运行 | 项目经理 | 第9周 | 第10周 | 试运行成功 |
| 上线 | 正式上线 | 项目经理 | 第11周 | 第11周 | 系统上线 |

## 3. 需求调研

### 3.1 调研内容

#### 3.1.1 业务需求调研
```
调研清单：
□ 当前数据质量检查流程
□ 主要数据质量问题类型
□ 检查规则和标准
□ 数据来源和格式
□ 检查频率和时间要求
□ 结果输出和报告需求
□ 用户角色和权限需求
□ 系统集成需求
```

#### 3.1.2 技术环境调研
```
调研清单：
□ 现有IT基础设施
□ 数据库环境和版本
□ 网络环境和安全策略
□ 服务器资源和配置
□ 操作系统和中间件
□ 备份和恢复策略
□ 监控和运维工具
□ 安全合规要求
```

#### 3.1.3 用户调研
```
调研清单：
□ 用户角色和职责
□ 技术水平和培训需求
□ 使用习惯和偏好
□ 工作流程和时间安排
□ 系统使用频率
□ 期望的功能特性
□ 界面和交互要求
□ 移动端使用需求
```

### 3.2 调研方法

#### 3.2.1 访谈调研
```
访谈对象：
- 数据管理负责人
- 质控部门负责人
- IT部门负责人
- 一线操作人员
- 系统管理员

访谈内容：
- 现状分析
- 痛点识别
- 需求收集
- 期望确认
```

#### 3.2.2 现场调研
```
调研内容：
- 工作环境观察
- 操作流程记录
- 系统使用情况
- 数据流向分析
- 问题现象收集
```

#### 3.2.3 文档调研
```
收集文档：
- 业务流程文档
- 技术架构文档
- 数据字典
- 规则标准文档
- 历史问题记录
```

## 4. 系统配置

### 4.1 环境配置

#### 4.1.1 生产环境配置
```python
# config/production.py
class ProductionConfig:
    # 基础配置
    DEBUG = False
    TESTING = False
    SECRET_KEY = os.environ.get('SECRET_KEY')
    
    # 数据库配置
    ORACLE_HOST = '*************'
    ORACLE_PORT = '1521'
    ORACLE_SERVICE = 'MICRA'
    ORACLE_USER = 'micra_prod'
    ORACLE_PASSWORD = os.environ.get('ORACLE_PASSWORD')
    
    # 文件存储配置
    UPLOAD_FOLDER = '/data/micra/uploads'
    BACKUP_FOLDER = '/data/micra/backups'
    LOG_FOLDER = '/data/micra/logs'
    
    # 安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    PERMANENT_SESSION_LIFETIME = 3600  # 1小时
    
    # 性能配置
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
    UPLOAD_TIMEOUT = 300  # 5分钟
```

#### 4.1.2 网络和安全配置
```bash
# 防火墙配置
sudo firewall-cmd --permanent --add-rich-rule='rule family="ipv4" source address="***********/24" port protocol="tcp" port="5000" accept'
sudo firewall-cmd --reload

# SSL证书配置
sudo mkdir -p /etc/ssl/micra
sudo cp micra.crt /etc/ssl/micra/
sudo cp micra.key /etc/ssl/micra/
sudo chmod 600 /etc/ssl/micra/micra.key

# 文件权限配置
sudo chown -R micra:micra /data/micra
sudo chmod 755 /data/micra/uploads
sudo chmod 700 /data/micra/backups
```

### 4.2 数据库配置

#### 4.2.1 数据库初始化
```sql
-- 创建生产环境表空间
CREATE TABLESPACE MICRA_PROD_DATA
DATAFILE '/oracle/data/micra_prod_data01.dbf' SIZE 5G
AUTOEXTEND ON NEXT 500M MAXSIZE 50G;

CREATE TABLESPACE MICRA_PROD_INDEX
DATAFILE '/oracle/data/micra_prod_index01.dbf' SIZE 2G
AUTOEXTEND ON NEXT 200M MAXSIZE 20G;

-- 创建生产用户
CREATE USER micra_prod IDENTIFIED BY "ProdPass2025!"
DEFAULT TABLESPACE MICRA_PROD_DATA
TEMPORARY TABLESPACE TEMP;

-- 授权
GRANT CONNECT, RESOURCE TO micra_prod;
GRANT CREATE VIEW, CREATE SEQUENCE, CREATE TRIGGER TO micra_prod;
GRANT UNLIMITED TABLESPACE TO micra_prod;
```

#### 4.2.2 数据表创建
```bash
# 执行数据库脚本
cd /home/<USER>/micra-app
source venv/bin/activate

# 创建表结构
python scripts/create_tables.py --env production

# 创建索引
python scripts/create_indexes.py --env production

# 初始化基础数据
python scripts/init_data.py --env production
```

### 4.3 应用配置

#### 4.3.1 用户和权限配置
```python
# scripts/init_users.py
from app.models.user import User, Role, Permission

def init_roles_and_permissions():
    """初始化角色和权限"""
    
    # 创建权限
    permissions = [
        ('selfcheck.uploads.view', '查看上传记录'),
        ('selfcheck.uploads.create', '创建上传记录'),
        ('selfcheck.uploads.delete', '删除上传记录'),
        ('selfcheck.schemes.view', '查看方案'),
        ('selfcheck.schemes.create', '创建方案'),
        ('selfcheck.schemes.edit', '编辑方案'),
        ('selfcheck.schemes.delete', '删除方案'),
        ('selfcheck.tasks.view', '查看任务'),
        ('selfcheck.tasks.create', '创建任务'),
        ('selfcheck.tasks.execute', '执行任务'),
        ('system.admin', '系统管理'),
    ]
    
    for code, name in permissions:
        permission = Permission(code=code, name=name)
        permission.save()
    
    # 创建角色
    roles = [
        ('admin', '系统管理员', ['system.admin']),
        ('data_manager', '数据管理员', [
            'selfcheck.uploads.view', 'selfcheck.uploads.create', 'selfcheck.uploads.delete',
            'selfcheck.schemes.view', 'selfcheck.schemes.create', 'selfcheck.schemes.edit',
            'selfcheck.tasks.view', 'selfcheck.tasks.create', 'selfcheck.tasks.execute'
        ]),
        ('quality_controller', '质控人员', [
            'selfcheck.uploads.view', 'selfcheck.schemes.view',
            'selfcheck.tasks.view', 'selfcheck.tasks.create'
        ]),
        ('viewer', '查看者', [
            'selfcheck.uploads.view', 'selfcheck.schemes.view', 'selfcheck.tasks.view'
        ])
    ]
    
    for code, name, permission_codes in roles:
        role = Role(code=code, name=name)
        role.save()
        
        # 分配权限
        for perm_code in permission_codes:
            permission = Permission.query.filter_by(code=perm_code).first()
            if permission:
                role.permissions.append(permission)
        role.save()

def create_admin_user():
    """创建管理员用户"""
    admin_role = Role.query.filter_by(code='admin').first()
    
    admin_user = User(
        username='admin',
        real_name='系统管理员',
        email='<EMAIL>',
        is_admin=True
    )
    admin_user.set_password('Admin123!')
    admin_user.roles.append(admin_role)
    admin_user.save()
    
    print(f"管理员用户创建成功: {admin_user.username}")

if __name__ == '__main__':
    init_roles_and_permissions()
    create_admin_user()
```

## 5. 数据迁移

### 5.1 历史数据迁移

#### 5.1.1 数据评估
```python
# scripts/assess_data.py
def assess_legacy_data():
    """评估历史数据"""
    
    assessment_report = {
        'total_records': 0,
        'data_quality': {},
        'migration_complexity': 'medium',
        'estimated_time': '2-3 hours'
    }
    
    # 评估数据量
    # 评估数据质量
    # 评估迁移复杂度
    
    return assessment_report
```

#### 5.1.2 数据清洗
```python
# scripts/clean_data.py
import pandas as pd

def clean_upload_data(source_file):
    """清洗上传数据"""
    
    # 读取源数据
    df = pd.read_csv(source_file)
    
    # 数据清洗规则
    # 1. 去除重复记录
    df = df.drop_duplicates()
    
    # 2. 标准化文件类型
    df['file_type'] = df['file_type'].str.lower()
    
    # 3. 处理空值
    df['description'] = df['description'].fillna('')
    
    # 4. 验证数据格式
    df = df[df['file_size'] > 0]
    
    return df

def migrate_data():
    """执行数据迁移"""
    
    try:
        # 1. 备份现有数据
        backup_current_data()
        
        # 2. 清洗历史数据
        cleaned_data = clean_upload_data('legacy_uploads.csv')
        
        # 3. 导入新系统
        import_to_new_system(cleaned_data)
        
        # 4. 验证迁移结果
        validate_migration()
        
        print("数据迁移完成")
        
    except Exception as e:
        print(f"数据迁移失败: {e}")
        rollback_migration()
```

### 5.2 规则迁移

#### 5.2.1 规则标准化
```python
# scripts/migrate_rules.py
def standardize_rules(legacy_rules):
    """标准化检查规则"""
    
    standardized_rules = []
    
    for rule in legacy_rules:
        # 标准化规则格式
        std_rule = {
            'rule_name': rule['name'].strip(),
            'rule_type': map_rule_type(rule['type']),
            'sql_content': format_sql(rule['sql']),
            'description': rule.get('description', ''),
            'city': rule.get('city', ''),
            'visit_type': map_visit_type(rule.get('visit_type')),
            'data_type': map_data_type(rule.get('data_type')),
            'status': 'active'
        }
        
        standardized_rules.append(std_rule)
    
    return standardized_rules

def import_rules(rules_data):
    """导入规则数据"""
    
    from app.models.selfcheck import SelfCheckRule
    
    success_count = 0
    error_count = 0
    
    for rule_data in rules_data:
        try:
            rule = SelfCheckRule(**rule_data)
            rule.created_by = 1  # 系统用户
            rule.save()
            success_count += 1
            
        except Exception as e:
            print(f"规则导入失败: {rule_data['rule_name']}, 错误: {e}")
            error_count += 1
    
    print(f"规则导入完成: 成功 {success_count}, 失败 {error_count}")
```

## 6. 用户培训

### 6.1 培训计划

#### 6.1.1 培训对象分类
```
管理员培训：
- 系统管理员
- IT运维人员
培训内容：系统配置、用户管理、故障处理

业务培训：
- 数据管理员
- 质控人员
培训内容：功能操作、业务流程、规则配置

用户培训：
- 一线操作人员
培训内容：基本操作、常见问题、注意事项
```

#### 6.1.2 培训方式
```
理论培训：
- 系统介绍
- 功能演示
- 操作指南

实操培训：
- 模拟环境操作
- 真实场景演练
- 问题解答

在线培训：
- 视频教程
- 操作手册
- FAQ文档
```

### 6.2 培训内容

#### 6.2.1 系统管理员培训
```
培训大纲：
1. 系统架构和部署
   - 系统组件介绍
   - 部署架构说明
   - 配置文件管理

2. 用户和权限管理
   - 用户创建和管理
   - 角色权限配置
   - 权限分配策略

3. 系统监控和维护
   - 日志查看和分析
   - 性能监控
   - 备份和恢复

4. 故障处理
   - 常见问题诊断
   - 故障排除流程
   - 应急处理方案
```

#### 6.2.2 业务用户培训
```
培训大纲：
1. 系统概述
   - 功能介绍
   - 业务价值
   - 使用流程

2. 数据上传管理
   - 文件上传操作
   - 状态查看
   - 问题处理

3. 规则和方案管理
   - 规则配置
   - 方案设计
   - 权限管理

4. 任务执行和结果分析
   - 任务创建
   - 执行监控
   - 结果查看

5. 报告生成和导出
   - 报告配置
   - 数据导出
   - 结果分享
```

### 6.3 培训材料

#### 6.3.1 操作手册
```
用户操作手册目录：
1. 系统登录和导航
2. 数据上传操作指南
3. 方案配置操作指南
4. 任务管理操作指南
5. 结果查看操作指南
6. 常见问题和解决方案
7. 联系方式和技术支持
```

#### 6.3.2 视频教程
```
视频教程清单：
1. 系统概述介绍（10分钟）
2. 文件上传操作（15分钟）
3. 方案配置演示（20分钟）
4. 任务执行流程（15分钟）
5. 结果分析方法（20分钟）
6. 常见问题处理（15分钟）
```

## 7. 上线运行

### 7.1 上线准备

#### 7.1.1 上线检查清单
```
技术准备：
□ 生产环境部署完成
□ 数据库配置正确
□ 应用服务正常运行
□ 网络连接测试通过
□ 安全配置验证
□ 备份策略就绪
□ 监控系统配置

业务准备：
□ 用户账号创建完成
□ 权限分配正确
□ 基础数据导入
□ 规则配置完成
□ 方案设计就绪
□ 培训完成
□ 操作手册发放

应急准备：
□ 回退方案准备
□ 应急联系人确定
□ 技术支持安排
□ 问题处理流程
□ 备用系统准备
```

#### 7.1.2 上线时间安排
```
上线时间表：
周五 18:00 - 系统停机维护
周五 18:30 - 生产环境部署
周五 20:00 - 系统测试验证
周五 21:00 - 数据迁移
周五 22:00 - 最终测试
周六 08:00 - 正式上线
周六 09:00 - 用户通知
周六 10:00 - 技术支持就位
```

### 7.2 上线执行

#### 7.2.1 上线步骤
```bash
# 1. 停止旧系统
sudo systemctl stop old_system

# 2. 备份数据
./scripts/backup_before_migration.sh

# 3. 部署新系统
sudo systemctl start micra
sudo systemctl start nginx

# 4. 验证系统
./scripts/verify_deployment.sh

# 5. 数据迁移
python scripts/migrate_production_data.py

# 6. 最终测试
./scripts/final_test.sh

# 7. 用户通知
./scripts/notify_users.sh
```

#### 7.2.2 验证测试
```python
# scripts/verify_deployment.py
def verify_system():
    """验证系统部署"""
    
    checks = [
        ('数据库连接', check_database_connection),
        ('Web服务', check_web_service),
        ('文件上传', check_file_upload),
        ('用户登录', check_user_login),
        ('权限控制', check_permissions),
        ('API接口', check_api_endpoints)
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, 'PASS', result))
            print(f"✓ {name}: 通过")
        except Exception as e:
            results.append((name, 'FAIL', str(e)))
            print(f"✗ {name}: 失败 - {e}")
    
    return results

def generate_verification_report(results):
    """生成验证报告"""
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'total_checks': len(results),
        'passed': len([r for r in results if r[1] == 'PASS']),
        'failed': len([r for r in results if r[1] == 'FAIL']),
        'details': results
    }
    
    with open('verification_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    return report
```

### 7.3 运行监控

#### 7.3.1 监控指标
```python
# scripts/monitor_system.py
def collect_metrics():
    """收集系统指标"""
    
    metrics = {
        'system': {
            'cpu_usage': get_cpu_usage(),
            'memory_usage': get_memory_usage(),
            'disk_usage': get_disk_usage(),
            'network_io': get_network_io()
        },
        'application': {
            'response_time': get_response_time(),
            'request_count': get_request_count(),
            'error_rate': get_error_rate(),
            'active_users': get_active_users()
        },
        'database': {
            'connection_count': get_db_connections(),
            'query_performance': get_query_performance(),
            'table_sizes': get_table_sizes(),
            'index_usage': get_index_usage()
        }
    }
    
    return metrics
```

#### 7.3.2 告警配置
```yaml
# monitoring/alerts.yml
alerts:
  - name: high_cpu_usage
    condition: cpu_usage > 80
    duration: 5m
    action: send_email
    
  - name: high_memory_usage
    condition: memory_usage > 85
    duration: 3m
    action: send_sms
    
  - name: application_error
    condition: error_rate > 5
    duration: 1m
    action: send_notification
    
  - name: database_slow_query
    condition: avg_query_time > 2s
    duration: 2m
    action: log_warning
```

## 8. 支持和维护

### 8.1 技术支持

#### 8.1.1 支持团队
```
一级支持：
- 现场技术人员
- 响应时间：30分钟
- 处理范围：常见问题、操作指导

二级支持：
- 远程技术专家
- 响应时间：2小时
- 处理范围：技术问题、系统故障

三级支持：
- 开发团队
- 响应时间：4小时
- 处理范围：复杂问题、系统缺陷
```

#### 8.1.2 支持流程
```
问题报告 → 问题分类 → 分级处理 → 解决方案 → 结果确认 → 问题关闭
     ↓           ↓           ↓           ↓           ↓           ↓
  用户反馈   一级支持   技术专家   实施解决   用户验证   文档更新
```

### 8.2 维护计划

#### 8.2.1 日常维护
```
每日维护：
□ 系统状态检查
□ 日志文件查看
□ 备份状态确认
□ 性能指标监控

每周维护：
□ 数据库优化
□ 临时文件清理
□ 安全补丁检查
□ 用户反馈收集

每月维护：
□ 系统性能评估
□ 容量规划分析
□ 安全审计
□ 文档更新
```

#### 8.2.2 升级计划
```
版本升级策略：
- 小版本升级：每月一次
- 大版本升级：每季度一次
- 安全补丁：及时更新
- 功能增强：按需升级

升级流程：
1. 升级计划制定
2. 测试环境验证
3. 用户通知
4. 生产环境升级
5. 验证测试
6. 用户培训
```

---

**文档状态：** 已审核  
**批准人：** 项目经理  
**批准日期：** 2025年1月7日  
**版本历史：**
- V1.0 (2025-01-07): 初始版本，包含完整实施指南
