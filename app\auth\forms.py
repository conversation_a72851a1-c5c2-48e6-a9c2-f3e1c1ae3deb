from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError
from app.models.user import User

class LoginForm(FlaskForm):
    """登录表单"""
    username = StringField('用户名或邮箱', validators=[DataRequired(), Length(1, 64)])
    password = PasswordField('密码', validators=[DataRequired()])
    remember_me = BooleanField('记住我')
    submit = SubmitField('登录')

class ChangePasswordForm(FlaskForm):
    """修改密码表单"""
    old_password = PasswordField('当前密码', validators=[DataRequired()])
    password = PasswordField('新密码', validators=[
        DataRequired(),
        Length(8, 128, message='密码长度必须在8-128个字符之间')
    ])
    password2 = PasswordField('确认新密码', validators=[
        DataRequired(),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('修改密码')
    
    def validate_password(self, field):
        if not User.validate_password_strength(field.data):
            raise ValidationError('密码必须包含大小写字母、数字和特殊字符')

class CreateUserForm(FlaskForm):
    """创建用户表单"""
    username = StringField('用户名', validators=[
        DataRequired(), 
        Length(3, 20, message='用户名长度必须在3-20个字符之间')
    ])
    email = StringField('邮箱', validators=[DataRequired(), Email()])
    real_name = StringField('真实姓名', validators=[DataRequired(), Length(1, 50)])
    phone = StringField('手机号码', validators=[Length(0, 20)])
    department = StringField('部门', validators=[Length(0, 100)])
    password = PasswordField('密码', validators=[
        DataRequired(),
        Length(8, 128, message='密码长度必须在8-128个字符之间')
    ])
    password2 = PasswordField('确认密码', validators=[
        DataRequired(),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    is_admin = BooleanField('管理员权限')
    submit = SubmitField('创建用户')
    
    def validate_username(self, field):
        if User.query.filter_by(username=field.data).first():
            raise ValidationError('用户名已存在')
    
    def validate_email(self, field):
        if User.query.filter_by(email=field.data).first():
            raise ValidationError('邮箱已被注册')
    
    def validate_password(self, field):
        if not User.validate_password_strength(field.data):
            raise ValidationError('密码必须包含大小写字母、数字和特殊字符')

class EditUserForm(FlaskForm):
    """编辑用户表单"""
    username = StringField('用户名', validators=[DataRequired(), Length(3, 20)])
    email = StringField('邮箱', validators=[DataRequired(), Email()])
    real_name = StringField('真实姓名', validators=[DataRequired(), Length(1, 50)])
    phone = StringField('手机号码', validators=[Length(0, 20)])
    department = StringField('部门', validators=[Length(0, 100)])
    is_active = BooleanField('账户状态')
    is_admin = BooleanField('管理员权限')
    submit = SubmitField('保存修改')
    
    def __init__(self, user, *args, **kwargs):
        super(EditUserForm, self).__init__(*args, **kwargs)
        self.user = user
    
    def validate_username(self, field):
        if field.data != self.user.username and \
           User.query.filter_by(username=field.data).first():
            raise ValidationError('用户名已存在')
    
    def validate_email(self, field):
        if field.data != self.user.email and \
           User.query.filter_by(email=field.data).first():
            raise ValidationError('邮箱已被注册')

class ResetPasswordForm(FlaskForm):
    """重置密码表单（管理员用）"""
    password = PasswordField('新密码', validators=[
        DataRequired(),
        Length(8, 128, message='密码长度必须在8-128个字符之间')
    ])
    password2 = PasswordField('确认新密码', validators=[
        DataRequired(),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('重置密码')
    
    def validate_password(self, field):
        if not User.validate_password_strength(field.data):
            raise ValidationError('密码必须包含大小写字母、数字和特殊字符')
