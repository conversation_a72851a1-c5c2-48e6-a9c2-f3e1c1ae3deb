#!/usr/bin/env python3
"""
添加文件验证权限
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def add_validation_permissions():
    """添加文件验证权限"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            print("=== 添加文件验证权限 ===")
            
            # 检查是否已存在验证权限
            check_query = "SELECT id FROM permissions WHERE code = 'selfcheck.uploads.validate'"
            existing = db_manager.execute_query(check_query)
            
            if existing:
                print("✅ 验证权限已存在")
                return
            
            # 获取uploads父权限ID
            parent_query = "SELECT id FROM permissions WHERE code = 'selfcheck.uploads'"
            parent_result = db_manager.execute_query(parent_query)
            
            if not parent_result:
                print("❌ 无法找到父权限 selfcheck.uploads")
                return
            
            parent_id = parent_result[0]['id']
            
            # 获取下一个可用ID
            max_id_query = "SELECT MAX(id) as max_id FROM permissions"
            max_result = db_manager.execute_query(max_id_query)
            next_id = (max_result[0]['max_id'] or 0) + 1
            
            # 插入验证权限
            insert_query = """
            INSERT INTO permissions (id, name, code, description, module, resource_type, parent_id, sort_order, is_active)
            VALUES (:id, :name, :code, :description, 'selfcheck', 'button', :parent_id, 3, 1)
            """
            
            db_manager.execute_update(insert_query, {
                'id': next_id,
                'name': '验证文件',
                'code': 'selfcheck.uploads.validate',
                'description': '验证上传文件的表结构和格式',
                'parent_id': parent_id
            })
            
            print(f"✅ 已添加验证权限: selfcheck.uploads.validate (ID: {next_id})")
            
            # 为超级管理员角色分配新权限
            admin_role_query = "SELECT id FROM roles WHERE name = '超级管理员'"
            admin_role_result = db_manager.execute_query(admin_role_query)
            
            if admin_role_result:
                admin_role_id = admin_role_result[0]['id']
                
                # 检查是否已分配
                check_role_perm = """
                SELECT 1 FROM role_permissions 
                WHERE role_id = :role_id AND permission_id = :permission_id
                """
                existing_assignment = db_manager.execute_query(check_role_perm, {
                    'role_id': admin_role_id,
                    'permission_id': next_id
                })
                
                if not existing_assignment:
                    assign_query = """
                    INSERT INTO role_permissions (role_id, permission_id, created_at)
                    VALUES (:role_id, :permission_id, CURRENT_TIMESTAMP)
                    """
                    
                    db_manager.execute_update(assign_query, {
                        'role_id': admin_role_id,
                        'permission_id': next_id
                    })
                    
                    print(f"✅ 为超级管理员角色分配了验证权限")
                else:
                    print("超级管理员角色已有验证权限")
            
            print("\n=== 验证权限添加结果 ===")
            
            # 验证权限是否正确添加
            verify_query = """
            SELECT p.id, p.name, p.code, p.description, p.parent_id, p.sort_order
            FROM permissions p
            WHERE p.code = 'selfcheck.uploads.validate'
            """
            
            result = db_manager.execute_query(verify_query)
            if result:
                perm = result[0]
                print(f"权限ID: {perm['id']}")
                print(f"权限名称: {perm['name']}")
                print(f"权限代码: {perm['code']}")
                print(f"权限描述: {perm['description']}")
                print(f"父权限ID: {perm['parent_id']}")
                print(f"排序: {perm['sort_order']}")
            else:
                print("❌ 权限验证失败")
            
        except Exception as e:
            print(f"添加失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    add_validation_permissions()
