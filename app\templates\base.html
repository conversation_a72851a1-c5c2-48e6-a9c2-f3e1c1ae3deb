<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}MICRA飞检数据处理工具箱{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="{{ url_for('static', filename='css/dataTables.bootstrap5.min.css') }}" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #0078D4;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --hover-bg: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --radius: 12px;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
        }

        .sidebar {
            width: 280px;
            background-color: var(--card-bg);
            height: 100vh;
            position: fixed;
            box-shadow: var(--shadow-sm);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(0,0,0,0.06);
            overflow-y: auto;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .sidebar.collapsed {
            width: 80px;
        }

        .sidebar.collapsed .navbar-brand span {
            display: none;
        }

        .sidebar.collapsed .nav-link span {
            display: none;
        }

        .sidebar.collapsed .nav-header {
            display: none;
        }

        .sidebar .navbar-brand {
            font-size: 1.25rem;
            font-weight: 600;
            padding: 1rem 1.5rem;
            color: var(--text-primary);
            text-decoration: none;
        }

        .sidebar .nav-link {
            color: var(--text-secondary);
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            background-color: var(--hover-bg);
            color: var(--text-primary);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .sidebar .nav-link i {
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .main-content.expanded {
            margin-left: 80px;
        }

        .sidebar-toggle {
            position: fixed;
            top: 15px;
            left: 290px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: #106ebe;
            transform: scale(1.1);
        }

        .sidebar.collapsed + .main-content .sidebar-toggle,
        .sidebar-toggle.collapsed {
            left: 90px;
        }

        .navbar-top {
            background-color: var(--card-bg);
            border-bottom: 1px solid rgba(0,0,0,0.06);
            margin-bottom: 2rem;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .top-nav-tabs {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0.5rem 1rem;
            margin-bottom: 1rem;
        }

        .top-nav-tabs .nav-link {
            color: #495057;
            border: none;
            border-radius: 0.375rem;
            margin-right: 0.5rem;
            padding: 0.5rem 1rem;
            background: transparent;
            transition: all 0.2s ease;
        }

        .top-nav-tabs .nav-link:hover {
            background-color: #e9ecef;
            color: #212529;
        }

        .top-nav-tabs .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .breadcrumb-container {
            background-color: #f8f9fa;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
        }



        .color-option-modal {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .color-option-modal:hover {
            transform: scale(1.1);
            border-color: #ffffff;
            box-shadow: 0 0 0 2px #000000;
        }

        .color-option-modal.active {
            border-color: #ffffff;
            box-shadow: 0 0 0 2px #000000;
        }

        .card {
            border: 1px solid rgba(0,0,0,0.06);
            border-radius: var(--radius);
            box-shadow: var(--shadow-sm);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #106ebe;
            border-color: #106ebe;
        }

        .text-purple {
            color: #6f42c1 !important;
        }

        .btn-purple {
            background-color: #6f42c1;
            border-color: #6f42c1;
            color: white;
        }

        .btn-purple:hover {
            background-color: #5a2d91;
            border-color: #5a2d91;
            color: white;
        }

        .btn-outline-purple {
            color: #6f42c1;
            border-color: #6f42c1;
        }

        .btn-outline-purple:hover {
            background-color: #6f42c1;
            border-color: #6f42c1;
            color: white;
        }

        .alert {
            border-radius: var(--radius);
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 80px;
            }
            
            .sidebar .navbar-brand {
                display: none;
            }
            
            .sidebar .nav-link span {
                display: none;
            }
            
            .main-content {
                margin-left: 80px;
            }
        }
    </style>
    
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- 侧边栏折叠按钮 -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars" id="toggleIcon"></i>
    </button>



    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <a class="navbar-brand" href="{{ url_for('main.index') }}">
            <i class="fas fa-tools"></i> <span>MICRA工具箱</span>
        </a>
        
        {% if current_user.is_authenticated %}
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('main.index') }}">
                    <i class="fas fa-home"></i><span>主页</span>
                </a>
            </li>

            <!-- 1. 自查自纠 -->
            {% if current_user.has_permission('selfcheck') %}
            <li class="nav-item">
                <h6 class="nav-header px-3 py-2 text-muted">自查自纠</h6>
            </li>
            {% if current_user.has_permission('selfcheck.rules.view') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('selfcheck.rules') }}">
                    <i class="fas fa-cogs"></i><span>规则管理</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('selfcheck.schemes.view') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('selfcheck.schemes') }}">
                    <i class="fas fa-project-diagram"></i><span>方案管理</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('selfcheck.uploads.view') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('selfcheck.uploads') }}">
                    <i class="fas fa-upload"></i><span>数据上传</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('selfcheck.tasks.view') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('selfcheck.tasks') }}">
                    <i class="fas fa-tasks"></i><span>自查任务</span>
                </a>
            </li>
            {% endif %}
            {% endif %}

            <!-- 2. 规则管理 -->
            {% if current_user.has_permission('rules') %}
            <li class="nav-item">
                <h6 class="nav-header px-3 py-2 text-muted">规则管理</h6>
            </li>
            {% if current_user.has_permission('rules.knowledge_base') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('rules.knowledge_base') }}">
                    <i class="fas fa-book"></i><span>飞检规则知识库</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('rules.sql_generator') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('rules.sql_generator') }}">
                    <i class="fas fa-code"></i><span>规则SQL生成器</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('rules.system_rules') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('rules.system_rules') }}">
                    <i class="fas fa-cogs"></i><span>系统规则语句</span>
                </a>
            </li>
            {% endif %}
            {% endif %}

            <!-- 3. 数据库工具 -->
            {% if current_user.has_permission('database') %}
            <li class="nav-item">
                <h6 class="nav-header px-3 py-2 text-muted">数据库工具</h6>
            </li>
            {% if current_user.has_permission('database.sql_generator') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('database.sql_generator') }}">
                    <i class="fas fa-database"></i><span>SQL生成器</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('database.query') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('database.query') }}">
                    <i class="fas fa-table"></i><span>数据库查询生成Excel</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('database.batch_query') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('database.batch_query') }}">
                    <i class="fas fa-tasks"></i><span>批量SQL查询生成Excel</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('database.performance_test') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('database.performance_test') }}">
                    <i class="fas fa-tachometer-alt"></i><span>SQL性能测试</span>
                </a>
            </li>
            {% endif %}
            {% endif %}

            <!-- 4. Excel工具 -->
            {% if current_user.has_permission('excel') %}
            <li class="nav-item">
                <h6 class="nav-header px-3 py-2 text-muted">Excel工具</h6>
            </li>
            {% if current_user.has_permission('excel.splitter') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('excel.splitter') }}">
                    <i class="far fa-file-excel"></i><span>Excel文件拆分</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('excel.delete') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('excel.delete') }}">
                    <i class="fas fa-eraser"></i><span>Excel内容删除</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('excel.compare') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('excel.compare') }}">
                    <i class="fas fa-file-excel"></i><span>Excel比对工具</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('excel.to_sql') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('excel.to_sql') }}">
                    <i class="fas fa-file-code"></i><span>Excel转SQL工具</span>
                </a>
            </li>
            {% endif %}
            {% endif %}

            <!-- 5. 数据处理 -->
            {% if current_user.has_permission('data') %}
            <li class="nav-item">
                <h6 class="nav-header px-3 py-2 text-muted">数据处理</h6>
            </li>
            {% if current_user.has_permission('data.find_duplicates') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('data.find_duplicates') }}">
                    <i class="fas fa-file-alt"></i><span>查找重复文件</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('data.validator') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('data.validator') }}">
                    <i class="fas fa-check-circle"></i><span>数据校验</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('data.standardization') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('data.standardization') }}">
                    <i class="fas fa-cogs"></i><span>数据标准化</span>
                </a>
            </li>
            {% endif %}
            {% endif %}

            <!-- 6. 系统管理 -->
            {% if current_user.has_permission('system') %}
            <li class="nav-item">
                <h6 class="nav-header px-3 py-2 text-muted">系统管理</h6>
            </li>
            {% if current_user.has_permission('system.user.view') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin.users') }}">
                    <i class="fas fa-users"></i><span>用户管理</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('system.role.view') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin.roles') }}">
                    <i class="fas fa-user-tag"></i><span>角色管理</span>
                </a>
            </li>
            {% endif %}
            {% if current_user.has_permission('system.audit.view') %}
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin.audit_logs') }}">
                    <i class="fas fa-history"></i><span>审计日志</span>
                </a>
            </li>
            {% endif %}
            {% endif %}
        </ul>
        {% endif %}
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content" id="mainContent">
        {% if current_user.is_authenticated %}
        <!-- 顶部导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-top">
            <div class="container-fluid">
                <!-- 顶部导航标签 -->
                <div class="top-nav-tabs">
                    <ul class="nav nav-pills" id="topNavTabs">
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('main.index') }}">
                                <i class="fas fa-home"></i> 首页
                            </a>
                        </li>
                        {% if current_user.has_permission('selfcheck') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('selfcheck.index') }}">
                                <i class="fas fa-search-plus"></i> 自查自纠
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('rules') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('rules.index') }}">
                                <i class="fas fa-book"></i> 规则管理
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('database') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('database.index') }}">
                                <i class="fas fa-database"></i> 数据库工具
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('excel') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('excel.index') }}">
                                <i class="fas fa-file-excel"></i> Excel工具
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('data') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('data.index') }}">
                                <i class="fas fa-chart-bar"></i> 数据处理
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.has_permission('system') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin.index') }}">
                                <i class="fas fa-cog"></i> 系统管理
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>

                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ current_user.real_name or current_user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="fas fa-key"></i> 修改密码
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="openSettings()">
                                <i class="fas fa-cog"></i> 系统设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        {% endif %}

        <!-- Flash消息 - 悬浮显示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show position-fixed"
                         style="top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
                <script>
                    // 自动关闭Flash消息
                    setTimeout(function() {
                        $('.alert').fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 5000);
                </script>
            {% endif %}
        {% endwith %}

        <!-- 面包屑导航 -->
        {% block breadcrumb %}
        {% if breadcrumb_items %}
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb">
                {% for item in breadcrumb_items %}
                    {% if loop.last %}
                        <li class="breadcrumb-item active" aria-current="page">{{ item.name }}</li>
                    {% else %}
                        <li class="breadcrumb-item">
                            <a href="{{ item.url }}" class="text-decoration-none">
                                {% if item.icon %}
                                    <i class="{{ item.icon }} me-1"></i>
                                {% endif %}
                                {{ item.name }}
                            </a>
                        </li>
                    {% endif %}
                {% endfor %}
            </ol>
        </nav>
        {% endif %}
        {% endblock %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- 设置模态框 -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-cog me-2"></i>系统设置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary">主题风格设置</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="modalThemeStyle" id="modalThemeLight" value="light" checked>
                                        <label class="form-check-label" for="modalThemeLight">
                                            浅色主题
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="modalThemeStyle" id="modalThemeDark" value="dark">
                                        <label class="form-check-label" for="modalThemeDark">
                                            深色主题
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary">主题颜色</h6>
                            <div class="d-flex gap-2 flex-wrap">
                                <div class="color-option-modal" data-color="#0078D4" style="background-color: #0078D4;" title="蓝色"></div>
                                <div class="color-option-modal" data-color="#28a745" style="background-color: #28a745;" title="绿色"></div>
                                <div class="color-option-modal" data-color="#dc3545" style="background-color: #dc3545;" title="红色"></div>
                                <div class="color-option-modal" data-color="#ffc107" style="background-color: #ffc107;" title="黄色"></div>
                                <div class="color-option-modal" data-color="#6f42c1" style="background-color: #6f42c1;" title="紫色"></div>
                                <div class="color-option-modal" data-color="#fd7e14" style="background-color: #fd7e14;" title="橙色"></div>
                                <div class="color-option-modal" data-color="#20c997" style="background-color: #20c997;" title="青色"></div>
                                <div class="color-option-modal" data-color="#6c757d" style="background-color: #6c757d;" title="灰色"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary">系统布局配置</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="modalEnableTopNav" checked>
                                        <label class="form-check-label" for="modalEnableTopNav">开启 TopNav</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="modalEnableTagsViews" checked>
                                        <label class="form-check-label" for="modalEnableTagsViews">开启 Tags-Views</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="modalFixedHeader">
                                        <label class="form-check-label" for="modalFixedHeader">固定 Header</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="modalShowLogo" checked>
                                        <label class="form-check-label" for="modalShowLogo">显示 Logo</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="modalDynamicTitle">
                                        <label class="form-check-label" for="modalDynamicTitle">动态标题</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="resetModalSettings()">重置配置</button>
                    <button type="button" class="btn btn-primary" onclick="saveModalSettings()">保存配置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dataTables.bootstrap5.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/table-utils.js') }}"></script>

    <script>
        // 侧边栏折叠功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const toggleIcon = document.getElementById('toggleIcon');
            const toggleButton = document.querySelector('.sidebar-toggle');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            const isCollapsed = sidebar.classList.contains('collapsed');
            if (isCollapsed) {
                toggleIcon.className = 'fas fa-chevron-right';
                toggleButton.style.left = '90px';
                localStorage.setItem('sidebar_collapsed', 'true');
            } else {
                toggleIcon.className = 'fas fa-bars';
                toggleButton.style.left = '290px';
                localStorage.setItem('sidebar_collapsed', 'false');
            }
        }

        // 恢复侧边栏状态
        function restoreSidebarState() {
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('mainContent');
                const toggleIcon = document.getElementById('toggleIcon');
                const toggleButton = document.querySelector('.sidebar-toggle');

                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
                toggleIcon.className = 'fas fa-chevron-right';
                if (toggleButton) {
                    toggleButton.style.left = '90px';
                }
            }
        }

        // 顶部导航标签激活状态
        $(document).ready(function() {
            const currentPath = window.location.pathname;
            $('#topNavTabs .nav-link').removeClass('active');

            $('#topNavTabs .nav-link').each(function() {
                const href = $(this).attr('href');
                if (currentPath === href || (href !== '/' && currentPath.startsWith(href))) {
                    $(this).addClass('active');
                }
            });

            // 如果没有匹配的，默认激活首页
            if ($('#topNavTabs .nav-link.active').length === 0) {
                $('#topNavTabs .nav-link[href="/"]').addClass('active');
            }
        });

        // 表格排序功能
        function initSortableTable(tableId) {
            if ($.fn.DataTable.isDataTable('#' + tableId)) {
                $('#' + tableId).DataTable().destroy();
            }

            $('#' + tableId).DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Chinese.json"
                },
                "order": [],
                "columnDefs": [
                    { "orderable": false, "targets": -1 } // 最后一列（操作列）不排序
                ],
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],
                "responsive": true,
                "autoWidth": false
            });
        }

        // 打开设置模态框
        function openSettings() {
            loadModalSettings();
            $('#settingsModal').modal('show');
        }

        // 加载模态框设置
        function loadModalSettings() {
            const settings = JSON.parse(localStorage.getItem('micra_settings') || '{}');

            // 主题设置
            if (settings.theme) {
                $(`input[name="modalThemeStyle"][value="${settings.theme}"]`).prop('checked', true);
            }

            // 颜色设置
            $('.color-option-modal').removeClass('active');
            if (settings.primaryColor) {
                $(`.color-option-modal[data-color="${settings.primaryColor}"]`).addClass('active');
            } else {
                $('.color-option-modal[data-color="#0078D4"]').addClass('active');
            }

            // 布局设置
            $('#modalEnableTopNav').prop('checked', settings.enableTopNav !== false);
            $('#modalEnableTagsViews').prop('checked', settings.enableTagsViews !== false);
            $('#modalFixedHeader').prop('checked', settings.fixedHeader === true);
            $('#modalShowLogo').prop('checked', settings.showLogo !== false);
            $('#modalDynamicTitle').prop('checked', settings.dynamicTitle === true);
        }

        // 保存模态框设置
        function saveModalSettings() {
            const settings = {
                theme: $('input[name="modalThemeStyle"]:checked').val(),
                primaryColor: $('.color-option-modal.active').data('color'),
                enableTopNav: $('#modalEnableTopNav').is(':checked'),
                enableTagsViews: $('#modalEnableTagsViews').is(':checked'),
                fixedHeader: $('#modalFixedHeader').is(':checked'),
                showLogo: $('#modalShowLogo').is(':checked'),
                dynamicTitle: $('#modalDynamicTitle').is(':checked')
            };

            localStorage.setItem('micra_settings', JSON.stringify(settings));
            applySettings(settings);
            $('#settingsModal').modal('hide');
            showAlert('设置已保存并应用', 'success');
        }

        // 重置模态框设置
        function resetModalSettings() {
            if (confirm('确定要重置所有设置吗？')) {
                localStorage.removeItem('micra_settings');
                $('#settingsModal').modal('hide');
                location.reload();
            }
        }

        // 应用设置
        function applySettings(settings) {
            // 应用主色调
            if (settings.primaryColor) {
                document.documentElement.style.setProperty('--primary-color', settings.primaryColor);
            }

            // 应用顶部导航设置
            if (settings.enableTopNav) {
                $('.top-nav-tabs').show();
            } else {
                $('.top-nav-tabs').hide();
            }

            // 应用固定头部设置
            if (settings.fixedHeader) {
                $('.navbar-top').addClass('position-sticky');
                $('.navbar-top').css('top', '0');
            } else {
                $('.navbar-top').removeClass('position-sticky');
                $('.navbar-top').css('top', 'auto');
            }

            // 应用Logo显示设置
            if (settings.showLogo) {
                $('.navbar-brand').show();
            } else {
                $('.navbar-brand').hide();
            }
        }

        // 页面加载时应用保存的设置
        $(document).ready(function() {
            const settings = JSON.parse(localStorage.getItem('micra_settings') || '{}');

            // 设置默认值
            const defaultSettings = {
                enableTopNav: true,
                enableTagsViews: true,
                fixedHeader: false,
                showLogo: true,
                dynamicTitle: false,
                primaryColor: '#0078D4'
            };

            // 合并默认设置和用户设置
            const finalSettings = Object.assign({}, defaultSettings, settings);
            applySettings(finalSettings);

            // 恢复侧边栏状态
            restoreSidebarState();

            // 绑定颜色选择事件
            $('.color-option-modal').click(function() {
                $('.color-option-modal').removeClass('active');
                $(this).addClass('active');
            });
        });

        // 显示提示信息
        function showAlert(message, type) {
            // 移除已存在的alert
            $('.alert.position-fixed').remove();

            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed"
                     style="top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;
                            box-shadow: 0 4px 12px rgba(0,0,0,0.15);" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('body').append(alertHtml);

            // 自动关闭
            setTimeout(function() {
                $('.alert.position-fixed').fadeOut(500, function() {
                    $(this).remove();
                });
            }, 4000);
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
