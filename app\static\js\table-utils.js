/**
 * 表格工具类 - 提供排序、分页、搜索等功能
 */

// 设置DataTable全局默认语言配置，防止加载外部CDN
$(document).ready(function() {
    if (typeof $.fn.dataTable !== 'undefined') {
        $.extend(true, $.fn.dataTable.defaults, {
            "language": {
                "sProcessing": "处理中...",
                "sLengthMenu": "显示 _MENU_ 项结果",
                "sZeroRecords": "没有匹配结果",
                "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
                "sInfoPostFix": "",
                "sSearch": "搜索:",
                "sUrl": "",
                "sEmptyTable": "表中数据为空",
                "sLoadingRecords": "载入中...",
                "sInfoThousands": ",",
                "oPaginate": {
                    "sFirst": "首页",
                    "sPrevious": "上页",
                    "sNext": "下页",
                    "sLast": "末页"
                },
                "oAria": {
                    "sSortAscending": ": 以升序排列此列",
                    "sSortDescending": ": 以降序排列此列"
                }
            }
        });
    }
});

// 初始化可排序表格
function initSortableTable(tableId, options = {}) {
    const defaultOptions = {
        "language": {
            "sProcessing": "处理中...",
            "sLengthMenu": "显示 _MENU_ 项结果",
            "sZeroRecords": "没有匹配结果",
            "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
            "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
            "sInfoPostFix": "",
            "sSearch": "搜索:",
            "sUrl": "",
            "sEmptyTable": "表中数据为空",
            "sLoadingRecords": "载入中...",
            "sInfoThousands": ",",
            "oPaginate": {
                "sFirst": "首页",
                "sPrevious": "上页",
                "sNext": "下页",
                "sLast": "末页"
            },
            "oAria": {
                "sSortAscending": ": 以升序排列此列",
                "sSortDescending": ": 以降序排列此列"
            }
        },
        "order": [],
        "columnDefs": [
            { "orderable": false, "targets": -1 } // 最后一列（操作列）不排序
        ],
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],
        "responsive": true,
        "autoWidth": false,
        "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
               '<"row"<"col-sm-12"tr>>' +
               '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        "processing": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": true
    };

    // 合并选项
    const finalOptions = Object.assign({}, defaultOptions, options);

    // 销毁已存在的DataTable实例
    if ($.fn.DataTable.isDataTable('#' + tableId)) {
        $('#' + tableId).DataTable().destroy();
    }

    // 初始化DataTable
    return $('#' + tableId).DataTable(finalOptions);
}

// 初始化简单排序表格（无分页）
function initSimpleSortableTable(tableId, options = {}) {
    const simpleOptions = {
        "language": {
            "sProcessing": "处理中...",
            "sLengthMenu": "显示 _MENU_ 项结果",
            "sZeroRecords": "没有匹配结果",
            "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
            "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
            "sInfoPostFix": "",
            "sSearch": "搜索:",
            "sUrl": "",
            "sEmptyTable": "表中数据为空",
            "sLoadingRecords": "载入中...",
            "sInfoThousands": ",",
            "oPaginate": {
                "sFirst": "首页",
                "sPrevious": "上页",
                "sNext": "下页",
                "sLast": "末页"
            },
            "oAria": {
                "sSortAscending": ": 以升序排列此列",
                "sSortDescending": ": 以降序排列此列"
            }
        },
        "order": [],
        "columnDefs": [
            { "orderable": false, "targets": -1 }
        ],
        "paging": false,
        "searching": true,
        "ordering": true,
        "info": false,
        "responsive": true,
        "autoWidth": false,
        "dom": '<"row"<"col-sm-12"f>><"row"<"col-sm-12"tr>>'
    };

    const finalOptions = Object.assign({}, simpleOptions, options);

    if ($.fn.DataTable.isDataTable('#' + tableId)) {
        $('#' + tableId).DataTable().destroy();
    }

    return $('#' + tableId).DataTable(finalOptions);
}

// 刷新表格数据
function refreshTable(tableId, newData) {
    const table = $('#' + tableId).DataTable();
    table.clear();
    table.rows.add(newData);
    table.draw();
}

// 添加表格行
function addTableRow(tableId, rowData) {
    const table = $('#' + tableId).DataTable();
    table.row.add(rowData).draw();
}

// 删除表格行
function removeTableRow(tableId, rowIndex) {
    const table = $('#' + tableId).DataTable();
    table.row(rowIndex).remove().draw();
}

// 更新表格行
function updateTableRow(tableId, rowIndex, newData) {
    const table = $('#' + tableId).DataTable();
    table.row(rowIndex).data(newData).draw();
}

// 获取表格选中行
function getSelectedRows(tableId) {
    const table = $('#' + tableId).DataTable();
    const selectedRows = [];
    
    table.rows('.selected').every(function() {
        selectedRows.push(this.data());
    });
    
    return selectedRows;
}

// 设置表格行选择
function enableRowSelection(tableId, multiSelect = false) {
    const table = $('#' + tableId).DataTable();
    
    $('#' + tableId + ' tbody').on('click', 'tr', function() {
        if (!multiSelect) {
            // 单选模式
            table.$('tr.selected').removeClass('selected');
        }
        
        $(this).toggleClass('selected');
    });
}

// 导出表格数据为CSV
function exportTableToCSV(tableId, filename = 'table_data.csv') {
    const table = $('#' + tableId).DataTable();
    const data = table.data().toArray();
    const headers = table.columns().header().toArray().map(th => $(th).text());
    
    let csvContent = headers.join(',') + '\n';
    
    data.forEach(row => {
        const rowData = Array.isArray(row) ? row : Object.values(row);
        csvContent += rowData.map(cell => `"${cell}"`).join(',') + '\n';
    });
    
    downloadCSV(csvContent, filename);
}

// 下载CSV文件
function downloadCSV(csvContent, filename) {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 表格搜索高亮
function highlightSearchResults(tableId) {
    const table = $('#' + tableId).DataTable();
    
    table.on('search.dt', function() {
        const searchTerm = table.search();
        
        if (searchTerm) {
            table.rows().every(function() {
                const node = this.node();
                $(node).find('td').each(function() {
                    const cellText = $(this).text();
                    if (cellText.toLowerCase().includes(searchTerm.toLowerCase())) {
                        const highlightedText = cellText.replace(
                            new RegExp(searchTerm, 'gi'),
                            '<mark>$&</mark>'
                        );
                        $(this).html(highlightedText);
                    }
                });
            });
        }
    });
}

// 表格列过滤器
function addColumnFilters(tableId) {
    const table = $('#' + tableId).DataTable();
    
    // 在表头下方添加过滤器行
    $('#' + tableId + ' thead tr').clone(true).appendTo('#' + tableId + ' thead');
    $('#' + tableId + ' thead tr:eq(1) th').each(function(i) {
        const title = $(this).text();
        $(this).html('<input type="text" placeholder="搜索 ' + title + '" />');
        
        $('input', this).on('keyup change', function() {
            if (table.column(i).search() !== this.value) {
                table.column(i).search(this.value).draw();
            }
        });
    });
}

// 表格统计信息
function getTableStats(tableId) {
    const table = $('#' + tableId).DataTable();
    
    return {
        totalRows: table.data().length,
        filteredRows: table.rows({ filter: 'applied' }).data().length,
        selectedRows: table.rows('.selected').data().length,
        currentPage: table.page.info().page + 1,
        totalPages: table.page.info().pages
    };
}

// 批量操作工具栏
function addBatchOperationToolbar(tableId, operations = []) {
    const toolbar = $(`
        <div class="batch-operations-toolbar mb-3" style="display: none;">
            <div class="d-flex align-items-center">
                <span class="me-3">已选择 <span class="selected-count">0</span> 项</span>
                <div class="btn-group" role="group">
                    ${operations.map(op => `
                        <button type="button" class="btn btn-sm btn-${op.type || 'primary'}" 
                                onclick="${op.action}">
                            <i class="${op.icon}"></i> ${op.label}
                        </button>
                    `).join('')}
                </div>
                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" 
                        onclick="clearTableSelection('${tableId}')">
                    取消选择
                </button>
            </div>
        </div>
    `);
    
    $('#' + tableId).before(toolbar);
    
    // 监听行选择变化
    $('#' + tableId + ' tbody').on('click', 'tr', function() {
        updateBatchToolbar(tableId);
    });
}

// 更新批量操作工具栏
function updateBatchToolbar(tableId) {
    const selectedCount = $('#' + tableId + ' tbody tr.selected').length;
    const toolbar = $('#' + tableId).prev('.batch-operations-toolbar');
    
    if (selectedCount > 0) {
        toolbar.show();
        toolbar.find('.selected-count').text(selectedCount);
    } else {
        toolbar.hide();
    }
}

// 清除表格选择
function clearTableSelection(tableId) {
    $('#' + tableId + ' tbody tr.selected').removeClass('selected');
    updateBatchToolbar(tableId);
}
