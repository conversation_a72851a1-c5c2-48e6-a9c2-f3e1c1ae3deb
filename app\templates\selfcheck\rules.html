{% extends "base.html" %}

{% block title %}规则管理 - 自查自纠{% endblock %}



{% block content %}
<meta name="csrf-token" content="{{ csrf_token() }}">

<style>
/* 表格布局优化 */
#rulesTable {
    table-layout: fixed;
    width: 100%;
}

#rulesTable th, #rulesTable td {
    word-wrap: break-word;
    vertical-align: middle;
}

/* 规则名称列样式 */
#rulesTable td:nth-child(2) {
    max-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#rulesTable td:nth-child(2):hover {
    overflow: visible;
    white-space: normal;
    background-color: #f8f9fa;
    position: relative;
    z-index: 10;
}

/* 规则编码样式 */
#rulesTable td:nth-child(3) code {
    font-size: 0.85em;
    padding: 2px 4px;
}

/* 状态徽章样式 */
#rulesTable .badge {
    font-size: 0.75em;
}

/* 操作按钮样式 */
#rulesTable .btn-sm {
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
    margin-right: 2px;
}

/* 批量操作工具栏样式 */
.batch-operations-toolbar {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 0.375rem;
    padding: 0.75rem;
}

.batch-operations-toolbar .selected-count {
    font-weight: bold;
    color: #1976d2;
}

/* 响应式调整 */
@media (max-width: 768px) {
    #rulesTable th:nth-child(5),
    #rulesTable td:nth-child(5),
    #rulesTable th:nth-child(6),
    #rulesTable td:nth-child(6) {
        display: none;
    }

    #rulesTable th:nth-child(2),
    #rulesTable td:nth-child(2) {
        width: 35%;
    }
}

/* 排序相关样式 */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

/* 移除悬停时的白色背景效果 */

.sort-icon {
    margin-left: 0.5rem;
    opacity: 0.6;
    font-size: 0.8rem;
}

.sortable.sort-asc .sort-icon {
    opacity: 1;
}

.sortable.sort-desc .sort-icon {
    opacity: 1;
}

.sortable.sort-asc .sort-icon:before {
    content: "\f0de"; /* fa-sort-up */
}

.sortable.sort-desc .sort-icon:before {
    content: "\f0dd"; /* fa-sort-down */
}
</style>
<div class="container-fluid">
    <!-- 操作工具栏 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <!-- 第一行：检索条件 -->
                            <div class="row g-2 mb-2">
                                <div class="col-md-2">
                                    <input type="text" class="form-control" id="searchRuleName" placeholder="规则名称">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="searchCity">
                                        <option value="">请选择城市</option>
                                        <!-- 城市选项通过JavaScript动态加载 -->
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="searchRuleType">
                                        <option value="">所有规则类型</option>
                                        <option value="重复收费">重复收费</option>
                                        <option value="日">日</option>
                                        <option value="周">周</option>
                                        <option value="两周">两周</option>
                                        <option value="月">月</option>
                                        <option value="年">年</option>
                                        <option value="小时">小时</option>
                                        <option value="金额">金额</option>
                                        <option value="年龄">年龄</option>
                                        <option value="性别">性别</option>
                                        <option value="住院天数">住院天数</option>
                                        <option value="合超住院天数">合超住院天数</option>
                                        <option value="频次上限">频次上限</option>
                                        <option value="天数上限">天数上限</option>
                                        <option value="全量病例">全量病例</option>
                                        <option value="组套收费">组套收费</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="searchTypes">
                                        <option value="">请选择类型</option>
                                        <option value="定量">定量</option>
                                        <option value="定性">定性</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="searchVisitType">
                                        <option value="">请选择用途</option>
                                        <option value="门诊">门诊</option>
                                        <option value="住院">住院</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="searchRuleSource">
                                        <option value="">请选择规则来源</option>
                                        <!-- 规则来源选项通过JavaScript动态加载 -->
                                    </select>
                                </div>
                            </div>
                            <!-- 第二行：状态和按钮 -->
                            <div class="row g-2">
                                <div class="col-md-2">
                                    <select class="form-select" id="searchStatus">
                                        <option value="">所有状态</option>
                                        <option value="active">启用</option>
                                        <option value="inactive">禁用</option>
                                        <option value="deleted">已删除</option>
                                    </select>
                                </div>
                                <div class="col-md-10">
                                    <button type="button" class="btn btn-primary" onclick="searchRules()">
                                        <i class="fas fa-search me-1"></i>搜索
                                    </button>
                                    <button type="button" class="btn btn-secondary ms-1" onclick="resetSearch()">
                                        <i class="fas fa-undo me-1"></i>重置
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            {% if current_user.has_permission('selfcheck.rules.create') %}
                            <button type="button" class="btn btn-success me-2" onclick="showCreateModal()">
                                <i class="fas fa-plus me-1"></i>新增规则
                            </button>
                            {% endif %}
                            {% if current_user.has_permission('selfcheck.rules.import') %}
                            <a href="{{ url_for('selfcheck.rules_import') }}" class="btn btn-info">
                                <i class="fas fa-download me-1"></i>导入规则
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 规则列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <!-- 表格上方的分页控制区域 -->
                    <div class="row mb-3 align-items-center">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0 small">每页显示:</label>
                                <select class="form-select form-select-sm" id="rulesPerPage" style="width: auto;" onchange="changeRulesPerPage()">
                                    <option value="10">10条</option>
                                    <option value="20" selected>20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <nav aria-label="规则分页">
                                <ul class="pagination pagination-sm justify-content-center mb-0" id="rulesPagination">
                                    <!-- 分页按钮将通过JavaScript生成 -->
                                </ul>
                            </nav>
                        </div>
                        <div class="col-md-3">
                            <div id="rulesPaginationInfo" class="text-end small text-muted"></div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations-toolbar mb-3" style="display: none;">
                            <div class="d-flex align-items-center">
                                <span class="me-3">已选择 <span class="selected-count">0</span> 项</span>
                                <div class="btn-group" role="group">
                                    {% if current_user.has_permission('selfcheck.rules.delete') %}
                                    <button type="button" class="btn btn-sm btn-danger" onclick="batchDeleteRules()">
                                        <i class="fas fa-trash"></i> 批量删除
                                    </button>
                                    {% endif %}
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                    取消选择
                                </button>
                            </div>
                        </div>

                        <table class="table table-striped table-hover" id="rulesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th width="25%" class="sortable" data-sort="rule_name">
                                        规则名称 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="10%" class="sortable" data-sort="rule_code">
                                        规则编码 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="8%" class="sortable" data-sort="rule_type">
                                        规则类型 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="8%" class="sortable" data-sort="city">
                                        城市 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="6%" class="sortable" data-sort="visit_type">
                                        用途 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="8%" class="sortable" data-sort="rule_source">
                                        规则来源 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="6%" class="sortable" data-sort="types">
                                        类型 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="6%" class="sortable" data-sort="status">
                                        状态 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="9%" class="sortable" data-sort="created_at">
                                        创建时间 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="10%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="rulesTableBody">
                                <!-- 数据通过AJAX加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 表格下方的分页控制区域 -->
                    <div class="row mt-3 align-items-center">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0 small">每页显示:</label>
                                <select class="form-select form-select-sm" id="rulesPerPageBottom" style="width: auto;" onchange="changeRulesPerPageFromBottom()">
                                    <option value="10">10条</option>
                                    <option value="20" selected>20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <nav aria-label="规则分页">
                                <ul class="pagination pagination-sm justify-content-center mb-0" id="rulesPaginationBottom">
                                    <!-- 分页按钮将通过JavaScript生成 -->
                                </ul>
                            </nav>
                        </div>
                        <div class="col-md-3">
                            <div id="rulesPaginationInfoBottom" class="text-end small text-muted"></div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建/编辑规则模态框 -->
<div class="modal fade" id="ruleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ruleModalTitle">新增规则</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="ruleForm">
                    <input type="hidden" id="ruleId" name="id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ruleName" class="form-label">规则名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="ruleName" name="rule_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ruleCode" class="form-label">规则编码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="ruleCode" name="rule_code" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ruleType" class="form-label">规则类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="ruleType" name="rule_type" required>
                                    <option value="">请选择规则类型</option>
                                    <option value="重复收费">重复收费</option>
                                    <option value="日">日</option>
                                    <option value="周">周</option>
                                    <option value="两周">两周</option>
                                    <option value="月">月</option>
                                    <option value="年">年</option>
                                    <option value="小时">小时</option>
                                    <option value="金额">金额</option>
                                    <option value="年龄">年龄</option>
                                    <option value="性别">性别</option>
                                    <option value="住院天数">住院天数</option>
                                    <option value="合超住院天数">合超住院天数</option>
                                    <option value="频次上限">频次上限</option>
                                    <option value="天数上限">天数上限</option>
                                    <option value="全量病例">全量病例</option>
                                    <option value="组套收费">组套收费</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ruleVersion" class="form-label">规则版本</label>
                                <input type="text" class="form-control" id="ruleVersion" name="rule_version" value="1.0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="ruleStatus" class="form-label">状态</label>
                                <select class="form-select" id="ruleStatus" name="status">
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="ruleCity" class="form-label">城市</label>
                                <select class="form-select" id="ruleCity" name="city">
                                    <option value="">请选择城市</option>
                                    <!-- 城市选项通过JavaScript动态加载 -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="ruleVisitType" class="form-label">用途</label>
                                <select class="form-select" id="ruleVisitType" name="visit_type">
                                    <option value="">请选择用途</option>
                                    <option value="门诊">门诊</option>
                                    <option value="住院">住院</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="sortOrder" class="form-label">排序</label>
                                <input type="number" class="form-control" id="sortOrder" name="sort_order" value="0">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ruleSource" class="form-label">规则来源</label>
                                <select class="form-select" id="ruleSource" name="rule_source">
                                    <option value="">请选择规则来源</option>
                                    <!-- 规则来源选项通过JavaScript动态加载 -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ruleTypes" class="form-label">类型</label>
                                <select class="form-select" id="ruleTypes" name="types">
                                    <option value="">请选择类型</option>
                                    <option value="定量">定量</option>
                                    <option value="定性">定性</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="ruleDescription" class="form-label">规则描述</label>
                        <textarea class="form-control" id="ruleDescription" name="rule_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="ruleContent" class="form-label">规则内容</label>
                        <textarea class="form-control" id="ruleContent" name="rule_content" rows="5"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRule()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 规则详情模态框 -->
<div class="modal fade" id="ruleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">规则详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="ruleDetailContent">
                <!-- 详情内容通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- SQL编辑模态框 -->
<div class="modal fade" id="sqlEditModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sqlEditModalTitle">编辑SQL语句</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="sqlEditForm">
                    <input type="hidden" id="sqlRuleId" name="rule_id">
                    <div class="mb-3">
                        <label for="sqlRuleName" class="form-label">规则名称</label>
                        <input type="text" class="form-control" id="sqlRuleName" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="sqlContent" class="form-label">SQL语句 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="sqlContent" name="sql_content" rows="15"
                                  placeholder="请输入SQL语句..." required></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            支持Oracle SQL语法，可以使用参数化查询
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sqlDbType" class="form-label">数据库类型</label>
                                <select class="form-select" id="sqlDbType" name="db_type">
                                    <option value="">请选择数据库类型</option>
                                    <option value="Oracle">Oracle</option>
                                    <option value="PostgreSQL">PostgreSQL</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sqlDbHost" class="form-label">数据库主机IP</label>
                                <input type="text" class="form-control" id="sqlDbHost" name="db_host"
                                       placeholder="如: *************" onblur="loadSqlSchemas()">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sqlDbSchema" class="form-label">站点Schema</label>
                                <select class="form-select" id="sqlDbSchema" name="db_schema">
                                    <option value="">请先输入主机IP</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- SQL测试结果显示区域 -->
                    <div id="sqlTestResultContainer" class="d-none">
                        <div class="mb-3">
                            <label class="form-label">测试结果 (仅显示前10行)</label>
                            <div id="sqlTestResult" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                <!-- 测试结果将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success me-2" onclick="testSQL()">
                    <i class="fas fa-play"></i> 测试SQL
                </button>
                <button type="button" class="btn btn-primary" onclick="saveRuleSQL()">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let totalPages = 1;
let currentPerPage = 20;
let currentSortBy = 'created_at';
let currentSortOrder = 'desc';

// 加载规则来源列表
function loadRuleSources() {
    $.get('/selfcheck/api/rule-sources')
        .done(function(response) {
            if (response.success) {
                const sources = response.rule_sources;

                // 更新搜索下拉框
                const searchSelect = $('#searchRuleSource');
                searchSelect.find('option:not(:first)').remove();
                sources.forEach(source => {
                    searchSelect.append(`<option value="${source}">${source}</option>`);
                });

                // 更新编辑表单下拉框
                const editSelect = $('#ruleSource');
                editSelect.find('option:not(:first)').remove();
                sources.forEach(source => {
                    editSelect.append(`<option value="${source}">${source}</option>`);
                });
            }
        })
        .fail(function() {
            console.error('加载规则来源列表失败');
        });
}

// 加载规则类型列表
function loadRuleTypes() {
    $.get('/selfcheck/api/rule-types')
        .done(function(response) {
            if (response.success) {
                const types = response.rule_types;

                // 更新搜索下拉框
                const searchSelect = $('#searchRuleType');
                searchSelect.find('option:not(:first)').remove();
                types.forEach(type => {
                    searchSelect.append(`<option value="${type}">${type}</option>`);
                });

                // 更新编辑表单下拉框
                const editSelect = $('#ruleType');
                editSelect.find('option:not(:first)').remove();
                types.forEach(type => {
                    editSelect.append(`<option value="${type}">${type}</option>`);
                });
            }
        })
        .fail(function() {
            console.error('加载规则类型列表失败');
        });
}

// 页面加载完成后初始化
$(document).ready(function() {
    loadCities();
    loadRuleSources();
    loadRuleTypes();
    loadRules(1);

    // 初始化排序事件
    initSortEvents();

    // 初始化所有tooltip
    $('[data-bs-toggle="tooltip"]').tooltip();

    // 数据库类型选择事件
    $('#sqlDbType').change(function() {
        $('#sqlDbSchema').html('<option value="">请先输入主机IP</option>');
        if ($(this).val() && $('#sqlDbHost').val().trim()) {
            loadSqlSchemas();
        }
    });
});

// 加载城市列表
function loadCities() {
    $.get('/selfcheck/api/cities')
        .done(function(response) {
            if (response.success) {
                const cities = response.cities;

                // 更新搜索区域的城市下拉框
                const searchCitySelect = $('#searchCity');
                searchCitySelect.find('option:not(:first)').remove(); // 保留第一个"请选择城市"选项
                cities.forEach(city => {
                    searchCitySelect.append(`<option value="${city}">${city}</option>`);
                });

                // 更新创建/编辑模态框的城市下拉框
                const ruleCitySelect = $('#ruleCity');
                ruleCitySelect.find('option:not(:first)').remove(); // 保留第一个"请选择城市"选项
                cities.forEach(city => {
                    ruleCitySelect.append(`<option value="${city}">${city}</option>`);
                });
            } else {
                console.error('加载城市列表失败:', response.message);
            }
        })
        .fail(function() {
            console.error('加载城市列表失败');
        });
}

// 加载规则列表
function loadRules(page = 1) {
    currentPage = page;

    const params = new URLSearchParams({
        page: page,
        per_page: currentPerPage,
        sort_by: currentSortBy,
        sort_order: currentSortOrder,
        rule_name: $('#searchRuleName').val(),
        rule_type: $('#searchRuleType').val(),
        status: $('#searchStatus').val(),
        city: $('#searchCity').val(),
        visit_type: $('#searchVisitType').val(),
        rule_source: $('#searchRuleSource').val(),
        types: $('#searchTypes').val()
    });

    // 移除空参数
    for (let [key, value] of [...params]) {
        if (!value) params.delete(key);
    }

    // 如果搜索已删除状态，需要包含已删除的记录
    if ($('#searchStatus').val() === 'deleted') {
        params.set('include_deleted', 'true');
    }

    $.get(`/selfcheck/api/rules?${params.toString()}`)
        .done(function(response) {
            if (response.success) {
                renderRulesTable(response.rules);
                renderRulesPagination(response.page, response.pages, response.total, response.per_page);
                updateSortIcons();
            } else {
                showAlert('加载规则列表失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载规则列表失败', 'danger');
        });
}

// 渲染规则表格
function renderRulesTable(rules) {
    const tbody = $('#rulesTableBody');
    tbody.empty();

    if (rules.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="11" class="text-center text-muted">暂无数据</td>
            </tr>
        `);
        return;
    }

    rules.forEach(rule => {
        let statusBadge;
        switch(rule.status) {
            case 'active':
                statusBadge = '<span class="badge bg-success">启用</span>';
                break;
            case 'inactive':
                statusBadge = '<span class="badge bg-secondary">禁用</span>';
                break;
            case 'deleted':
                statusBadge = '<span class="badge bg-danger">已删除</span>';
                break;
            default:
                statusBadge = '<span class="badge bg-light text-dark">未知</span>';
        }

        let actions;
        if (rule.status === 'deleted') {
            // 已删除的规则只显示查看和恢复按钮
            actions = `
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewRule(${rule.id})" title="查看" data-bs-toggle="tooltip">
                        <i class="fas fa-eye"></i>
                    </button>
                    {% if current_user.has_permission('selfcheck.rules.edit') %}
                    <button class="btn btn-sm btn-outline-success" onclick="restoreRule(${rule.id})" title="恢复" data-bs-toggle="tooltip">
                        <i class="fas fa-undo"></i>
                    </button>
                    {% endif %}
                </div>
            `;
        } else {
            // 正常状态的规则显示完整操作按钮
            actions = `
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewRule(${rule.id})" title="查看" data-bs-toggle="tooltip">
                        <i class="fas fa-eye"></i>
                    </button>
                    {% if current_user.has_permission('selfcheck.rules.edit') %}
                    <button class="btn btn-sm btn-outline-warning" onclick="editRule(${rule.id})" title="编辑" data-bs-toggle="tooltip">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="editRuleSQL(${rule.id})" title="编辑SQL" data-bs-toggle="tooltip">
                        <i class="fas fa-code"></i>
                    </button>
                    {% endif %}
                    {% if current_user.has_permission('selfcheck.rules.delete') %}
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteRule(${rule.id})" title="删除" data-bs-toggle="tooltip">
                        <i class="fas fa-trash"></i>
                    </button>
                    {% endif %}
                </div>
            `;
        }

        const row = `
            <tr>
                <td><input type="checkbox" class="rule-checkbox" value="${rule.id}" onchange="updateBatchToolbar()"></td>
                <td><span title="${rule.rule_name}" data-bs-toggle="tooltip" data-bs-placement="top">${rule.rule_name}</span></td>
                <td><code>${rule.rule_code}</code></td>
                <td><span class="badge bg-light text-dark">${rule.rule_type}</span></td>
                <td>${rule.city || '-'}</td>
                <td>${rule.visit_type || '-'}</td>
                <td>${rule.rule_source || '-'}</td>
                <td>${rule.types || '-'}</td>
                <td>${statusBadge}</td>
                <td><small class="text-muted">${formatDateTime(rule.created_at)}</small></td>
                <td>${actions}</td>
            </tr>
        `;
        tbody.append(row);
    });

    // 重置选择状态
    clearSelection();

    // 初始化tooltip
    setTimeout(() => {
        $('[data-bs-toggle="tooltip"]').tooltip();
    }, 100);
}



// 渲染规则分页
function renderRulesPagination(page, pages, total, perPage) {
    const paginationInfo = `共 ${total} 条记录，第 ${page} 页，共 ${pages} 页`;

    // 更新上方和下方的分页信息
    $('#rulesPaginationInfo').text(paginationInfo);
    $('#rulesPaginationInfoBottom').text(paginationInfo);

    // 同步每页显示数量选择器
    $('#rulesPerPage').val(perPage);
    $('#rulesPerPageBottom').val(perPage);

    // 生成分页按钮HTML
    function generatePaginationHTML() {
        if (pages <= 1) return '';

        let html = '';

        // 上一页
        html += `
            <li class="page-item ${page <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadRules(${page - 1})">上一页</a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="loadRules(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        html += `
            <li class="page-item ${page >= pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadRules(${page + 1})">下一页</a>
            </li>
        `;

        return html;
    }

    // 更新上方和下方的分页按钮
    const paginationHTML = generatePaginationHTML();
    $('#rulesPagination').html(paginationHTML);
    $('#rulesPaginationBottom').html(paginationHTML);
}

// 改变每页显示数量（上方控制）
function changeRulesPerPage() {
    const perPage = $('#rulesPerPage').val();
    $('#rulesPerPageBottom').val(perPage); // 同步下方选择器
    currentPerPage = parseInt(perPage);
    loadRules(1); // 重置到第一页
}

// 改变每页显示数量（下方控制）
function changeRulesPerPageFromBottom() {
    const perPage = $('#rulesPerPageBottom').val();
    $('#rulesPerPage').val(perPage); // 同步上方选择器
    currentPerPage = parseInt(perPage);
    loadRules(1); // 重置到第一页
}

// 搜索规则
function searchRules() {
    loadRules(1);
}

// 重置搜索
function resetSearch() {
    $('#searchRuleName').val('');
    $('#searchRuleType').val('');
    $('#searchStatus').val('');
    $('#searchCity').val(''); // 重置为第一个选项（请选择城市）
    $('#searchVisitType').val(''); // 重置为第一个选项（请选择用途）
    $('#searchRuleSource').val(''); // 重置为第一个选项（请选择规则来源）
    $('#searchTypes').val(''); // 重置为第一个选项（请选择类型）
    loadRules(1);
}

// 显示创建模态框
function showCreateModal() {
    $('#ruleModalTitle').text('新增规则');
    $('#ruleForm')[0].reset();
    $('#ruleId').val('');
    $('#ruleCity').val(''); // 重置城市选择
    $('#ruleVisitType').val(''); // 重置用途选择
    $('#ruleSource').val(''); // 重置规则来源选择
    $('#ruleTypes').val(''); // 重置类型选择
    $('#ruleModal').modal('show');
}

// 查看规则详情
function viewRule(id) {
    $.get(`/selfcheck/api/rules/${id}`)
        .done(function(response) {
            if (response.success) {
                const rule = response.rule;
                const detailHtml = `
                    <div class="row">
                        <div class="col-md-6"><strong>规则名称:</strong> ${rule.rule_name}</div>
                        <div class="col-md-6"><strong>规则编码:</strong> ${rule.rule_code}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>规则类型:</strong> ${rule.rule_type}</div>
                        <div class="col-md-6"><strong>状态:</strong> ${rule.status}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6"><strong>城市:</strong> ${rule.city || '-'}</div>
                        <div class="col-md-6"><strong>版本:</strong> ${rule.rule_version}</div>
                    </div>
                    <div class="mt-3">
                        <strong>规则描述:</strong>
                        <div class="border p-2 mt-1">${rule.rule_description || '-'}</div>
                    </div>
                    <div class="mt-3">
                        <strong>规则内容:</strong>
                        <div class="border p-2 mt-1" style="max-height: 200px; overflow-y: auto;">${rule.rule_content || '-'}</div>
                    </div>
                `;
                $('#ruleDetailContent').html(detailHtml);
                $('#ruleDetailModal').modal('show');
            } else {
                showAlert('获取规则详情失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('获取规则详情失败', 'danger');
        });
}

// 编辑规则
function editRule(id) {
    $.get(`/selfcheck/api/rules/${id}`)
        .done(function(response) {
            if (response.success) {
                const rule = response.rule;
                $('#ruleModalTitle').text('编辑规则');
                $('#ruleId').val(rule.id);
                $('#ruleName').val(rule.rule_name);
                $('#ruleCode').val(rule.rule_code);
                $('#ruleType').val(rule.rule_type);
                $('#ruleVersion').val(rule.rule_version);
                $('#ruleStatus').val(rule.status);
                $('#ruleCity').val(rule.city);
                $('#ruleVisitType').val(rule.visit_type);
                $('#ruleSource').val(rule.rule_source);
                $('#ruleTypes').val(rule.types);
                $('#sortOrder').val(rule.sort_order);
                $('#ruleDescription').val(rule.rule_description);
                $('#ruleContent').val(rule.rule_content);
                $('#ruleModal').modal('show');
            } else {
                showAlert('获取规则信息失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('获取规则信息失败', 'danger');
        });
}

// 保存规则
function saveRule() {
    const formData = {
        rule_name: $('#ruleName').val(),
        rule_code: $('#ruleCode').val(),
        rule_type: $('#ruleType').val(),
        rule_version: $('#ruleVersion').val(),
        status: $('#ruleStatus').val(),
        city: $('#ruleCity').val(),
        visit_type: $('#ruleVisitType').val(),
        rule_source: $('#ruleSource').val(),
        types: $('#ruleTypes').val(),
        sort_order: parseInt($('#sortOrder').val()) || 0,
        rule_description: $('#ruleDescription').val(),
        rule_content: $('#ruleContent').val()
    };
    
    const ruleId = $('#ruleId').val();
    const url = ruleId ? `/selfcheck/api/rules/${ruleId}` : '/selfcheck/api/rules';
    const method = ruleId ? 'PUT' : 'POST';

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify(formData)
    })
    .done(function(response) {
        if (response.success) {
            $('#ruleModal').modal('hide');
            showAlert(response.message, 'success');
            loadRules(currentPage);
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        showAlert('保存规则失败', 'danger');
    });
}

// 删除规则
function deleteRule(id) {
    if (!confirm('确定要删除这个规则吗？\n\n注意：删除后规则将被标记为已删除状态，在方案管理中会显示为删除状态，但不会影响现有的方案配置。')) return;

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: `/selfcheck/api/rules/${id}`,
        method: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            loadRules(currentPage);
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        showAlert('删除规则失败', 'danger');
    });
}

// 恢复规则
function restoreRule(id) {
    if (!confirm('确定要恢复这个规则吗？')) return;

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: `/selfcheck/api/rules/${id}/restore`,
        method: 'PUT',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            loadRules(currentPage);
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        showAlert('恢复规则失败', 'danger');
    });
}

// 使用base.html中的统一showAlert函数

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = $('#selectAll').prop('checked');
    $('.rule-checkbox').prop('checked', selectAll);
    updateBatchToolbar();
}

// 更新批量操作工具栏
function updateBatchToolbar() {
    const selectedCount = $('.rule-checkbox:checked').length;
    const toolbar = $('.batch-operations-toolbar');

    if (selectedCount > 0) {
        toolbar.show();
        toolbar.find('.selected-count').text(selectedCount);
    } else {
        toolbar.hide();
    }

    // 更新全选复选框状态
    const totalCount = $('.rule-checkbox').length;
    const allSelected = selectedCount === totalCount && totalCount > 0;
    const someSelected = selectedCount > 0 && selectedCount < totalCount;

    $('#selectAll').prop('checked', allSelected);
    $('#selectAll').prop('indeterminate', someSelected);
}

// 清除选择
function clearSelection() {
    $('.rule-checkbox').prop('checked', false);
    $('#selectAll').prop('checked', false);
    updateBatchToolbar();
}

// 批量删除规则
function batchDeleteRules() {
    const selectedIds = $('.rule-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    if (selectedIds.length === 0) {
        showAlert('请选择要删除的规则', 'warning');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedIds.length} 个规则吗？`)) return;

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/selfcheck/api/rules/batch-delete',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({ rule_ids: selectedIds })
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            clearSelection();
            loadRules(currentPage);
        } else {
            showAlert(response.message, 'danger');
        }
    })
    .fail(function() {
        showAlert('批量删除失败', 'danger');
    });
}

// 编辑规则SQL
function editRuleSQL(id) {
    $.get(`/selfcheck/api/rules/${id}`)
        .done(function(response) {
            if (response.success) {
                const rule = response.rule;
                $('#sqlEditModalTitle').text(`编辑SQL语句 - ${rule.rule_name}`);
                $('#sqlRuleId').val(rule.id);
                $('#sqlRuleName').val(rule.rule_name);
                $('#sqlContent').val(rule.sql_content || '');
                $('#sqlTemplateType').val(rule.template_name || '');
                $('#sqlVisitType').val(rule.visit_type || '');
                $('#sqlEditModal').modal('show');

                // 绑定键盘快捷键
                $('#sqlEditModal').off('keydown.sqlsave').on('keydown.sqlsave', function(e) {
                    if (e.ctrlKey && e.key === 's') {
                        e.preventDefault();
                        saveRuleSQL();
                    }
                });
            } else {
                showAlert('获取规则信息失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('获取规则信息失败', 'danger');
        });
}

// 保存规则SQL
function saveRuleSQL() {
    console.log('=== saveRuleSQL 函数被调用 ===');

    const ruleId = $('#sqlRuleId').val();
    console.log('规则ID:', ruleId);

    if (!ruleId) {
        showAlert('规则ID不能为空', 'danger');
        return;
    }

    const sqlContent = $('#sqlContent').val().trim();
    if (!sqlContent) {
        showAlert('SQL语句不能为空', 'warning');
        $('#sqlContent').focus();
        return;
    }

    const formData = {
        sql_content: sqlContent,
        template_name: $('#sqlTemplateType').val(),
        visit_type: $('#sqlVisitType').val()
    };
    console.log('表单数据:', formData);

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');
    console.log('CSRF令牌:', csrfToken);

    // 禁用保存按钮，显示加载状态
    const saveBtn = $('button[onclick="saveRuleSQL()"]');
    const originalText = saveBtn.html();
    saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');

    $.ajax({
        url: `/selfcheck/api/rules/${ruleId}/sql`,
        method: 'PUT',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify(formData)
    })
    .done(function(response) {
        if (response.success) {
            // 先显示成功提示
            showAlert('SQL语句保存成功！', 'success');

            // 延迟关闭模态框，让用户看到成功提示
            setTimeout(() => {
                $('#sqlEditModal').modal('hide');
                loadRules(currentPage);
            }, 1000);
        } else {
            showAlert(response.message || 'SQL语句保存失败', 'danger');
        }
    })
    .fail(function() {
        showAlert('保存SQL失败', 'danger');
    })
    .always(function() {
        // 恢复保存按钮状态
        saveBtn.prop('disabled', false).html(originalText);
    });
}

// 加载Schema列表
function loadSqlSchemas() {
    const dbType = $('#sqlDbType').val();
    const dbHost = $('#sqlDbHost').val().trim();

    if (!dbType || !dbHost) {
        $('#sqlDbSchema').html('<option value="">请先选择数据库类型和输入主机IP</option>');
        return;
    }

    // 显示加载状态
    $('#sqlDbSchema').html('<option value="">正在获取Schema列表...</option>').prop('disabled', true);

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/database/api/database_schemas',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            database: dbType,
            host: dbHost
        }),
        success: function(response) {
            $('#sqlDbSchema').prop('disabled', false);

            if (response.success && response.schemas) {
                $('#sqlDbSchema').html('<option value="">请选择Schema</option>');
                response.schemas.forEach(schema => {
                    $('#sqlDbSchema').append(`<option value="${schema}">${schema}</option>`);
                });
            } else {
                $('#sqlDbSchema').html('<option value="">获取Schema失败</option>');
                showAlert('获取Schema列表失败: ' + (response.error || '未知错误'), 'warning');
            }
        },
        error: function() {
            $('#sqlDbSchema').prop('disabled', false);
            $('#sqlDbSchema').html('<option value="">获取Schema失败</option>');
            showAlert('获取Schema列表失败', 'danger');
        }
    });
}

// 测试SQL
function testSQL() {
    const sqlContent = $('#sqlContent').val().trim();
    const dbType = $('#sqlDbType').val();
    const dbHost = $('#sqlDbHost').val().trim();
    const dbSchema = $('#sqlDbSchema').val();

    if (!sqlContent) {
        showAlert('请输入SQL语句', 'warning');
        return;
    }

    if (!dbType) {
        showAlert('请选择数据库类型', 'warning');
        return;
    }

    if (!dbHost) {
        showAlert('请输入数据库主机IP', 'warning');
        return;
    }

    if (!dbSchema) {
        showAlert('请选择Schema', 'warning');
        return;
    }

    // 显示加载状态
    const testBtn = $('button[onclick="testSQL()"]');
    const originalText = testBtn.html();
    testBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 测试中...');

    // 隐藏之前的测试结果
    $('#sqlTestResultContainer').addClass('d-none');

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/database/api/execute_query',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            query: sqlContent,
            export_format: 'json',
            db_type: dbType,
            db_host: dbHost,
            db_schema: dbSchema,
            limit_rows: 10  // 限制只显示10行
        }),
        success: function(response) {
            testBtn.prop('disabled', false).html(originalText);

            if (response.success) {
                displaySqlTestResult(response);
                showAlert('SQL测试成功', 'success');
            } else {
                const errorMsg = response.error || '未知错误';
                showAlert('SQL测试失败: ' + errorMsg, 'danger');

                // 显示错误信息
                $('#sqlTestResult').html(`
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> SQL执行失败</h6>
                        <p class="mb-0"><strong>错误详情：</strong></p>
                        <pre class="mt-2 mb-0" style="white-space: pre-wrap; font-size: 0.9em;">${errorMsg}</pre>
                    </div>
                `);
                $('#sqlTestResultContainer').removeClass('d-none');
            }
        },
        error: function(xhr, status, error) {
            testBtn.prop('disabled', false).html(originalText);

            let errorMsg = '网络错误，请稍后重试';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            }

            showAlert('SQL测试请求失败: ' + errorMsg, 'danger');

            // 显示错误信息
            $('#sqlTestResult').html(`
                <div class="alert alert-warning" role="alert">
                    <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> 请求处理失败</h6>
                    <p class="mb-0"><strong>错误详情：</strong></p>
                    <pre class="mt-2 mb-0" style="white-space: pre-wrap; font-size: 0.9em;">${errorMsg}</pre>
                    <hr>
                    <p class="mb-0"><small class="text-muted">状态码: ${xhr.status} | 状态: ${status}</small></p>
                </div>
            `);
            $('#sqlTestResultContainer').removeClass('d-none');
        }
    });
}

// 显示SQL测试结果
function displaySqlTestResult(response) {
    const { data, columns, total, displayed } = response;

    if (!data || data.length === 0) {
        $('#sqlTestResult').html(`
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle"></i> 查询结果为空
            </div>
        `);
        $('#sqlTestResultContainer').removeClass('d-none');
        return;
    }

    // 生成表格HTML
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-striped table-hover table-bordered table-sm">
                <thead class="table-dark">
                    <tr>
    `;

    // 生成表头
    columns.forEach(column => {
        tableHtml += `<th class="text-nowrap">${column}</th>`;
    });
    tableHtml += `</tr></thead><tbody>`;

    // 生成表体（限制显示10行）
    const displayData = data.slice(0, 10);
    displayData.forEach(row => {
        tableHtml += '<tr>';
        columns.forEach(column => {
            const value = row[column];
            let displayValue = '';

            if (value === null || value === undefined) {
                displayValue = '<span class="text-muted fst-italic">NULL</span>';
            } else {
                const strValue = String(value);
                if (strValue.length > 50) {
                    displayValue = strValue.substring(0, 50) + '<span class="text-muted">...</span>';
                } else {
                    displayValue = strValue;
                }
            }

            tableHtml += `<td title="${value || 'NULL'}" style="max-width: 200px; word-wrap: break-word;">${displayValue}</td>`;
        });
        tableHtml += '</tr>';
    });

    tableHtml += '</tbody></table></div>';

    // 添加结果信息
    let infoText = `共 <strong class="text-primary">${total || data.length}</strong> 条记录`;
    if (data.length > 10 || total > 10) {
        infoText += `，显示前 <strong class="text-success">10</strong> 条`;
    }

    const resultHtml = `
        <div class="mb-2">
            <div class="d-flex align-items-center">
                <i class="fas fa-chart-bar text-success me-1"></i>
                <span>${infoText}</span>
            </div>
        </div>
        ${tableHtml}
    `;

    $('#sqlTestResult').html(resultHtml);
    $('#sqlTestResultContainer').removeClass('d-none');
}

// 初始化排序事件
function initSortEvents() {
    $('.sortable').click(function() {
        const sortBy = $(this).data('sort');

        if (currentSortBy === sortBy) {
            // 如果点击的是当前排序字段，切换排序方向
            currentSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果点击的是新字段，设置为降序
            currentSortBy = sortBy;
            currentSortOrder = 'desc';
        }

        // 重新加载数据
        loadRules(1);
    });
}

// 更新排序图标
function updateSortIcons() {
    // 重置所有排序图标
    $('.sortable').removeClass('sort-asc sort-desc');
    $('.sort-icon').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');

    // 设置当前排序字段的图标
    const currentSortHeader = $(`.sortable[data-sort="${currentSortBy}"]`);
    if (currentSortHeader.length > 0) {
        currentSortHeader.addClass(`sort-${currentSortOrder}`);
        const icon = currentSortHeader.find('.sort-icon');
        icon.removeClass('fa-sort');
        if (currentSortOrder === 'asc') {
            icon.addClass('fa-sort-up');
        } else {
            icon.addClass('fa-sort-down');
        }
    }
}
</script>
{% endblock %}
