{% extends "base.html" %}

{% block title %}自查自纠 - MICRA飞检数据处理工具箱{% endblock %}



{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-search-plus me-2"></i>
                        自查自纠模块
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 规则管理 -->
                        {% if current_user.has_permission('selfcheck.rules.view') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-cogs fa-3x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">规则管理</h5>
                                    <p class="card-text">管理自查规则，支持从历史规则导入</p>
                                    <a href="{{ url_for('selfcheck.rules') }}" class="btn btn-primary">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        进入管理
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 方案管理 -->
                        {% if current_user.has_permission('selfcheck.schemes.view') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-layer-group fa-3x text-info"></i>
                                    </div>
                                    <h5 class="card-title">方案管理</h5>
                                    <p class="card-text">管理检查方案，组合多个规则进行批量检查</p>
                                    <a href="{{ url_for('selfcheck.schemes') }}" class="btn btn-info">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        方案管理
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 数据上传 -->
                        {% if current_user.has_permission('selfcheck.uploads.view') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-upload fa-3x text-success"></i>
                                    </div>
                                    <h5 class="card-title">数据上传</h5>
                                    <p class="card-text">上传待检查的数据文件，支持多种格式</p>
                                    <a href="{{ url_for('selfcheck.uploads') }}" class="btn btn-success">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        上传数据
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 自查任务 -->
                        {% if current_user.has_permission('selfcheck.tasks.view') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-tasks fa-3x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">自查任务</h5>
                                    <p class="card-text">创建和管理自查任务，查看检查结果</p>
                                    <a href="{{ url_for('selfcheck.tasks') }}" class="btn btn-warning">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        任务管理
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- 功能说明 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>
                                        功能说明
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <h6 class="text-primary">规则管理</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 查看和管理自查规则</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 从历史规则库导入规则</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 支持规则分类和版本管理</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-info">方案管理</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 组合多个规则成检查方案</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 规则可重复用于不同方案</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 支持方案内规则排序和启用控制</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-success">数据上传</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 支持CSV、DMP、DP、BAK格式</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 文件大小限制10MB以下</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 自动文件格式验证</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-warning">自查任务</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 基于方案批量检查数据</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 实时监控任务执行进度</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 详细的检查结果报告</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
