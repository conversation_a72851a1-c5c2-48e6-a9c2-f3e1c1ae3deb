#!/usr/bin/env python3
"""
直接查看权限数据库
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def check_permissions():
    """检查权限数据"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            
            db_manager = get_db_manager()
            
            # 查询所有权限
            query = """
            SELECT id, name, code, module, resource_type, parent_id, sort_order, is_active
            FROM permissions 
            ORDER BY module, sort_order, id
            """
            
            permissions = db_manager.execute_query(query)
            
            print('=== 权限结构分析 ===')
            print(f'总权限数: {len(permissions)}')
            
            # 按模块分组
            modules = {}
            for perm in permissions:
                module = perm.get('module') or 'other'
                if module not in modules:
                    modules[module] = []
                modules[module].append(perm)
            
            print(f'模块数: {len(modules)}')
            
            # 分析每个模块的权限结构
            for module, perms in modules.items():
                print(f'\n=== {module} 模块 ({len(perms)}个权限) ===')
                
                # 分析层级结构
                top_level = [p for p in perms if not p.get('parent_id')]
                has_parent = [p for p in perms if p.get('parent_id')]
                
                print(f'  顶级权限: {len(top_level)}个')
                print(f'  子权限: {len(has_parent)}个')
                
                # 显示所有顶级权限
                for p in top_level:
                    name = p.get('name', '')
                    pid = p.get('id', '')
                    rtype = p.get('resource_type', '')
                    code = p.get('code', '')
                    print(f'    - {name} (ID: {pid}, 类型: {rtype}, 代码: {code})')
            
            # 检查重复问题
            print('\n=== 检查重复问题 ===')
            
            # 1. 检查是否有相同名称的权限
            name_count = {}
            for perm in permissions:
                name = perm.get('name', '')
                if name not in name_count:
                    name_count[name] = []
                name_count[name].append(perm)
            
            duplicates = {name: perms for name, perms in name_count.items() if len(perms) > 1}
            if duplicates:
                print('发现重复名称的权限:')
                for name, perms in duplicates.items():
                    print(f'  权限名称: "{name}" ({len(perms)}个)')
                    for p in perms:
                        pid = p.get('id', '')
                        module = p.get('module', '')
                        rtype = p.get('resource_type', '')
                        code = p.get('code', '')
                        parent = p.get('parent_id', '')
                        print(f'    - ID: {pid}, 模块: {module}, 类型: {rtype}, 代码: {code}, 父权限: {parent}')
            else:
                print('没有发现重复名称的权限')
            
            # 2. 查找"数据处理"相关的权限
            print('\n=== "数据处理"相关权限 ===')
            data_permissions = [p for p in permissions if '数据处理' in p.get('name', '')]
            if data_permissions:
                print(f'包含"数据处理"的权限: {len(data_permissions)}个')
                for p in data_permissions:
                    name = p.get('name', '')
                    pid = p.get('id', '')
                    module = p.get('module', '')
                    rtype = p.get('resource_type', '')
                    code = p.get('code', '')
                    parent = p.get('parent_id', '')
                    parent_info = f', 父权限: {parent}' if parent else ', 顶级权限'
                    print(f'  - {name} (ID: {pid}, 模块: {module}, 类型: {rtype}, 代码: {code}{parent_info})')
            
            # 3. 查找所有模块的顶级权限
            print('\n=== 所有顶级权限 ===')
            all_top_level = [p for p in permissions if not p.get('parent_id')]
            print(f'顶级权限总数: {len(all_top_level)}个')
            for p in all_top_level:
                name = p.get('name', '')
                pid = p.get('id', '')
                module = p.get('module', '')
                rtype = p.get('resource_type', '')
                code = p.get('code', '')
                print(f'  - {name} (ID: {pid}, 模块: {module}, 类型: {rtype}, 代码: {code})')
                
        except Exception as e:
            print(f"检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_permissions()
