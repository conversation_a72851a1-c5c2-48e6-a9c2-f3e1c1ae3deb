from flask import render_template, request, jsonify, flash, redirect, url_for, make_response
from flask_login import login_required, current_user
from app.admin import bp
from app.auth.decorators import admin_required, permission_required
from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission
from app.models.audit import AuditLog
from app.auth.forms import CreateUserForm, EditUserForm, ResetPasswordForm
from app import db
from app.utils.breadcrumb import generate_breadcrumb

@bp.route('/')
@login_required
@permission_required('system')
def index():
    """系统管理模块首页"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='admin_index',
        details='访问系统管理模块首页'
    )
    breadcrumb_items = generate_breadcrumb('admin')
    return render_template('admin/index.html', breadcrumb_items=breadcrumb_items)

@bp.route('/users')
@login_required
@permission_required('system.user.view')
def users():
    """用户管理页面"""
    breadcrumb_items = generate_breadcrumb('admin', 'users')
    return render_template('admin/users.html', breadcrumb_items=breadcrumb_items)

@bp.route('/api/users')
@login_required
@permission_required('system.user.view')
def api_users():
    """获取用户列表API"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    users = User.query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'users': [user.to_dict() for user in users.items],
        'total': users.total,
        'pages': users.pages,
        'current_page': page
    })

@bp.route('/users/create', methods=['GET', 'POST'])
@login_required
@permission_required('system.user.create')
def create_user():
    """创建用户"""
    form = CreateUserForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            real_name=form.real_name.data,
            phone=form.phone.data,
            department=form.department.data,
            is_admin=form.is_admin.data
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        
        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='create_user',
            resource='user',
            resource_id=str(user.id),
            details=f'创建用户: {user.username}'
        )
        
        flash('用户创建成功', 'success')
        return redirect(url_for('admin.users'))
    
    return render_template('admin/create_user.html', form=form)

@bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('system.user.edit')
def edit_user(user_id):
    """编辑用户"""
    user = User.query.get_or_404(user_id)
    form = EditUserForm(user)
    
    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.real_name = form.real_name.data
        user.phone = form.phone.data
        user.department = form.department.data
        user.is_active = form.is_active.data
        user.is_admin = form.is_admin.data
        db.session.commit()
        
        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='edit_user',
            resource='user',
            resource_id=str(user.id),
            details=f'编辑用户: {user.username}'
        )
        
        flash('用户信息更新成功', 'success')
        return redirect(url_for('admin.users'))
    
    # 预填充表单
    if request.method == 'GET':
        form.username.data = user.username
        form.email.data = user.email
        form.real_name.data = user.real_name
        form.phone.data = user.phone
        form.department.data = user.department
        form.is_active.data = user.is_active
        form.is_admin.data = user.is_admin
    
    return render_template('admin/edit_user.html', form=form, user=user)

@bp.route('/users/<int:user_id>/reset_password', methods=['GET', 'POST'])
@login_required
@permission_required('system.user.reset_password')
def reset_password(user_id):
    """重置用户密码"""
    user = User.query.get_or_404(user_id)
    form = ResetPasswordForm()
    
    if form.validate_on_submit():
        user.set_password(form.password.data)
        user.unlock_account()  # 重置密码时解锁账户
        db.session.commit()
        
        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='reset_password',
            resource='user',
            resource_id=str(user.id),
            details=f'重置用户密码: {user.username}'
        )
        
        flash('密码重置成功', 'success')
        return redirect(url_for('admin.users'))
    
    return render_template('admin/reset_password.html', form=form, user=user)

@bp.route('/roles')
@login_required
@permission_required('system.role.view')
def roles():
    """角色管理页面"""
    breadcrumb_items = generate_breadcrumb('admin', 'roles')
    return render_template('admin/roles.html', breadcrumb_items=breadcrumb_items)

@bp.route('/permissions')
@login_required
@permission_required('system.permission.view')
def permissions():
    """权限管理页面"""
    breadcrumb_items = generate_breadcrumb('admin', 'permissions')
    return render_template('admin/permissions.html', breadcrumb_items=breadcrumb_items)

@bp.route('/audit_logs')
@login_required
@permission_required('system.audit.view')
def audit_logs():
    """审计日志页面"""
    breadcrumb_items = generate_breadcrumb('admin', 'audit_logs')
    return render_template('admin/audit_logs.html', breadcrumb_items=breadcrumb_items)

# API路由
@bp.route('/api/users', methods=['POST'])
@login_required
@permission_required('system.user.create')
def api_create_user():
    """创建用户API"""
    try:
        data = request.get_json() or request.form.to_dict()

        # 检查用户名是否已存在
        if User.query.filter_by(username=data.get('username')).first():
            return jsonify({'success': False, 'error': '用户名已存在'}), 400

        # 检查邮箱是否已存在
        if User.query.filter_by(email=data.get('email')).first():
            return jsonify({'success': False, 'error': '邮箱已存在'}), 400

        # 验证密码强度
        password = data.get('password')
        if not User.validate_password_strength(password):
            return jsonify({
                'success': False,
                'error': '密码强度不符合要求：密码必须至少8个字符，包含大写字母、小写字母、数字和特殊字符(!@#$%^&*(),.?":{}|<>)'
            }), 400

        user = User(
            username=data.get('username'),
            email=data.get('email'),
            real_name=data.get('real_name'),
            phone=data.get('phone'),
            department=data.get('department'),
            is_admin=data.get('is_admin', False)
        )
        # 直接设置密码哈希，避免重复验证
        from werkzeug.security import generate_password_hash
        user.password_hash = generate_password_hash(password)

        # 设置角色
        if data.get('role_id'):
            role = Role.query.get(data.get('role_id'))
            if role:
                user.roles.append(role)

        db.session.add(user)
        db.session.commit()

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='create_user',
            resource='user',
            resource_id=str(user.id),
            details=f'创建用户: {user.username}'
        )

        return jsonify({'success': True, 'user_id': user.id})

    except Exception as e:
        db.session.rollback()
        import traceback
        print(f"创建用户错误: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/users/<int:user_id>')
@login_required
@permission_required('system.user.view')
def api_get_user(user_id):
    """获取单个用户信息API"""
    user = User.query.get_or_404(user_id)
    return jsonify(user.to_dict())

@bp.route('/api/users/<int:user_id>', methods=['PUT'])
@login_required
@permission_required('system.user.edit')
def api_update_user(user_id):
    """更新用户API"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json() or request.form.to_dict()

        # 检查用户名是否被其他用户使用
        existing_user = User.query.filter_by(username=data.get('username')).first()
        if existing_user and existing_user.id != user_id:
            return jsonify({'success': False, 'error': '用户名已被其他用户使用'}), 400

        # 检查邮箱是否被其他用户使用
        existing_user = User.query.filter_by(email=data.get('email')).first()
        if existing_user and existing_user.id != user_id:
            return jsonify({'success': False, 'error': '邮箱已被其他用户使用'}), 400

        user.username = data.get('username')
        user.email = data.get('email')
        user.real_name = data.get('real_name')
        user.phone = data.get('phone')
        user.department = data.get('department')
        user.is_active = data.get('is_active', True)
        user.is_admin = data.get('is_admin', False)

        # 更新角色
        if data.get('role_id'):
            user.roles.clear()
            role = Role.query.get(data.get('role_id'))
            if role:
                user.roles.append(role)

        db.session.commit()

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='update_user',
            resource='user',
            resource_id=str(user.id),
            details=f'更新用户: {user.username}'
        )

        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/users/<int:user_id>', methods=['DELETE'])
@login_required
@permission_required('system.user.delete')
def api_delete_user(user_id):
    """删除用户API"""
    try:
        user = User.query.get_or_404(user_id)

        # 不能删除自己
        if user.id == current_user.id:
            return jsonify({'success': False, 'error': '不能删除自己的账户'}), 400

        username = user.username
        db.session.delete(user)
        db.session.commit()

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='delete_user',
            resource='user',
            resource_id=str(user_id),
            details=f'删除用户: {username}'
        )

        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/users/<int:user_id>/reset_password', methods=['POST'])
@login_required
@permission_required('system.user.reset_password')
def api_reset_user_password(user_id):
    """重置用户密码API"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json() or request.form.to_dict()

        # 验证密码强度
        password = data.get('password')
        if not User.validate_password_strength(password):
            return jsonify({
                'success': False,
                'error': '密码强度不符合要求：密码必须至少8个字符，包含大写字母、小写字母、数字和特殊字符(!@#$%^&*(),.?":{}|<>)'
            }), 400

        # 直接设置密码哈希，避免重复验证
        from werkzeug.security import generate_password_hash
        user.password_hash = generate_password_hash(password)
        user.unlock_account()  # 重置密码时解锁账户
        db.session.commit()

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='reset_password',
            resource='user',
            resource_id=str(user.id),
            details=f'重置用户密码: {user.username}'
        )

        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/roles')
@login_required
@permission_required('system.role.view')
def api_get_roles():
    """获取角色列表API"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    name = request.args.get('name', '')
    description = request.args.get('description', '')

    query = Role.query

    if name:
        query = query.filter(Role.name.contains(name))
    if description:
        query = query.filter(Role.description.contains(description))

    roles = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    role_list = []
    for role in roles.items:
        role_dict = role.to_dict()
        role_dict['permission_count'] = len(role.permissions)
        role_dict['user_count'] = len(role.users)
        role_list.append(role_dict)

    return jsonify({
        'roles': role_list,
        'total': roles.total,
        'pages': roles.pages,
        'current_page': page
    })

@bp.route('/api/roles', methods=['POST'])
@login_required
@permission_required('system.role.create')
def api_create_role():
    """创建角色API"""
    try:
        data = request.get_json() or request.form.to_dict()

        # 检查角色名是否已存在
        if Role.query.filter_by(name=data.get('name')).first():
            return jsonify({'success': False, 'error': '角色名已存在'}), 400

        role = Role(
            name=data.get('name'),
            description=data.get('description')
        )

        # 设置权限
        permission_ids = data.get('permission_ids', [])
        if isinstance(permission_ids, str):
            permission_ids = [int(x) for x in permission_ids.split(',') if x.strip()]

        for permission_id in permission_ids:
            permission = Permission.query.get(permission_id)
            if permission:
                role.permissions.append(permission)

        db.session.add(role)
        db.session.commit()

        # 保存上传配置
        upload_config = data.get('upload_config', {})
        if upload_config:
            from app.services.role_config_service import RoleConfigService
            RoleConfigService.set_role_upload_config(role.id, upload_config)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='create_role',
            resource='role',
            resource_id=str(role.id),
            details=f'创建角色: {role.name}'
        )

        return jsonify({'success': True, 'role_id': role.id})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/roles/<int:role_id>')
@login_required
@permission_required('system.role.view')
def api_get_role(role_id):
    """获取单个角色信息API"""
    role = Role.query.get_or_404(role_id)
    role_dict = role.to_dict()
    role_dict['permissions'] = [{'id': p.id, 'name': p.name, 'description': p.description} for p in role.permissions]
    return jsonify(role_dict)

@bp.route('/api/roles/<int:role_id>/upload-config')
@login_required
@permission_required('system.role.view')
def api_get_role_upload_config(role_id):
    """获取角色上传配置API"""
    try:
        from app.services.role_config_service import RoleConfigService
        config = RoleConfigService.get_role_upload_config(role_id)
        return jsonify({'success': True, 'config': config})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/roles/<int:role_id>', methods=['PUT'])
@login_required
@permission_required('system.role.edit')
def api_update_role(role_id):
    """更新角色API"""
    try:
        role = Role.query.get_or_404(role_id)
        data = request.get_json() or request.form.to_dict()

        # 检查角色名是否被其他角色使用
        existing_role = Role.query.filter_by(name=data.get('name')).first()
        if existing_role and existing_role.id != role_id:
            return jsonify({'success': False, 'error': '角色名已被其他角色使用'}), 400

        role.name = data.get('name')
        role.description = data.get('description')

        # 更新权限
        role.permissions.clear()
        permission_ids = data.get('permission_ids', [])
        if isinstance(permission_ids, str):
            permission_ids = [int(x) for x in permission_ids.split(',') if x.strip()]

        for permission_id in permission_ids:
            permission = Permission.query.get(permission_id)
            if permission:
                role.permissions.append(permission)

        db.session.commit()

        # 更新上传配置
        upload_config = data.get('upload_config', {})
        if upload_config:
            from app.services.role_config_service import RoleConfigService
            RoleConfigService.set_role_upload_config(role.id, upload_config)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='update_role',
            resource='role',
            resource_id=str(role.id),
            details=f'更新角色: {role.name}'
        )

        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/roles/<int:role_id>', methods=['DELETE'])
@login_required
@permission_required('system.role.delete')
def api_delete_role(role_id):
    """删除角色API"""
    try:
        role = Role.query.get_or_404(role_id)

        # 检查是否有用户使用此角色
        if role.users:
            return jsonify({'success': False, 'error': f'无法删除角色，还有{len(role.users)}个用户使用此角色'}), 400

        role_name = role.name
        db.session.delete(role)
        db.session.commit()

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='delete_role',
            resource='role',
            resource_id=str(role_id),
            details=f'删除角色: {role_name}'
        )

        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/permissions')
@login_required
@permission_required('system.permission.view')
def api_get_permissions():
    """获取权限列表API"""
    permissions = Permission.query.filter_by(is_active=True).order_by(Permission.module, Permission.sort_order).all()
    return jsonify({
        'permissions': [{
            'id': p.id,
            'name': p.name,
            'code': p.code,
            'description': p.description,
            'module': p.module,
            'resource_type': p.resource_type,
            'parent_id': p.parent_id,
            'sort_order': p.sort_order,
            'is_active': p.is_active
        } for p in permissions]
    })

@bp.route('/api/audit_logs')
@login_required
@permission_required('system.audit.view')
def api_get_audit_logs():
    """获取审计日志列表API"""
    from datetime import datetime, timedelta

    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    user = request.args.get('user', '')
    action = request.args.get('action', '')
    resource = request.args.get('resource', '')
    date_range = request.args.get('date_range', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    query = AuditLog.query

    # 用户过滤
    if user:
        query = query.join(User).filter(
            db.or_(
                User.username.contains(user),
                User.real_name.contains(user)
            )
        )

    # 操作类型过滤
    if action:
        query = query.filter(AuditLog.action == action)

    # 资源类型过滤
    if resource:
        query = query.filter(AuditLog.resource == resource)

    # 时间范围过滤
    if date_range:
        now = datetime.now()
        if date_range == 'today':
            start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            query = query.filter(AuditLog.created_at >= start)
        elif date_range == 'yesterday':
            start = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            end = now.replace(hour=0, minute=0, second=0, microsecond=0)
            query = query.filter(AuditLog.created_at >= start, AuditLog.created_at < end)
        elif date_range == 'week':
            start = now - timedelta(days=7)
            query = query.filter(AuditLog.created_at >= start)
        elif date_range == 'month':
            start = now - timedelta(days=30)
            query = query.filter(AuditLog.created_at >= start)

    # 自定义时间范围
    if start_date:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        query = query.filter(AuditLog.created_at >= start)
    if end_date:
        end = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
        query = query.filter(AuditLog.created_at < end)

    # 按时间倒序排列
    query = query.order_by(AuditLog.created_at.desc())

    logs = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    log_list = []
    for log in logs.items:
        log_dict = log.to_dict()
        # 添加用户信息
        if log.user:
            log_dict['user_name'] = log.user.username
            log_dict['user_real_name'] = log.user.real_name
        log_list.append(log_dict)

    return jsonify({
        'logs': log_list,
        'total': logs.total,
        'pages': logs.pages,
        'current_page': page
    })

@bp.route('/api/audit_logs/<int:log_id>')
@login_required
@permission_required('system.audit.view')
def api_get_audit_log(log_id):
    """获取单个审计日志详情API"""
    log = AuditLog.query.get_or_404(log_id)
    log_dict = log.to_dict()
    if log.user:
        log_dict['user_name'] = log.user.username
        log_dict['user_real_name'] = log.user.real_name
    return jsonify(log_dict)

@bp.route('/api/audit_logs/statistics')
@login_required
@permission_required('system.audit.view')
def api_get_audit_statistics():
    """获取审计日志统计信息API"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, distinct

    now = datetime.now()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

    # 总日志数
    total_logs = AuditLog.query.count()

    # 今日日志数
    today_logs = AuditLog.query.filter(AuditLog.created_at >= today_start).count()

    # 活跃用户数（今日有操作的用户）
    active_users = db.session.query(distinct(AuditLog.user_id)).filter(
        AuditLog.created_at >= today_start,
        AuditLog.user_id.isnot(None)
    ).count()

    # 成功率（假设所有记录的操作都是成功的，这里返回100%）
    success_rate = 100

    return jsonify({
        'total_logs': total_logs,
        'today_logs': today_logs,
        'active_users': active_users,
        'success_rate': success_rate
    })

@bp.route('/api/audit_logs/export')
@login_required
@permission_required('system.audit.view')
def api_export_audit_logs():
    """导出审计日志API"""
    import csv
    import io
    from datetime import datetime, timedelta

    # 获取过滤参数
    user = request.args.get('user', '')
    action = request.args.get('action', '')
    resource = request.args.get('resource', '')
    date_range = request.args.get('date_range', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    query = AuditLog.query

    # 应用相同的过滤逻辑
    if user:
        query = query.join(User).filter(
            db.or_(
                User.username.contains(user),
                User.real_name.contains(user)
            )
        )

    if action:
        query = query.filter(AuditLog.action == action)

    if resource:
        query = query.filter(AuditLog.resource == resource)

    if date_range:
        now = datetime.now()
        if date_range == 'today':
            start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            query = query.filter(AuditLog.created_at >= start)
        elif date_range == 'yesterday':
            start = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            end = now.replace(hour=0, minute=0, second=0, microsecond=0)
            query = query.filter(AuditLog.created_at >= start, AuditLog.created_at < end)
        elif date_range == 'week':
            start = now - timedelta(days=7)
            query = query.filter(AuditLog.created_at >= start)
        elif date_range == 'month':
            start = now - timedelta(days=30)
            query = query.filter(AuditLog.created_at >= start)

    if start_date:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        query = query.filter(AuditLog.created_at >= start)
    if end_date:
        end = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
        query = query.filter(AuditLog.created_at < end)

    # 按时间倒序排列，限制导出数量
    logs = query.order_by(AuditLog.created_at.desc()).limit(10000).all()

    # 创建CSV内容
    output = io.StringIO()
    writer = csv.writer(output)

    # 写入表头
    writer.writerow(['时间', '用户', '真实姓名', '操作', '资源', '资源ID', 'IP地址', '详情'])

    # 写入数据
    for log in logs:
        writer.writerow([
            log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            log.user.username if log.user else '系统',
            log.user.real_name if log.user else '',
            log.action,
            log.resource,
            log.resource_id or '',
            log.ip_address or '',
            log.details or ''
        ])

    # 创建响应
    output.seek(0)
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv; charset=utf-8'
    response.headers['Content-Disposition'] = f'attachment; filename=audit_logs_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

    return response
