#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据生成器
"""

import random
import string
import time
import csv
import json
import os
from typing import Dict, List, Any
from datetime import datetime, timedelta


class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self):
        self.cities = ["北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都", "西安", "重庆"]
        self.rule_types = ["重复收费", "频次上限", "金额限制", "年龄限制", "性别限制", "住院天数", "组套收费"]
        self.rule_sources = ["飞检规则", "内控规则", "监管规则", "系统规则", "自定义规则"]
        self.visit_types = ["住院", "门诊", "急诊", "体检"]
        self.medical_behaviors = ["诊疗", "检查", "治疗", "手术", "护理", "药品", "材料"]
        
    def generate_random_string(self, length: int = 8) -> str:
        """生成随机字符串"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    def generate_timestamp(self) -> int:
        """生成时间戳"""
        return int(time.time())
    
    def generate_rule_data(self, count: int = 1) -> List[Dict[str, Any]]:
        """生成规则测试数据"""
        rules = []
        timestamp = self.generate_timestamp()
        
        for i in range(count):
            rule = {
                "rule_name": f"测试规则_{timestamp}_{i:03d}",
                "rule_code": f"TEST_RULE_{timestamp}_{i:03d}",
                "rule_description": f"这是第{i+1}个测试规则，用于自动化测试",
                "rule_type": random.choice(self.rule_types),
                "city": random.choice(self.cities),
                "rule_source": random.choice(self.rule_sources),
                "medical_behavior": random.choice(self.medical_behaviors),
                "visit_type": random.choice(self.visit_types),
                "sql_content": self._generate_sql_content(i),
                "template_name": f"模板_{i+1}",
                "types": random.choice(["A", "B", "C"]),
                "rule_content": f"规则内容描述_{i+1}"
            }
            rules.append(rule)
        
        return rules
    
    def generate_scheme_data(self, count: int = 1) -> List[Dict[str, Any]]:
        """生成方案测试数据"""
        schemes = []
        timestamp = self.generate_timestamp()
        
        for i in range(count):
            scheme = {
                "scheme_name": f"测试方案_{timestamp}_{i:03d}",
                "scheme_code": f"TEST_SCHEME_{timestamp}_{i:03d}",
                "description": f"这是第{i+1}个测试方案，包含多个检查规则",
                "status": random.choice(["active", "inactive"]),
                "city": random.choice(self.cities),
                "created_by": 1,
                "updated_by": 1
            }
            schemes.append(scheme)
        
        return schemes
    
    def generate_upload_data(self, count: int = 1) -> List[Dict[str, Any]]:
        """生成上传记录测试数据"""
        uploads = []
        timestamp = self.generate_timestamp()
        
        for i in range(count):
            upload = {
                "file_name": f"test_data_{timestamp}_{i:03d}.csv",
                "file_type": random.choice(["csv", "dmp", "dp", "bak"]),
                "file_size": random.randint(1024, 10*1024*1024),  # 1KB - 10MB
                "status": random.choice(["pending", "processing", "completed", "failed"]),
                "record_count": random.randint(100, 10000),
                "user_id": 1
            }
            uploads.append(upload)
        
        return uploads
    
    def generate_task_data(self, count: int = 1, upload_ids: List[int] = None, 
                          scheme_ids: List[int] = None) -> List[Dict[str, Any]]:
        """生成任务测试数据"""
        tasks = []
        timestamp = self.generate_timestamp()
        
        for i in range(count):
            task = {
                "task_name": f"测试任务_{timestamp}_{i:03d}",
                "upload_id": random.choice(upload_ids) if upload_ids else 1,
                "scheme_id": random.choice(scheme_ids) if scheme_ids else 1,
                "status": random.choice(["pending", "running", "completed", "failed", "stopped"]),
                "progress": random.randint(0, 100),
                "user_id": 1
            }
            tasks.append(task)
        
        return tasks
    
    def _generate_sql_content(self, index: int) -> str:
        """生成SQL语句内容"""
        sql_templates = [
            "SELECT * FROM medical_records WHERE patient_id = {index} AND visit_date > SYSDATE - 30",
            "SELECT COUNT(*) FROM prescriptions WHERE drug_code = 'D{index:03d}' GROUP BY patient_id HAVING COUNT(*) > 1",
            "SELECT patient_id, SUM(amount) FROM charges WHERE charge_type = 'TYPE_{index}' GROUP BY patient_id",
            "SELECT * FROM diagnoses WHERE icd_code LIKE 'I{index:02d}%' AND diagnosis_date > SYSDATE - 7",
            "SELECT patient_id FROM treatments WHERE treatment_code = 'T{index:03d}' AND status = 'COMPLETED'"
        ]
        
        template = random.choice(sql_templates)
        return template.format(index=index)
    
    def generate_csv_file(self, file_path: str, row_count: int = 1000) -> str:
        """生成CSV测试文件"""
        headers = [
            "patient_id", "visit_id", "visit_date", "diagnosis_code", 
            "treatment_code", "drug_code", "amount", "quantity", "doctor_id"
        ]
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(headers)
            
            for i in range(row_count):
                row = [
                    f"P{i+1:06d}",  # patient_id
                    f"V{i+1:08d}",  # visit_id
                    (datetime.now() - timedelta(days=random.randint(0, 365))).strftime('%Y-%m-%d'),  # visit_date
                    f"I{random.randint(10, 99)}.{random.randint(0, 9)}",  # diagnosis_code
                    f"T{random.randint(1000, 9999)}",  # treatment_code
                    f"D{random.randint(100, 999)}",  # drug_code
                    round(random.uniform(10.0, 1000.0), 2),  # amount
                    random.randint(1, 10),  # quantity
                    f"DOC{random.randint(1, 100):03d}"  # doctor_id
                ]
                writer.writerow(row)
        
        return file_path
    
    def generate_json_file(self, file_path: str, data: List[Dict]) -> str:
        """生成JSON测试文件"""
        with open(file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=2)
        
        return file_path
    
    def generate_test_files(self, output_dir: str) -> Dict[str, str]:
        """生成所有测试文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        files = {}
        
        # 生成规则数据文件
        rules_data = self.generate_rule_data(50)
        files['rules'] = self.generate_json_file(
            os.path.join(output_dir, 'sample_rules.json'), 
            rules_data
        )
        
        # 生成方案数据文件
        schemes_data = self.generate_scheme_data(20)
        files['schemes'] = self.generate_json_file(
            os.path.join(output_dir, 'test_schemes.json'), 
            schemes_data
        )
        
        # 生成CSV数据文件
        files['csv_data'] = self.generate_csv_file(
            os.path.join(output_dir, 'sample_data.csv'), 
            1000
        )
        
        # 生成小型CSV文件（用于快速测试）
        files['small_csv'] = self.generate_csv_file(
            os.path.join(output_dir, 'small_data.csv'), 
            100
        )
        
        # 生成大型CSV文件（用于性能测试）
        files['large_csv'] = self.generate_csv_file(
            os.path.join(output_dir, 'large_data.csv'), 
            10000
        )
        
        return files
    
    def generate_import_rules_data(self, count: int = 100) -> List[Dict[str, Any]]:
        """生成规则导入测试数据（模拟rule_sql_history表数据）"""
        rules = []
        timestamp = self.generate_timestamp()
        
        for i in range(count):
            rule = {
                "rule_id": i + 1,
                "compare_id": f"CMP_{timestamp}_{i:04d}",
                "visit_type": random.choice(self.visit_types),
                "rule_name": f"历史规则_{timestamp}_{i:03d}",
                "rule_content": f"历史规则内容描述_{i+1}",
                "rule_type": random.choice(self.rule_types),
                "rule_source": random.choice(self.rule_sources),
                "medical_behavior": random.choice(self.medical_behaviors),
                "template_name": f"历史模板_{i+1}",
                "city": random.choice(self.cities),
                "types": random.choice(["A", "B", "C"]),
                "sql_content": self._generate_sql_content(i),
                "create_time": datetime.now() - timedelta(days=random.randint(1, 365)),
                "is_imported": random.choice([None, "Y"]) if i % 3 == 0 else None  # 1/3概率已导入
            }
            rules.append(rule)
        
        return rules
    
    def generate_performance_test_data(self, output_dir: str) -> Dict[str, str]:
        """生成性能测试数据"""
        os.makedirs(output_dir, exist_ok=True)
        
        files = {}
        
        # 大量规则数据
        large_rules = self.generate_rule_data(1000)
        files['large_rules'] = self.generate_json_file(
            os.path.join(output_dir, 'large_rules.json'),
            large_rules
        )
        
        # 大量方案数据
        large_schemes = self.generate_scheme_data(200)
        files['large_schemes'] = self.generate_json_file(
            os.path.join(output_dir, 'large_schemes.json'),
            large_schemes
        )
        
        # 超大CSV文件
        files['huge_csv'] = self.generate_csv_file(
            os.path.join(output_dir, 'huge_data.csv'),
            50000
        )
        
        return files
    
    def cleanup_test_files(self, file_paths: List[str]):
        """清理测试文件"""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                print(f"清理文件失败 {file_path}: {e}")


# 使用示例
if __name__ == "__main__":
    generator = TestDataGenerator()
    
    # 生成测试数据
    output_dir = "test_data"
    files = generator.generate_test_files(output_dir)
    
    print("生成的测试文件:")
    for file_type, file_path in files.items():
        print(f"  {file_type}: {file_path}")
    
    # 生成性能测试数据
    perf_files = generator.generate_performance_test_data(os.path.join(output_dir, "performance"))
    
    print("\n生成的性能测试文件:")
    for file_type, file_path in perf_files.items():
        print(f"  {file_type}: {file_path}")
