-- 创建智能转换工具相关表

-- 1. 创建转换上传记录表
CREATE TABLE converter_uploads (
    id NUMBER PRIMARY KEY,
    user_id NUMBER NOT NULL,
    original_filename VARCHAR2(255) NOT NULL,
    file_path VARCHAR2(500) NOT NULL,
    file_size NUMBER NOT NULL,
    file_type VARCHAR2(10) NOT NULL,
    status VARCHAR2(20) DEFAULT 'uploaded',  -- uploaded, parsing, parsed, failed
    status_message VARCHAR2(1000),
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建序列
CREATE SEQUENCE converter_uploads_seq START WITH 1 INCREMENT BY 1;

-- 创建索引
CREATE INDEX idx_converter_uploads_user ON converter_uploads(user_id);
CREATE INDEX idx_converter_uploads_status ON converter_uploads(status);
CREATE INDEX idx_converter_uploads_time ON converter_uploads(upload_time);

-- 2. 创建转换规则表
CREATE TABLE converter_rules (
    id NUMBER PRIMARY KEY,
    upload_id NUMBER NOT NULL,
    rule_name VARCHAR2(500) NOT NULL,
    rule_description CLOB,
    rule_type VARCHAR2(50),              -- 规则类型：禁止性规则、限制性规则等
    medical_category VARCHAR2(50),       -- 医疗类别：药品、诊疗、检查等
    severity VARCHAR2(10),               -- 严重程度：高、中、低
    confidence NUMBER(3,2),              -- 识别置信度 0.00-1.00
    source_info CLOB,                    -- 来源信息JSON
    original_content CLOB,               -- 原始内容
    status VARCHAR2(20) DEFAULT 'pending_review',  -- pending_review, reviewed, approved, rejected, deleted
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_converter_rules_upload FOREIGN KEY (upload_id) REFERENCES converter_uploads(id) ON DELETE CASCADE
);

-- 创建序列
CREATE SEQUENCE converter_rules_seq START WITH 1 INCREMENT BY 1;

-- 创建索引
CREATE INDEX idx_converter_rules_upload ON converter_rules(upload_id);
CREATE INDEX idx_converter_rules_status ON converter_rules(status);
CREATE INDEX idx_converter_rules_type ON converter_rules(rule_type);
CREATE INDEX idx_converter_rules_category ON converter_rules(medical_category);
CREATE INDEX idx_converter_rules_confidence ON converter_rules(confidence);

-- 3. 创建规则模板表（用于提高识别准确性）
CREATE TABLE converter_rule_templates (
    id NUMBER PRIMARY KEY,
    template_name VARCHAR2(100) NOT NULL,
    template_pattern VARCHAR2(1000) NOT NULL,  -- 正则表达式模式
    rule_type VARCHAR2(50) NOT NULL,
    medical_category VARCHAR2(50),
    keywords CLOB,                              -- 关键词JSON数组
    confidence_weight NUMBER(3,2) DEFAULT 1.0, -- 权重
    is_active NUMBER(1) DEFAULT 1,
    created_by NUMBER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建序列
CREATE SEQUENCE converter_rule_templates_seq START WITH 1 INCREMENT BY 1;

-- 创建索引
CREATE INDEX idx_converter_templates_type ON converter_rule_templates(rule_type);
CREATE INDEX idx_converter_templates_active ON converter_rule_templates(is_active);

-- 4. 创建解析日志表
CREATE TABLE converter_parse_logs (
    id NUMBER PRIMARY KEY,
    upload_id NUMBER NOT NULL,
    operation_type VARCHAR2(50) NOT NULL,  -- parse, extract, review, export
    operation_status VARCHAR2(20) NOT NULL, -- start, success, error
    message CLOB,
    details CLOB,                           -- 详细信息JSON
    execution_time NUMBER,                  -- 执行时间（毫秒）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_converter_logs_upload FOREIGN KEY (upload_id) REFERENCES converter_uploads(id) ON DELETE CASCADE
);

-- 创建序列
CREATE SEQUENCE converter_parse_logs_seq START WITH 1 INCREMENT BY 1;

-- 创建索引
CREATE INDEX idx_converter_logs_upload ON converter_parse_logs(upload_id);
CREATE INDEX idx_converter_logs_type ON converter_parse_logs(operation_type);
CREATE INDEX idx_converter_logs_status ON converter_parse_logs(operation_status);

-- 插入默认规则模板
INSERT INTO converter_rule_templates (id, template_name, template_pattern, rule_type, medical_category, keywords) VALUES 
(converter_rule_templates_seq.NEXTVAL, '禁止性药品规则', '(不得|禁止|严禁).*(药品|药物|处方)', '禁止性规则', '药品', '["药品", "药物", "处方", "用药", "不得", "禁止"]');

INSERT INTO converter_rule_templates (id, template_name, template_pattern, rule_type, medical_category, keywords) VALUES 
(converter_rule_templates_seq.NEXTVAL, '限制性诊疗规则', '(超出|超过|违反).*(诊疗|治疗|手术)', '限制性规则', '诊疗', '["诊疗", "治疗", "手术", "超出", "超过", "违反"]');

INSERT INTO converter_rule_templates (id, template_name, template_pattern, rule_type, medical_category, keywords) VALUES 
(converter_rule_templates_seq.NEXTVAL, '频次性检查规则', '(重复|多次|频繁).*(检查|检验|化验)', '频次性规则', '检查', '["检查", "检验", "化验", "重复", "多次", "频繁"]');

INSERT INTO converter_rule_templates (id, template_name, template_pattern, rule_type, medical_category, keywords) VALUES 
(converter_rule_templates_seq.NEXTVAL, '欺诈性费用规则', '(虚假|伪造|冒用).*(费用|收费|结算)', '欺诈性规则', '费用', '["费用", "收费", "结算", "虚假", "伪造", "冒用"]');

INSERT INTO converter_rule_templates (id, template_name, template_pattern, rule_type, medical_category, keywords) VALUES 
(converter_rule_templates_seq.NEXTVAL, '耗材使用规则', '(不当|违规|超量).*(耗材|器械|设备)', '限制性规则', '耗材', '["耗材", "器械", "设备", "不当", "违规", "超量"]');

COMMIT;
