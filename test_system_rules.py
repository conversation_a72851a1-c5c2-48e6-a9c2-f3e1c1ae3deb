#!/usr/bin/env python3
"""
测试系统规则页面的API调用
"""

import requests
import json
import re

def test_system_rules_api():
    base_url = "http://127.0.0.1:5002"
    session = requests.Session()
    
    # 先登录
    print("🔐 正在登录...")
    login_page = session.get(f"{base_url}/auth/login")
    
    csrf_patterns = [
        r'name="csrf_token" value="([^"]+)"',
        r'<input[^>]*name="csrf_token"[^>]*value="([^"]+)"',
    ]
    
    csrf_token = None
    for pattern in csrf_patterns:
        csrf_match = re.search(pattern, login_page.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            break
    
    login_data = {
        'username': 'admin',
        'password': 'Admin123!',
        'remember_me': False
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    response = session.post(f"{base_url}/auth/login", data=login_data, allow_redirects=True)
    
    if response.status_code == 200 and '/auth/login' not in response.url:
        print("✅ 登录成功")
    else:
        print("❌ 登录失败")
        return
    
    # 测试系统规则页面使用的API调用
    print("\n🧪 测试系统规则页面API调用...")
    
    # 模拟系统规则页面的API调用
    filters = {
        'rule_type': '',
        'behavior_type': '',
        'scope_type': '',
        'page': 1,
        'per_page': 20
    }
    
    # 移除空值
    clean_filters = {k: v for k, v in filters.items() if v != ''}
    
    print(f"调用API: /rules/api/search")
    print(f"参数: {clean_filters}")
    
    try:
        response = session.get(f"{base_url}/rules/api/search", params=clean_filters)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"返回数据类型: {type(data)}")
                print(f"success字段: {data.get('success', 'N/A')}")
                
                if data.get('success'):
                    rules = data.get('rules', [])
                    print(f"✅ 成功获取 {len(rules)} 条规则")
                    if rules:
                        print(f"第一条规则字段: {list(rules[0].keys())}")
                else:
                    print(f"❌ API返回失败: {data.get('error', '未知错误')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容前500字符: {response.text[:500]}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

if __name__ == '__main__':
    test_system_rules_api()
