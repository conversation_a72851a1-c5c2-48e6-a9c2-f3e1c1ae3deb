package com.ruoyi.selfcheck.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 数据上传记录对象 biz_upload_record
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class UploadRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 上传ID */
    private Long uploadId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 原始文件名 */
    @Excel(name = "原始文件名")
    private String fileName;

    /** 文件存储路径 */
    @Excel(name = "文件存储路径")
    private String filePath;

    /** 文件大小(字节) */
    @Excel(name = "文件大小", readConverterExp = "字=节")
    private Long fileSize;

    /** 文件类型 */
    @Excel(name = "文件类型")
    private String fileType;

    /** 上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /** 状态(0待校验 1校验中 2校验通过 3校验失败) */
    @Excel(name = "状态", readConverterExp = "0=待校验,1=校验中,2=校验通过,3=校验失败")
    private String status;

    /** 校验结果JSON */
    private String validationResult;

    /** 错误信息 */
    private String errorMessage;

    /** 用户名称 */
    private String userName;

    public void setUploadId(Long uploadId) 
    {
        this.uploadId = uploadId;
    }

    public Long getUploadId() 
    {
        return uploadId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }
    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }
    public void setFileSize(Long fileSize) 
    {
        this.fileSize = fileSize;
    }

    public Long getFileSize() 
    {
        return fileSize;
    }
    public void setFileType(String fileType) 
    {
        this.fileType = fileType;
    }

    public String getFileType() 
    {
        return fileType;
    }
    public void setUploadTime(Date uploadTime) 
    {
        this.uploadTime = uploadTime;
    }

    public Date getUploadTime() 
    {
        return uploadTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setValidationResult(String validationResult) 
    {
        this.validationResult = validationResult;
    }

    public String getValidationResult() 
    {
        return validationResult;
    }
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    @Override
    public String toString() {
        return "UploadRecord{" +
                "uploadId=" + uploadId +
                ", userId=" + userId +
                ", fileName='" + fileName + '\'' +
                ", filePath='" + filePath + '\'' +
                ", fileSize=" + fileSize +
                ", fileType='" + fileType + '\'' +
                ", uploadTime=" + uploadTime +
                ", status='" + status + '\'' +
                ", validationResult='" + validationResult + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", userName='" + userName + '\'' +
                '}';
    }
}
