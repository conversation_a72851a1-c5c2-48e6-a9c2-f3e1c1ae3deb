#!/usr/bin/env python3
"""
检查数据上传权限
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def check_upload_permissions():
    """检查数据上传权限"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            print("=== 检查数据上传权限 ===")
            
            # 需要检查的权限
            upload_permissions = [
                'selfcheck.upload.view',
                'selfcheck.upload.create', 
                'selfcheck.upload.delete'
            ]
            
            for perm_code in upload_permissions:
                query = "SELECT id, name, code, module, resource_type, parent_id, is_active FROM permissions WHERE code = :code"
                result = db_manager.execute_query(query, {'code': perm_code})
                
                if result:
                    perm = result[0]
                    active_text = '✅' if perm['is_active'] else '❌'
                    parent_text = f", 父权限: {perm['parent_id']}" if perm['parent_id'] else ", 顶级权限"
                    print(f"{active_text} {perm_code}: 存在 (ID: {perm['id']}, 名称: {perm['name']}{parent_text})")
                else:
                    print(f"❌ {perm_code}: 不存在")
            
            # 检查数据上传相关的所有权限
            print("\n=== 所有数据上传相关权限 ===")
            query = "SELECT id, name, code, module, resource_type, parent_id, is_active FROM permissions WHERE code LIKE 'selfcheck.upload%' ORDER BY code"
            all_upload_perms = db_manager.execute_query(query)
            
            if all_upload_perms:
                for perm in all_upload_perms:
                    active_text = '✅' if perm['is_active'] else '❌'
                    parent_text = f", 父权限: {perm['parent_id']}" if perm['parent_id'] else ", 顶级权限"
                    print(f"{active_text} {perm['code']}: {perm['name']} (ID: {perm['id']}, 类型: {perm['resource_type']}{parent_text})")
            else:
                print("没有找到任何数据上传相关权限")
            
            # 检查selfcheck模块的权限结构
            print("\n=== selfcheck模块权限结构 ===")
            query = """
            SELECT id, name, code, module, resource_type, parent_id, is_active 
            FROM permissions 
            WHERE module = 'selfcheck' 
            ORDER BY parent_id NULLS FIRST, code
            """
            selfcheck_perms = db_manager.execute_query(query)
            
            if selfcheck_perms:
                # 按层级显示
                top_level = [p for p in selfcheck_perms if not p['parent_id']]
                
                for top_perm in top_level:
                    active_text = '✅' if top_perm['is_active'] else '❌'
                    print(f"{active_text} {top_perm['name']} (ID: {top_perm['id']}, 代码: {top_perm['code']}, 类型: {top_perm['resource_type']})")
                    
                    # 查找子权限
                    children = [p for p in selfcheck_perms if p['parent_id'] == top_perm['id']]
                    for child in children:
                        child_active_text = '✅' if child['is_active'] else '❌'
                        print(f"  {child_active_text} {child['name']} (ID: {child['id']}, 代码: {child['code']}, 类型: {child['resource_type']})")
                        
                        # 查找孙权限
                        grandchildren = [p for p in selfcheck_perms if p['parent_id'] == child['id']]
                        for grandchild in grandchildren:
                            gc_active_text = '✅' if grandchild['is_active'] else '❌'
                            print(f"    {gc_active_text} {grandchild['name']} (ID: {grandchild['id']}, 代码: {grandchild['code']}, 类型: {grandchild['resource_type']})")
            
        except Exception as e:
            print(f"检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_upload_permissions()
