#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自查自纠模块API客户端
"""

import requests
import json
import logging
from typing import Dict, Any, List, Optional, Union
from urllib.parse import urljoin

logger = logging.getLogger(__name__)


class SelfCheckAPIClient:
    """自查自纠模块API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:5002"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
    
    def login(self, username: str, password: str) -> bool:
        """用户登录"""
        try:
            # 获取登录页面
            login_page = self.session.get(urljoin(self.base_url, "/auth/login"))
            if login_page.status_code != 200:
                return False
            
            # 提取CSRF token
            self.csrf_token = self._extract_csrf_token(login_page.text)
            
            # 执行登录
            login_data = {
                'username': username,
                'password': password,
                'csrf_token': self.csrf_token
            }
            
            response = self.session.post(
                urljoin(self.base_url, "/auth/login"),
                data=login_data,
                allow_redirects=False
            )
            
            return response.status_code in [302, 200]
            
        except Exception as e:
            logger.error(f"登录失败: {e}")
            return False
    
    def _extract_csrf_token(self, html_content: str) -> str:
        """提取CSRF token"""
        import re
        pattern = r'name="csrf_token".*?value="([^"]+)"'
        match = re.search(pattern, html_content)
        return match.group(1) if match else ""
    
    def _request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发送HTTP请求"""
        url = urljoin(self.base_url, endpoint)
        
        # 添加CSRF token到headers
        headers = kwargs.get('headers', {})
        if self.csrf_token:
            headers['X-CSRFToken'] = self.csrf_token
        kwargs['headers'] = headers
        
        response = self.session.request(method, url, **kwargs)
        logger.debug(f"{method} {url} - {response.status_code}")
        return response
    
    # ==================== 规则管理API ====================
    
    def get_rules(self, page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取规则列表"""
        params = {'page': page, 'per_page': per_page, **filters}
        response = self._request('GET', '/selfcheck/api/rules', params=params)
        return response.json() if response.status_code == 200 else {}
    
    def get_rule(self, rule_id: int) -> Dict[str, Any]:
        """获取单个规则"""
        response = self._request('GET', f'/selfcheck/api/rules/{rule_id}')
        return response.json() if response.status_code == 200 else {}
    
    def create_rule(self, rule_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建规则"""
        response = self._request('POST', '/selfcheck/api/rules', json=rule_data)
        return response.json() if response.status_code == 200 else {}
    
    def update_rule(self, rule_id: int, rule_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新规则"""
        response = self._request('PUT', f'/selfcheck/api/rules/{rule_id}', json=rule_data)
        return response.json() if response.status_code == 200 else {}
    
    def delete_rule(self, rule_id: int) -> Dict[str, Any]:
        """删除规则"""
        response = self._request('DELETE', f'/selfcheck/api/rules/{rule_id}')
        return response.json() if response.status_code == 200 else {}
    
    def restore_rule(self, rule_id: int) -> Dict[str, Any]:
        """恢复规则"""
        response = self._request('POST', f'/selfcheck/api/rules/{rule_id}/restore')
        return response.json() if response.status_code == 200 else {}
    
    def batch_delete_rules(self, rule_ids: List[int]) -> Dict[str, Any]:
        """批量删除规则"""
        data = {'rule_ids': rule_ids}
        response = self._request('POST', '/selfcheck/api/rules/batch-delete', json=data)
        return response.json() if response.status_code == 200 else {}
    
    def test_rule_sql(self, sql_content: str) -> Dict[str, Any]:
        """测试规则SQL"""
        data = {'sql_content': sql_content}
        response = self._request('POST', '/selfcheck/api/rules/test-sql', json=data)
        return response.json() if response.status_code == 200 else {}
    
    # ==================== 规则导入API ====================
    
    def get_importable_rules(self, page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取可导入规则列表"""
        params = {'page': page, 'per_page': per_page, **filters}
        response = self._request('GET', '/selfcheck/api/rules/importable', params=params)
        return response.json() if response.status_code == 200 else {}
    
    def import_rules(self, compare_ids: List[str], visit_types: List[str]) -> Dict[str, Any]:
        """导入规则"""
        data = {'compare_ids': compare_ids, 'visit_types': visit_types}
        response = self._request('POST', '/selfcheck/api/rules/import-from-history', json=data)
        return response.json() if response.status_code == 200 else {}
    
    def get_rule_sources(self) -> Dict[str, Any]:
        """获取规则来源列表"""
        response = self._request('GET', '/selfcheck/api/rules/rule-sources')
        return response.json() if response.status_code == 200 else {}
    
    def get_cities(self) -> Dict[str, Any]:
        """获取城市列表"""
        response = self._request('GET', '/selfcheck/api/rules/cities')
        return response.json() if response.status_code == 200 else {}
    
    def get_rule_types(self) -> Dict[str, Any]:
        """获取规则类型列表"""
        response = self._request('GET', '/selfcheck/api/rules/rule-types')
        return response.json() if response.status_code == 200 else {}
    
    def get_visit_types(self) -> Dict[str, Any]:
        """获取用途列表"""
        response = self._request('GET', '/selfcheck/api/rules/visit-types')
        return response.json() if response.status_code == 200 else {}
    
    # ==================== 方案管理API ====================
    
    def get_schemes(self, page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取方案列表"""
        params = {'page': page, 'per_page': per_page, **filters}
        response = self._request('GET', '/selfcheck/api/schemes', params=params)
        return response.json() if response.status_code == 200 else {}
    
    def get_scheme(self, scheme_id: int) -> Dict[str, Any]:
        """获取单个方案"""
        response = self._request('GET', f'/selfcheck/api/schemes/{scheme_id}')
        return response.json() if response.status_code == 200 else {}
    
    def create_scheme(self, scheme_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建方案"""
        response = self._request('POST', '/selfcheck/api/schemes', json=scheme_data)
        return response.json() if response.status_code == 200 else {}
    
    def update_scheme(self, scheme_id: int, scheme_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新方案"""
        response = self._request('PUT', f'/selfcheck/api/schemes/{scheme_id}', json=scheme_data)
        return response.json() if response.status_code == 200 else {}
    
    def delete_scheme(self, scheme_id: int) -> Dict[str, Any]:
        """删除方案"""
        response = self._request('DELETE', f'/selfcheck/api/schemes/{scheme_id}')
        return response.json() if response.status_code == 200 else {}
    
    def get_scheme_rules(self, scheme_id: int, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """获取方案规则列表"""
        params = {'page': page, 'per_page': per_page}
        response = self._request('GET', f'/selfcheck/api/schemes/{scheme_id}/rules', params=params)
        return response.json() if response.status_code == 200 else {}
    
    def add_scheme_rules(self, scheme_id: int, rule_ids: List[int]) -> Dict[str, Any]:
        """添加方案规则"""
        data = {'rule_ids': rule_ids}
        response = self._request('POST', f'/selfcheck/api/schemes/{scheme_id}/rules', json=data)
        return response.json() if response.status_code == 200 else {}
    
    def remove_scheme_rule(self, scheme_id: int, rule_id: int) -> Dict[str, Any]:
        """移除方案规则"""
        response = self._request('DELETE', f'/selfcheck/api/schemes/{scheme_id}/rules/{rule_id}')
        return response.json() if response.status_code == 200 else {}
    
    # ==================== 数据上传API ====================
    
    def get_uploads(self, page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取上传记录列表"""
        params = {'page': page, 'per_page': per_page, **filters}
        response = self._request('GET', '/selfcheck/api/uploads', params=params)
        return response.json() if response.status_code == 200 else {}
    
    def upload_file(self, file_path: str, file_name: str = None) -> Dict[str, Any]:
        """上传文件"""
        if file_name is None:
            import os
            file_name = os.path.basename(file_path)
        
        with open(file_path, 'rb') as f:
            files = {'file': (file_name, f)}
            response = self._request('POST', '/selfcheck/api/uploads', files=files)
        
        return response.json() if response.status_code == 200 else {}
    
    def get_upload(self, upload_id: int) -> Dict[str, Any]:
        """获取上传记录详情"""
        response = self._request('GET', f'/selfcheck/api/uploads/{upload_id}')
        return response.json() if response.status_code == 200 else {}
    
    def delete_upload(self, upload_id: int) -> Dict[str, Any]:
        """删除上传记录"""
        response = self._request('DELETE', f'/selfcheck/api/uploads/{upload_id}')
        return response.json() if response.status_code == 200 else {}
    
    # ==================== 任务管理API ====================
    
    def get_tasks(self, page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取任务列表"""
        params = {'page': page, 'per_page': per_page, **filters}
        response = self._request('GET', '/selfcheck/api/tasks', params=params)
        return response.json() if response.status_code == 200 else {}
    
    def create_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建任务"""
        response = self._request('POST', '/selfcheck/api/tasks', json=task_data)
        return response.json() if response.status_code == 200 else {}
    
    def get_task(self, task_id: int) -> Dict[str, Any]:
        """获取任务详情"""
        response = self._request('GET', f'/selfcheck/api/tasks/{task_id}')
        return response.json() if response.status_code == 200 else {}
    
    def start_task(self, task_id: int) -> Dict[str, Any]:
        """启动任务"""
        response = self._request('POST', f'/selfcheck/api/tasks/{task_id}/start')
        return response.json() if response.status_code == 200 else {}
    
    def stop_task(self, task_id: int) -> Dict[str, Any]:
        """停止任务"""
        response = self._request('POST', f'/selfcheck/api/tasks/{task_id}/stop')
        return response.json() if response.status_code == 200 else {}
    
    def delete_task(self, task_id: int) -> Dict[str, Any]:
        """删除任务"""
        response = self._request('DELETE', f'/selfcheck/api/tasks/{task_id}')
        return response.json() if response.status_code == 200 else {}
    
    def get_task_result(self, task_id: int) -> Dict[str, Any]:
        """获取任务结果"""
        response = self._request('GET', f'/selfcheck/api/tasks/{task_id}/result')
        return response.json() if response.status_code == 200 else {}
    
    # ==================== 工具方法 ====================
    
    def close(self):
        """关闭会话"""
        self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
