# MICRA自查自纠模块产品设计说明书

**文档版本：** V1.0  
**编写日期：** 2025年1月7日  
**项目名称：** MICRA工具箱 - 自查自纠模块  
**文档类型：** 产品设计说明书  

## 1. 设计概述

### 1.1 设计目标
本文档描述MICRA自查自纠模块的系统架构、技术方案、接口设计和实现细节，为开发团队提供详细的技术指导。

### 1.2 设计原则
- **模块化设计：** 功能模块独立，便于维护和扩展
- **用户体验优先：** 界面友好，操作流程简洁直观
- **性能优化：** 异步处理，响应迅速，支持大数据量
- **安全可靠：** 完善的权限控制和错误处理机制

### 1.3 技术栈
- **后端框架：** Python Flask 2.3+
- **前端技术：** Bootstrap 5 + jQuery 3.6+
- **数据库：** Oracle 11g+ (主库)
- **文件存储：** 本地文件系统
- **部署环境：** Linux + Nginx + Gunicorn

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面层     │    │   业务逻辑层     │    │   数据访问层     │
│                │    │                │    │                │
│ - 用户界面      │◄──►│ - 路由控制      │◄──►│ - 数据模型      │
│ - 交互逻辑      │    │ - 业务服务      │    │ - 数据库操作    │
│ - 状态管理      │    │ - 权限控制      │    │ - 文件操作      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 模块架构
```
自查自纠模块
├── 数据上传模块 (uploads)
│   ├── 文件上传服务
│   ├── 数据验证服务
│   └── 状态管理服务
├── 规则管理模块 (rules)
│   ├── 规则CRUD服务
│   ├── 规则分类服务
│   └── 规则导入导出服务
├── 方案管理模块 (schemes)
│   ├── 方案配置服务
│   ├── 规则关联服务
│   └── 权限管理服务
├── 任务管理模块 (tasks)
│   ├── 任务创建服务
│   ├── 任务执行服务
│   └── 任务监控服务
└── 结果管理模块 (results)
    ├── 结果展示服务
    ├── 报告生成服务
    └── 数据导出服务
```

### 2.3 数据流设计
```
用户操作 → 前端验证 → 后端路由 → 业务服务 → 数据访问 → 数据库
    ↑                                                        ↓
响应返回 ← 结果处理 ← 业务逻辑 ← 数据处理 ← 查询结果 ← 数据存储
```

## 3. 核心功能设计

### 3.1 数据上传模块设计

#### 3.1.1 文件上传流程
```python
class UploadService:
    @staticmethod
    def save_upload(file, user_id):
        # 1. 文件验证
        is_valid, message = UploadService.validate_file(file)
        
        # 2. 文件保存
        file_path = UploadService._save_file_to_disk(file, user_id)
        
        # 3. 创建记录 (status: pending)
        upload_record = SelfCheckUpload(
            user_id=user_id,
            file_name=file.filename,
            file_path=file_path,
            status='pending'
        )
        
        # 4. 异步数据处理
        UploadService._start_data_processing(upload_record.id)
        
        return upload_record.to_dict()
```

#### 3.1.2 状态流程设计
```
pending (待处理)
    ↓ 异步处理启动
validating (验证中)
    ↓ 数据处理完成
validated (已验证) / failed (失败)
```

#### 3.1.3 异步处理架构
```python
def _start_data_processing(upload_id):
    """启动异步数据处理"""
    thread = threading.Thread(
        target=_process_upload_data,
        args=(upload_id,),
        daemon=True
    )
    thread.start()

def _process_upload_data(upload_id):
    """数据处理主流程"""
    try:
        # 1. 更新状态为验证中
        _update_upload_status(upload_id, 'validating')
        
        # 2. 根据文件类型处理
        if file_type == 'csv':
            result = _process_csv_file(file_path)
        elif file_type in ['dmp', 'dp', 'bak']:
            result = _process_database_file(file_path)
        
        # 3. 更新最终状态
        final_status = 'validated' if result.success else 'failed'
        _update_upload_status(upload_id, final_status, result.message)
        
    except Exception as e:
        _update_upload_status(upload_id, 'failed', str(e))
```

### 3.2 方案管理模块设计

#### 3.2.1 序号显示优化
```javascript
// 前端序号生成逻辑
function renderSchemeRules(rules) {
    rules.forEach((rule, index) => {
        const rowNumber = index + 1;  // 行号序号
        const rowHtml = `
            <tr>
                <td><span class="badge bg-secondary">${rowNumber}</span></td>
                <td>${rule.rule_name}</td>
                <td>${rule.rule_type}</td>
                <td>${getActionButtons(rule)}</td>
            </tr>
        `;
        tbody.append(rowHtml);
    });
}
```

#### 3.2.2 并发添加优化
```javascript
// 批量添加规则 - 并发处理
function addSelectedRulesToScheme() {
    const selectedRules = getSelectedRules();
    
    // 并发发送请求
    const addPromises = selectedRules.map(ruleId => {
        return fetch(`/selfcheck/api/schemes/${schemeId}/rules`, {
            method: 'POST',
            body: JSON.stringify({ rule_id: ruleId })
        });
    });
    
    // 等待所有请求完成
    Promise.all(addPromises)
        .then(responses => {
            // 处理响应结果
            refreshSchemeRules();
        })
        .catch(error => {
            showAlert('批量添加失败', 'danger');
        });
}
```

### 3.3 前端架构设计

#### 3.3.1 模板分离架构
```html
<!-- 配置传递层 -->
<script>
window.APP_CONFIG = {
    isAdmin: {{ 'true' if current_user.is_admin else 'false' }},
    colspanCount: {{ '8' if current_user.is_admin else '7' }},
    hasDeletePermission: {{ 'true' if current_user.has_permission('selfcheck.uploads.delete') else 'false' }}
};
</script>

<!-- 纯JavaScript逻辑层 -->
<script>
// 使用配置进行条件处理
if (window.APP_CONFIG.isAdmin) {
    loadUsers();
}

// 动态生成HTML
function renderTable(data) {
    const colspan = window.APP_CONFIG.colspanCount;
    // 渲染逻辑
}
</script>
```

#### 3.3.2 错误处理机制
```javascript
// 统一错误处理
function handleApiError(response, defaultMessage) {
    if (response.success) {
        return response;
    } else {
        const message = response.message || defaultMessage;
        showAlert(message, 'danger');
        throw new Error(message);
    }
}

// API调用封装
function apiCall(url, options = {}) {
    return fetch(url, {
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken(),
            ...options.headers
        },
        ...options
    })
    .then(response => response.json())
    .then(data => handleApiError(data, '操作失败'))
    .catch(error => {
        console.error('API调用失败:', error);
        showAlert('网络错误，请重试', 'danger');
        throw error;
    });
}
```

## 4. 数据库设计要点

### 4.1 核心表结构
```sql
-- 上传记录表
CREATE TABLE selfcheck_uploads (
    id NUMBER PRIMARY KEY,
    user_id NUMBER NOT NULL,
    file_name VARCHAR2(500) NOT NULL,
    file_path VARCHAR2(1000) NOT NULL,
    file_size NUMBER,
    file_type VARCHAR2(50),
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR2(20) DEFAULT 'pending',
    validation_result CLOB,
    error_message CLOB,
    record_count NUMBER DEFAULT 0
);

-- 方案表
CREATE TABLE selfcheck_schemes (
    id NUMBER PRIMARY KEY,
    scheme_name VARCHAR2(200) NOT NULL,
    description CLOB,
    status VARCHAR2(20) DEFAULT 'active',
    created_by NUMBER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 方案规则关联表
CREATE TABLE selfcheck_scheme_rules (
    id NUMBER PRIMARY KEY,
    scheme_id NUMBER NOT NULL,
    rule_id NUMBER NOT NULL,
    sort_order NUMBER,
    is_enabled NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4.2 索引设计
```sql
-- 性能优化索引
CREATE INDEX idx_uploads_user_status ON selfcheck_uploads(user_id, status);
CREATE INDEX idx_uploads_upload_time ON selfcheck_uploads(upload_time);
CREATE INDEX idx_scheme_rules_scheme ON selfcheck_scheme_rules(scheme_id);
CREATE INDEX idx_scheme_rules_rule ON selfcheck_scheme_rules(rule_id);
```

## 5. 接口设计

### 5.1 RESTful API设计
```python
# 上传管理API
POST   /selfcheck/api/uploads          # 文件上传
GET    /selfcheck/api/uploads          # 获取上传列表
GET    /selfcheck/api/uploads/{id}     # 获取上传详情
DELETE /selfcheck/api/uploads/{id}     # 删除上传记录

# 方案管理API
GET    /selfcheck/api/schemes          # 获取方案列表
POST   /selfcheck/api/schemes          # 创建方案
GET    /selfcheck/api/schemes/{id}     # 获取方案详情
PUT    /selfcheck/api/schemes/{id}     # 更新方案
DELETE /selfcheck/api/schemes/{id}     # 删除方案

# 方案规则管理API
GET    /selfcheck/api/schemes/{id}/rules           # 获取方案规则
POST   /selfcheck/api/schemes/{id}/rules           # 添加规则到方案
DELETE /selfcheck/api/schemes/{id}/rules/{rule_id} # 从方案移除规则
```

### 5.2 响应格式标准
```json
{
    "success": true,
    "message": "操作成功",
    "data": {
        // 具体数据
    },
    "pagination": {
        "page": 1,
        "per_page": 20,
        "total": 100,
        "pages": 5
    }
}
```

## 6. 安全设计

### 6.1 权限控制
```python
# 基于装饰器的权限控制
@bp.route('/api/uploads', methods=['POST'])
@login_required
@permission_required('selfcheck.uploads.create')
def api_upload_file():
    # 文件上传逻辑
    pass

# 数据访问权限控制
def get_user_uploads(user_id, is_admin=False):
    if is_admin:
        # 管理员可查看所有记录
        return get_all_uploads()
    else:
        # 普通用户只能查看自己的记录
        return get_uploads_by_user(user_id)
```

### 6.2 数据验证
```python
# 文件上传验证
def validate_file(file):
    # 1. 文件类型验证
    allowed_types = {'csv', 'dmp', 'dp', 'bak'}
    if not file.filename.endswith(tuple(allowed_types)):
        return False, '不支持的文件类型'
    
    # 2. 文件大小验证
    if file.content_length > 10 * 1024 * 1024:
        return False, '文件大小超过10MB限制'
    
    # 3. 文件名安全验证
    if not is_safe_filename(file.filename):
        return False, '文件名包含非法字符'
    
    return True, '验证通过'
```

## 7. 性能优化设计

### 7.1 异步处理
- 文件上传后立即返回，异步处理数据验证
- 使用线程池处理并发任务
- 实现任务队列机制，支持大量文件处理

### 7.2 缓存策略
- 规则列表缓存，减少数据库查询
- 用户权限缓存，提高权限检查效率
- 静态资源缓存，提升页面加载速度

### 7.3 数据库优化
- 合理的索引设计，提高查询性能
- 分页查询，避免大数据量查询
- 连接池管理，提高数据库连接效率

## 8. 部署架构

### 8.1 部署结构
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Nginx     │    │   Gunicorn  │    │   Oracle    │
│  (反向代理)  │◄──►│  (应用服务)  │◄──►│  (数据库)   │
│   静态文件   │    │   Flask应用  │    │   数据存储   │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 8.2 配置管理
```python
# 环境配置
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY')
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')
    MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10MB
    
class DevelopmentConfig(Config):
    DEBUG = True
    DATABASE_URL = 'oracle://dev_user:password@localhost:1521/xe'
    
class ProductionConfig(Config):
    DEBUG = False
    DATABASE_URL = os.environ.get('DATABASE_URL')
```

---

**文档状态：** 已审核  
**批准人：** 技术负责人  
**批准日期：** 2025年1月7日  
**版本历史：**
- V1.0 (2025-01-07): 初始版本，包含核心设计方案
