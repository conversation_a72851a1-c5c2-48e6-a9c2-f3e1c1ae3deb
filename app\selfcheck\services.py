"""
自查自纠模块服务层
"""
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
# 移除secure_filename导入，因为它会丢失中文字符
from flask import current_app

from app.selfcheck.models import SelfCheckRule, SelfCheckUpload
from app.selfcheck.database import execute_query, get_db_manager

logger = logging.getLogger(__name__)


class RuleService:
    """规则管理服务"""
    
    @staticmethod
    def get_all_rules(page: int = 1, per_page: int = 20, sort_by: str = 'created_at', sort_order: str = 'desc', **filters) -> Dict[str, Any]:
        """获取所有规则（分页）"""
        try:
            return SelfCheckRule.get_all(page=page, per_page=per_page, sort_by=sort_by, sort_order=sort_order, **filters)
        except Exception as e:
            logger.error(f"获取规则列表失败: {str(e)}")
            raise
    
    @staticmethod
    def get_rule_by_id(rule_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取规则"""
        try:
            rule = SelfCheckRule.get_by_id(rule_id)
            return rule.to_dict() if rule else None
        except Exception as e:
            logger.error(f"获取规则详情失败: {str(e)}")
            raise
    
    @staticmethod
    def create_rule(data: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """创建规则"""
        try:
            rule = SelfCheckRule(
                rule_name=data['rule_name'],
                rule_code=data['rule_code'],
                rule_description=data.get('rule_description', ''),
                rule_type=data['rule_type'],
                rule_content=data.get('rule_content', ''),
                rule_version=data.get('rule_version', '1.0'),
                status=data.get('status', 'active'),
                city=data.get('city', ''),
                visit_type=data.get('visit_type', ''),
                rule_source=data.get('rule_source', ''),
                types=data.get('types', ''),
                sort_order=data.get('sort_order', 0),
                created_by=user_id,
                updated_by=user_id
            )
            
            if rule.save():
                return rule.to_dict()
            else:
                raise Exception("保存规则失败")
        except Exception as e:
            logger.error(f"创建规则失败: {str(e)}")
            raise
    
    @staticmethod
    def update_rule(rule_id: int, data: Dict[str, Any], user_id: int) -> Optional[Dict[str, Any]]:
        """更新规则"""
        try:
            rule = SelfCheckRule.get_by_id(rule_id)
            if not rule:
                return None
            
            # 更新字段
            for key, value in data.items():
                if hasattr(rule, key):
                    setattr(rule, key, value)
            
            rule.updated_by = user_id
            
            if rule.save():
                return rule.to_dict()
            else:
                raise Exception("更新规则失败")
        except Exception as e:
            logger.error(f"更新规则失败: {str(e)}")
            raise
    
    @staticmethod
    def delete_rule(rule_id: int) -> bool:
        """删除规则（软删除）"""
        try:
            rule = SelfCheckRule.get_by_id(rule_id)
            if not rule:
                return False
            
            # 检查规则是否已经被删除
            if rule.status == 'deleted':
                raise ValueError("该规则已经被删除")

            # 检查是否有关联的任务
            task_count_query = "SELECT COUNT(*) as count FROM selfcheck_tasks WHERE rule_id = ?"
            task_result = execute_query(task_count_query, [rule_id])
            task_count = task_result[0]['count'] if task_result else 0
            
            # 检查是否有关联的方案
            scheme_count_query = "SELECT COUNT(*) as count FROM selfcheck_scheme_rules WHERE rule_id = ?"
            scheme_result = execute_query(scheme_count_query, [rule_id])
            scheme_count = scheme_result[0]['count'] if scheme_result else 0

            if task_count > 0 or scheme_count > 0:
                # 有外键约束，使用软删除
                logger.info(f"规则 {rule_id} 被 {task_count} 个任务和 {scheme_count} 个方案使用，执行软删除")
                return rule.delete()  # 软删除
            else:
                # 没有外键约束，可以选择物理删除或软删除
                # 为了保持一致性，统一使用软删除
                logger.info(f"规则 {rule_id} 没有被其他记录引用，执行软删除")
                return rule.delete()  # 软删除

        except Exception as e:
            logger.error(f"删除规则失败: {str(e)}")
            raise
    
    @staticmethod
    def restore_rule(rule_id: int) -> bool:
        """恢复已删除的规则"""
        try:
            rule = SelfCheckRule.get_by_id(rule_id)
            if not rule:
                return False

            # 检查规则是否是删除状态
            if rule.status != 'deleted':
                raise ValueError("该规则未被删除，无需恢复")

            logger.info(f"恢复规则 {rule_id}")
            return rule.restore()

        except Exception as e:
            logger.error(f"恢复规则失败: {str(e)}")
            raise
    
    @staticmethod
    def get_importable_rules(page: int = 1, per_page: int = 20, sort_by: str = 'create_time', sort_order: str = 'desc', **filters) -> Dict[str, Any]:
        """获取可导入的历史规则"""
        try:
            # 构建查询
            where_conditions = []
            params = []
            
            # 添加过滤条件
            if filters.get('rule_name'):
                where_conditions.append("rule_name LIKE ?")
                params.append(f"%{filters['rule_name']}%")
            
            if filters.get('city'):
                where_conditions.append("city = ?")
                params.append(filters['city'])
            
            if filters.get('rule_type'):
                where_conditions.append("rule_type = ?")
                params.append(filters['rule_type'])
            
            if filters.get('rule_source'):
                where_conditions.append("rule_source = ?")
                params.append(filters['rule_source'])

            if filters.get('visit_type'):
                where_conditions.append("visit_type = ?")
                params.append(filters['visit_type'])

            if filters.get('is_imported') == 'false':
                where_conditions.append("(is_imported IS NULL OR is_imported != 'Y')")
            elif filters.get('is_imported') == 'true':
                where_conditions.append("is_imported = 'Y'")

            # 添加必须有COMPARE_ID和VISIT_TYPE的条件
            where_conditions.append("compare_id IS NOT NULL")
            where_conditions.append("visit_type IS NOT NULL")
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            # 构建排序子句
            # 映射前端字段名到数据库字段名
            sort_field_mapping = {
                'rule_name': 'rule_name',
                'rule_type': 'rule_type',
                'city': 'city',
                'medical_behavior': 'medical_behavior',
                'rule_source': 'rule_source',
                'create_time': 'create_time',
                'is_imported': 'is_imported'
            }

            # 验证排序字段
            if sort_by not in sort_field_mapping:
                sort_by = 'create_time'

            # 验证排序方向
            if sort_order.lower() not in ['asc', 'desc']:
                sort_order = 'desc'

            db_sort_field = sort_field_mapping[sort_by]
            order_clause = f"{db_sort_field} {sort_order.upper()}"
            
            # 获取总数
            count_query = f"SELECT COUNT(*) as total FROM rule_sql_history WHERE {where_clause}"
            count_result = execute_query(count_query, params)
            total = count_result[0]['total'] if count_result else 0
            
            # 获取分页数据
            offset = (page - 1) * per_page
            end_row = offset + per_page

            # 使用Oracle 11g兼容的分页查询，包含COMPARE_ID字段
            query = f"""
            SELECT * FROM (
                SELECT a.*, ROWNUM rn FROM (
                SELECT 
                    rule_id, rule_name, rule_content, rule_type,
                    rule_source, medical_behavior, template_name,
                    city, visit_type, types, sql_content,
                        create_time, is_imported, compare_id
                FROM rule_sql_history
                WHERE {where_clause}
                    ORDER BY {order_clause}
                ) a WHERE ROWNUM <= {end_row}
            ) 
            WHERE rn > {offset}
            """
            
            rules = execute_query(query, params)
            
            # 为每条规则添加唯一标识信息
            for rule in rules:
                rule['unique_key'] = f"{rule.get('compare_id', '')}_{rule.get('visit_type', '')}"

            return {
                'rules': rules,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page if total > 0 else 0
            }
        except Exception as e:
            logger.error(f"获取可导入规则失败: {str(e)}")
            raise
    
    @staticmethod
    def import_rules_from_history(compare_ids: List[str], visit_types: List[str], user_id: int) -> Dict[str, Any]:
        """从历史规则导入 - 使用单线程批量处理，避免重复导入"""
        try:
            logger.info(f"开始导入规则，compare_ids: {compare_ids}, visit_types: {visit_types}, user_id: {user_id}")

            # 使用批量导入连接池
            from app.selfcheck.database import get_batch_import_db_manager
            batch_db_manager = get_batch_import_db_manager()

            # 一次性获取所有规则信息
            if not compare_ids or not visit_types or len(compare_ids) != len(visit_types):
                return {'imported_count': 0, 'errors': ['参数不匹配或为空']}

            # 构建查询参数
            params = {}
            in_clause_parts = []
            for i, (compare_id, visit_type) in enumerate(zip(compare_ids, visit_types)):
                param_compare = f"compare_id_{i}"
                param_visit = f"visit_type_{i}"
                in_clause_parts.append(f"(:{ param_compare }, :{ param_visit })")
                params[param_compare] = compare_id
                params[param_visit] = visit_type

            in_clause = ", ".join(in_clause_parts)
            query = f"""
            SELECT rule_id, compare_id, visit_type, rule_name, rule_content, rule_type,
                   rule_source, medical_behavior, template_name, city, types, sql_content,appl_scope,selected_rule_type,sql_type
            FROM rule_sql_history
            WHERE (compare_id, visit_type) IN ({in_clause})
            """

            logger.info("批量获取规则信息...")
            results = batch_db_manager.execute_query(query, params)

            if not results:
                return {'imported_count': 0, 'errors': ['没有找到有效的规则记录']}

            logger.info(f"找到 {len(results)} 个有效的规则记录")

            # 单线程批量处理，避免重复导入和连接池问题
            imported_count = 0
            errors = []
            
            # 使用集合去重，避免重复导入
            processed_keys = set()

            import time
            base_timestamp = int(time.time() * 1000)

            for i, rule_data in enumerate(results):
                try:
                    rule_id = rule_data.get('rule_id')
                    compare_id = rule_data.get('compare_id')
                    visit_type = rule_data.get('visit_type')

                    # 使用COMPARE_ID+VISIT_TYPE作为唯一键，避免重复导入
                    unique_key = f"{compare_id}_{visit_type}"
                    if unique_key in processed_keys:
                        logger.warning(f"跳过重复规则: rule_id={rule_id}, compare_id={compare_id}, visit_type={visit_type}")
                        continue
                    
                    processed_keys.add(unique_key)
                    logger.info(f"处理规则 {i+1}/{len(results)}: rule_id={rule_id}, compare_id={compare_id}, visit_type={visit_type}")
                    
                    # 生成唯一的规则编码（使用索引确保唯一性）
                    rule_code = f"IMPORT_{compare_id}_{visit_type}_{base_timestamp}_{i}"
                    
                    # 创建新规则
                    rule = SelfCheckRule(
                        rule_name=rule_data.get('rule_name', f'导入规则_{rule_id}'),
                        rule_code=rule_code,
                        rule_description=rule_data.get('rule_content', ''),
                        rule_type=rule_data.get('rule_type', 'imported'),
                        rule_content=rule_data.get('rule_content', ''),
                        sql_content=rule_data.get('sql_content', ''),
                        city=rule_data.get('city', ''),
                        rule_source=rule_data.get('rule_source', ''),
                        medical_behavior=rule_data.get('medical_behavior', ''),
                        template_name=rule_data.get('template_name', ''),
                        visit_type=rule_data.get('visit_type', ''),
                        types=rule_data.get('types', ''),
                        appl_scope=rule_data.get('appl_scope', ''),
                        selected_rule_type=rule_data.get('selected_rule_type', ''),
                        sql_type=rule_data.get('sql_type', ''),
                        original_rule_id=rule_id,
                        created_by=user_id,
                        updated_by=user_id
                    )
                    
                    # 保存规则
                    save_success = rule.save_with_verification()
                        
                    if save_success:
                        # 标记为已导入
                        try:
                            update_query = """
                            UPDATE rule_sql_history 
                            SET is_imported = 'Y', import_time = CURRENT_TIMESTAMP 
                                WHERE compare_id = :compare_id AND visit_type = :visit_type
                                """
                            affected_rows = batch_db_manager.execute_update(update_query, {
                                'compare_id': compare_id,
                                'visit_type': visit_type
                            })

                            if affected_rows > 0:
                                imported_count += 1
                                logger.info(f"规则 {rule_id} 导入成功 ({imported_count}/{len(results)})")
                            else:
                                error_msg = f"规则 {rule_id} 标记为已导入失败：未找到对应记录"
                                logger.error(error_msg)
                                errors.append(error_msg)

                        except Exception as update_e:
                            error_msg = f"规则 {rule_id} 标记为已导入失败: {str(update_e)}"
                            logger.error(error_msg)
                            errors.append(error_msg)
                    else:
                        error_msg = f"保存规则 {rule_id} 失败"
                        logger.error(error_msg)
                        errors.append(error_msg)
                    
                except Exception as e:
                    error_msg = f"导入规则 {rule_data.get('rule_id', 'unknown')} 失败: {str(e)}"
                    logger.error(error_msg, exc_info=True)
                    errors.append(error_msg)

            # 添加未找到有效记录的错误信息
            invalid_count = len(compare_ids) - len(processed_keys)
            if invalid_count > 0:
                errors.append(f"有 {invalid_count} 个规则缺少有效的记录信息")

            result = {
                'imported_count': imported_count,
                'errors': errors
            }
            logger.info(f"导入完成，结果: {result}")
            return result
        except Exception as e:
            logger.error(f"批量导入规则失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def get_cities():
        """获取所有城市列表"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()
            query = """
            SELECT DISTINCT city
            FROM selfcheck_rules
            WHERE city IS NOT NULL
            AND TRIM(city) IS NOT NULL
            ORDER BY city
            """

            result = db_manager.execute_query(query)

            # 修正数据提取方式，result是字典列表
            cities = [row['city'] for row in result if row.get('city')]

            return cities
        except Exception as e:
            logger.error(f"获取城市列表失败: {str(e)}", exc_info=True)
            return []

    @staticmethod
    def get_visit_types():
        """获取所有用途列表"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()
            query = """
            SELECT DISTINCT visit_type
            FROM selfcheck_rules
            WHERE visit_type IS NOT NULL
            AND TRIM(visit_type) is not null
            ORDER BY visit_type
            """

            result = db_manager.execute_query(query)
            visit_types = [row['visit_type'] for row in result if row.get('visit_type')]

            return visit_types
        except Exception as e:
            logger.error(f"获取用途列表失败: {str(e)}")
            return []

    @staticmethod
    def get_rule_sources():
        """获取所有规则来源列表（从selfcheck_rules表）"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()
            query = """
            SELECT DISTINCT rule_source
            FROM selfcheck_rules
            WHERE rule_source IS NOT NULL
            AND TRIM(rule_source) IS NOT NULL
            and STATUS<>'deleted'
            ORDER BY rule_source
            """

            result = db_manager.execute_query(query)

            # 修正数据提取方式，result是字典列表
            rule_sources = [row['rule_source'] for row in result if row.get('rule_source')]

            return rule_sources
        except Exception as e:
            logger.error(f"获取规则来源列表失败: {str(e)}")
            return []

    @staticmethod
    def get_rule_sources_from_history():
        """获取所有规则来源列表（从rule_sql_history表）"""
        try:
            from app.selfcheck.database import get_batch_import_db_manager

            batch_db_manager = get_batch_import_db_manager()
            query = """
            SELECT DISTINCT rule_source
            FROM rule_sql_history
            WHERE rule_source IS NOT NULL
            AND TRIM(rule_source) IS NOT NULL
            ORDER BY rule_source
            """

            result = batch_db_manager.execute_query(query)
            rule_sources = [row['rule_source'] for row in result if row.get('rule_source')]

            return rule_sources
        except Exception as e:
            logger.error(f"获取规则来源列表失败: {str(e)}")
            return []

    @staticmethod
    def get_cities_from_history():
        """获取所有城市列表（从rule_sql_history表）"""
        try:
            from app.selfcheck.database import get_batch_import_db_manager

            batch_db_manager = get_batch_import_db_manager()
            query = """
            SELECT DISTINCT city
            FROM rule_sql_history
            WHERE city IS NOT NULL
            AND TRIM(city) IS NOT NULL
            ORDER BY city
            """

            result = batch_db_manager.execute_query(query)
            cities = [row['city'] for row in result if row.get('city')]

            return cities
        except Exception as e:
            logger.error(f"获取城市列表失败: {str(e)}")
            return []

    @staticmethod
    def get_rule_types_from_history():
        """获取所有规则类型列表（从rule_sql_history表）"""
        try:
            from app.selfcheck.database import get_batch_import_db_manager

            batch_db_manager = get_batch_import_db_manager()
            query = """
            SELECT DISTINCT rule_type
            FROM rule_sql_history
            WHERE rule_type IS NOT NULL
            AND TRIM(rule_type) IS NOT NULL
            ORDER BY rule_type
            """

            result = batch_db_manager.execute_query(query)
            rule_types = [row['rule_type'] for row in result if row.get('rule_type')]

            return rule_types
        except Exception as e:
            logger.error(f"获取规则类型列表失败: {str(e)}")
            return []

    @staticmethod
    def get_visit_types_from_history():
        """获取所有用途（就诊类型）列表（从rule_sql_history表）"""
        try:
            from app.selfcheck.database import get_batch_import_db_manager

            batch_db_manager = get_batch_import_db_manager()
            query = """
            SELECT DISTINCT visit_type
            FROM rule_sql_history
            WHERE visit_type IS NOT NULL
            AND TRIM(visit_type) IS NOT NULL
            ORDER BY visit_type
            """

            result = batch_db_manager.execute_query(query)
            visit_types = [row['visit_type'] for row in result if row.get('visit_type')]

            return visit_types
        except Exception as e:
            logger.error(f"获取用途列表失败: {str(e)}")
            return []

    @staticmethod
    def get_rule_types():
        """获取所有规则类型列表"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()
            query = """
            SELECT DISTINCT rule_type
            FROM selfcheck_rules
            WHERE rule_type IS NOT NULL
            AND TRIM(rule_type) IS NOT NULL
            ORDER BY rule_type
            """

            result = db_manager.execute_query(query)

            # 修正数据提取方式，result是字典列表
            rule_types = [row['rule_type'] for row in result if row.get('rule_type')]

            return rule_types
        except Exception as e:
            logger.error(f"获取规则类型列表失败: {str(e)}")
            return []


class UploadService:
    """文件上传服务"""

    ALLOWED_EXTENSIONS = {'csv', 'dmp', 'dp', 'bak'}
    MAX_FOLDER_SIZE = 200 * 1024 * 1024  # 200MB文件夹总大小限制
    
    @staticmethod
    def validate_file(file, user_id: int = None) -> tuple[bool, str]:
        """校验上传文件"""
        if not file or file.filename == '':
            return False, '请选择文件'

        # 获取用户对象进行权限检查
        if user_id:
            from app.models.user import User
            from app.services.role_config_service import RoleConfigService

            user = User.query.get(user_id)
            if not user:
                return False, '用户不存在'

            # 获取用户上传限制
            limits = RoleConfigService.get_user_upload_limits(user)
            allowed_types = limits.get('allowed_types', ['csv', 'dmp', 'dp', 'bak'])

            # 检查文件类型
            file_ext = UploadService._get_file_extension(file.filename).lower()
            if file_ext not in allowed_types:
                return False, f'不支持的文件类型，您可以上传: {", ".join(allowed_types)}'

            # 检查单文件大小限制
            file.seek(0, 2)  # 移动到文件末尾
            file_size = file.tell()
            file.seek(0)  # 重置到文件开头

            single_file_limit_mb = limits.get('upload_single_file_max_mb', 10)
            if single_file_limit_mb != -1 and file_size > single_file_limit_mb * 1024 * 1024:
                return False, f'单个文件大小超过限制({single_file_limit_mb}MB)'

            # 检查总大小限制
            current_usage = RoleConfigService.get_user_current_usage(user)
            max_size_mb = current_usage.get('max_size_mb', 350)
            current_size_mb = current_usage.get('current_size_mb', 0)

            if max_size_mb != -1:
                new_total_mb = current_size_mb + (file_size / 1024 / 1024)
                if new_total_mb > max_size_mb:
                    return False, f'上传后将超过总大小限制({max_size_mb}MB)，当前已使用{current_size_mb:.1f}MB'

            # 检查文件名是否已存在
            is_duplicate, message = UploadService._check_duplicate_filename(file.filename, user_id)
            if is_duplicate:
                return False, message
        else:
            # 兼容旧的验证逻辑
            if not UploadService._allowed_file(file.filename):
                return False, '不支持的文件类型，仅支持: csv, dmp, dp, bak'

        return True, '文件校验通过'
    
    @staticmethod
    def _allowed_file(filename: str) -> bool:
        """检查文件扩展名"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in UploadService.ALLOWED_EXTENSIONS
    
    @staticmethod
    def _get_file_extension(filename: str) -> str:
        """获取文件扩展名"""
        return filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

    @staticmethod
    def _sanitize_filename(filename: str) -> str:
        """清理文件名中的危险字符，但保留中文字符"""
        import re

        # 移除或替换危险字符，但保留中文字符
        # 危险字符：/ \ : * ? " < > |
        dangerous_chars = r'[/\\:*?"<>|]'

        # 替换危险字符为下划线
        safe_name = re.sub(dangerous_chars, '_', filename)

        # 移除开头和结尾的空格和点
        safe_name = safe_name.strip(' .')

        # 如果文件名为空或只有扩展名，使用默认名称
        if not safe_name or safe_name.startswith('.'):
            # 保留扩展名
            extension = ''
            if '.' in filename:
                extension = '.' + filename.rsplit('.', 1)[1]
            safe_name = f"upload_file{extension}"

        return safe_name

    @staticmethod
    def _check_duplicate_filename(filename: str, user_id: int) -> tuple[bool, str]:
        """检查文件名是否重复"""
        try:
            from app.models.user import User
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在"

            # 检查数据库中是否已有同名文件
            from app.selfcheck.models import SelfCheckUpload
            existing_upload = SelfCheckUpload.query.filter_by(
                user_id=user_id,
                file_name=filename
            ).first()

            if existing_upload:
                return True, f"文件名 '{filename}' 已存在，请重命名后再上传"

            # 检查文件系统中是否已有同名文件
            base_upload_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), 'selfcheck')
            user_upload_dir = os.path.join(base_upload_dir, user.username)

            if os.path.exists(user_upload_dir):
                existing_files = os.listdir(user_upload_dir)
                if filename in existing_files:
                    return True, f"文件名 '{filename}' 已存在，请重命名后再上传"

            return False, "文件名可用"

        except Exception as e:
            logger.error(f"检查重复文件名失败: {str(e)}")
            return False, "检查文件名失败"

    @staticmethod
    def _check_folder_size_limit(user_id: int, additional_size: int = 0) -> tuple[bool, str, dict]:
        """检查文件夹大小限制"""
        try:
            from app.models.user import User
            user = User.query.get(user_id)
            if not user:
                return False, "用户不存在", {}

            # 计算用户文件夹当前总大小
            base_upload_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), 'selfcheck')
            user_upload_dir = os.path.join(base_upload_dir, user.username)

            current_size = 0
            file_count = 0
            if os.path.exists(user_upload_dir):
                for root, _, files in os.walk(user_upload_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if os.path.exists(file_path):
                            current_size += os.path.getsize(file_path)
                            file_count += 1

            # 检查是否超过限制
            total_size = current_size + additional_size
            size_info = {
                'current_size': current_size,
                'additional_size': additional_size,
                'total_size': total_size,
                'max_size': UploadService.MAX_FOLDER_SIZE,
                'current_mb': current_size / (1024 * 1024),
                'additional_mb': additional_size / (1024 * 1024),
                'total_mb': total_size / (1024 * 1024),
                'max_mb': UploadService.MAX_FOLDER_SIZE / (1024 * 1024),
                'file_count': file_count
            }

            if total_size > UploadService.MAX_FOLDER_SIZE:
                return False, f"文件夹总大小将超过200MB限制（当前: {size_info['current_mb']:.1f}MB + 新增: {size_info['additional_mb']:.1f}MB = {size_info['total_mb']:.1f}MB）", size_info

            return True, "大小检查通过", size_info

        except Exception as e:
            logger.error(f"检查文件夹大小失败: {str(e)}")
            return False, "检查文件夹大小失败", {}

    @staticmethod
    def save_upload(file, user_id: int) -> Dict[str, Any]:
        """保存上传文件"""
        try:
            # 获取用户信息
            from app.models.user import User
            user = User.query.get(user_id)
            if not user:
                raise ValueError(f"用户不存在: {user_id}")

            # 按用户名创建上传目录
            base_upload_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), 'selfcheck')
            user_upload_dir = os.path.join(base_upload_dir, user.username)
            os.makedirs(user_upload_dir, exist_ok=True)
            upload_dir = user_upload_dir

            # 保留原始文件名
            original_filename = file.filename

            # 清理文件名中的危险字符，但保留中文字符
            safe_filename = UploadService._sanitize_filename(original_filename)
            file_path = os.path.join(upload_dir, safe_filename)

            # 检查文件名是否重复
            if os.path.exists(file_path):
                raise ValueError(f"文件名 '{original_filename}' 已存在，请重命名后再上传")

            # 获取文件大小（在保存前检查）
            file.seek(0, 2)  # 移动到文件末尾
            file_size = file.tell()
            file.seek(0)  # 重置到文件开头

            # 使用新的权限系统检查文件大小限制
            from app.models.user import User
            from app.services.role_config_service import RoleConfigService

            user = User.query.get(user_id)
            if not user:
                raise ValueError(f"用户不存在: {user_id}")

            # 获取用户上传限制
            limits = RoleConfigService.get_user_upload_limits(user)

            # 检查单文件大小限制
            single_file_limit_mb = limits.get('upload_single_file_max_mb', 10)
            if single_file_limit_mb != -1 and file_size > single_file_limit_mb * 1024 * 1024:
                raise ValueError(f'单个文件大小超过限制({single_file_limit_mb}MB)')

            # 检查总大小限制
            current_usage = RoleConfigService.get_user_current_usage(user)
            max_size_mb = current_usage.get('max_size_mb', 350)
            current_size_mb = current_usage.get('current_size_mb', 0)

            if max_size_mb != -1:
                new_total_mb = current_size_mb + (file_size / 1024 / 1024)
                if new_total_mb > max_size_mb:
                    raise ValueError(f'上传后将超过总大小限制({max_size_mb}MB)，当前已使用{current_size_mb:.1f}MB')

            # 保存文件
            file.save(file_path)

            # 创建上传记录
            upload_record = SelfCheckUpload(
                user_id=user_id,
                file_name=original_filename,  # 使用原始文件名保留中文
                file_path=file_path,
                file_size=file_size,
                file_type=UploadService._get_file_extension(original_filename),
                status='pending'  # 文件上传后初始状态为待处理
            )

            if upload_record.save():
                # 异步启动数据处理流程
                UploadService._start_data_processing(upload_record.id)
                return upload_record.to_dict()
            else:
                # 如果数据库保存失败，删除已保存的文件
                if os.path.exists(file_path):
                    os.remove(file_path)
                raise Exception("保存上传记录失败")
        except Exception as e:
            logger.error(f"保存上传文件失败: {str(e)}")
            raise

    @staticmethod
    def save_folder_upload(files, folder_name: str, user_id: int) -> Dict[str, Any]:
        """保存文件夹上传"""
        try:
            # 获取用户信息
            from app.models.user import User
            user = User.query.get(user_id)
            if not user:
                raise ValueError(f"用户不存在: {user_id}")

            # 按用户名创建上传目录
            base_upload_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), 'selfcheck')
            user_upload_dir = os.path.join(base_upload_dir, user.username)
            os.makedirs(user_upload_dir, exist_ok=True)

            # 获取用户上传限制
            from app.services.role_config_service import RoleConfigService
            limits = RoleConfigService.get_user_upload_limits(user)
            allowed_types = limits.get('allowed_types', ['csv', 'dmp', 'dp', 'bak'])

            # 过滤出支持的文件类型
            valid_files = []
            for file in files:
                # 提取纯文件名（去掉路径）
                pure_filename = file.filename.split('/')[-1] if '/' in file.filename else file.filename
                file_ext = UploadService._get_file_extension(pure_filename).lower()
                if file_ext in allowed_types:
                    # 为文件对象添加纯文件名属性
                    file.pure_filename = pure_filename
                    valid_files.append(file)

            if not valid_files:
                raise ValueError("文件夹中没有支持的文件类型（CSV、DMP、DP、BAK）")

            # 计算总文件大小
            total_size = 0
            file_sizes = {}
            for file in valid_files:
                file.seek(0, 2)  # 移动到文件末尾
                file_size = file.tell()
                file.seek(0)  # 重置到文件开头
                file_sizes[file.pure_filename] = file_size
                total_size += file_size

            # 检查文件夹总大小限制
            current_usage = RoleConfigService.get_user_current_usage(user)
            max_size_mb = current_usage.get('max_size_mb', 350)
            current_size_mb = current_usage.get('current_size_mb', 0)

            if max_size_mb != -1:
                new_total_mb = current_size_mb + (total_size / 1024 / 1024)
                if new_total_mb > max_size_mb:
                    raise ValueError(f'上传后将超过总大小限制({max_size_mb}MB)，当前已使用{current_size_mb:.1f}MB')

            # 检查文件名重复
            duplicate_files = []
            for file in valid_files:
                safe_filename = UploadService._sanitize_filename(file.pure_filename)
                file_path = os.path.join(user_upload_dir, safe_filename)
                if os.path.exists(file_path):
                    duplicate_files.append(file.pure_filename)

            if duplicate_files:
                raise ValueError(f"以下文件名已存在，请重命名后再上传：{', '.join(duplicate_files)}")

            # 保存所有文件
            uploaded_files = []
            upload_records = []

            try:
                for file in valid_files:
                    # 清理文件名
                    safe_filename = UploadService._sanitize_filename(file.pure_filename)
                    file_path = os.path.join(user_upload_dir, safe_filename)

                    # 保存文件
                    file.save(file_path)
                    uploaded_files.append(file_path)

                    # 创建上传记录
                    upload_record = SelfCheckUpload(
                        user_id=user_id,
                        file_name=file.pure_filename,  # 使用纯文件名
                        file_path=file_path,
                        file_size=file_sizes[file.pure_filename],
                        file_type=UploadService._get_file_extension(file.pure_filename),
                        status='pending'
                    )

                    if upload_record.save():
                        upload_records.append(upload_record)
                        # 异步启动数据处理流程
                        UploadService._start_data_processing(upload_record.id)
                    else:
                        raise Exception(f"保存上传记录失败: {file.pure_filename}")

                return {
                    'success': True,
                    'uploaded_count': len(upload_records),
                    'total_size': total_size,
                    'folder_name': folder_name,
                    'files': [record.to_dict() for record in upload_records]
                }

            except Exception as e:
                # 如果出错，清理已上传的文件
                for file_path in uploaded_files:
                    if os.path.exists(file_path):
                        try:
                            os.remove(file_path)
                        except:
                            pass
                raise e

        except Exception as e:
            logger.error(f"保存文件夹上传失败: {str(e)}")
            raise

    @staticmethod
    def save_multiple_files(files, user_id: int) -> Dict[str, Any]:
        """保存多文件上传（不带文件夹名前缀）"""
        try:
            # 获取用户信息
            from app.models.user import User
            user = User.query.get(user_id)
            if not user:
                raise ValueError(f"用户不存在: {user_id}")

            # 按用户名创建上传目录
            base_upload_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), 'selfcheck')
            user_upload_dir = os.path.join(base_upload_dir, user.username)
            os.makedirs(user_upload_dir, exist_ok=True)

            # 获取用户上传限制
            from app.services.role_config_service import RoleConfigService
            limits = RoleConfigService.get_user_upload_limits(user)
            allowed_types = limits.get('allowed_types', ['csv', 'dmp', 'dp', 'bak'])

            # 过滤出支持的文件类型
            valid_files = []
            for file in files:
                # 提取纯文件名（去掉路径）
                pure_filename = file.filename.split('/')[-1] if '/' in file.filename else file.filename
                file_ext = UploadService._get_file_extension(pure_filename).lower()
                if file_ext in allowed_types:
                    # 为文件对象添加纯文件名属性
                    file.pure_filename = pure_filename
                    valid_files.append(file)

            if not valid_files:
                raise ValueError("没有支持的文件类型（CSV、DMP、DP、BAK）")

            # 计算总文件大小
            total_size = 0
            file_sizes = {}
            for file in valid_files:
                file.seek(0, 2)  # 移动到文件末尾
                file_size = file.tell()
                file.seek(0)  # 重置到文件开头
                file_sizes[file.pure_filename] = file_size
                total_size += file_size

            # 检查文件夹总大小限制
            current_usage = RoleConfigService.get_user_current_usage(user)
            max_size_mb = current_usage.get('max_size_mb', 350)
            current_size_mb = current_usage.get('current_size_mb', 0)

            if max_size_mb != -1:
                new_total_mb = current_size_mb + (total_size / 1024 / 1024)
                if new_total_mb > max_size_mb:
                    raise ValueError(f'上传后将超过总大小限制({max_size_mb}MB)，当前已使用{current_size_mb:.1f}MB')

            # 检查文件名重复
            duplicate_files = []
            for file in valid_files:
                safe_filename = UploadService._sanitize_filename(file.pure_filename)
                file_path = os.path.join(user_upload_dir, safe_filename)
                if os.path.exists(file_path):
                    duplicate_files.append(file.pure_filename)

            if duplicate_files:
                raise ValueError(f"以下文件名已存在，请重命名后再上传：{', '.join(duplicate_files)}")

            # 保存所有文件
            uploaded_files = []
            upload_records = []

            try:
                for file in valid_files:
                    # 清理文件名（保持原始文件名，不添加文件夹前缀）
                    safe_filename = UploadService._sanitize_filename(file.pure_filename)
                    file_path = os.path.join(user_upload_dir, safe_filename)

                    # 保存文件
                    file.save(file_path)
                    uploaded_files.append(file_path)

                    # 创建上传记录
                    upload_record = SelfCheckUpload(
                        user_id=user_id,
                        file_name=file.pure_filename,  # 使用纯文件名
                        file_path=file_path,
                        file_size=file_sizes[file.pure_filename],
                        file_type=UploadService._get_file_extension(file.pure_filename),
                        status='pending'
                    )

                    if upload_record.save():
                        upload_records.append(upload_record)
                        # 异步启动数据处理流程
                        UploadService._start_data_processing(upload_record.id)
                    else:
                        raise Exception(f"保存上传记录失败: {file.pure_filename}")

                return {
                    'success': True,
                    'uploaded_count': len(upload_records),
                    'total_size': total_size,
                    'files': [record.to_dict() for record in upload_records]
                }

            except Exception as e:
                # 如果出错，清理已上传的文件
                for file_path in uploaded_files:
                    if os.path.exists(file_path):
                        try:
                            os.remove(file_path)
                        except:
                            pass
                raise e

        except Exception as e:
            logger.error(f"保存多文件上传失败: {str(e)}")
            raise

    @staticmethod
    def get_user_folder_info(user_id: int) -> Dict[str, Any]:
        """获取用户文件夹信息"""
        try:
            from app.models.user import User
            from app.services.role_config_service import RoleConfigService

            user = User.query.get(user_id)
            if not user:
                raise ValueError(f"用户不存在: {user_id}")

            # 使用新的权限系统获取用户使用情况
            usage_info = RoleConfigService.get_user_current_usage(user)

            return {
                'success': True,
                'current_size': usage_info.get('current_size_mb', 0) * 1024 * 1024,
                'current_mb': usage_info.get('current_size_mb', 0),
                'max_size': usage_info.get('max_size_mb', 350) * 1024 * 1024 if usage_info.get('max_size_mb', 350) != -1 else -1,
                'max_mb': usage_info.get('max_size_mb', 350),
                'available_size': (usage_info.get('max_size_mb', 350) - usage_info.get('current_size_mb', 0)) * 1024 * 1024 if usage_info.get('max_size_mb', 350) != -1 else -1,
                'available_mb': usage_info.get('max_size_mb', 350) - usage_info.get('current_size_mb', 0) if usage_info.get('max_size_mb', 350) != -1 else -1,
                'file_count': usage_info.get('current_file_count', 0),
                'usage_percentage': usage_info.get('usage_percentage', 0)
            }
        except Exception as e:
            logger.error(f"获取用户文件夹信息失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'current_size': 0,
                'current_mb': 0,
                'max_size': 350 * 1024 * 1024,
                'max_mb': 350,
                'available_size': 350 * 1024 * 1024,
                'available_mb': 350,
                'file_count': 0,
                'usage_percentage': 0
            }
    
    @staticmethod
    def _start_data_processing(upload_id: int):
        """启动数据处理流程（异步）"""
        try:
            import threading

            # 在新线程中处理数据，避免阻塞上传响应
            thread = threading.Thread(
                target=UploadService._process_upload_data,
                args=(upload_id,),
                daemon=True
            )
            thread.start()
            logger.info(f"启动数据处理线程，上传ID: {upload_id}")
        except Exception as e:
            logger.error(f"启动数据处理失败: {str(e)}")

    @staticmethod
    def _process_upload_data(upload_id: int):
        """处理上传的数据文件"""
        try:
            # 获取上传记录
            upload = SelfCheckUpload.get_by_id(upload_id)
            if not upload:
                logger.error(f"上传记录不存在: {upload_id}")
                return

            logger.info(f"开始处理上传文件: {upload.file_name}")

            # 1. 更新状态为验证中
            UploadService._update_upload_status(upload_id, 'validating', '开始验证数据文件')

            # 2. 根据文件类型进行数据导入和验证
            file_type = upload.file_type.lower()

            if file_type == 'csv':
                success, message, record_count = UploadService._process_csv_file(upload.file_path, upload_id)
            elif file_type in ['dmp', 'dp', 'bak']:
                success, message, record_count = UploadService._process_database_file(upload.file_path, file_type, upload_id)
            else:
                success, message, record_count = False, f"不支持的文件类型: {file_type}", 0

            # 3. 更新最终状态
            if success:
                UploadService._update_upload_status(
                    upload_id,
                    'validated',
                    f'数据验证通过，共处理 {record_count} 条记录',
                    record_count
                )
                logger.info(f"文件处理成功: {upload.file_name}, 记录数: {record_count}")
            else:
                UploadService._update_upload_status(
                    upload_id,
                    'failed',
                    f'数据验证失败: {message}'
                )
                logger.error(f"文件处理失败: {upload.file_name}, 错误: {message}")

        except Exception as e:
            logger.error(f"处理上传数据失败: {str(e)}")
            UploadService._update_upload_status(
                upload_id,
                'failed',
                f'处理过程中发生错误: {str(e)}'
            )

    @staticmethod
    def _update_upload_status(upload_id: int, status: str, message: str = None, record_count: int = None):
        """更新上传记录状态"""
        try:
            upload = SelfCheckUpload.get_by_id(upload_id)
            if upload:
                upload.status = status
                if message:
                    upload.validation_result = message
                if record_count is not None:
                    upload.record_count = record_count
                upload.save()
                logger.info(f"更新上传状态: ID={upload_id}, status={status}, message={message}")
        except Exception as e:
            logger.error(f"更新上传状态失败: {str(e)}")

    @staticmethod
    def _process_csv_file(file_path: str, upload_id: int = None) -> tuple[bool, str, int]:
        """处理CSV文件 - 现在包含实际的数据导入"""
        try:
            logger.info(f"开始处理CSV文件: {file_path}")

            # 如果提供了upload_id，则进行实际的数据导入
            if upload_id:
                from .data_import_service import DataImportService
                result = DataImportService.import_upload_data(upload_id)

                if result['success']:
                    return True, f"CSV数据导入成功，共导入 {result['imported_count']} 条记录", result['imported_count']
                else:
                    return False, f"CSV数据导入失败: {result['error']}", 0
            else:
                # 兼容性处理：只做基本验证
                import pandas as pd
                df = pd.read_csv(file_path, encoding='utf-8')
                record_count = len(df)

                if record_count == 0:
                    return False, "CSV文件为空", 0

                return True, "CSV文件验证通过", record_count

        except Exception as e:
            logger.error(f"CSV文件处理失败: {str(e)}", exc_info=True)
            return False, f"CSV文件处理失败: {str(e)}", 0

    @staticmethod
    def _process_database_file(file_path: str, file_type: str, upload_id: int = None) -> tuple[bool, str, int]:
        """处理数据库文件（DMP、DP、BAK）- 现在包含实际的数据导入"""
        try:
            import os
            logger.info(f"开始处理{file_type.upper()}文件: {file_path}")

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False, f"{file_type.upper()}文件为空", 0

            # 如果提供了upload_id，则进行实际的数据导入
            if upload_id:
                from .data_import_service import DataImportService
                result = DataImportService.import_upload_data(upload_id)

                if result['success']:
                    return True, f"{file_type.upper()}数据导入成功，共导入 {result['imported_count']} 条记录", result['imported_count']
                else:
                    return False, f"{file_type.upper()}数据导入失败: {result['error']}", 0
            else:
                # 兼容性处理：只做基本验证
                # 模拟记录数（实际应该从文件中解析）
                record_count = file_size // 100  # 简单估算
                return True, f"{file_type.upper()}文件验证通过", record_count

        except Exception as e:
            logger.error(f"{file_type.upper()}文件处理失败: {str(e)}", exc_info=True)
            return False, f"{file_type.upper()}文件处理失败: {str(e)}", 0
    
    @staticmethod
    def get_all_uploads(page: int = 1, per_page: int = 20, user_id: Optional[int] = None, selected_user_id: Optional[int] = None, **filters) -> Dict[str, Any]:
        """获取所有上传记录（分页）"""
        try:
            # 如果指定了selected_user_id，则查看该用户的文件
            target_user_id = selected_user_id if selected_user_id else user_id
            return SelfCheckUpload.get_all(page=page, per_page=per_page, user_id=target_user_id, **filters)
        except Exception as e:
            logger.error(f"获取上传记录列表失败: {str(e)}")
            raise
    
    @staticmethod
    def get_upload_by_id(upload_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取上传记录"""
        try:
            upload = SelfCheckUpload.get_by_id(upload_id)
            return upload.to_dict() if upload else None
        except Exception as e:
            logger.error(f"获取上传记录详情失败: {str(e)}")
            raise
    
    @staticmethod
    def delete_upload(upload_id: int) -> bool:
        """删除上传记录"""
        try:
            logger.info(f"开始删除上传记录: {upload_id}")

            upload = SelfCheckUpload.get_by_id(upload_id)
            if not upload:
                logger.warning(f"上传记录不存在: {upload_id}")
                return False

            logger.info(f"找到上传记录: {upload.file_name}, 路径: {upload.file_path}")

            # 检查是否有关联的任务
            task_count_query = "SELECT COUNT(*) as count FROM selfcheck_tasks WHERE upload_id = ?"
            task_result = execute_query(task_count_query, [upload_id])
            task_count = task_result[0]['count'] if task_result else 0

            if task_count > 0:
                logger.warning(f"上传记录被 {task_count} 个任务使用，无法删除: {upload_id}")
                raise ValueError(f"该上传记录已被 {task_count} 个任务使用，无法删除")

            # 删除物理文件
            if upload.file_path and os.path.exists(upload.file_path):
                try:
                    os.remove(upload.file_path)
                    logger.info(f"物理文件删除成功: {upload.file_path}")
                except OSError as e:
                    logger.error(f"删除物理文件失败: {upload.file_path}, 错误: {str(e)}")
                    # 即使物理文件删除失败，也继续删除数据库记录
            else:
                logger.warning(f"物理文件不存在或路径为空: {upload.file_path}")

            # 删除数据库记录
            result = upload.delete()
            if result:
                logger.info(f"数据库记录删除成功: {upload_id}")
            else:
                logger.error(f"数据库记录删除失败: {upload_id}")

            return result
        except Exception as e:
            logger.error(f"删除上传记录失败: {upload_id}, 错误: {str(e)}")
            raise

    @staticmethod
    def get_all_users_with_uploads() -> List[Dict[str, Any]]:
        """获取所有有上传记录的用户列表（只包含有实际文件的用户）"""
        try:
            from app.selfcheck.database import get_db_manager
            import os
            from flask import current_app

            db_manager = get_db_manager()
            query = """
            SELECT DISTINCT u.id, u.username, u.real_name, COUNT(up.id) as upload_count
            FROM users u
            INNER JOIN selfcheck_uploads up ON u.id = up.user_id
            GROUP BY u.id, u.username, u.real_name
            ORDER BY u.username
            """

            result = db_manager.execute_query(query)

            # 进一步过滤：只返回在文件系统中有对应文件夹且文件夹不为空的用户
            filtered_result = []
            base_upload_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), 'selfcheck')

            for user in result:
                user_upload_dir = os.path.join(base_upload_dir, user['username'])
                # 检查用户文件夹是否存在且不为空
                if os.path.exists(user_upload_dir) and os.path.isdir(user_upload_dir):
                    files = os.listdir(user_upload_dir)
                    # 过滤掉隐藏文件和临时文件
                    actual_files = [f for f in files if not f.startswith('.') and not f.endswith('.tmp')]
                    if actual_files:
                        # 更新实际文件数量
                        user['upload_count'] = len(actual_files)
                        filtered_result.append(user)

            return filtered_result
        except Exception as e:
            logger.error(f"获取用户列表失败: {str(e)}")
            return []


class DataSourceService:
    """数据源管理服务"""

    @staticmethod
    def get_available_data_sources(user_id: int) -> List[Dict[str, Any]]:
        """获取用户可用的数据源列表"""
        try:
            import configparser
            import os

            # 读取配置文件
            config = configparser.ConfigParser()
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config.ini')
            config.read(config_path, encoding='utf-8')

            data_sources = []

            # 获取Oracle连接池配置（除了orcl以外的实例）
            if 'oracle_pools' in config.sections():
                for pool_name in config.options('oracle_pools'):
                    pool_config = eval(config.get('oracle_pools', pool_name))
                    dsn = pool_config.get('dsn', '')

                    # 排除orcl实例
                    if 'orcl' not in dsn.lower():
                        # 提取实例名称
                        instance_name = dsn.split('/')[-1] if '/' in dsn else dsn.split(':')[-1]

                        data_sources.append({
                            'id': pool_name,
                            'name': f"Oracle-{instance_name}",
                            'type': 'oracle',
                            'dsn': dsn,
                            'description': f"Oracle数据库实例: {instance_name}"
                        })

            # 获取PostgreSQL连接池配置
            if 'postgresql_pools' in config.sections():
                for pool_name in config.options('postgresql_pools'):
                    pool_config = eval(config.get('postgresql_pools', pool_name))
                    host = pool_config.get('host', '')
                    dbname = pool_config.get('dbname', '')

                    data_sources.append({
                        'id': pool_name,
                        'name': f"PostgreSQL-{dbname}",
                        'type': 'postgresql',
                        'host': host,
                        'dbname': dbname,
                        'description': f"PostgreSQL数据库: {dbname}@{host}"
                    })

            # 获取MySQL连接池配置
            if 'mysql_pools' in config.sections():
                for pool_name in config.options('mysql_pools'):
                    pool_config = eval(config.get('mysql_pools', pool_name))
                    host = pool_config.get('host', '')
                    db = pool_config.get('db', '')

                    data_sources.append({
                        'id': pool_name,
                        'name': f"MySQL-{db}",
                        'type': 'mysql',
                        'host': host,
                        'database': db,
                        'description': f"MySQL数据库: {db}@{host}"
                    })

            # 获取SQL Server连接池配置
            if 'sqlserver_pools' in config.sections():
                for pool_name in config.options('sqlserver_pools'):
                    pool_config = eval(config.get('sqlserver_pools', pool_name))
                    server = pool_config.get('server', '')
                    database = pool_config.get('database', '')

                    data_sources.append({
                        'id': pool_name,
                        'name': f"SQLServer-{database}",
                        'type': 'sqlserver',
                        'server': server,
                        'database': database,
                        'description': f"SQL Server数据库: {database}@{server}"
                    })

            return data_sources

        except Exception as e:
            logger.error(f"获取数据源列表失败: {str(e)}")
            return []


class TaskService:
    """任务管理服务"""

    @staticmethod
    def get_all_tasks(page: int = 1, per_page: int = 20, user_id: Optional[int] = None, **filters) -> Dict[str, Any]:
        """获取所有任务（分页）"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()
            where_conditions = []
            params = {}

            if user_id:
                where_conditions.append("t.user_id = :user_id")
                params['user_id'] = user_id

            if filters.get('task_name'):
                where_conditions.append("t.task_name LIKE :task_name")
                params['task_name'] = f"%{filters['task_name']}%"

            if filters.get('status'):
                where_conditions.append("t.status = :status")
                params['status'] = filters['status']

            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            # 获取总数
            count_query = f"SELECT COUNT(*) as total FROM selfcheck_tasks t WHERE {where_clause}"
            count_result = db_manager.execute_query(count_query, params)
            total = count_result[0]['total'] if count_result else 0

            # 查询任务列表（新的表结构）
            query = f"""
            SELECT t.id, t.task_name, t.status, t.created_at,
                   t.data_source, t.scheme_id, t.user_id, t.progress, t.error_count,
                   s.scheme_name
            FROM selfcheck_tasks t
            LEFT JOIN selfcheck_schemes s ON t.scheme_id = s.id
            WHERE {where_clause}
            ORDER BY t.created_at DESC
            """

            tasks = db_manager.execute_query(query, params)

            return {
                'tasks': tasks,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page if total > 0 else 0
            }
        except Exception as e:
            logger.error(f"获取任务列表失败: {str(e)}")
            raise

    @staticmethod
    def create_task(task_name: str, data_source: str, scheme_id: int, user_id: int = None) -> Dict[str, Any]:
        """创建新任务"""
        try:
            from app.selfcheck.database import get_db_manager
            from datetime import datetime

            db_manager = get_db_manager()

            # 验证数据源是否存在
            data_sources = DataSourceService.get_available_data_sources(user_id)
            data_source_info = next((ds for ds in data_sources if ds['id'] == data_source), None)
            if not data_source_info:
                raise ValueError(f"数据源不存在或无权限访问: {data_source}")

            # 验证方案是否存在且用户有权限
            scheme_check = db_manager.execute_query(
                "SELECT id, scheme_name FROM selfcheck_schemes WHERE id = :scheme_id AND status = 'active'",
                {'scheme_id': scheme_id}
            )
            if not scheme_check:
                raise ValueError(f"方案不存在或已禁用: {scheme_id}")

            # 先获取下一个序列值
            seq_query = "SELECT selfcheck_tasks_seq.NEXTVAL as task_id FROM dual"
            seq_result = db_manager.execute_query(seq_query)
            task_id = seq_result[0]['task_id'] if seq_result else None

            # 插入新任务（只插入新字段，旧字段保持NULL）
            insert_query = """
            INSERT INTO selfcheck_tasks (
                id, task_name, data_source, scheme_id, user_id,
                status, progress, error_count, warning_count,
                upload_id, rule_id
            ) VALUES (
                :task_id, :task_name, :data_source, :scheme_id, :user_id,
                'pending', 0, 0, 0,
                NULL, NULL
            )
            """

            params = {
                'task_id': task_id,
                'task_name': task_name,
                'data_source': data_source,
                'scheme_id': scheme_id,
                'user_id': user_id
            }

            # 执行插入
            db_manager.execute_update(insert_query, params)

            # 返回创建的任务信息
            from datetime import datetime
            now = datetime.now()

            return {
                'id': task_id,
                'task_name': task_name,
                'data_source': data_source,
                'data_source_name': data_source_info['name'],
                'scheme_id': scheme_id,
                'scheme_name': scheme_check[0]['scheme_name'],
                'user_id': user_id,
                'status': 'pending',
                'progress': 0,
                'error_count': 0,
                'created_at': now.isoformat()
            }

        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            raise

    @staticmethod
    def get_task_by_id(task_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取任务"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()
            query = """
            SELECT t.id, t.task_name, t.status, t.created_at,
                   t.data_source, t.scheme_id, t.user_id, t.progress, t.error_count,
                   s.scheme_name
            FROM selfcheck_tasks t
            LEFT JOIN selfcheck_schemes s ON t.scheme_id = s.id
            WHERE t.id = :task_id
            """
            result = db_manager.execute_query(query, {'task_id': task_id})

            return result[0] if result else None
        except Exception as e:
            logger.error(f"根据ID获取任务失败: {str(e)}")
            raise

    @staticmethod
    def delete_task(task_id: int, user_id: int) -> bool:
        """删除任务"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()

            # 检查任务是否存在
            task = TaskService.get_task_by_id(task_id)
            if not task:
                raise ValueError(f"任务不存在: {task_id}")

            # 检查权限（可选：只允许创建者删除自己的任务）
            # if task['user_id'] != user_id:
            #     raise ValueError("无权限删除此任务")

            # 检查任务状态
            if task.get('status') == 'running':
                raise ValueError("运行中的任务不能删除")

            # 删除任务（可以考虑软删除）
            delete_query = "DELETE FROM selfcheck_tasks WHERE id = :task_id"
            db_manager.execute_update(delete_query, {'task_id': task_id})

            return True
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")
            raise

    @staticmethod
    def get_task_rules_detail(task_id: int) -> List[Dict[str, Any]]:
        """获取任务关联的规则执行详情"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()

            # 获取任务关联的方案
            task = TaskService.get_task_by_id(task_id)
            if not task:
                return []

            scheme_id = task.get('scheme_id')
            if not scheme_id:
                return []

            # 获取方案中的规则及其执行状态
            query = """
            SELECT
                r.id,
                r.rule_name,
                r.sql_content,
                sr.is_enabled,
                sr.sort_order,
                -- 模拟规则执行状态（实际应该从任务执行记录表获取）
                CASE
                    WHEN MOD(r.id, 4) = 0 THEN 'completed'
                    WHEN MOD(r.id, 4) = 1 THEN 'running'
                    WHEN MOD(r.id, 4) = 2 THEN 'failed'
                    ELSE 'pending'
                END as status,
                CASE
                    WHEN MOD(r.id, 4) = 0 THEN 100
                    WHEN MOD(r.id, 4) = 1 THEN 60
                    WHEN MOD(r.id, 4) = 2 THEN 30
                    ELSE 0
                END as progress,
                -- 模拟结果数量
                CASE
                    WHEN MOD(r.id, 4) = 0 THEN ABS(MOD(r.id * 7, 50))
                    ELSE 0
                END as result_count,
                -- 模拟错误信息
                CASE
                    WHEN MOD(r.id, 4) = 2 THEN '执行SQL时发生错误：表不存在'
                    ELSE NULL
                END as error_message,
                -- 模拟执行时间
                CASE
                    WHEN MOD(r.id, 4) != 3 THEN SYSDATE - (MOD(r.id, 10) / 24)
                    ELSE NULL
                END as executed_at
            FROM selfcheck_scheme_rules sr
            JOIN selfcheck_rules r ON sr.rule_id = r.id
            WHERE sr.scheme_id = :scheme_id
            AND sr.is_enabled = 1
            ORDER BY sr.sort_order, r.id
            """

            rules = db_manager.execute_query(query, {'scheme_id': scheme_id})
            return rules

        except Exception as e:
            logger.error(f"获取任务规则详情失败: {str(e)}")
            return []

    @staticmethod
    def generate_task_report_html(task_id: int) -> str:
        """生成任务报告HTML"""
        try:
            # 获取任务信息
            task = TaskService.get_task_by_id(task_id)
            if not task:
                return "<h1>任务不存在</h1>"

            # 获取规则执行详情
            rules = TaskService.get_task_rules_detail(task_id)

            # 生成HTML报告
            html = f"""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>自查报告 - {task['task_name']}</title>
                <style>
                    body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }}
                    .header {{ text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }}
                    .info-table {{ width: 100%; border-collapse: collapse; margin-bottom: 30px; }}
                    .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    .info-table th {{ background-color: #f8f9fa; font-weight: bold; }}
                    .rules-table {{ width: 100%; border-collapse: collapse; }}
                    .rules-table th, .rules-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    .rules-table th {{ background-color: #007bff; color: white; }}
                    .status-completed {{ color: #28a745; font-weight: bold; }}
                    .status-failed {{ color: #dc3545; font-weight: bold; }}
                    .status-running {{ color: #007bff; font-weight: bold; }}
                    .status-pending {{ color: #6c757d; font-weight: bold; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>自查自纠报告</h1>
                    <h2>{task['task_name']}</h2>
                    <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>

                <h3>任务基本信息</h3>
                <table class="info-table">
                    <tr><th>任务名称</th><td>{task['task_name']}</td></tr>
                    <tr><th>数据源</th><td>{task.get('data_source', '-')}</td></tr>
                    <tr><th>检查方案</th><td>{task.get('scheme_name', '-')}</td></tr>
                    <tr><th>任务状态</th><td>{task.get('status', '-')}</td></tr>
                    <tr><th>创建时间</th><td>{task.get('created_at', '-')}</td></tr>
                    <tr><th>开始时间</th><td>{task.get('start_time', '-')}</td></tr>
                    <tr><th>结束时间</th><td>{task.get('end_time', '-')}</td></tr>
                    <tr><th>错误数量</th><td>{task.get('error_count', 0)}</td></tr>
                    <tr><th>警告数量</th><td>{task.get('warning_count', 0)}</td></tr>
                </table>

                <h3>规则执行详情</h3>
                <table class="rules-table">
                    <thead>
                        <tr>
                            <th>规则名称</th>
                            <th>执行状态</th>
                            <th>进度</th>
                            <th>结果数量</th>
                            <th>错误信息</th>
                            <th>执行时间</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            for rule in rules:
                status_class = f"status-{rule.get('status', 'pending')}"
                html += f"""
                        <tr>
                            <td>{rule.get('rule_name', '-')}</td>
                            <td class="{status_class}">{rule.get('status', '-')}</td>
                            <td>{rule.get('progress', 0)}%</td>
                            <td>{rule.get('result_count', 0)}</td>
                            <td>{rule.get('error_message', '-')}</td>
                            <td>{rule.get('executed_at', '-')}</td>
                        </tr>
                """

            html += """
                    </tbody>
                </table>
            </body>
            </html>
            """

            return html

        except Exception as e:
            logger.error(f"生成任务报告HTML失败: {str(e)}")
            return f"<h1>生成报告失败: {str(e)}</h1>"

    @staticmethod
    def generate_task_report_file(task_id: int) -> str:
        """生成任务报告文件"""
        try:
            import tempfile
            import os

            # 生成HTML内容
            html_content = TaskService.generate_task_report_html(task_id)

            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_file = os.path.join(temp_dir, f"task_report_{task_id}.html")

            # 写入HTML文件
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            return temp_file

        except Exception as e:
            logger.error(f"生成任务报告文件失败: {str(e)}")
            raise


class SchemeService:
    """方案管理服务"""

    @staticmethod
    def get_all_schemes(page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取所有方案（分页）"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()
            where_conditions = []
            params = {}

            # 构建过滤条件
            if filters.get('scheme_name'):
                where_conditions.append("s.scheme_name LIKE :scheme_name")
                params['scheme_name'] = f"%{filters['scheme_name']}%"

            if filters.get('status'):
                where_conditions.append("s.status = :status")
                params['status'] = filters['status']

            if filters.get('created_by'):
                where_conditions.append("u.username LIKE :created_by")
                params['created_by'] = f"%{filters['created_by']}%"

            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            # 获取总数
            count_query = f"""
            SELECT COUNT(*) as total
            FROM selfcheck_schemes s
            LEFT JOIN users u ON s.created_by = u.id
            WHERE {where_clause}
            """
            count_result = db_manager.execute_query(count_query, params)
            total = count_result[0]['total'] if count_result else 0

            # 获取方案列表（包含规则数量）
            query = f"""
            SELECT s.id, s.scheme_name, s.description, s.status,
                   s.created_at, s.updated_at,
                   u.username as created_by_name,
                   NVL(rule_count.cnt, 0) as rule_count
            FROM selfcheck_schemes s
            LEFT JOIN users u ON s.created_by = u.id
            LEFT JOIN (
                SELECT scheme_id, COUNT(*) as cnt
                FROM selfcheck_scheme_rules
                GROUP BY scheme_id
            ) rule_count ON s.id = rule_count.scheme_id
            WHERE {where_clause}
            ORDER BY s.created_at DESC
            """

            schemes = db_manager.execute_query(query, params)

            return {
                'schemes': schemes,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page if total > 0 else 0
            }
        except Exception as e:
            logger.error(f"获取方案列表失败: {str(e)}")
            raise

    @staticmethod
    def create_scheme(scheme_name: str, description: str = '', created_by: int = None) -> Dict[str, Any]:
        """创建新方案"""
        try:
            from app.selfcheck.models import SelfCheckScheme

            # 检查方案名称是否重复
            if SchemeService.get_scheme_by_name(scheme_name):
                raise ValueError(f"方案名称已存在: {scheme_name}")

            # 创建方案
            scheme = SelfCheckScheme(
                scheme_name=scheme_name,
                description=description,
                created_by=created_by
            )

            return scheme.save()
        except Exception as e:
            logger.error(f"创建方案失败: {str(e)}")
            raise

    @staticmethod
    def get_scheme_by_id(scheme_id: int) -> Dict[str, Any]:
        """根据ID获取方案详情"""
        try:
            from app.selfcheck.models import SelfCheckScheme

            scheme = SelfCheckScheme.get_by_id(scheme_id)
            if not scheme:
                return None

            return scheme.to_dict()
        except Exception as e:
            logger.error(f"获取方案详情失败: {str(e)}")
            raise

    @staticmethod
    def update_scheme(scheme_id: int, scheme_name: str = None, description: str = None,
                     status: str = None) -> Dict[str, Any]:
        """更新方案"""
        try:
            from app.selfcheck.models import SelfCheckScheme

            scheme = SelfCheckScheme.get_by_id(scheme_id)
            if not scheme:
                raise ValueError(f"方案不存在: {scheme_id}")

            # 更新字段
            if scheme_name is not None:
                # 检查名称是否重复（排除自己）
                existing = SchemeService.get_scheme_by_name(scheme_name)
                if existing and existing['id'] != scheme_id:
                    raise ValueError(f"方案名称已存在: {scheme_name}")
                scheme.scheme_name = scheme_name

            if description is not None:
                scheme.description = description

            if status is not None:
                scheme.status = status

            return scheme.save()
        except Exception as e:
            logger.error(f"更新方案失败: {str(e)}")
            raise

    @staticmethod
    def delete_scheme(scheme_id: int) -> bool:
        """删除方案"""
        try:
            from app.selfcheck.models import SelfCheckScheme

            scheme = SelfCheckScheme.get_by_id(scheme_id)
            if not scheme:
                raise ValueError(f"方案不存在: {scheme_id}")

            scheme.delete()
            return True
        except Exception as e:
            logger.error(f"删除方案失败: {str(e)}")
            raise

    @staticmethod
    def get_scheme_by_name(scheme_name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取方案"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()
            query = "SELECT * FROM selfcheck_schemes WHERE scheme_name = :scheme_name"
            result = db_manager.execute_query(query, {'scheme_name': scheme_name})

            return result[0] if result else None
        except Exception as e:
            logger.error(f"根据名称获取方案失败: {str(e)}")
            raise

    @staticmethod
    def get_user_authorized_schemes(user_id: int) -> List[Dict[str, Any]]:
        """获取用户授权的方案列表"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()

            # 目前简化处理：返回所有活跃的方案
            # 后续可以根据用户权限、角色等进行过滤
            query = """
            SELECT s.id, s.scheme_name, s.description, s.status,
                   s.created_at, s.updated_at,
                   u.username as created_by_name,
                   NVL(rule_count.cnt, 0) as rule_count
            FROM selfcheck_schemes s
            LEFT JOIN users u ON s.created_by = u.id
            LEFT JOIN (
                SELECT scheme_id, COUNT(*) as cnt
                FROM selfcheck_scheme_rules
                WHERE is_enabled = 1
                GROUP BY scheme_id
            ) rule_count ON s.id = rule_count.scheme_id
            WHERE s.status = 'active'
            ORDER BY s.created_at DESC
            """

            schemes = db_manager.execute_query(query)
            return schemes

        except Exception as e:
            logger.error(f"获取用户授权方案失败: {str(e)}")
            raise

    @staticmethod
    def get_scheme_rules(scheme_id: int) -> List[Dict[str, Any]]:
        """获取方案中的规则列表"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()
            query = """
            SELECT sr.id as scheme_rule_id, sr.sort_order, sr.is_enabled,
                   r.id as rule_id, r.rule_name, r.rule_type, r.rule_description as description,
                   r.sql_content, r.city, r.status as rule_status
            FROM selfcheck_scheme_rules sr
            JOIN selfcheck_rules r ON sr.rule_id = r.id
            WHERE sr.scheme_id = :scheme_id
            ORDER BY sr.sort_order, sr.id
            """

            return db_manager.execute_query(query, {'scheme_id': scheme_id})
        except Exception as e:
            logger.error(f"获取方案规则失败: {str(e)}")
            raise

    @staticmethod
    def add_rule_to_scheme(scheme_id: int, rule_id: int, sort_order: int = None) -> bool:
        """向方案中添加规则（使用批量导入连接池，支持重试）"""
        import time

        max_retries = 3
        retry_delay = 0.5  # 秒

        for attempt in range(max_retries):
            try:
                from app.selfcheck.database import get_batch_import_db_manager

                db_manager = get_batch_import_db_manager()

                logger.debug(f"添加规则到方案 - 尝试 {attempt + 1}/{max_retries}: scheme_id={scheme_id}, rule_id={rule_id}")

                # 检查方案是否存在
                scheme_check = db_manager.execute_query(
                    "SELECT id FROM selfcheck_schemes WHERE id = :scheme_id",
                    {'scheme_id': scheme_id}
                )
                if not scheme_check:
                    raise ValueError(f"方案不存在: {scheme_id}")

                # 检查规则是否存在
                rule_check = db_manager.execute_query(
                    "SELECT id FROM selfcheck_rules WHERE id = :rule_id",
                    {'rule_id': rule_id}
                )
                if not rule_check:
                    raise ValueError(f"规则不存在: {rule_id}")

                # 检查是否已经添加过
                existing_check = db_manager.execute_query(
                    "SELECT id FROM selfcheck_scheme_rules WHERE scheme_id = :scheme_id AND rule_id = :rule_id",
                    {'scheme_id': scheme_id, 'rule_id': rule_id}
                )
                if existing_check:
                    raise ValueError("规则已存在于该方案中")

                # 如果没有指定排序，使用最大值+1
                if sort_order is None:
                    max_order_query = """
                    SELECT NVL(MAX(sort_order), 0) + 1 as next_order
                    FROM selfcheck_scheme_rules
                    WHERE scheme_id = :scheme_id
                    """
                    max_result = db_manager.execute_query(max_order_query, {'scheme_id': scheme_id})
                    sort_order = max_result[0]['next_order'] if max_result else 1

                # 添加规则到方案
                insert_query = """
                INSERT INTO selfcheck_scheme_rules (id, scheme_id, rule_id, sort_order, is_enabled)
                VALUES (selfcheck_scheme_rules_seq.NEXTVAL, :scheme_id, :rule_id, :sort_order, 1)
                """

                db_manager.execute_update(insert_query, {
                    'scheme_id': scheme_id,
                    'rule_id': rule_id,
                    'sort_order': sort_order
                })

                logger.debug(f"规则添加成功: scheme_id={scheme_id}, rule_id={rule_id}")
                return True

            except ValueError as e:
                # 业务逻辑错误，不重试
                logger.error(f"添加规则到方案业务错误: {str(e)}")
                raise
            except Exception as e:
                error_str = str(e)
                if "ORA-12516" in error_str or "TNS" in error_str:
                    # 连接池耗尽或连接问题，重试
                    if attempt < max_retries - 1:
                        wait_time = retry_delay * (attempt + 1)
                        logger.warning(f"数据库连接失败，{wait_time}秒后重试 (尝试 {attempt + 1}/{max_retries}): {error_str}")
                        time.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"添加规则到方案失败，已重试 {max_retries} 次: {error_str}")
                        raise
                else:
                    # 其他错误，不重试
                    logger.error(f"添加规则到方案失败: {error_str}")
                    raise

    @staticmethod
    def remove_rule_from_scheme(scheme_id: int, rule_id: int) -> bool:
        """从方案中移除规则"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()

            # 删除规则
            delete_query = """
            DELETE FROM selfcheck_scheme_rules
            WHERE scheme_id = :scheme_id AND rule_id = :rule_id
            """

            db_manager.execute_update(delete_query, {
                'scheme_id': scheme_id,
                'rule_id': rule_id
            })

            return True
        except Exception as e:
            logger.error(f"从方案中移除规则失败: {str(e)}")
            raise

    @staticmethod
    def update_rule_in_scheme(scheme_id: int, rule_id: int, sort_order: int = None,
                             is_enabled: bool = None) -> bool:
        """更新方案中规则的配置"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()

            update_fields = []
            params = {'scheme_id': scheme_id, 'rule_id': rule_id}

            if sort_order is not None:
                update_fields.append("sort_order = :sort_order")
                params['sort_order'] = sort_order

            if is_enabled is not None:
                update_fields.append("is_enabled = :is_enabled")
                params['is_enabled'] = 1 if is_enabled else 0

            if not update_fields:
                return True

            update_query = f"""
            UPDATE selfcheck_scheme_rules
            SET {', '.join(update_fields)}
            WHERE scheme_id = :scheme_id AND rule_id = :rule_id
            """

            db_manager.execute_update(update_query, params)
            return True
        except Exception as e:
            logger.error(f"更新方案中规则配置失败: {str(e)}")
            raise

    @staticmethod
    def get_available_rules(scheme_id: int = None, page: int = 1, per_page: int = 20, **filters) -> Dict[str, Any]:
        """获取可添加到方案的规则列表（支持分页）"""
        try:
            from app.selfcheck.database import get_db_manager
            import math

            db_manager = get_db_manager()
            where_conditions = ["r.status = 'active'"]
            params = {}

            # 如果指定了方案ID，排除已添加的规则
            if scheme_id:
                where_conditions.append("""
                r.id NOT IN (
                    SELECT rule_id FROM selfcheck_scheme_rules
                    WHERE scheme_id = :scheme_id
                )
                """)
                params['scheme_id'] = scheme_id

            # 添加过滤条件
            if filters.get('rule_name'):
                where_conditions.append("r.rule_name LIKE :rule_name")
                params['rule_name'] = f"%{filters['rule_name']}%"

            if filters.get('rule_type'):
                where_conditions.append("r.rule_type = :rule_type")
                params['rule_type'] = filters['rule_type']

            if filters.get('city'):
                where_conditions.append("r.city = :city")
                params['city'] = filters['city']

            if filters.get('visit_type'):
                where_conditions.append("r.visit_type = :visit_type")
                params['visit_type'] = filters['visit_type']

            if filters.get('types'):
                where_conditions.append("r.types = :types")
                params['types'] = filters['types']

            # 添加规则来源过滤（直接从SELFCHECK_RULES表）
            if filters.get('rule_source'):
                where_conditions.append("r.rule_source = :rule_source")
                params['rule_source'] = filters['rule_source']

            where_clause = " AND ".join(where_conditions)

            # 先获取总数
            count_query = f"""
            SELECT COUNT(*) as total
            FROM selfcheck_rules r
            WHERE {where_clause}
            """

            count_result = db_manager.execute_query(count_query, params)
            total = count_result[0]['total'] if count_result else 0

            # 计算分页信息
            pages = math.ceil(total / per_page) if total > 0 else 1
            offset = (page - 1) * per_page

            # 获取分页数据 (Oracle分页语法)
            start_row = offset + 1
            end_row = offset + per_page

            query = f"""
            SELECT * FROM (
                SELECT r.id, r.rule_name, r.rule_type, r.rule_description as description,
                       r.city, r.visit_type, r.types, r.status, r.created_at,
                       ROW_NUMBER() OVER (ORDER BY r.rule_name) as rn
                FROM selfcheck_rules r
                WHERE {where_clause}
            ) WHERE rn BETWEEN {start_row} AND {end_row}
            """

            rules = db_manager.execute_query(query, params)

            return {
                'rules': rules,
                'page': page,
                'pages': pages,
                'total': total,
                'per_page': per_page
            }
        except Exception as e:
            logger.error(f"获取可用规则失败: {str(e)}")
            raise

    @staticmethod
    def get_scheme_statistics(scheme_id: int) -> Dict[str, Any]:
        """获取方案统计信息"""
        try:
            from app.selfcheck.database import get_db_manager

            db_manager = get_db_manager()

            # 获取方案规则统计
            stats_query = """
            SELECT
                COUNT(*) as total_rules,
                SUM(CASE WHEN sr.is_enabled = 1 THEN 1 ELSE 0 END) as enabled_rules,
                SUM(CASE WHEN sr.is_enabled = 0 THEN 1 ELSE 0 END) as disabled_rules
            FROM selfcheck_scheme_rules sr
            WHERE sr.scheme_id = :scheme_id
            """

            stats_result = db_manager.execute_query(stats_query, {'scheme_id': scheme_id})

            if stats_result:
                stats = stats_result[0]
                return {
                    'total_rules': stats.get('total_rules', 0),
                    'enabled_rules': stats.get('enabled_rules', 0),
                    'disabled_rules': stats.get('disabled_rules', 0)
                }
            else:
                return {
                    'total_rules': 0,
                    'enabled_rules': 0,
                    'disabled_rules': 0
                }
        except Exception as e:
            logger.error(f"获取方案统计信息失败: {str(e)}")
            raise

