#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel导出功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.rules.services import ConverterService
import json

def test_export_function():
    """测试导出功能"""
    print("=== 测试Excel导出功能 ===\n")
    
    # 测试upload_id = 27 (从您的请求中获取)
    upload_id = 27
    
    try:
        print(f"开始测试导出功能，upload_id: {upload_id}")
        
        # 调用导出方法
        result = ConverterService.export_rules(upload_id)
        
        print("导出结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        if result['success']:
            print(f"✅ 导出成功!")
            print(f"文件路径: {result['file_path']}")
            print(f"文件名: {result['filename']}")
            print(f"规则数量: {result['rules_count']}")
            
            # 检查文件是否存在
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"文件大小: {file_size} bytes")
                print("✅ 文件已成功创建")
            else:
                print("❌ 文件未找到")
        else:
            print(f"❌ 导出失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===\n")
    
    try:
        from app.utils.database import db_manager, execute_rules_query
        
        with db_manager.get_connection() as conn:
            # 测试查询converter_rules表
            test_sql = "SELECT COUNT(*) FROM converter_rules WHERE upload_id = :upload_id"
            df = execute_rules_query(conn, test_sql, {'upload_id': 27})
            
            if not df.empty:
                count = int(df.iloc[0, 0])
                print(f"✅ 数据库连接正常")
                print(f"upload_id=27 的规则数量: {count}")
                
                if count > 0:
                    # 查询一些示例数据
                    sample_sql = """
                    SELECT rule_name, violation_type, department, status
                    FROM converter_rules 
                    WHERE upload_id = :upload_id AND ROWNUM <= 3
                    """
                    sample_df = execute_rules_query(conn, sample_sql, {'upload_id': 27})
                    
                    print("示例数据:")
                    for _, row in sample_df.iterrows():
                        print(f"  - {row['rule_name']} ({row['violation_type']}) - {row['status']}")
                else:
                    print("❌ 没有找到相关规则数据")
            else:
                print("❌ 查询结果为空")
                
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_connection()
    print()
    test_export_function()
