#!/usr/bin/env python3
"""
测试权限修复
"""

import requests
import json
from bs4 import BeautifulSoup

def test_permission_fixes():
    """测试权限修复"""
    
    # 创建会话
    session = requests.Session()
    
    print("=== 🔧 测试权限管理修复 ===")
    
    # 1. 登录
    print("\n1. 🔐 登录...")
    login_url = 'http://127.0.0.1:5002/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }
    
    try:
        login_page = session.get(login_url, timeout=10)
        soup = BeautifulSoup(login_page.text, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        if csrf_token:
            login_data['csrf_token'] = csrf_token['value']
        
        login_response = session.post(login_url, data=login_data, allow_redirects=True, timeout=10)
        
        if '/index' not in login_response.url:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录过程出错: {str(e)}")
        return False
    
    # 2. 测试数据上传权限
    print("\n2. 📤 测试数据上传权限...")
    try:
        uploads_url = 'http://127.0.0.1:5002/selfcheck/uploads'
        uploads_response = session.get(uploads_url, timeout=10)
        
        if uploads_response.status_code == 200:
            print("✅ 数据上传页面访问成功")
            
            # 测试上传API
            uploads_api_url = 'http://127.0.0.1:5002/selfcheck/api/uploads'
            uploads_api_response = session.get(uploads_api_url, timeout=10)
            
            if uploads_api_response.status_code == 200:
                print("✅ 数据上传API访问成功")
            else:
                print(f"❌ 数据上传API访问失败: {uploads_api_response.status_code}")
        else:
            print(f"❌ 数据上传页面访问失败: {uploads_response.status_code}")
            
    except Exception as e:
        print(f"❌ 数据上传权限测试出错: {str(e)}")
    
    # 3. 测试任务删除权限
    print("\n3. 🗑️ 测试任务删除权限...")
    try:
        tasks_url = 'http://127.0.0.1:5002/selfcheck/tasks'
        tasks_response = session.get(tasks_url, timeout=10)
        
        if tasks_response.status_code == 200:
            print("✅ 任务页面访问成功")
            
            # 检查页面是否包含删除按钮
            if 'fa-trash' in tasks_response.text:
                print("✅ 任务删除按钮已显示")
            else:
                print("❌ 任务删除按钮未显示")
        else:
            print(f"❌ 任务页面访问失败: {tasks_response.status_code}")
            
    except Exception as e:
        print(f"❌ 任务删除权限测试出错: {str(e)}")
    
    # 4. 测试权限分配界面
    print("\n4. 👥 测试权限分配界面...")
    try:
        roles_url = 'http://127.0.0.1:5002/admin/roles'
        roles_response = session.get(roles_url, timeout=10)
        
        if roles_response.status_code == 200:
            print("✅ 角色管理页面访问成功")
            
            # 测试权限API
            permissions_url = 'http://127.0.0.1:5002/admin/api/permissions'
            permissions_response = session.get(permissions_url, timeout=10)
            
            if permissions_response.status_code == 200:
                permissions_data = permissions_response.json()
                if permissions_data.get('success'):
                    permissions = permissions_data['permissions']
                    print(f"✅ 权限API访问成功，获取到 {len(permissions)} 个权限")
                    
                    # 检查是否有重复的顶级权限
                    top_level_perms = [p for p in permissions if not p.get('parent_id')]
                    module_perms = [p for p in top_level_perms if p.get('resource_type') == 'menu']
                    
                    print(f"📊 顶级权限: {len(top_level_perms)} 个")
                    print(f"📊 模块级权限: {len(module_perms)} 个")
                    
                    # 检查是否有删除任务权限
                    delete_task_perm = [p for p in permissions if p.get('code') == 'selfcheck.tasks.delete']
                    if delete_task_perm:
                        print("✅ 删除任务权限已存在")
                    else:
                        print("❌ 删除任务权限不存在")
                        
                else:
                    print(f"❌ 权限API返回错误: {permissions_data.get('message', '未知错误')}")
            else:
                print(f"❌ 权限API访问失败: {permissions_response.status_code}")
        else:
            print(f"❌ 角色管理页面访问失败: {roles_response.status_code}")
            
    except Exception as e:
        print(f"❌ 权限分配界面测试出错: {str(e)}")
    
    print("\n🎉 权限管理修复测试完成！")
    
    return True

if __name__ == '__main__':
    try:
        success = test_permission_fixes()
        if success:
            print("\n✅ 权限管理修复验证成功！")
        else:
            print("\n❌ 权限管理修复验证失败")
    except Exception as e:
        print(f"验证过程出错: {e}")
        import traceback
        traceback.print_exc()
