#!/usr/bin/env python3
"""创建方案管理相关的数据库表"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def create_scheme_tables():
    """创建方案管理相关表"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            print("=== 创建方案管理数据库表 ===")
            
            # 1. 创建方案表序列
            print("\n1. 创建序列...")
            
            sequences = [
                {
                    'name': 'selfcheck_schemes_seq',
                    'sql': """
                    CREATE SEQUENCE selfcheck_schemes_seq
                    START WITH 1
                    INCREMENT BY 1
                    NOCACHE
                    """
                },
                {
                    'name': 'selfcheck_scheme_rules_seq', 
                    'sql': """
                    CREATE SEQUENCE selfcheck_scheme_rules_seq
                    START WITH 1
                    INCREMENT BY 1
                    NOCACHE
                    """
                }
            ]
            
            for seq in sequences:
                try:
                    # 检查序列是否存在
                    check_query = f"SELECT sequence_name FROM user_sequences WHERE sequence_name = '{seq['name'].upper()}'"
                    exists = db_manager.execute_query(check_query)
                    
                    if not exists:
                        db_manager.execute_update(seq['sql'])
                        print(f"   ✅ 创建序列: {seq['name']}")
                    else:
                        print(f"   ✅ 序列已存在: {seq['name']}")
                        
                except Exception as e:
                    print(f"   ❌ 创建序列失败 {seq['name']}: {str(e)}")
            
            # 2. 创建方案表
            print("\n2. 创建方案表...")
            
            scheme_table_sql = """
            CREATE TABLE selfcheck_schemes (
                id NUMBER PRIMARY KEY,
                scheme_name VARCHAR2(200) NOT NULL,
                description CLOB,
                status VARCHAR2(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
                created_by NUMBER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                CONSTRAINT fk_schemes_user FOREIGN KEY (created_by) REFERENCES users(id),
                CONSTRAINT uk_scheme_name UNIQUE (scheme_name)
            )
            """
            
            try:
                # 检查表是否存在
                check_table = "SELECT table_name FROM user_tables WHERE table_name = 'SELFCHECK_SCHEMES'"
                exists = db_manager.execute_query(check_table)
                
                if not exists:
                    db_manager.execute_update(scheme_table_sql)
                    print("   ✅ 创建方案表: selfcheck_schemes")
                else:
                    print("   ✅ 方案表已存在: selfcheck_schemes")
                    
            except Exception as e:
                print(f"   ❌ 创建方案表失败: {str(e)}")
            
            # 3. 创建方案规则关联表
            print("\n3. 创建方案规则关联表...")
            
            scheme_rules_table_sql = """
            CREATE TABLE selfcheck_scheme_rules (
                id NUMBER PRIMARY KEY,
                scheme_id NUMBER NOT NULL,
                rule_id NUMBER NOT NULL,
                sort_order NUMBER DEFAULT 1,
                is_enabled NUMBER(1) DEFAULT 1 CHECK (is_enabled IN (0, 1)),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                CONSTRAINT fk_scheme_rules_scheme FOREIGN KEY (scheme_id) REFERENCES selfcheck_schemes(id) ON DELETE CASCADE,
                CONSTRAINT fk_scheme_rules_rule FOREIGN KEY (rule_id) REFERENCES selfcheck_rules(id),
                CONSTRAINT uk_scheme_rule UNIQUE (scheme_id, rule_id)
            )
            """
            
            try:
                # 检查表是否存在
                check_table = "SELECT table_name FROM user_tables WHERE table_name = 'SELFCHECK_SCHEME_RULES'"
                exists = db_manager.execute_query(check_table)
                
                if not exists:
                    db_manager.execute_update(scheme_rules_table_sql)
                    print("   ✅ 创建关联表: selfcheck_scheme_rules")
                else:
                    print("   ✅ 关联表已存在: selfcheck_scheme_rules")
                    
            except Exception as e:
                print(f"   ❌ 创建关联表失败: {str(e)}")
            
            # 4. 创建索引
            print("\n4. 创建索引...")
            
            indexes = [
                {
                    'name': 'idx_schemes_status',
                    'sql': 'CREATE INDEX idx_schemes_status ON selfcheck_schemes(status)'
                },
                {
                    'name': 'idx_schemes_created_by',
                    'sql': 'CREATE INDEX idx_schemes_created_by ON selfcheck_schemes(created_by)'
                },
                {
                    'name': 'idx_scheme_rules_scheme',
                    'sql': 'CREATE INDEX idx_scheme_rules_scheme ON selfcheck_scheme_rules(scheme_id)'
                },
                {
                    'name': 'idx_scheme_rules_rule',
                    'sql': 'CREATE INDEX idx_scheme_rules_rule ON selfcheck_scheme_rules(rule_id)'
                },
                {
                    'name': 'idx_scheme_rules_order',
                    'sql': 'CREATE INDEX idx_scheme_rules_order ON selfcheck_scheme_rules(scheme_id, sort_order)'
                }
            ]
            
            for idx in indexes:
                try:
                    # 检查索引是否存在
                    check_idx = f"SELECT index_name FROM user_indexes WHERE index_name = '{idx['name'].upper()}'"
                    exists = db_manager.execute_query(check_idx)
                    
                    if not exists:
                        db_manager.execute_update(idx['sql'])
                        print(f"   ✅ 创建索引: {idx['name']}")
                    else:
                        print(f"   ✅ 索引已存在: {idx['name']}")
                        
                except Exception as e:
                    print(f"   ❌ 创建索引失败 {idx['name']}: {str(e)}")
            
            # 5. 插入示例数据
            print("\n5. 插入示例数据...")
            
            try:
                # 检查是否已有数据
                count_query = "SELECT COUNT(*) as cnt FROM selfcheck_schemes"
                count_result = db_manager.execute_query(count_query)
                
                if count_result and count_result[0]['cnt'] == 0:
                    # 插入示例方案
                    sample_schemes = [
                        {
                            'name': '医保门诊基础检查方案',
                            'description': '包含门诊挂号、诊疗费用等基础检查规则的方案'
                        },
                        {
                            'name': '住院费用全面审核方案', 
                            'description': '涵盖住院期间各类费用审核的综合检查方案'
                        },
                        {
                            'name': '药品费用专项检查方案',
                            'description': '专门针对药品费用合理性的检查方案'
                        }
                    ]
                    
                    for scheme in sample_schemes:
                        insert_sql = """
                        INSERT INTO selfcheck_schemes (id, scheme_name, description, created_by)
                        VALUES (selfcheck_schemes_seq.NEXTVAL, :name, :description, 1)
                        """
                        db_manager.execute_update(insert_sql, {
                            'name': scheme['name'],
                            'description': scheme['description']
                        })
                        print(f"   ✅ 插入示例方案: {scheme['name']}")
                else:
                    print("   ✅ 示例数据已存在，跳过插入")
                    
            except Exception as e:
                print(f"   ❌ 插入示例数据失败: {str(e)}")
            
            print("\n🎉 方案管理数据库表创建完成！")
            
        except Exception as e:
            print(f"❌ 创建失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    create_scheme_tables()
