"""
角色配置模型
"""
from datetime import datetime
from app.selfcheck.database import execute_query, execute_update
import json


class RoleConfig:
    """角色配置模型"""
    TABLE_NAME = 'role_configs'

    def __init__(self, id=None, role_id=None, config_key=None, config_value=None,
                 config_type='string', description=None, created_at=None, updated_at=None):
        self.id = id
        self.role_id = role_id
        self.config_key = config_key
        self.config_value = config_value
        self.config_type = config_type
        self.description = description
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()

    def get_typed_value(self):
        """获取类型化的配置值"""
        if self.config_value is None:
            return None

        if self.config_type == 'number':
            try:
                if '.' in str(self.config_value):
                    return float(self.config_value)
                return int(self.config_value)
            except (ValueError, TypeError):
                return 0
        elif self.config_type == 'boolean':
            return str(self.config_value).lower() in ('true', '1', 'yes', 'on')
        elif self.config_type == 'json':
            try:
                return json.loads(self.config_value)
            except (json.JSONDecodeError, TypeError):
                return {}
        else:  # string
            return self.config_value

    def set_typed_value(self, value):
        """设置类型化的配置值"""
        if self.config_type == 'json':
            self.config_value = json.dumps(value, ensure_ascii=False)
        else:
            self.config_value = str(value)

    def save(self):
        """保存配置"""
        try:
            # 检查是否已存在
            existing_query = f"""
            SELECT id FROM {self.TABLE_NAME}
            WHERE role_id = ? AND config_key = ?
            """
            existing = execute_query(existing_query, [self.role_id, self.config_key])

            if existing:
                # 更新
                update_query = f"""
                UPDATE {self.TABLE_NAME} SET
                    config_value = ?, config_type = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                WHERE role_id = ? AND config_key = ?
                """
                execute_update(update_query, [
                    self.config_value, self.config_type, self.description,
                    self.role_id, self.config_key
                ])
                self.id = existing[0]['id']
            else:
                # 插入
                seq_query = "SELECT role_configs_seq.NEXTVAL as new_id FROM dual"
                seq_result = execute_query(seq_query)
                if seq_result:
                    self.id = seq_result[0]['new_id']

                    insert_query = f"""
                    INSERT INTO {self.TABLE_NAME} (
                        id, role_id, config_key, config_value, config_type,
                        description, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """
                    execute_update(insert_query, [
                        self.id, self.role_id, self.config_key, self.config_value,
                        self.config_type, self.description
                    ])
            return True
        except Exception as e:
            print(f"保存角色配置失败: {str(e)}")
            return False

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'role_id': self.role_id,
            'config_key': self.config_key,
            'config_value': self.config_value,
            'typed_value': self.get_typed_value(),
            'config_type': self.config_type,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_role_config(cls, role_id, config_key, default_value=None):
        """获取角色配置值"""
        try:
            query = f"""
            SELECT * FROM {cls.TABLE_NAME}
            WHERE role_id = ? AND config_key = ?
            """
            result = execute_query(query, [role_id, config_key])
            if result:
                config = cls(**result[0])
                return config.get_typed_value()
            return default_value
        except Exception as e:
            print(f"获取角色配置失败: {str(e)}")
            return default_value

    @classmethod
    def set_role_config(cls, role_id, config_key, value, config_type='string', description=None):
        """设置角色配置值"""
        try:
            config = cls(
                role_id=role_id,
                config_key=config_key,
                config_type=config_type,
                description=description
            )
            config.set_typed_value(value)
            return config.save()
        except Exception as e:
            print(f"设置角色配置失败: {str(e)}")
            return False

    @classmethod
    def get_role_configs(cls, role_id):
        """获取角色的所有配置"""
        try:
            query = f"SELECT * FROM {cls.TABLE_NAME} WHERE role_id = ?"
            results = execute_query(query, [role_id])
            configs = {}
            for result in results:
                config = cls(**result)
                configs[config.config_key] = config.get_typed_value()
            return configs
        except Exception as e:
            print(f"获取角色配置列表失败: {str(e)}")
            return {}

    @classmethod
    def get_upload_limits_for_user(cls, user):
        """获取用户的上传限制配置"""
        # 管理员无限制
        if user.is_admin:
            return {
                'max_size_mb': -1,  # 无限制
                'max_file_count': -1,  # 无限制
                'allowed_types': ['csv', 'dmp', 'dp', 'bak'],
                'upload_single_file_max_mb': -1
            }

        # 获取用户的所有角色配置
        upload_limits = {
            'max_size_mb': 350,  # 默认350MB
            'max_file_count': 100,  # 默认100个文件
            'allowed_types': ['csv', 'dmp', 'dp', 'bak'],
            'upload_single_file_max_mb': 10  # 默认10MB
        }

        # 遍历用户的所有角色，取最大值
        try:
            # 获取用户角色
            from app.models.user import User
            user_roles_query = """
            SELECT r.id FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = ? AND r.is_active = 1
            """
            role_results = execute_query(user_roles_query, [user.id])

            for role_result in role_results:
                role_id = role_result['id']
                role_max_size = cls.get_role_config(role_id, 'upload_max_size_mb', 350)
                role_max_count = cls.get_role_config(role_id, 'upload_max_file_count', 100)
                role_single_max = cls.get_role_config(role_id, 'upload_single_file_max_mb', 10)
                role_allowed_types = cls.get_role_config(role_id, 'upload_allowed_types', 'csv,dmp,dp,bak')

                # 取最大值（-1表示无限制）
                if role_max_size == -1 or upload_limits['max_size_mb'] == -1:
                    upload_limits['max_size_mb'] = -1
                elif role_max_size > upload_limits['max_size_mb']:
                    upload_limits['max_size_mb'] = role_max_size

                if role_max_count == -1 or upload_limits['max_file_count'] == -1:
                    upload_limits['max_file_count'] = -1
                elif role_max_count > upload_limits['max_file_count']:
                    upload_limits['max_file_count'] = role_max_count

                if role_single_max == -1 or upload_limits['upload_single_file_max_mb'] == -1:
                    upload_limits['upload_single_file_max_mb'] = -1
                elif role_single_max > upload_limits['upload_single_file_max_mb']:
                    upload_limits['upload_single_file_max_mb'] = role_single_max

                # 合并允许的文件类型
                if isinstance(role_allowed_types, str):
                    role_types = [t.strip().lower() for t in role_allowed_types.split(',')]
                    upload_limits['allowed_types'] = list(set(upload_limits['allowed_types'] + role_types))
        except Exception as e:
            print(f"获取用户角色配置失败: {str(e)}")

        return upload_limits
