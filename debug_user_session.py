#!/usr/bin/env python3
"""
调试用户会话和权限
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission
from flask import session

def debug_user_session():
    """调试用户会话和权限"""
    app = create_app()
    
    with app.app_context():
        print("=== 用户信息 ===")
        users = User.query.all()
        for user in users:
            print(f"用户: {user.username} (ID: {user.id})")
            print(f"  邮箱: {user.email}")
            print(f"  激活状态: {user.is_active}")
            print(f"  角色: {[role.name for role in user.roles]}")
            
            # 检查用户权限
            user_permissions = set()
            for role in user.roles:
                for permission in role.permissions:
                    user_permissions.add(permission.code)
            
            print(f"  权限数量: {len(user_permissions)}")
            if 'system.permission.view' in user_permissions:
                print("  ✅ 有权限查看权限列表")
            else:
                print("  ❌ 没有权限查看权限列表")
            
            if 'system.role.view' in user_permissions:
                print("  ✅ 有权限查看角色列表")
            else:
                print("  ❌ 没有权限查看角色列表")
            print()
        
        print("=== 权限统计 ===")
        permissions = Permission.query.filter_by(is_active=True).all()
        print(f"总权限数: {len(permissions)}")
        
        # 按模块统计
        modules = {}
        for perm in permissions:
            module = perm.module or 'unknown'
            if module not in modules:
                modules[module] = 0
            modules[module] += 1
        
        for module, count in modules.items():
            print(f"  {module}: {count}个权限")
        
        # 检查权限层级
        root_permissions = Permission.query.filter_by(is_active=True, parent_id=None).all()
        child_permissions = Permission.query.filter(Permission.is_active == True, Permission.parent_id.isnot(None)).all()
        
        print(f"\n权限层级:")
        print(f"  根权限: {len(root_permissions)}个")
        print(f"  子权限: {len(child_permissions)}个")
        
        print("\n=== 根权限列表 ===")
        for perm in root_permissions:
            print(f"  {perm.code} - {perm.name} (模块: {perm.module})")
        
        print("\n=== 角色权限分配 ===")
        roles = Role.query.all()
        for role in roles:
            print(f"角色: {role.name}")
            print(f"  权限数量: {len(role.permissions)}")
            if role.permissions:
                print(f"  权限列表: {[p.code for p in role.permissions[:5]]}{'...' if len(role.permissions) > 5 else ''}")
            print()

if __name__ == '__main__':
    debug_user_session()
