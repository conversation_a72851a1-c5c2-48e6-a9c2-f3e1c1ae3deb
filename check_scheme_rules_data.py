#!/usr/bin/env python3
"""
检查方案规则数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from run import create_app

def check_scheme_rules_data():
    """检查方案规则数据"""
    
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            print("=== 🔍 检查方案规则数据 ===")
            
            # 1. 检查方案数据
            print("\n1. 📋 检查方案数据...")
            schemes_query = "SELECT id, scheme_name, status FROM selfcheck_schemes ORDER BY id"
            schemes = db_manager.execute_query(schemes_query)
            
            if schemes:
                print(f"✅ 找到 {len(schemes)} 个方案:")
                for scheme in schemes:
                    print(f"  - ID: {scheme['id']}, 名称: {scheme['scheme_name']}, 状态: {scheme['status']}")
            else:
                print("❌ 没有找到方案数据")
                return
            
            # 2. 检查规则数据
            print("\n2. 📝 检查规则数据...")
            rules_query = "SELECT id, rule_name, rule_type, status FROM selfcheck_rules WHERE status = 'active' ORDER BY id"
            rules = db_manager.execute_query(rules_query)
            
            if rules:
                print(f"✅ 找到 {len(rules)} 个活跃规则:")
                for i, rule in enumerate(rules[:5]):  # 只显示前5个
                    print(f"  - ID: {rule['id']}, 名称: {rule['rule_name']}, 类型: {rule['rule_type']}")
                if len(rules) > 5:
                    print(f"  ... 还有 {len(rules) - 5} 个规则")
            else:
                print("❌ 没有找到活跃规则数据")
                return
            
            # 3. 检查方案规则关联数据
            print("\n3. 🔗 检查方案规则关联数据...")
            scheme_rules_query = """
            SELECT sr.id, sr.scheme_id, sr.rule_id, sr.sort_order, sr.is_enabled,
                   s.scheme_name, r.rule_name
            FROM selfcheck_scheme_rules sr
            JOIN selfcheck_schemes s ON sr.scheme_id = s.id
            JOIN selfcheck_rules r ON sr.rule_id = r.id
            ORDER BY sr.scheme_id, sr.sort_order
            """
            scheme_rules = db_manager.execute_query(scheme_rules_query)
            
            if scheme_rules:
                print(f"✅ 找到 {len(scheme_rules)} 个方案规则关联:")
                current_scheme_id = None
                for sr in scheme_rules:
                    if sr['scheme_id'] != current_scheme_id:
                        current_scheme_id = sr['scheme_id']
                        print(f"\n  方案: {sr['scheme_name']} (ID: {sr['scheme_id']})")
                    enabled_text = "启用" if sr['is_enabled'] else "禁用"
                    print(f"    - 规则: {sr['rule_name']} (排序: {sr['sort_order']}, {enabled_text})")
            else:
                print("❌ 没有找到方案规则关联数据")
                print("ℹ️  需要先添加规则到方案中")
                
                # 尝试添加一些测试数据
                print("\n4. ➕ 添加测试数据...")
                if schemes and rules:
                    test_scheme = schemes[0]
                    test_rules = rules[:3]  # 取前3个规则
                    
                    print(f"  向方案 '{test_scheme['scheme_name']}' 添加规则...")
                    
                    for i, rule in enumerate(test_rules):
                        try:
                            insert_query = """
                            INSERT INTO selfcheck_scheme_rules (id, scheme_id, rule_id, sort_order, is_enabled)
                            VALUES (selfcheck_scheme_rules_seq.NEXTVAL, :scheme_id, :rule_id, :sort_order, 1)
                            """
                            
                            params = {
                                'scheme_id': test_scheme['id'],
                                'rule_id': rule['id'],
                                'sort_order': (i + 1) * 10  # 10, 20, 30
                            }
                            
                            db_manager.execute_update(insert_query, params)
                            print(f"    ✅ 添加规则: {rule['rule_name']} (排序: {(i + 1) * 10})")
                            
                        except Exception as e:
                            if "unique constraint" in str(e).lower():
                                print(f"    ℹ️  规则已存在: {rule['rule_name']}")
                            else:
                                print(f"    ❌ 添加规则失败: {rule['rule_name']} - {str(e)}")
                    
                    # 重新检查方案规则数据
                    print("\n5. 🔍 重新检查方案规则数据...")
                    updated_scheme_rules = db_manager.execute_query(scheme_rules_query)
                    
                    if updated_scheme_rules:
                        print(f"✅ 现在有 {len(updated_scheme_rules)} 个方案规则关联:")
                        for sr in updated_scheme_rules:
                            enabled_text = "启用" if sr['is_enabled'] else "禁用"
                            print(f"  - 方案: {sr['scheme_name']}, 规则: {sr['rule_name']} (排序: {sr['sort_order']}, {enabled_text})")
                    else:
                        print("❌ 仍然没有方案规则关联数据")
            
            # 4. 检查序列
            print("\n6. 🔢 检查序列状态...")
            try:
                seq_query = "SELECT selfcheck_scheme_rules_seq.NEXTVAL as next_val FROM dual"
                seq_result = db_manager.execute_query(seq_query)
                if seq_result:
                    print(f"✅ selfcheck_scheme_rules_seq 序列正常，下一个值: {seq_result[0]['next_val']}")
                else:
                    print("❌ 无法获取序列值")
            except Exception as e:
                print(f"❌ 序列检查失败: {str(e)}")
            
            print("\n🎉 数据检查完成！")
            
        except Exception as e:
            print(f"❌ 数据检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_scheme_rules_data()
