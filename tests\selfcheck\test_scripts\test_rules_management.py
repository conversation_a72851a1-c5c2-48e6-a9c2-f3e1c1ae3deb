#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则管理模块自动化测试脚本
"""

import unittest
import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from tests.selfcheck.utils.test_base import SelfCheckTestBase, PerformanceTestMixin, DataValidationMixin
from tests.selfcheck.utils.api_client import SelfCheckAPIClient
from tests.selfcheck.utils.data_generator import TestDataGenerator


class TestRulesManagement(SelfCheckTestBase, PerformanceTestMixin, DataValidationMixin):
    """规则管理模块测试类"""
    
    def setUp(self):
        """测试前置设置"""
        super().setUp()
        self.api_client = SelfCheckAPIClient(self.base_url)
        self.api_client.login("admin", "Admin123!")
        self.data_generator = TestDataGenerator()
    
    def tearDown(self):
        """测试后清理"""
        self.api_client.close()
        super().tearDown()
    
    def test_TC001_rules_list_query(self):
        """TC001 - 规则列表查询测试"""
        print("\n=== TC001 - 规则列表查询测试 ===")
        
        # 创建测试数据
        test_rules = self.data_generator.generate_rule_data(5)
        created_rule_ids = []
        
        for rule_data in test_rules:
            result = self.api_client.create_rule(rule_data)
            self.assertTrue(result.get('success', False), "创建测试规则失败")
            rule_id = result.get('rule', {}).get('id')
            if rule_id:
                created_rule_ids.append(rule_id)
                self.created_resources['rules'].append(rule_id)
        
        # 测试规则列表查询
        response = self.api_client.get_rules(page=1, per_page=10)
        
        # 验证响应结构
        self.assertTrue(response.get('success', False), "获取规则列表失败")
        self.assertIn('rules', response, "响应中缺少rules字段")
        self.assertIn('total', response, "响应中缺少total字段")
        
        # 验证分页数据
        self.assert_pagination_data(response)
        
        # 验证规则数据结构
        rules = response.get('rules', [])
        if rules:
            rule = rules[0]
            required_fields = ['id', 'rule_name', 'rule_code', 'rule_type', 'status']
            self.assert_required_fields(rule, required_fields)
        
        print(f"✅ 规则列表查询测试通过，共获取到 {len(rules)} 条规则")
    
    def test_TC002_rules_search_functionality(self):
        """TC002 - 规则搜索功能测试"""
        print("\n=== TC002 - 规则搜索功能测试 ===")
        
        # 创建特定的测试规则
        test_rule = {
            "rule_name": "重复收费检查规则",
            "rule_code": "DUPLICATE_CHARGE_CHECK",
            "rule_description": "检查重复收费的规则",
            "rule_type": "重复收费",
            "city": "北京",
            "sql_content": "SELECT * FROM charges WHERE duplicate = 1"
        }
        
        result = self.api_client.create_rule(test_rule)
        self.assertTrue(result.get('success', False), "创建测试规则失败")
        rule_id = result.get('rule', {}).get('id')
        self.created_resources['rules'].append(rule_id)
        
        # 测试按规则名称搜索
        response = self.api_client.get_rules(rule_name="重复收费")
        self.assertTrue(response.get('success', False), "按名称搜索失败")
        
        rules = response.get('rules', [])
        self.assertGreater(len(rules), 0, "搜索结果为空")
        
        # 验证搜索结果包含关键词
        found_rule = False
        for rule in rules:
            if "重复收费" in rule.get('rule_name', ''):
                found_rule = True
                break
        self.assertTrue(found_rule, "搜索结果不包含期望的规则")
        
        # 测试按城市过滤
        response = self.api_client.get_rules(city="北京")
        self.assertTrue(response.get('success', False), "按城市过滤失败")
        
        # 测试按规则类型过滤
        response = self.api_client.get_rules(rule_type="重复收费")
        self.assertTrue(response.get('success', False), "按规则类型过滤失败")
        
        # 测试组合搜索
        response = self.api_client.get_rules(
            rule_name="重复收费",
            city="北京",
            rule_type="重复收费"
        )
        self.assertTrue(response.get('success', False), "组合搜索失败")
        
        print("✅ 规则搜索功能测试通过")
    
    def test_TC003_create_new_rule(self):
        """TC003 - 创建新规则测试"""
        print("\n=== TC003 - 创建新规则测试 ===")
        
        # 准备测试数据
        rule_data = {
            "rule_name": f"测试规则_{int(time.time())}",
            "rule_code": f"TEST_RULE_{int(time.time())}",
            "rule_description": "这是一个自动化测试创建的规则",
            "rule_type": "频次上限",
            "city": "上海",
            "sql_content": "SELECT * FROM test_table WHERE frequency > 5"
        }
        
        # 执行创建操作
        result = self.api_client.create_rule(rule_data)
        
        # 验证创建结果
        self.assertTrue(result.get('success', False), f"创建规则失败: {result.get('message', '')}")
        self.assertIn('rule', result, "响应中缺少rule字段")
        
        created_rule = result.get('rule', {})
        rule_id = created_rule.get('id')
        self.assertIsNotNone(rule_id, "创建的规则缺少ID")
        self.created_resources['rules'].append(rule_id)
        
        # 验证创建的规则数据
        self.assertEqual(created_rule.get('rule_name'), rule_data['rule_name'])
        self.assertEqual(created_rule.get('rule_code'), rule_data['rule_code'])
        self.assertEqual(created_rule.get('rule_type'), rule_data['rule_type'])
        
        print(f"✅ 规则创建测试通过，规则ID: {rule_id}")
    
    def test_TC004_edit_rule(self):
        """TC004 - 编辑规则测试"""
        print("\n=== TC004 - 编辑规则测试 ===")
        
        # 先创建一个规则
        original_rule = self.create_test_rule()
        rule_id = original_rule.get('rule', {}).get('id')
        
        # 准备更新数据
        update_data = {
            "rule_name": f"更新后的规则_{int(time.time())}",
            "rule_description": "这是更新后的规则描述",
            "rule_type": "金额限制"
        }
        
        # 执行更新操作
        result = self.api_client.update_rule(rule_id, update_data)
        
        # 验证更新结果
        self.assertTrue(result.get('success', False), f"更新规则失败: {result.get('message', '')}")
        
        # 获取更新后的规则验证
        updated_rule = self.api_client.get_rule(rule_id)
        self.assertTrue(updated_rule.get('success', False), "获取更新后的规则失败")
        
        rule_data = updated_rule.get('rule', {})
        self.assertEqual(rule_data.get('rule_name'), update_data['rule_name'])
        self.assertEqual(rule_data.get('rule_description'), update_data['rule_description'])
        self.assertEqual(rule_data.get('rule_type'), update_data['rule_type'])
        
        print(f"✅ 规则编辑测试通过，规则ID: {rule_id}")
    
    def test_TC005_soft_delete_rule(self):
        """TC005 - 规则软删除测试"""
        print("\n=== TC005 - 规则软删除测试 ===")
        
        # 创建测试规则
        test_rule = self.create_test_rule()
        rule_id = test_rule.get('rule', {}).get('id')
        
        # 执行软删除
        result = self.api_client.delete_rule(rule_id)
        self.assertTrue(result.get('success', False), f"删除规则失败: {result.get('message', '')}")
        
        # 验证规则状态变为deleted
        deleted_rule = self.api_client.get_rule(rule_id)
        self.assertTrue(deleted_rule.get('success', False), "获取删除后的规则失败")
        
        rule_data = deleted_rule.get('rule', {})
        self.assertEqual(rule_data.get('status'), 'deleted', "规则状态未正确更新为deleted")
        
        print(f"✅ 规则软删除测试通过，规则ID: {rule_id}")
    
    def test_TC006_restore_deleted_rule(self):
        """TC006 - 恢复已删除规则测试"""
        print("\n=== TC006 - 恢复已删除规则测试 ===")
        
        # 创建并删除规则
        test_rule = self.create_test_rule()
        rule_id = test_rule.get('rule', {}).get('id')
        
        # 先删除规则
        delete_result = self.api_client.delete_rule(rule_id)
        self.assertTrue(delete_result.get('success', False), "删除规则失败")
        
        # 恢复规则
        restore_result = self.api_client.restore_rule(rule_id)
        self.assertTrue(restore_result.get('success', False), f"恢复规则失败: {restore_result.get('message', '')}")
        
        # 验证规则状态恢复为active
        restored_rule = self.api_client.get_rule(rule_id)
        self.assertTrue(restored_rule.get('success', False), "获取恢复后的规则失败")
        
        rule_data = restored_rule.get('rule', {})
        self.assertEqual(rule_data.get('status'), 'active', "规则状态未正确恢复为active")
        
        print(f"✅ 规则恢复测试通过，规则ID: {rule_id}")
    
    def test_TC007_batch_delete_rules(self):
        """TC007 - 批量删除规则测试"""
        print("\n=== TC007 - 批量删除规则测试 ===")
        
        # 创建多个测试规则
        test_rules = self.data_generator.generate_rule_data(3)
        rule_ids = []
        
        for rule_data in test_rules:
            result = self.api_client.create_rule(rule_data)
            self.assertTrue(result.get('success', False), "创建测试规则失败")
            rule_id = result.get('rule', {}).get('id')
            rule_ids.append(rule_id)
            self.created_resources['rules'].append(rule_id)
        
        # 执行批量删除
        result = self.api_client.batch_delete_rules(rule_ids)
        self.assertTrue(result.get('success', False), f"批量删除失败: {result.get('message', '')}")
        
        # 验证所有规则都被删除
        for rule_id in rule_ids:
            rule = self.api_client.get_rule(rule_id)
            if rule.get('success', False):
                rule_data = rule.get('rule', {})
                self.assertEqual(rule_data.get('status'), 'deleted', f"规则 {rule_id} 状态未正确更新")
        
        print(f"✅ 批量删除测试通过，删除了 {len(rule_ids)} 个规则")
    
    def test_TC008_sql_test_functionality(self):
        """TC008 - SQL测试功能测试"""
        print("\n=== TC008 - SQL测试功能测试 ===")
        
        # 测试有效的SQL
        valid_sql = "SELECT * FROM test_table WHERE id > 0"
        result = self.api_client.test_rule_sql(valid_sql)
        self.assertTrue(result.get('success', False), "有效SQL测试失败")
        
        # 测试包含危险操作的SQL
        dangerous_sql = "DROP TABLE test_table"
        result = self.api_client.test_rule_sql(dangerous_sql)
        self.assertFalse(result.get('success', True), "危险SQL应该被拒绝")
        
        # 测试非SELECT语句
        non_select_sql = "UPDATE test_table SET value = 1"
        result = self.api_client.test_rule_sql(non_select_sql)
        self.assertFalse(result.get('success', True), "非SELECT语句应该被拒绝")
        
        print("✅ SQL测试功能测试通过")
    
    def test_TC011_large_data_performance(self):
        """TC011 - 大数据量加载性能测试"""
        print("\n=== TC011 - 大数据量加载性能测试 ===")
        
        # 创建大量规则（如果不存在）
        rules_count = 50
        test_rules = self.data_generator.generate_rule_data(rules_count)
        
        for rule_data in test_rules:
            result = self.api_client.create_rule(rule_data)
            if result.get('success', False):
                rule_id = result.get('rule', {}).get('id')
                if rule_id:
                    self.created_resources['rules'].append(rule_id)
        
        # 测试大数据量查询性能
        def query_large_data():
            return self.api_client.get_rules(page=1, per_page=50)
        
        result, response_time = self.measure_response_time(query_large_data)
        
        # 验证性能要求（3秒内）
        self.assertLessEqual(response_time, 3.0, f"大数据量查询性能不达标: {response_time:.2f}s > 3.0s")
        self.assertTrue(result.get('success', False), "大数据量查询失败")
        
        print(f"✅ 大数据量性能测试通过，响应时间: {response_time:.2f}s")
    
    def test_TC012_search_performance(self):
        """TC012 - 搜索性能测试"""
        print("\n=== TC012 - 搜索性能测试 ===")
        
        # 测试复杂搜索性能
        def complex_search():
            return self.api_client.get_rules(
                rule_name="测试",
                rule_type="重复收费",
                city="北京",
                page=1,
                per_page=20
            )
        
        result, response_time = self.measure_response_time(complex_search)
        
        # 验证性能要求（2秒内）
        self.assertLessEqual(response_time, 2.0, f"搜索性能不达标: {response_time:.2f}s > 2.0s")
        self.assertTrue(result.get('success', False), "复杂搜索失败")
        
        print(f"✅ 搜索性能测试通过，响应时间: {response_time:.2f}s")


if __name__ == '__main__':
    # 配置测试运行器
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestRulesManagement)
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)

    # 输出测试结果统计
    print(f"\n{'='*50}")
    print(f"测试结果统计:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"{'='*50}")
