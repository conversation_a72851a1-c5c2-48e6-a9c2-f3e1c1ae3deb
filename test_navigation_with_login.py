#!/usr/bin/env python3
"""测试登录后的导航功能"""

import requests
import json
import re

def test_navigation_with_login():
    """测试登录后的导航功能"""
    base_url = "http://127.0.0.1:5002"
    
    print("=== 登录后导航功能测试 ===")
    
    # 创建会话
    session = requests.Session()
    
    # 1. 登录
    print("\n1. 登录...")
    
    # 先获取登录页面
    login_page = session.get(f"{base_url}/auth/login")
    if login_page.status_code != 200:
        print(f"❌ 无法访问登录页面: {login_page.status_code}")
        return False
    
    # 提取CSRF token
    csrf_match = re.search(r'<meta name="csrf-token" content="([^"]+)"', login_page.text)
    login_csrf_token = csrf_match.group(1) if csrf_match else None
    
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    if login_csrf_token:
        login_data['csrf_token'] = login_csrf_token
    
    login_response = session.post(f"{base_url}/auth/login", data=login_data)
    
    # 检查是否登录成功
    if login_response.status_code == 200 or login_response.status_code == 302:
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
    
    # 2. 测试主页（登录后）
    print("\n2. 测试主页（登录后）...")
    main_response = session.get(f"{base_url}/")
    
    if main_response.status_code == 200:
        print("✅ 主页访问成功")
        
        # 检查是否包含方案配置按钮
        if '方案配置' in main_response.text:
            print("✅ 主页包含方案配置按钮")
        else:
            print("❌ 主页缺少方案配置按钮")
        
        # 检查是否包含左边栏导航
        if '方案管理' in main_response.text:
            print("✅ 左边栏包含方案管理")
        else:
            print("❌ 左边栏缺少方案管理")
            
        if '规则SQL生成器' in main_response.text:
            print("✅ 左边栏包含规则SQL生成器")
        else:
            print("❌ 左边栏缺少规则SQL生成器")
            
        # 检查自查自纠模块
        if '自查自纠' in main_response.text:
            print("✅ 主页包含自查自纠模块")
            
            # 检查自查自纠模块中的按钮
            selfcheck_section_start = main_response.text.find('自查自纠')
            if selfcheck_section_start != -1:
                # 查找自查自纠模块的内容
                selfcheck_section = main_response.text[selfcheck_section_start:selfcheck_section_start+2000]
                
                if '规则管理' in selfcheck_section:
                    print("  ✅ 自查自纠模块包含规则管理按钮")
                else:
                    print("  ❌ 自查自纠模块缺少规则管理按钮")
                
                if '方案配置' in selfcheck_section or '方案管理' in selfcheck_section:
                    print("  ✅ 自查自纠模块包含方案配置按钮")
                else:
                    print("  ❌ 自查自纠模块缺少方案配置按钮")
                
                if '数据上传' in selfcheck_section:
                    print("  ✅ 自查自纠模块包含数据上传按钮")
                else:
                    print("  ❌ 自查自纠模块缺少数据上传按钮")
        else:
            print("❌ 主页缺少自查自纠模块")
    else:
        print(f"❌ 主页访问失败: {main_response.status_code}")
    
    # 3. 测试方案管理页面（登录后）
    print("\n3. 测试方案管理页面（登录后）...")
    schemes_response = session.get(f"{base_url}/selfcheck/schemes")
    
    if schemes_response.status_code == 200:
        print("✅ 方案管理页面访问成功")
        
        # 检查页面内容
        if '方案管理' in schemes_response.text and '新建方案' in schemes_response.text:
            print("  ✅ 方案管理页面内容正常")
        else:
            print("  ❌ 方案管理页面内容异常")
    else:
        print(f"❌ 方案管理页面访问失败: {schemes_response.status_code}")
    
    # 4. 测试规则SQL生成器页面（登录后）
    print("\n4. 测试规则SQL生成器页面（登录后）...")
    sql_gen_response = session.get(f"{base_url}/rules/sql_generator")
    
    if sql_gen_response.status_code == 200:
        print("✅ 规则SQL生成器页面访问成功")
        
        # 检查页面内容
        if 'SQL生成器' in sql_gen_response.text or '规则SQL生成器' in sql_gen_response.text:
            print("  ✅ 规则SQL生成器页面内容正常")
        else:
            print("  ❌ 规则SQL生成器页面内容异常")
    else:
        print(f"❌ 规则SQL生成器页面访问失败: {sql_gen_response.status_code}")
    
    # 5. 测试左边栏导航链接
    print("\n5. 测试左边栏导航链接...")
    
    # 检查主页HTML中的导航链接
    if main_response.status_code == 200:
        # 检查方案管理链接
        if '/selfcheck/schemes' in main_response.text:
            print("  ✅ 左边栏包含方案管理链接")
        else:
            print("  ❌ 左边栏缺少方案管理链接")
        
        # 检查规则SQL生成器链接
        if '/rules/sql_generator' in main_response.text:
            print("  ✅ 左边栏包含规则SQL生成器链接")
        else:
            print("  ❌ 左边栏缺少规则SQL生成器链接")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_navigation_with_login()
