#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def simple_verify_permissions():
    """简单验证权限结构"""
    app = create_app()
    
    with app.app_context():
        try:
            print('=== 验证三级权限结构 ===')
            
            # 查询第一级权限（大模块）
            level1_result = db.session.execute(text("""
                SELECT id, name, code, module, resource_type, sort_order
                FROM permissions 
                WHERE parent_id IS NULL
                ORDER BY sort_order
            """))
            
            level1_permissions = level1_result.fetchall()
            
            for level1 in level1_permissions:
                print(f'\n🏢 【{level1[4]}】{level1[1]} ({level1[2]}) - 模块: {level1[3]} - 排序: {level1[5]}')
                
                # 查询第二级权限（页面）
                level2_result = db.session.execute(text("""
                    SELECT id, name, code, resource_type, sort_order
                    FROM permissions 
                    WHERE parent_id = :parent_id
                    ORDER BY sort_order
                """), {'parent_id': level1[0]})
                
                level2_permissions = level2_result.fetchall()
                
                for level2 in level2_permissions:
                    print(f'  📄 【{level2[3]}】{level2[1]} ({level2[2]}) - 排序: {level2[4]}')
                    
                    # 查询第三级权限（功能按钮）
                    level3_result = db.session.execute(text("""
                        SELECT id, name, code, resource_type, sort_order
                        FROM permissions 
                        WHERE parent_id = :parent_id
                        ORDER BY sort_order
                    """), {'parent_id': level2[0]})
                    
                    level3_permissions = level3_result.fetchall()
                    
                    for level3 in level3_permissions:
                        print(f'    🔘 【{level3[3]}】{level3[1]} ({level3[2]}) - 排序: {level3[4]}')
            
            # 统计信息
            print('\n=== 权限统计信息 ===')
            stats_result = db.session.execute(text("""
                SELECT 
                    module,
                    COUNT(*) as total_count,
                    SUM(CASE WHEN parent_id IS NULL THEN 1 ELSE 0 END) as level1_count,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count
                FROM permissions 
                GROUP BY module
                ORDER BY module
            """))
            
            stats = stats_result.fetchall()
            print(f'{"模块":<15} {"总计":<6} {"一级":<6} {"激活":<6}')
            print('-' * 40)
            for stat in stats:
                print(f'{stat[0]:<15} {stat[1]:<6} {stat[2]:<6} {stat[3]:<6}')
            
            # 总计
            total_result = db.session.execute(text("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN parent_id IS NULL THEN 1 ELSE 0 END) as level1,
                    SUM(CASE WHEN parent_id IS NOT NULL AND 
                        EXISTS(SELECT 1 FROM permissions p2 WHERE p2.id = permissions.parent_id AND p2.parent_id IS NULL) 
                        THEN 1 ELSE 0 END) as level2,
                    SUM(CASE WHEN parent_id IS NOT NULL AND 
                        EXISTS(SELECT 1 FROM permissions p2 WHERE p2.id = permissions.parent_id AND p2.parent_id IS NOT NULL) 
                        THEN 1 ELSE 0 END) as level3
                FROM permissions
            """))
            
            total = total_result.fetchone()
            if total:
                print(f'\n总计权限: {total[0]} 个')
                print(f'  - 第一级（大模块）: {total[1]} 个')
                print(f'  - 第二级（页面）: {total[2]} 个')
                print(f'  - 第三级（按钮）: {total[3]} 个')
            
            print('\n✅ 权限结构验证完成！')
                
        except Exception as e:
            print(f"验证失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    simple_verify_permissions()
