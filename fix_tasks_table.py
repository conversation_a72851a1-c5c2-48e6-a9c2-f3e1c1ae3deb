#!/usr/bin/env python3
"""
修复selfcheck_tasks表结构
将旧的upload_id和rule_id字段设置为可空，或者删除它们
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def fix_tasks_table():
    """修复任务表结构"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            print("=== 修复selfcheck_tasks表结构 ===")
            
            # 1. 检查当前数据
            print("\n1. 检查当前数据...")
            count_query = "SELECT COUNT(*) as total FROM selfcheck_tasks"
            count_result = db_manager.execute_query(count_query)
            total_count = count_result[0]['total'] if count_result else 0
            print(f"当前表中有 {total_count} 条数据")
            
            if total_count > 0:
                # 查看现有数据
                data_query = "SELECT id, task_name, upload_id, rule_id, data_source, scheme_id FROM selfcheck_tasks"
                existing_data = db_manager.execute_query(data_query)
                print("现有数据:")
                for row in existing_data:
                    print(f"  ID: {row['id']}, 任务: {row['task_name']}, 上传ID: {row['upload_id']}, 规则ID: {row['rule_id']}, 数据源: {row.get('data_source', 'NULL')}, 方案ID: {row.get('scheme_id', 'NULL')}")
            
            # 2. 方案1：将旧字段设置为可空
            print("\n2. 将旧字段设置为可空...")
            
            try:
                # 修改upload_id为可空
                modify_upload_id_sql = """
                ALTER TABLE selfcheck_tasks 
                MODIFY upload_id NUMBER NULL
                """
                db_manager.execute_update(modify_upload_id_sql)
                print("  ✅ upload_id字段设置为可空")
            except Exception as e:
                print(f"  ❌ 修改upload_id失败: {str(e)}")
            
            try:
                # 修改rule_id为可空
                modify_rule_id_sql = """
                ALTER TABLE selfcheck_tasks 
                MODIFY rule_id NUMBER NULL
                """
                db_manager.execute_update(modify_rule_id_sql)
                print("  ✅ rule_id字段设置为可空")
            except Exception as e:
                print(f"  ❌ 修改rule_id失败: {str(e)}")
            
            # 3. 为现有数据设置默认值（如果data_source和scheme_id为空）
            print("\n3. 为现有数据设置默认值...")
            
            if total_count > 0:
                # 检查是否有数据需要更新
                check_query = """
                SELECT COUNT(*) as need_update 
                FROM selfcheck_tasks 
                WHERE data_source IS NULL OR scheme_id IS NULL
                """
                check_result = db_manager.execute_query(check_query)
                need_update = check_result[0]['need_update'] if check_result else 0
                
                if need_update > 0:
                    print(f"  需要更新 {need_update} 条数据")
                    
                    # 为没有data_source的记录设置默认值
                    update_data_source_sql = """
                    UPDATE selfcheck_tasks 
                    SET data_source = 'pool1' 
                    WHERE data_source IS NULL
                    """
                    db_manager.execute_update(update_data_source_sql)
                    
                    # 为没有scheme_id的记录设置默认值
                    update_scheme_id_sql = """
                    UPDATE selfcheck_tasks 
                    SET scheme_id = 1 
                    WHERE scheme_id IS NULL
                    """
                    db_manager.execute_update(update_scheme_id_sql)
                    
                    print("  ✅ 默认值设置完成")
                else:
                    print("  ✅ 所有数据都有完整的字段值")
            
            # 4. 验证修复结果
            print("\n4. 验证修复结果...")
            
            # 检查表结构
            structure_query = """
            SELECT column_name, data_type, nullable
            FROM user_tab_columns 
            WHERE table_name = 'SELFCHECK_TASKS'
            AND column_name IN ('UPLOAD_ID', 'RULE_ID', 'DATA_SOURCE', 'SCHEME_ID')
            ORDER BY column_name
            """
            columns = db_manager.execute_query(structure_query)
            
            print("关键字段状态:")
            for col in columns:
                nullable = "NULL" if col['nullable'] == 'Y' else "NOT NULL"
                print(f"  {col['column_name']}: {col['data_type']} {nullable}")
            
            # 检查数据完整性
            final_count_query = "SELECT COUNT(*) as total FROM selfcheck_tasks"
            final_count_result = db_manager.execute_query(final_count_query)
            final_total_count = final_count_result[0]['total'] if final_count_result else 0
            
            print(f"\n修复后数据总数: {final_total_count}")
            
            if final_total_count > 0:
                # 检查数据完整性
                integrity_query = """
                SELECT 
                    COUNT(*) as total,
                    COUNT(data_source) as has_data_source,
                    COUNT(scheme_id) as has_scheme_id,
                    COUNT(upload_id) as has_upload_id,
                    COUNT(rule_id) as has_rule_id
                FROM selfcheck_tasks
                """
                integrity_result = db_manager.execute_query(integrity_query)
                if integrity_result:
                    result = integrity_result[0]
                    print("数据完整性检查:")
                    print(f"  总记录数: {result['total']}")
                    print(f"  有data_source: {result['has_data_source']}")
                    print(f"  有scheme_id: {result['has_scheme_id']}")
                    print(f"  有upload_id: {result['has_upload_id']}")
                    print(f"  有rule_id: {result['has_rule_id']}")
            
            print("\n✅ 表结构修复完成！")
            print("\n现在可以使用新的API创建任务了：")
            print("  - data_source: 数据源ID")
            print("  - scheme_id: 方案ID")
            print("  - upload_id 和 rule_id 现在是可选的")
            
        except Exception as e:
            print(f"❌ 修复失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    fix_tasks_table()
