#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入规则调试测试脚本
"""

import sys
import os
import time
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    
    try:
        from app.selfcheck.database import get_db_manager, get_batch_import_db_manager
        
        # 测试普通连接
        print("1. 测试普通数据库连接...")
        db_manager = get_db_manager()
        
        # 执行简单查询
        result = db_manager.execute_query("SELECT 1 FROM DUAL")
        if result:
            print("✅ 普通数据库连接正常")
        else:
            print("❌ 普通数据库连接失败")
            
        # 测试批量导入连接
        print("2. 测试批量导入数据库连接...")
        batch_db_manager = get_batch_import_db_manager()
        
        # 检查连接池状态
        pool_status = batch_db_manager.get_pool_status()
        if pool_status:
            print(f"✅ 批量导入连接池状态: 已打开={pool_status['opened']}, 繁忙={pool_status['busy']}")
        else:
            print("⚠️ 批量导入连接池不可用")
            
        # 执行查询测试
        result = batch_db_manager.execute_query("SELECT 1 FROM DUAL")
        if result:
            print("✅ 批量导入数据库连接正常")
        else:
            print("❌ 批量导入数据库连接失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rule_history_query():
    """测试规则历史查询"""
    print("\n=== 测试规则历史查询 ===")
    
    try:
        from app.selfcheck.database import get_batch_import_db_manager
        
        batch_db_manager = get_batch_import_db_manager()
        
        # 查询规则历史表
        query = """
        SELECT COUNT(*) as total_count 
        FROM rule_sql_history 
        WHERE ROWNUM <= 10
        """
        
        print("执行查询: 获取规则历史表记录数...")
        result = batch_db_manager.execute_query(query)
        
        if result:
            total_count = result[0]['total_count']
            print(f"✅ 规则历史表查询成功，总记录数: {total_count}")
            
            if total_count > 0:
                # 获取一些示例数据
                sample_query = """
                SELECT rule_id, compare_id, visit_type, rule_name, city
                FROM rule_sql_history 
                WHERE ROWNUM <= 5
                """
                sample_result = batch_db_manager.execute_query(sample_query)
                
                print("示例数据:")
                for i, row in enumerate(sample_result, 1):
                    print(f"  {i}. rule_id={row['rule_id']}, compare_id={row['compare_id']}, visit_type={row['visit_type']}")
                    
            return True
        else:
            print("❌ 规则历史表查询失败")
            return False
            
    except Exception as e:
        print(f"❌ 规则历史查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_simulation():
    """模拟导入过程"""
    print("\n=== 模拟导入过程 ===")
    
    try:
        from app.selfcheck.services import RuleService
        
        # 获取一些可导入的规则
        print("1. 获取可导入规则...")
        importable_rules = RuleService.get_importable_rules(page=1, per_page=5)
        
        if importable_rules['rules']:
            print(f"✅ 找到 {len(importable_rules['rules'])} 个可导入规则")
            
            # 选择第一个规则进行测试
            first_rule = importable_rules['rules'][0]
            compare_id = first_rule['compare_id']
            visit_type = first_rule['visit_type']
            
            print(f"2. 测试导入规则: compare_id={compare_id}, visit_type={visit_type}")
            
            # 模拟导入
            result = RuleService.import_rules_from_history([compare_id], [visit_type], 1)
            
            print(f"✅ 导入结果: 成功={result['imported_count']}, 错误={len(result['errors'])}")
            
            if result['errors']:
                print("错误信息:")
                for error in result['errors']:
                    print(f"  - {error}")
                    
            return True
        else:
            print("⚠️ 没有找到可导入的规则")
            return False
            
    except Exception as e:
        print(f"❌ 导入模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始导入规则调试测试...")
    
    # 测试数据库连接
    if not test_database_connection():
        print("数据库连接测试失败，退出")
        sys.exit(1)
    
    # 测试规则历史查询
    if not test_rule_history_query():
        print("规则历史查询测试失败，退出")
        sys.exit(1)
    
    # 模拟导入过程
    if not test_import_simulation():
        print("导入模拟测试失败")
    
    print("\n调试测试完成")
