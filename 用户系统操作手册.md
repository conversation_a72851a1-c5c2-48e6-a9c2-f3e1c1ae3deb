# MICRA自查自纠模块用户操作手册

**文档版本：** V1.0  
**编写日期：** 2025年1月7日  
**适用对象：** 医疗机构数据管理人员、质控人员  
**文档类型：** 用户操作手册  

## 1. 系统概述

### 1.1 系统介绍
MICRA自查自纠模块是专为医疗机构设计的数据质量检查工具，帮助您快速发现和纠正医疗数据中的问题，提高数据质量和合规性。

### 1.2 主要功能
- **数据上传：** 支持CSV、DMP、DP、BAK等格式文件上传
- **规则管理：** 灵活配置各种数据检查规则
- **方案管理：** 组合多个规则形成检查方案
- **任务执行：** 自动执行数据检查任务
- **结果分析：** 详细的检查结果和统计报告

### 1.3 用户角色
- **数据管理员：** 负责数据上传、方案配置、任务执行
- **质控人员：** 负责规则制定、结果审核
- **查看者：** 查看检查结果和报告

## 2. 系统登录

### 2.1 登录方式

#### 2.1.1 用户名密码登录
1. 打开浏览器，访问系统地址：`https://your-domain.com`
2. 在登录页面输入用户名和密码
3. 点击"登录"按钮

#### 2.1.2 微信登录（如果启用）
1. 点击"微信登录"按钮
2. 使用微信扫描二维码
3. 在微信中确认授权登录

### 2.2 首次登录
首次登录时，系统会要求您：
1. 修改初始密码
2. 完善个人信息
3. 绑定微信账号（可选）

### 2.3 忘记密码
如果忘记密码，请：
1. 点击登录页面的"忘记密码"链接
2. 联系系统管理员重置密码
3. 或通过绑定的微信账号登录

## 3. 系统导航

### 3.1 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏：用户信息、退出登录                              │
├─────────────────────────────────────────────────────────┤
│ 左侧菜单栏          │           主内容区域                │
│ - 首页             │                                   │
│ - 数据上传          │                                   │
│ - 方案管理          │                                   │
│ - 规则管理          │                                   │
│ - 任务管理          │                                   │
│ - 结果查看          │                                   │
└─────────────────────────────────────────────────────────┘
```

### 3.2 菜单说明
- **首页：** 系统概览和快捷操作
- **数据上传：** 上传待检查的数据文件
- **方案管理：** 配置和管理检查方案
- **规则管理：** 查看和管理检查规则
- **任务管理：** 创建和监控检查任务
- **结果查看：** 查看检查结果和报告

### 3.3 快捷操作
- **返回首页：** 点击页面左上角的"首页"按钮
- **刷新页面：** 点击浏览器刷新按钮或按F5键
- **帮助信息：** 点击页面右上角的"?"图标

## 4. 数据上传操作

### 4.1 上传文件

#### 4.1.1 支持的文件格式
- **CSV文件：** 逗号分隔值文件，常用于数据导出
- **DMP文件：** Oracle数据库导出文件
- **DP文件：** 数据处理文件
- **BAK文件：** 数据库备份文件

#### 4.1.2 文件大小限制
- 单个文件不超过10MB
- 建议文件大小在1-5MB之间以获得最佳性能

#### 4.1.3 上传步骤
1. 点击左侧菜单"数据上传"
2. 在上传区域拖拽文件或点击"选择文件"按钮
3. 选择要上传的文件
4. 等待上传完成，查看上传状态

#### 4.1.4 上传状态说明
- **待处理：** 文件上传成功，等待系统处理
- **验证中：** 系统正在验证文件格式和内容
- **已验证：** 文件验证通过，可用于检查任务
- **失败：** 文件验证失败，需要重新上传

### 4.2 管理上传记录

#### 4.2.1 查看上传记录
1. 在"数据上传"页面下方查看上传记录列表
2. 可以看到文件名、大小、状态、上传时间等信息

#### 4.2.2 搜索和筛选
- **按文件名搜索：** 在搜索框输入文件名关键词
- **按文件类型筛选：** 选择特定的文件类型
- **按状态筛选：** 选择特定的处理状态

#### 4.2.3 查看详情
1. 点击记录行的"查看详情"按钮
2. 可以看到文件的详细信息和处理结果
3. 如果处理失败，可以查看具体的错误信息

#### 4.2.4 删除记录
1. 点击记录行的"删除"按钮
2. 确认删除操作
3. 注意：正在使用的文件不能删除

## 5. 方案管理操作

### 5.1 查看方案列表

#### 5.1.1 方案信息
每个方案包含以下信息：
- **方案名称：** 方案的标识名称
- **描述：** 方案的详细说明
- **状态：** 启用/禁用状态
- **规则数量：** 包含的检查规则数量
- **创建时间：** 方案创建的时间

#### 5.1.2 搜索方案
1. 在搜索框输入方案名称关键词
2. 点击"搜索"按钮查找匹配的方案
3. 点击"重置"按钮清空搜索条件

### 5.2 查看方案详情

#### 5.2.1 进入方案详情
1. 点击方案列表中的方案名称
2. 或点击"查看详情"按钮

#### 5.2.2 方案规则列表
在方案详情页面可以看到：
- **序号：** 规则在方案中的顺序（1, 2, 3...）
- **规则名称：** 检查规则的名称
- **规则类型：** 规则的分类类型
- **状态：** 规则的启用/禁用状态
- **操作：** 可执行的操作按钮

#### 5.2.3 规则状态管理
- **启用规则：** 点击"启用"按钮激活规则
- **禁用规则：** 点击"禁用"按钮停用规则
- **查看规则：** 点击"查看"按钮查看规则详情

### 5.3 添加规则到方案

#### 5.3.1 单个添加
1. 在方案详情页面点击"添加规则"按钮
2. 在弹出的规则列表中选择要添加的规则
3. 点击"确定"完成添加

#### 5.3.2 批量添加
1. 点击"批量添加"按钮
2. 在规则列表中勾选多个规则
3. 点击"添加选中规则"按钮
4. 系统会并发处理，提高添加速度

### 5.4 从方案中移除规则

#### 5.4.1 单个移除
1. 在方案规则列表中找到要移除的规则
2. 点击该规则行的"移除"按钮
3. 确认移除操作

#### 5.4.2 注意事项
- 移除规则不会删除规则本身，只是从方案中移除
- 移除后的规则可以重新添加到方案中
- 正在执行的任务不会受到影响

## 6. 规则管理操作

### 6.1 查看规则列表

#### 6.1.1 规则信息
规则列表显示以下信息：
- **规则名称：** 规则的标识名称
- **规则类型：** 规则的分类（如重复检查、格式验证等）
- **城市：** 适用的城市或地区
- **就诊类型：** 门诊/住院/通用
- **数据类型：** 定量/定性
- **状态：** 启用/禁用状态

#### 6.1.2 搜索和筛选
- **按名称搜索：** 输入规则名称关键词
- **按类型筛选：** 选择特定的规则类型
- **按城市筛选：** 选择特定的城市
- **按状态筛选：** 选择启用或禁用的规则

### 6.2 查看规则详情

#### 6.2.1 规则基本信息
点击规则名称可以查看：
- **规则描述：** 规则的详细说明
- **SQL内容：** 规则的检查逻辑（只读）
- **创建信息：** 创建人和创建时间
- **使用情况：** 被哪些方案使用

#### 6.2.2 规则使用统计
- **使用次数：** 规则被执行的总次数
- **成功率：** 规则执行成功的比例
- **平均执行时间：** 规则执行的平均耗时
- **最近使用时间：** 规则最后一次被使用的时间

### 6.3 规则操作权限

#### 6.3.1 查看权限
所有用户都可以：
- 查看规则列表
- 查看规则详情
- 查看规则使用统计

#### 6.3.2 管理权限
具有管理权限的用户可以：
- 创建新规则
- 编辑现有规则
- 启用/禁用规则
- 删除未使用的规则

## 7. 任务管理操作

### 7.1 创建检查任务

#### 7.1.1 任务基本信息
1. 点击"任务管理"菜单
2. 点击"创建任务"按钮
3. 填写任务基本信息：
   - **任务名称：** 给任务起一个有意义的名称
   - **任务描述：** 简要说明任务的目的

#### 7.1.2 选择数据源
1. 在"数据源"下拉列表中选择要检查的数据文件
2. 只能选择状态为"已验证"的文件
3. 只显示您有权限使用的数据源

#### 7.1.3 选择检查方案
1. 在"检查方案"下拉列表中选择要使用的方案
2. 只能选择您有权限使用的方案
3. 可以查看方案包含的规则数量

#### 7.1.4 确认创建
1. 检查所有信息是否正确
2. 点击"创建任务"按钮
3. 系统会自动开始执行任务

### 7.2 监控任务执行

#### 7.2.1 任务状态
任务执行过程中会显示以下状态：
- **待执行：** 任务已创建，等待系统处理
- **执行中：** 任务正在执行，显示进度百分比
- **已完成：** 任务执行完成，可以查看结果
- **失败：** 任务执行失败，可以查看错误信息
- **已取消：** 任务被用户取消

#### 7.2.2 查看执行进度
1. 在任务列表中可以看到执行进度条
2. 进度条显示当前执行的百分比
3. 可以看到正在执行的规则名称

#### 7.2.3 任务操作
- **查看详情：** 点击任务名称查看详细信息
- **取消任务：** 点击"取消"按钮停止执行中的任务
- **重新执行：** 点击"重新执行"按钮重新运行失败的任务
- **删除任务：** 点击"删除"按钮删除不需要的任务

### 7.3 查看任务详情

#### 7.3.1 任务概览
任务详情页面显示：
- **任务基本信息：** 名称、描述、创建时间
- **执行信息：** 开始时间、结束时间、执行耗时
- **数据源信息：** 使用的数据文件和记录数
- **方案信息：** 使用的检查方案和规则数量

#### 7.3.2 执行结果统计
- **总规则数：** 方案包含的规则总数
- **执行成功：** 成功执行的规则数量
- **执行失败：** 执行失败的规则数量
- **发现问题：** 检查出问题的规则数量
- **问题记录数：** 发现的问题记录总数

#### 7.3.3 规则执行详情
可以查看每个规则的执行情况：
- **规则名称：** 执行的规则名称
- **执行状态：** 成功/失败
- **执行时间：** 规则执行耗时
- **问题数量：** 发现的问题记录数
- **详细结果：** 点击查看具体的问题记录

## 8. 结果查看操作

### 8.1 查看检查结果

#### 8.1.1 结果概览
在结果查看页面可以看到：
- **任务列表：** 所有已完成的检查任务
- **结果统计：** 每个任务的问题统计
- **趋势分析：** 数据质量的变化趋势

#### 8.1.2 筛选和搜索
- **按任务名称搜索：** 输入任务名称关键词
- **按执行时间筛选：** 选择特定的时间范围
- **按结果状态筛选：** 选择有问题或无问题的任务

### 8.2 查看详细结果

#### 8.2.1 问题记录列表
点击任务可以查看详细的问题记录：
- **问题类型：** 问题的分类
- **问题描述：** 具体的问题说明
- **涉及字段：** 出现问题的数据字段
- **问题记录：** 具体的问题数据记录

#### 8.2.2 问题数据导出
1. 选择要导出的问题记录
2. 点击"导出"按钮
3. 选择导出格式（Excel、CSV）
4. 下载导出文件

### 8.3 生成检查报告

#### 8.3.1 报告类型
系统支持生成以下类型的报告：
- **概要报告：** 检查结果的总体统计
- **详细报告：** 包含所有问题记录的详细报告
- **对比报告：** 多次检查结果的对比分析

#### 8.3.2 生成报告
1. 选择要生成报告的任务
2. 点击"生成报告"按钮
3. 选择报告类型和格式
4. 等待报告生成完成
5. 下载生成的报告文件

## 9. 常见问题解答

### 9.1 登录问题

**Q: 忘记密码怎么办？**
A: 请联系系统管理员重置密码，或使用绑定的微信账号登录。

**Q: 微信登录失败怎么办？**
A: 请检查网络连接，确保微信账号已正确绑定，或联系管理员。

### 9.2 文件上传问题

**Q: 支持哪些文件格式？**
A: 支持CSV、DMP、DP、BAK格式的文件。

**Q: 文件上传失败怎么办？**
A: 请检查文件格式和大小是否符合要求，确保网络连接正常。

**Q: 文件状态一直是"验证中"怎么办？**
A: 大文件验证需要时间，请耐心等待。如果长时间无变化，请联系管理员。

### 9.3 任务执行问题

**Q: 任务执行失败怎么办？**
A: 查看任务详情中的错误信息，检查数据源和方案配置是否正确。

**Q: 可以同时执行多个任务吗？**
A: 可以，系统支持并发执行多个任务。

**Q: 任务执行时间很长怎么办？**
A: 执行时间取决于数据量和规则复杂度，大数据量需要更长时间。

### 9.4 结果查看问题

**Q: 如何理解检查结果？**
A: 检查结果显示发现的数据质量问题，需要根据具体业务规则进行分析。

**Q: 可以导出检查结果吗？**
A: 可以，支持导出Excel和CSV格式的结果文件。

## 10. 联系支持

### 10.1 技术支持
- **支持热线：** 400-XXX-XXXX
- **支持邮箱：** <EMAIL>
- **工作时间：** 周一至周五 9:00-18:00

### 10.2 在线帮助
- **用户手册：** 系统内置完整的操作手册
- **视频教程：** 提供操作演示视频
- **FAQ：** 常见问题和解答

### 10.3 培训服务
- **在线培训：** 定期举办在线培训课程
- **现场培训：** 可安排专业人员现场培训
- **培训资料：** 提供完整的培训材料和练习数据

---

**文档状态：** 已审核  
**版本历史：**
- V1.0 (2025-01-07): 初始版本，包含完整操作指南
