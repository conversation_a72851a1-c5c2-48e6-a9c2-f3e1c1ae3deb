#!/usr/bin/env python3
"""
测试规则管理API端点
"""

import requests
import json

def test_api_endpoints():
    base_url = "http://127.0.0.1:5002"
    session = requests.Session()
    
    # 先登录
    print("🔐 正在登录...")
    login_page = session.get(f"{base_url}/auth/login")
    
    import re
    csrf_patterns = [
        r'name="csrf_token" value="([^"]+)"',
        r'<input[^>]*name="csrf_token"[^>]*value="([^"]+)"',
    ]
    
    csrf_token = None
    for pattern in csrf_patterns:
        csrf_match = re.search(pattern, login_page.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            break
    
    login_data = {
        'username': 'admin',
        'password': 'Admin123!',
        'remember_me': False
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    response = session.post(f"{base_url}/auth/login", data=login_data, allow_redirects=True)
    
    if response.status_code == 200 and '/auth/login' not in response.url:
        print("✅ 登录成功")
    else:
        print("❌ 登录失败")
        return
    
    # 测试各个API端点
    api_endpoints = [
        '/rules/api/behavior_types',
        '/rules/api/city_types', 
        '/rules/api/rule_sources',
        '/rules/api/type_types',
        '/rules/api/rule_type_types'
    ]
    
    for endpoint in api_endpoints:
        print(f"\n🧪 测试: {endpoint}")
        try:
            response = session.get(f"{base_url}{endpoint}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    types = data.get('types', [])
                    print(f"✅ 成功 - 返回 {len(types)} 个选项")
                    if types:
                        print(f"   示例: {types[:3]}")
                else:
                    print(f"❌ API返回失败: {data.get('error', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 异常: {str(e)}")

if __name__ == '__main__':
    test_api_endpoints()
