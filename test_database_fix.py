#!/usr/bin/env python3
"""
测试数据库修复效果的脚本
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_normal_database_operations():
    """测试普通数据库操作（不使用连接池）"""
    print("=== 测试普通数据库操作 ===")
    
    try:
        from app.selfcheck.database import get_db_manager, execute_query, execute_update
        
        # 获取数据库管理器
        db_manager = get_db_manager()
        print("✅ 普通数据库管理器获取成功")
        
        # 测试查询操作
        print("\n1. 测试普通查询操作...")
        query = "SELECT COUNT(*) as count FROM selfcheck_rules"
        result = execute_query(query)
        print(f"✅ 普通查询成功，规则总数: {result[0]['count'] if result else 0}")
        
        # 测试删除操作（模拟）
        print("\n2. 测试删除操作...")
        from app.selfcheck.services import RuleService
        
        # 获取一个测试规则
        test_rules = execute_query("SELECT id FROM selfcheck_rules WHERE status = 'active' AND ROWNUM <= 1")
        if test_rules:
            test_rule_id = test_rules[0]['id']
            print(f"找到测试规则ID: {test_rule_id}")
            
            # 测试删除（软删除）
            try:
                success = RuleService.delete_rule(test_rule_id)
                if success:
                    print("✅ 软删除操作成功")
                    
                    # 测试恢复
                    from app.selfcheck.models import SelfCheckRule
                    rule = SelfCheckRule.get_by_id(test_rule_id)
                    if rule and rule.restore():
                        print("✅ 恢复操作成功")
                    else:
                        print("⚠️ 恢复操作失败")
                else:
                    print("⚠️ 软删除操作失败")
            except Exception as e:
                print(f"⚠️ 删除测试失败: {e}")
        else:
            print("⚠️ 没有找到可用的测试规则")
        
        print("\n✅ 普通数据库操作测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 普通数据库操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_import_operations():
    """测试批量导入操作（使用连接池）"""
    print("\n=== 测试批量导入操作 ===")
    
    try:
        from app.selfcheck.database import get_batch_import_db_manager, batch_import_execute_query, batch_import_execute_update
        
        # 获取批量导入数据库管理器
        batch_db_manager = get_batch_import_db_manager()
        print("✅ 批量导入数据库管理器获取成功")
        
        # 检查连接池状态
        print("\n1. 检查批量导入连接池状态...")
        pool_status = batch_db_manager.get_pool_status()
        if pool_status:
            print(f"✅ 批量导入连接池状态: 已打开={pool_status['opened']}, 繁忙={pool_status['busy']}, 最大={pool_status['max']}, 最小={pool_status['min']}")
        else:
            print("⚠️ 批量导入连接池不可用，使用直接连接")
        
        # 测试批量导入查询
        print("\n2. 测试批量导入查询操作...")
        query = "SELECT COUNT(*) as count FROM rule_sql_history WHERE ROWNUM <= 10"
        result = batch_import_execute_query(query)
        print(f"✅ 批量导入查询成功，结果: {result[0]['count'] if result else 0}")
        
        # 测试命名参数查询
        print("\n3. 测试命名参数查询...")
        param_query = "SELECT rule_id, rule_name FROM rule_sql_history WHERE rule_id = :rule_id AND ROWNUM <= 1"
        param_result = batch_import_execute_query(param_query, {'rule_id': 401})
        if param_result:
            print(f"✅ 命名参数查询成功，规则名称: {param_result[0].get('rule_name', 'N/A')}")
        else:
            print("⚠️ 命名参数查询无结果")
        
        # 测试并发操作
        print("\n4. 测试并发操作...")
        import threading
        from concurrent.futures import ThreadPoolExecutor
        
        def concurrent_query(thread_id):
            """并发查询测试"""
            try:
                query = "SELECT :thread_id as thread_id, COUNT(*) as count FROM rule_sql_history"
                result = batch_import_execute_query(query, {'thread_id': thread_id})
                print(f"线程 {thread_id}: 查询成功，结果: {result[0] if result else 'None'}")
                return True
            except Exception as e:
                print(f"线程 {thread_id}: 查询失败: {e}")
                return False
        
        # 使用3个并发线程测试
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(concurrent_query, i) for i in range(3)]
            results = [future.result() for future in futures]
        
        success_count = sum(results)
        print(f"并发查询测试: {success_count}/3 成功")
        
        # 再次检查连接池状态
        print("\n5. 检查连接池最终状态...")
        final_pool_status = batch_db_manager.get_pool_status()
        if final_pool_status:
            print(f"✅ 最终连接池状态: 已打开={final_pool_status['opened']}, 繁忙={final_pool_status['busy']}")
            if final_pool_status['busy'] == 0:
                print("✅ 连接池已恢复正常，无繁忙连接")
            else:
                print("⚠️ 连接池仍有繁忙连接")
        
        print("\n✅ 批量导入操作测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 批量导入操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rule_import_simulation():
    """模拟规则导入过程"""
    print("\n=== 模拟规则导入过程 ===")
    
    try:
        from app.selfcheck.models import SelfCheckRule
        from app.selfcheck.database import get_batch_import_db_manager
        
        # 创建测试规则
        print("1. 创建测试规则...")
        test_rule = SelfCheckRule(
            rule_name="测试导入规则",
            rule_code=f"TEST_IMPORT_{int(time.time())}",
            rule_description="这是一个测试导入的规则",
            rule_type="imported",
            rule_content="测试规则内容",
            sql_content="SELECT 1 FROM DUAL",
            city="测试城市",
            rule_source="测试来源",
            medical_behavior="测试行为",
            template_name="test_template",
            visit_type="住院",
            types="A",
            original_rule_id=999999,
            created_by=1,
            updated_by=1
        )
        
        # 使用批量导入方法保存
        print("2. 使用批量导入方法保存规则...")
        save_success = test_rule.save_with_verification()
        
        if save_success:
            print(f"✅ 规则保存成功，ID: {test_rule.id}")
            
            # 验证规则是否真的保存了
            batch_db_manager = get_batch_import_db_manager()
            verify_query = "SELECT COUNT(*) as count FROM selfcheck_rules WHERE id = :id"
            verify_result = batch_db_manager.execute_query(verify_query, {'id': test_rule.id})
            
            if verify_result and verify_result[0]['count'] > 0:
                print("✅ 规则保存验证成功")
                
                # 清理测试数据
                print("3. 清理测试数据...")
                if test_rule.delete():
                    print("✅ 测试规则删除成功")
                else:
                    print("⚠️ 测试规则删除失败")
            else:
                print("❌ 规则保存验证失败")
                return False
        else:
            print("❌ 规则保存失败")
            return False
        
        print("\n✅ 规则导入模拟测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 规则导入模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试数据库修复效果...\n")
    
    # 运行所有测试
    tests = [
        ("普通数据库操作测试", test_normal_database_operations),
        ("批量导入操作测试", test_batch_import_operations),
        ("规则导入模拟测试", test_rule_import_simulation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"开始 {test_name}")
            print('='*60)
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*60)
    print("测试总结:")
    print("="*60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"• {test_name}: {status}")
    
    passed_count = sum(result for _, result in results)
    total_count = len(results)
    
    if passed_count == total_count:
        print(f"\n🎉 所有测试通过！({passed_count}/{total_count})")
        print("\n数据库修复效果：")
        print("1. ✅ 普通数据库操作（删除等）恢复正常")
        print("2. ✅ 批量导入使用专用连接池，避免连接冲突")
        print("3. ✅ 两套连接方式互不干扰")
        print("4. ✅ 连接池管理正常，支持并发操作")
    else:
        print(f"\n⚠️ 部分测试失败 ({passed_count}/{total_count})，请检查配置。")
    
    print("="*60)
