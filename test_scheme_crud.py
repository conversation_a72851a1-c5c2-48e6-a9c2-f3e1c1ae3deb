#!/usr/bin/env python3
"""测试方案管理的CRUD功能"""

import requests
import json
import time

def test_scheme_crud():
    """测试方案的增删改查功能"""
    base_url = "http://127.0.0.1:5002"
    
    # 创建会话
    session = requests.Session()
    
    print("=== 方案管理CRUD功能测试 ===")
    
    # 1. 登录
    print("\n1. 登录...")

    # 先获取登录页面
    login_page = session.get(f"{base_url}/auth/login")
    if login_page.status_code != 200:
        print(f"❌ 无法访问登录页面: {login_page.status_code}")
        return False

    # 提取CSRF token
    import re
    csrf_match = re.search(r'<meta name="csrf-token" content="([^"]+)"', login_page.text)
    login_csrf_token = csrf_match.group(1) if csrf_match else None

    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }

    if login_csrf_token:
        login_data['csrf_token'] = login_csrf_token

    login_response = session.post(f"{base_url}/auth/login", data=login_data)

    # 检查是否重定向到dashboard（登录成功的标志）
    if login_response.status_code == 200 or (login_response.status_code == 302 and 'dashboard' in login_response.headers.get('Location', '')):
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(f"   响应内容: {login_response.text[:200]}")
        return False
    
    # 2. 获取CSRF token
    print("\n2. 获取CSRF token...")
    schemes_page = session.get(f"{base_url}/selfcheck/schemes")
    csrf_token = None
    
    if schemes_page.status_code == 200:
        import re
        csrf_match = re.search(r'<meta name="csrf-token" content="([^"]+)"', schemes_page.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            print(f"✅ 获取CSRF token成功: {csrf_token[:20]}...")
        else:
            print("❌ 无法获取CSRF token")
            return False
    else:
        print(f"❌ 无法访问方案页面: {schemes_page.status_code}")
        return False
    
    headers = {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrf_token
    }
    
    # 3. 创建测试方案
    print("\n3. 创建测试方案...")
    test_scheme_name = f"测试方案_{int(time.time())}"
    create_data = {
        'scheme_name': test_scheme_name,
        'description': '这是一个用于测试CRUD功能的方案'
    }
    
    create_response = session.post(
        f"{base_url}/selfcheck/api/schemes",
        json=create_data,
        headers=headers
    )
    
    if create_response.status_code == 200:
        create_result = create_response.json()
        if create_result.get('success'):
            scheme_id = create_result['scheme']['id']
            print(f"✅ 创建方案成功，ID: {scheme_id}")
            print(f"   方案名称: {create_result['scheme']['scheme_name']}")
        else:
            print(f"❌ 创建方案失败: {create_result.get('message')}")
            return False
    else:
        print(f"❌ 创建方案请求失败: {create_response.status_code}")
        print(f"   响应内容: {create_response.text[:200]}")
        return False
    
    # 4. 获取方案详情
    print(f"\n4. 获取方案详情 (ID: {scheme_id})...")
    detail_response = session.get(f"{base_url}/selfcheck/api/schemes/{scheme_id}")
    
    if detail_response.status_code == 200:
        detail_result = detail_response.json()
        if detail_result.get('success'):
            scheme = detail_result['scheme']
            print("✅ 获取方案详情成功")
            print(f"   方案名称: {scheme['scheme_name']}")
            print(f"   方案描述: {scheme.get('description', 'N/A')}")
            print(f"   方案状态: {scheme.get('status', 'N/A')}")
        else:
            print(f"❌ 获取方案详情失败: {detail_result.get('message')}")
    else:
        print(f"❌ 获取方案详情请求失败: {detail_response.status_code}")
    
    # 5. 更新方案
    print(f"\n5. 更新方案 (ID: {scheme_id})...")
    update_data = {
        'scheme_name': test_scheme_name + "_已更新",
        'description': '这是一个已更新的测试方案',
        'status': 'inactive'
    }
    
    update_response = session.put(
        f"{base_url}/selfcheck/api/schemes/{scheme_id}",
        json=update_data,
        headers=headers
    )
    
    if update_response.status_code == 200:
        update_result = update_response.json()
        if update_result.get('success'):
            print("✅ 更新方案成功")
            updated_scheme = update_result['scheme']
            print(f"   新方案名称: {updated_scheme['scheme_name']}")
            print(f"   新方案状态: {updated_scheme['status']}")
        else:
            print(f"❌ 更新方案失败: {update_result.get('message')}")
    else:
        print(f"❌ 更新方案请求失败: {update_response.status_code}")
        print(f"   响应内容: {update_response.text[:200]}")
    
    # 6. 再次获取方案详情验证更新
    print(f"\n6. 验证方案更新...")
    verify_response = session.get(f"{base_url}/selfcheck/api/schemes/{scheme_id}")
    
    if verify_response.status_code == 200:
        verify_result = verify_response.json()
        if verify_result.get('success'):
            scheme = verify_result['scheme']
            print("✅ 验证更新成功")
            print(f"   当前方案名称: {scheme['scheme_name']}")
            print(f"   当前方案状态: {scheme['status']}")
            print(f"   当前方案描述: {scheme.get('description', 'N/A')}")
        else:
            print(f"❌ 验证更新失败: {verify_result.get('message')}")
    else:
        print(f"❌ 验证更新请求失败: {verify_response.status_code}")
    
    # 7. 删除方案
    print(f"\n7. 删除方案 (ID: {scheme_id})...")
    delete_response = session.delete(
        f"{base_url}/selfcheck/api/schemes/{scheme_id}",
        headers={'X-CSRFToken': csrf_token}
    )
    
    if delete_response.status_code == 200:
        delete_result = delete_response.json()
        if delete_result.get('success'):
            print("✅ 删除方案成功")
        else:
            print(f"❌ 删除方案失败: {delete_result.get('message')}")
    else:
        print(f"❌ 删除方案请求失败: {delete_response.status_code}")
        print(f"   响应内容: {delete_response.text[:200]}")
    
    # 8. 验证删除
    print(f"\n8. 验证方案删除...")
    verify_delete_response = session.get(f"{base_url}/selfcheck/api/schemes/{scheme_id}")
    
    if verify_delete_response.status_code == 200:
        verify_delete_result = verify_delete_response.json()
        if not verify_delete_result.get('success'):
            print("✅ 验证删除成功，方案已不存在")
        else:
            print("❌ 验证删除失败，方案仍然存在")
    else:
        print(f"验证删除请求状态: {verify_delete_response.status_code}")
    
    # 9. 获取方案列表
    print(f"\n9. 获取方案列表...")
    list_response = session.get(f"{base_url}/selfcheck/api/schemes")
    
    if list_response.status_code == 200:
        list_result = list_response.json()
        if list_result.get('success'):
            schemes = list_result.get('schemes', [])
            print(f"✅ 获取方案列表成功，共 {len(schemes)} 个方案")
            for scheme in schemes[:3]:  # 只显示前3个
                print(f"   - {scheme['scheme_name']} (ID: {scheme['id']}, 状态: {scheme['status']})")
        else:
            print(f"❌ 获取方案列表失败: {list_result.get('message')}")
    else:
        print(f"❌ 获取方案列表请求失败: {list_response.status_code}")
    
    print("\n=== CRUD测试完成 ===")
    return True

if __name__ == '__main__':
    test_scheme_crud()
