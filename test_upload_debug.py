#!/usr/bin/env python3
"""测试文件上传功能的调试脚本"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.selfcheck.services import UploadService
from werkzeug.datastructures import FileStorage
import io

def test_upload_validation():
    """测试文件验证功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 测试文件验证功能 ===")
        
        # 测试1: 空文件
        print("\n1. 测试空文件:")
        is_valid, message = UploadService.validate_file(None)
        print(f"   结果: {is_valid}, 消息: {message}")
        
        # 测试2: 无效扩展名
        print("\n2. 测试无效扩展名:")
        fake_file = FileStorage(
            stream=io.BytesIO(b"test content"),
            filename="test.txt",
            content_type="text/plain"
        )
        is_valid, message = UploadService.validate_file(fake_file)
        print(f"   结果: {is_valid}, 消息: {message}")
        
        # 测试3: 有效CSV文件
        print("\n3. 测试有效CSV文件:")
        csv_content = "rule_id,sql_content,city\n1,SELECT * FROM test,北京".encode('utf-8')
        csv_file = FileStorage(
            stream=io.BytesIO(csv_content),
            filename="test.csv",
            content_type="text/csv"
        )
        is_valid, message = UploadService.validate_file(csv_file)
        print(f"   结果: {is_valid}, 消息: {message}")

def test_upload_save():
    """测试文件保存功能"""
    app = create_app()
    
    with app.app_context():
        print("\n=== 测试文件保存功能 ===")
        
        # 创建测试文件
        csv_content = "rule_id,sql_content,city\n1,SELECT * FROM test,北京\n2,SELECT count(*) FROM users,上海".encode('utf-8')
        csv_file = FileStorage(
            stream=io.BytesIO(csv_content),
            filename="test_upload.csv",
            content_type="text/csv"
        )
        
        try:
            # 测试保存
            print("开始保存文件...")
            upload_record = UploadService.save_upload(csv_file, 1)  # 用户ID=1
            print(f"保存成功! 记录: {upload_record}")
        except Exception as e:
            print(f"保存失败: {str(e)}")
            import traceback
            traceback.print_exc()

def test_database_connection():
    """测试数据库连接"""
    app = create_app()
    
    with app.app_context():
        print("\n=== 测试数据库连接 ===")
        
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            # 测试简单查询
            result = db_manager.execute_query("SELECT 1 as test FROM dual")
            print(f"数据库连接正常: {result}")
            
            # 测试序列是否存在
            seq_query = """
            SELECT sequence_name 
            FROM user_sequences 
            WHERE sequence_name = 'SELFCHECK_UPLOADS_SEQ'
            """
            seq_result = db_manager.execute_query(seq_query)
            print(f"序列检查结果: {seq_result}")
            
            # 测试表是否存在
            table_query = """
            SELECT table_name 
            FROM user_tables 
            WHERE table_name = 'SELFCHECK_UPLOADS'
            """
            table_result = db_manager.execute_query(table_query)
            print(f"表检查结果: {table_result}")
            
        except Exception as e:
            print(f"数据库连接失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_database_connection()
    test_upload_validation()
    test_upload_save()
