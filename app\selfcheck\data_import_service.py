#!/usr/bin/env python3
"""
数据导入服务
负责将上传的文件数据导入到标准化数据表中
"""

import os
import json
import uuid
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

from .database import get_db_manager, execute_query, execute_update

logger = logging.getLogger(__name__)


class DataImportService:
    """数据导入服务类"""

    # 多表格式的标准表头定义
    STANDARD_TABLE_HEADERS = {
        # 默认通用格式
        'default': [
            '患者姓名', '病案号', '身份证号', '结算单据号', '医疗机构编码', '医疗机构名称',
            '入院科室', '出院科室', '主诊医师姓名', '患者性别', '患者年龄', '险种类型',
            '医保等级', '入院日期', '出院日期', '项目使用日期', '结算日期',
            '入院诊断编码', '入院诊断名称', '出院诊断编码', '出院诊断名称',
            '主手术及操作编码', '主手术及操作名称', '其他手术及操作编码', '其他手术及操作名称',
            '医院项目编码', '医院项目名称', '医保项目编码', '医保项目名称', '规格',
            '单价', '数量', '金额', '医保范围内金额', '医疗总费用', '基本统筹支付',
            '个人自付', '个人自费', '符合基本医疗保险的费用', '报销比例', '自付比例',
            '费用类别', '支付类别', '开单科室名称', '执行科室名称', '开单医师姓名'
        ],
        # 这里可以添加更多表格式
        # '表名1': ['字段1', '字段2', ...],
        # '表名2': ['字段1', '字段2', ...],
    }

    # 支持的表名模式（文件名匹配）
    SUPPORTED_TABLE_PATTERNS = {
        'default': ['医保数据', '费用明细', 'medical_data', 'cost_detail'],
        # 可以添加更多表名模式
    }

    @staticmethod
    def import_upload_data(upload_id: int) -> Dict[str, Any]:
        """导入上传的数据文件"""
        try:
            logger.info(f"开始导入上传数据: {upload_id}")

            # 获取上传记录
            upload_query = """
            SELECT u.*, usr.username
            FROM selfcheck_uploads u
            JOIN users usr ON u.user_id = usr.id
            WHERE u.id = :upload_id
            """
            upload_result = execute_query(upload_query, {'upload_id': upload_id})

            if not upload_result:
                raise ValueError(f"上传记录不存在: {upload_id}")

            upload = upload_result[0]
            file_path = upload['file_path']
            file_type = upload['file_type'].lower()
            username = upload['username']

            # 1. 确保用户Schema存在
            schema_result = DataImportService._ensure_user_schema(username)
            if not schema_result['success']:
                raise ValueError(f"创建用户Schema失败: {schema_result['error']}")

            # 生成批次ID
            batch_id = f"batch_{upload_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 记录开始导入
            DataImportService._log_import_operation(
                upload_id, batch_id, 'import', 'start', '开始数据导入'
            )

            # 根据文件类型选择导入方法
            if file_type == 'csv':
                result = DataImportService._import_csv_data(upload_id, file_path, batch_id, username)
            elif file_type in ['dmp', 'dp', 'bak']:
                result = DataImportService._import_database_file(upload_id, file_path, file_type, batch_id, username)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")

            # 更新上传记录状态
            if result['success']:
                status = 'imported'
                message = f"数据导入成功，共导入 {result['imported_count']} 条记录到用户Schema: {username}"
                record_count = result['imported_count']
            else:
                status = 'import_failed'
                message = f"数据导入失败: {result['error']}"
                record_count = 0

            update_query = """
            UPDATE selfcheck_uploads
            SET status = :status, validation_result = :message, record_count = :record_count,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :upload_id
            """
            execute_update(update_query, {
                'status': status,
                'message': message,
                'record_count': record_count,
                'upload_id': upload_id
            })

            # 记录导入完成
            DataImportService._log_import_operation(
                upload_id, batch_id, 'import',
                'success' if result['success'] else 'error',
                message, result.get('imported_count', 0), result.get('error_count', 0)
            )

            logger.info(f"数据导入完成: {upload_id}, 结果: {message}")
            return result

        except Exception as e:
            error_msg = f"数据导入失败: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # 更新上传记录为失败状态
            try:
                update_query = """
                UPDATE selfcheck_uploads
                SET status = 'import_failed', error_message = :error_msg,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = :upload_id
                """
                execute_update(update_query, {'error_msg': error_msg, 'upload_id': upload_id})
            except Exception as update_error:
                logger.error(f"更新上传记录状态失败: {str(update_error)}")

            return {'success': False, 'error': error_msg}

    @staticmethod
    def _ensure_user_schema(username: str) -> Dict[str, Any]:
        """确保用户Schema存在，不存在则创建"""
        try:
            logger.info(f"检查用户Schema: {username}")

            # 检查Schema是否存在
            check_schema_sql = """
            SELECT COUNT(*) as count
            FROM all_users
            WHERE username = UPPER(:username)
            """

            result = execute_query(check_schema_sql, {'username': username})
            schema_exists = result[0]['count'] > 0 if result else False

            if schema_exists:
                logger.info(f"用户Schema已存在: {username}")

                # 检查用户数据表是否存在
                table_exists = DataImportService._check_user_data_table(username)
                if not table_exists:
                    # 创建用户数据表
                    DataImportService._create_user_data_table(username)

                return {'success': True, 'message': f'用户Schema已存在: {username}'}
            else:
                # 创建用户Schema
                logger.info(f"创建用户Schema: {username}")

                # 生成随机密码
                import secrets
                import string
                password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))

                # 创建用户和Schema
                create_user_sql = f"""
                CREATE USER {username} IDENTIFIED BY "{password}"
                DEFAULT TABLESPACE USERS
                TEMPORARY TABLESPACE TEMP
                QUOTA UNLIMITED ON USERS
                """

                execute_update(create_user_sql)

                # 授予必要权限
                grant_sql = f"""
                GRANT CONNECT, RESOURCE TO {username}
                """
                execute_update(grant_sql)

                # 创建用户数据表
                DataImportService._create_user_data_table(username)

                logger.info(f"用户Schema创建成功: {username}")
                return {'success': True, 'message': f'用户Schema创建成功: {username}'}

        except Exception as e:
            error_msg = f"处理用户Schema失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}

    @staticmethod
    def _check_user_data_table(username: str) -> bool:
        """检查用户数据表是否存在"""
        try:
            check_table_sql = """
            SELECT COUNT(*) as count
            FROM all_tables
            WHERE owner = UPPER(:username) AND table_name = 'MEDICAL_DATA'
            """

            result = execute_query(check_table_sql, {'username': username})
            return result[0]['count'] > 0 if result else False

        except Exception as e:
            logger.error(f"检查用户数据表失败: {str(e)}")
            return False

    @staticmethod
    def _create_user_data_table(username: str):
        """在用户Schema中创建数据表"""
        try:
            logger.info(f"在用户Schema中创建数据表: {username}")

            create_table_sql = f"""
            CREATE TABLE {username}.MEDICAL_DATA (
                ID NUMBER PRIMARY KEY,
                BATCH_ID VARCHAR2(50) NOT NULL,
                ROW_NUMBER NUMBER NOT NULL,

                -- 基本信息字段
                患者姓名 VARCHAR2(100),
                病案号 VARCHAR2(50),
                身份证号 VARCHAR2(20),
                结算单据号 VARCHAR2(50),
                医疗机构编码 VARCHAR2(50),
                医疗机构名称 VARCHAR2(200),
                入院科室 VARCHAR2(100),
                出院科室 VARCHAR2(100),
                主诊医师姓名 VARCHAR2(100),
                患者性别 VARCHAR2(10),
                患者年龄 NUMBER,
                险种类型 VARCHAR2(50),
                医保等级 VARCHAR2(50),

                -- 时间信息
                入院日期 DATE,
                出院日期 DATE,
                项目使用日期 DATE,
                结算日期 DATE,

                -- 诊断信息
                入院诊断编码 VARCHAR2(50),
                入院诊断名称 VARCHAR2(500),
                出院诊断编码 VARCHAR2(50),
                出院诊断名称 VARCHAR2(500),

                -- 手术信息
                主手术及操作编码 VARCHAR2(50),
                主手术及操作名称 VARCHAR2(500),
                其他手术及操作编码 VARCHAR2(500),
                其他手术及操作名称 VARCHAR2(1000),

                -- 项目信息
                医院项目编码 VARCHAR2(50),
                医院项目名称 VARCHAR2(500),
                医保项目编码 VARCHAR2(50),
                医保项目名称 VARCHAR2(500),
                规格 VARCHAR2(200),

                -- 费用信息
                单价 NUMBER(10,2),
                数量 NUMBER(10,2),
                金额 NUMBER(10,2),
                医保范围内金额 NUMBER(10,2),
                医疗总费用 NUMBER(10,2),
                基本统筹支付 NUMBER(10,2),
                个人自付 NUMBER(10,2),
                个人自费 NUMBER(10,2),
                符合基本医疗保险的费用 NUMBER(10,2),
                报销比例 NUMBER(5,2),
                自付比例 NUMBER(5,2),

                -- 分类信息
                费用类别 VARCHAR2(100),
                支付类别 VARCHAR2(100),
                开单科室名称 VARCHAR2(100),
                执行科室名称 VARCHAR2(100),
                开单医师姓名 VARCHAR2(100),

                -- 元数据
                IMPORT_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """

            execute_update(create_table_sql)

            # 创建序列
            create_seq_sql = f"CREATE SEQUENCE {username}.MEDICAL_DATA_SEQ START WITH 1 INCREMENT BY 1"
            execute_update(create_seq_sql)

            # 创建索引
            create_index_sql = f"CREATE INDEX {username}.IDX_MEDICAL_DATA_BATCH ON {username}.MEDICAL_DATA(BATCH_ID)"
            execute_update(create_index_sql)

            logger.info(f"用户数据表创建成功: {username}.MEDICAL_DATA")

        except Exception as e:
            logger.error(f"创建用户数据表失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def _validate_csv_headers(file_path: str, file_name: str = None) -> Dict[str, Any]:
        """校验CSV文件表头，支持文件名匹配和多表格式"""
        try:
            logger.info(f"开始校验CSV文件表头: {file_path}")

            # 从文件路径提取文件名
            if file_name is None:
                file_name = os.path.basename(file_path)

            # 根据文件名确定表格式
            table_format = DataImportService._detect_table_format(file_name)
            logger.info(f"检测到表格式: {table_format}")

            # 读取CSV文件的第一行（表头）
            import pandas as pd

            # 尝试不同编码读取
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            headers = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding, nrows=0)  # 只读取表头
                    headers = list(df.columns)
                    logger.info(f"成功使用编码 {encoding} 读取CSV表头")
                    break
                except UnicodeDecodeError:
                    continue

            if headers is None:
                return {'success': False, 'error': '无法读取CSV文件表头，请检查文件编码'}

            # 获取对应的标准表头
            standard_headers = DataImportService.STANDARD_TABLE_HEADERS.get(table_format,
                                                                          DataImportService.STANDARD_TABLE_HEADERS['default'])

            logger.info(f"CSV文件表头: {headers}")
            logger.info(f"标准表头({table_format}): {standard_headers}")

            # 校验表头
            missing_headers = []
            extra_headers = []

            # 检查缺失的必需表头
            for standard_header in standard_headers:
                if standard_header not in headers:
                    missing_headers.append(standard_header)

            # 检查多余的表头
            for header in headers:
                if header not in standard_headers:
                    extra_headers.append(header)

            # 生成校验结果
            validation_result = {
                'success': len(missing_headers) == 0,
                'file_headers': headers,
                'standard_headers': standard_headers,
                'missing_headers': missing_headers,
                'extra_headers': extra_headers,
                'total_headers': len(headers),
                'matched_headers': len(headers) - len(extra_headers),
                'table_format': table_format,
                'file_name': file_name
            }

            if validation_result['success']:
                validation_result['message'] = f'CSV文件表头校验通过（格式: {table_format}）'
                logger.info(f"CSV文件表头校验通过，格式: {table_format}")
            else:
                error_msg = f"CSV文件表头校验失败（格式: {table_format}），缺少必需字段: {', '.join(missing_headers)}"
                validation_result['error'] = error_msg
                logger.warning(error_msg)

                if extra_headers:
                    logger.info(f"发现额外字段: {', '.join(extra_headers)}")

            return validation_result

        except Exception as e:
            error_msg = f"CSV表头校验失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {'success': False, 'error': error_msg}

    @staticmethod
    def _detect_table_format(file_name: str) -> str:
        """根据文件名检测表格式"""
        try:
            file_name_lower = file_name.lower()

            # 遍历支持的表名模式
            for table_format, patterns in DataImportService.SUPPORTED_TABLE_PATTERNS.items():
                for pattern in patterns:
                    if pattern.lower() in file_name_lower:
                        logger.info(f"文件名 '{file_name}' 匹配到格式: {table_format}")
                        return table_format

            # 如果没有匹配到，返回默认格式
            logger.info(f"文件名 '{file_name}' 未匹配到特定格式，使用默认格式")
            return 'default'

        except Exception as e:
            logger.warning(f"检测表格式失败: {str(e)}，使用默认格式")
            return 'default'

    @staticmethod
    def _import_csv_data(upload_id: int, file_path: str, batch_id: str, username: str) -> Dict[str, Any]:
        """导入CSV数据到用户Schema"""
        try:
            logger.info(f"开始导入CSV文件到用户Schema: {file_path} -> {username}")

            # 1. 校验CSV文件表头
            header_validation = DataImportService._validate_csv_headers(file_path)
            if not header_validation['success']:
                return {
                    'success': False,
                    'error': header_validation['error'],
                    'validation_details': header_validation
                }

            logger.info("CSV文件表头校验通过，开始读取数据")

            # 2. 读取CSV文件
            try:
                # 尝试不同的编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                df = None

                for encoding in encodings:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        logger.info(f"成功使用编码 {encoding} 读取CSV文件")
                        break
                    except UnicodeDecodeError:
                        continue

                if df is None:
                    raise ValueError("无法读取CSV文件，请检查文件编码")

            except Exception as e:
                raise ValueError(f"读取CSV文件失败: {str(e)}")

            if df.empty:
                raise ValueError("CSV文件为空")

            logger.info(f"CSV文件包含 {len(df)} 行数据，{len(df.columns)} 列")

            # 3. 数据清洗和标准化
            cleaned_data = DataImportService._clean_csv_data(df)

            # 4. 数据质量检查
            quality_result = DataImportService._validate_csv_data_quality(cleaned_data)

            # 5. 批量插入数据到用户Schema
            imported_count = DataImportService._batch_insert_to_user_schema(
                username, batch_id, cleaned_data
            )

            return {
                'success': True,
                'imported_count': imported_count,
                'total_rows': len(df),
                'quality_issues': quality_result['issues'],
                'batch_id': batch_id,
                'user_schema': username,
                'validation_details': header_validation
            }

        except Exception as e:
            logger.error(f"CSV数据导入失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': str(e)}

    @staticmethod
    def _clean_csv_data(df: pd.DataFrame) -> List[Dict[str, Any]]:
        """清洗CSV数据"""
        try:
            logger.info("开始清洗CSV数据")

            cleaned_data = []

            for index, row in df.iterrows():
                record = {
                    'row_number': index + 1
                }

                # 直接使用CSV列名作为字段名，进行数据类型转换
                for column in df.columns:
                    value = row[column]

                    # 处理空值
                    if pd.isna(value) or value == '' or str(value).strip() == '':
                        record[column] = None
                        continue

                    # 根据字段名进行类型转换
                    try:
                        if column in ['患者年龄']:
                            record[column] = int(float(value)) if value is not None else None
                        elif column in ['单价', '数量', '金额', '医保范围内金额', '医疗总费用',
                                       '基本统筹支付', '个人自付', '个人自费', '符合基本医疗保险的费用',
                                       '报销比例', '自付比例']:
                            record[column] = float(value) if value is not None else None
                        elif column in ['入院日期', '出院日期', '项目使用日期', '结算日期']:
                            # 日期转换
                            if value is not None:
                                date_value = pd.to_datetime(value, errors='coerce')
                                record[column] = date_value.date() if pd.notna(date_value) else None
                            else:
                                record[column] = None
                        else:
                            # 字符串类型
                            record[column] = str(value).strip() if value is not None else None
                    except (ValueError, TypeError) as e:
                        logger.warning(f"字段 {column} 类型转换失败: {str(e)}, 行 {index + 1}, 值: {value}")
                        record[column] = str(value).strip() if value is not None else None

                cleaned_data.append(record)

            logger.info(f"数据清洗完成，处理 {len(cleaned_data)} 条记录")
            return cleaned_data

        except Exception as e:
            logger.error(f"CSV数据清洗失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def _validate_csv_data_quality(data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """CSV数据质量检查"""
        try:
            logger.info("开始CSV数据质量检查")

            issues = []
            required_fields = ['患者姓名', '病案号', '结算单据号']  # 必填字段

            for record in data:
                row_number = record.get('row_number', 0)

                # 检查必填字段
                for field in required_fields:
                    value = record.get(field)
                    if not value or str(value).strip() == '':
                        issues.append({
                            'row_number': row_number,
                            'field': field,
                            'rule': '必填字段检查',
                            'message': f'{field}不能为空',
                            'severity': 'error',
                            'value': str(value) if value is not None else 'NULL'
                        })

                # 检查年龄范围
                age = record.get('患者年龄')
                if age is not None:
                    try:
                        age_num = float(age)
                        if age_num < 0 or age_num > 150:
                            issues.append({
                                'row_number': row_number,
                                'field': '患者年龄',
                                'rule': '年龄范围检查',
                                'message': '年龄必须在0-150之间',
                                'severity': 'warning',
                                'value': str(age)
                            })
                    except (ValueError, TypeError):
                        issues.append({
                            'row_number': row_number,
                            'field': '患者年龄',
                            'rule': '数据类型检查',
                            'message': '年龄必须是数字',
                            'severity': 'error',
                            'value': str(age)
                        })

                # 检查金额字段
                amount_fields = ['单价', '数量', '金额', '医保范围内金额']
                for field in amount_fields:
                    value = record.get(field)
                    if value is not None:
                        try:
                            amount = float(value)
                            if amount < 0:
                                issues.append({
                                    'row_number': row_number,
                                    'field': field,
                                    'rule': '金额范围检查',
                                    'message': f'{field}不能为负数',
                                    'severity': 'warning',
                                    'value': str(value)
                                })
                        except (ValueError, TypeError):
                            issues.append({
                                'row_number': row_number,
                                'field': field,
                                'rule': '数据类型检查',
                                'message': f'{field}必须是数字',
                                'severity': 'error',
                                'value': str(value)
                            })

            logger.info(f"CSV数据质量检查完成，发现 {len(issues)} 个问题")

            return {
                'total_issues': len(issues),
                'issues': issues,
                'error_count': len([i for i in issues if i['severity'] == 'error']),
                'warning_count': len([i for i in issues if i['severity'] == 'warning'])
            }

        except Exception as e:
            logger.error(f"CSV数据质量检查失败: {str(e)}", exc_info=True)
            return {'total_issues': 0, 'issues': [], 'error_count': 0, 'warning_count': 0}

    @staticmethod
    def _batch_insert_to_user_schema(username: str, batch_id: str, data: List[Dict[str, Any]]) -> int:
        """批量插入数据到用户Schema"""
        try:
            logger.info(f"开始批量插入数据到用户Schema: {username}，共 {len(data)} 条记录")

            if not data:
                return 0

            db_manager = get_db_manager()
            inserted_count = 0
            batch_size = 1000  # 每批处理1000条记录

            # 准备插入SQL - 使用CSV的所有字段
            columns = list(DataImportService.STANDARD_CSV_HEADERS)
            columns_str = ', '.join([f'"{col}"' for col in columns])  # 使用双引号包围列名
            placeholders = ', '.join([f':{i}' for i in range(len(columns))])

            insert_sql = f"""
            INSERT INTO {username}.MEDICAL_DATA (
                ID, BATCH_ID, ROW_NUMBER, {columns_str}
            ) VALUES (
                {username}.MEDICAL_DATA_SEQ.NEXTVAL, :batch_id, :row_number, {placeholders}
            )
            """

            # 分批插入
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i + batch_size]
                batch_params = []

                for record in batch_data:
                    # 构建参数列表
                    params = [batch_id, record.get('row_number', 0)]

                    # 按照标准字段顺序添加值
                    for col in columns:
                        params.append(record.get(col))

                    # 转换为字典格式（Oracle需要命名参数）
                    param_dict = {'batch_id': batch_id, 'row_number': record.get('row_number', 0)}
                    for idx, col in enumerate(columns):
                        param_dict[str(idx)] = record.get(col)

                    batch_params.append(param_dict)

                # 执行批量插入
                try:
                    with db_manager.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.executemany(insert_sql, batch_params)
                        conn.commit()
                        inserted_count += len(batch_params)
                        logger.info(f"批量插入完成: {inserted_count}/{len(data)}")

                except Exception as e:
                    logger.error(f"批量插入失败: {str(e)}")
                    # 尝试单条插入
                    for param_dict in batch_params:
                        try:
                            execute_update(insert_sql, param_dict)
                            inserted_count += 1
                        except Exception as single_error:
                            logger.error(f"单条插入失败: {str(single_error)}")

            logger.info(f"数据插入完成，成功插入 {inserted_count} 条记录到 {username}.MEDICAL_DATA")
            return inserted_count

        except Exception as e:
            logger.error(f"批量插入数据到用户Schema失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def _import_database_file(upload_id: int, file_path: str, file_type: str, batch_id: str, username: str) -> Dict[str, Any]:
        """导入数据库文件（DMP、DP、BAK）"""
        try:
            logger.info(f"开始导入{file_type.upper()}文件: {file_path}")
            
            if file_type == 'dmp':
                return DataImportService._import_dmp_file(upload_id, file_path, batch_id)
            elif file_type == 'dp':
                return DataImportService._import_dp_file(upload_id, file_path, batch_id)
            elif file_type == 'bak':
                return DataImportService._import_bak_file(upload_id, file_path, batch_id)
            else:
                raise ValueError(f"不支持的数据库文件类型: {file_type}")
                
        except Exception as e:
            logger.error(f"{file_type.upper()}文件导入失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def _import_dmp_file(upload_id: int, file_path: str, batch_id: str) -> Dict[str, Any]:
        """导入Oracle DMP文件"""
        try:
            # 这里需要使用Oracle Data Pump工具导入
            # 由于需要系统级权限，这里提供基本框架
            
            logger.info("开始导入DMP文件")
            
            # 1. 创建临时表空间和用户（如果需要）
            # 2. 使用impdp命令导入数据
            # 3. 从临时表中读取数据并转换为标准格式
            
            # 模拟导入过程（实际需要调用Oracle工具）
            import time
            time.sleep(2)  # 模拟导入时间
            
            # 这里应该实现实际的DMP导入逻辑
            # 可以使用subprocess调用impdp命令
            
            return {
                'success': True,
                'imported_count': 0,  # 实际导入数量
                'message': 'DMP文件导入功能开发中'
            }
            
        except Exception as e:
            return {'success': False, 'error': f"DMP文件导入失败: {str(e)}"}
    
    @staticmethod
    def _import_dp_file(upload_id: int, file_path: str, batch_id: str) -> Dict[str, Any]:
        """导入Oracle DP文件"""
        try:
            logger.info("开始导入DP文件")
            
            # DP文件通常是Oracle Export格式
            # 需要使用imp命令导入
            
            return {
                'success': True,
                'imported_count': 0,
                'message': 'DP文件导入功能开发中'
            }
            
        except Exception as e:
            return {'success': False, 'error': f"DP文件导入失败: {str(e)}"}
    
    @staticmethod
    def _import_bak_file(upload_id: int, file_path: str, batch_id: str) -> Dict[str, Any]:
        """导入备份文件"""
        try:
            logger.info("开始导入BAK文件")
            
            # BAK文件可能是各种格式的备份文件
            # 需要根据具体格式进行处理
            
            return {
                'success': True,
                'imported_count': 0,
                'message': 'BAK文件导入功能开发中'
            }
            
        except Exception as e:
            return {'success': False, 'error': f"BAK文件导入失败: {str(e)}"}
    
    @staticmethod
    def _get_field_mapping(file_type: str) -> Dict[str, Dict[str, Any]]:
        """获取字段映射配置"""
        try:
            query = """
            SELECT source_field, target_field, field_type, is_required, 
                   default_value, validation_rule
            FROM selfcheck_field_mapping 
            WHERE file_type = :file_type AND is_active = 1
            ORDER BY sort_order
            """
            
            result = execute_query(query, {'file_type': file_type})
            
            mapping = {}
            for row in result:
                mapping[row['source_field']] = {
                    'target_field': row['target_field'],
                    'field_type': row['field_type'],
                    'is_required': bool(row['is_required']),
                    'default_value': row['default_value'],
                    'validation_rule': row['validation_rule']
                }
            
            return mapping
            
        except Exception as e:
            logger.error(f"获取字段映射配置失败: {str(e)}")
            return {}
    
    @staticmethod
    def _clean_and_standardize_data(df: pd.DataFrame, field_mapping: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """数据清洗和标准化"""
        try:
            logger.info("开始数据清洗和标准化")
            
            cleaned_data = []
            
            for index, row in df.iterrows():
                record = {
                    'row_number': index + 1,
                    'original_data': json.dumps(row.to_dict(), ensure_ascii=False, default=str)
                }
                
                # 字段映射和类型转换
                for source_field, mapping_config in field_mapping.items():
                    target_field = mapping_config['target_field']
                    field_type = mapping_config['field_type']
                    default_value = mapping_config.get('default_value')
                    
                    # 获取源数据值
                    if source_field in row and pd.notna(row[source_field]):
                        value = row[source_field]
                    else:
                        value = default_value
                    
                    # 类型转换
                    try:
                        if field_type == 'number' and value is not None:
                            value = float(value) if '.' in str(value) else int(value)
                        elif field_type == 'date' and value is not None:
                            value = pd.to_datetime(value).date() if pd.notna(pd.to_datetime(value, errors='coerce')) else None
                        elif field_type == 'string' and value is not None:
                            value = str(value).strip()
                    except (ValueError, TypeError) as e:
                        logger.warning(f"字段 {source_field} 类型转换失败: {str(e)}, 使用默认值")
                        value = default_value
                    
                    record[target_field] = value
                
                cleaned_data.append(record)
            
            logger.info(f"数据清洗完成，处理 {len(cleaned_data)} 条记录")
            return cleaned_data
            
        except Exception as e:
            logger.error(f"数据清洗失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def _validate_data_quality(data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """数据质量检查"""
        try:
            logger.info("开始数据质量检查")

            # 获取数据质量规则
            rules_query = """
            SELECT rule_name, rule_type, target_field, rule_expression,
                   error_message, severity
            FROM selfcheck_data_quality_rules
            WHERE is_active = 1
            ORDER BY sort_order
            """

            rules = execute_query(rules_query)
            issues = []

            for rule in rules:
                rule_name = rule['rule_name']
                rule_type = rule['rule_type']
                target_field = rule['target_field']
                expression = rule['rule_expression']
                error_message = rule['error_message']
                severity = rule['severity']

                # 检查每条记录
                for record in data:
                    row_number = record.get('row_number', 0)
                    field_value = record.get(target_field)

                    # 执行验证规则
                    is_valid = DataImportService._execute_validation_rule(
                        rule_type, field_value, expression
                    )

                    if not is_valid:
                        issues.append({
                            'row_number': row_number,
                            'field': target_field,
                            'rule': rule_name,
                            'message': error_message,
                            'severity': severity,
                            'value': str(field_value) if field_value is not None else 'NULL'
                        })

            logger.info(f"数据质量检查完成，发现 {len(issues)} 个问题")

            return {
                'total_issues': len(issues),
                'issues': issues,
                'error_count': len([i for i in issues if i['severity'] == 'error']),
                'warning_count': len([i for i in issues if i['severity'] == 'warning'])
            }

        except Exception as e:
            logger.error(f"数据质量检查失败: {str(e)}", exc_info=True)
            return {'total_issues': 0, 'issues': [], 'error_count': 0, 'warning_count': 0}

    @staticmethod
    def _execute_validation_rule(rule_type: str, value: Any, expression: str) -> bool:
        """执行验证规则"""
        try:
            if rule_type == 'required':
                return value is not None and str(value).strip() != ''
            elif rule_type == 'format':
                if value is None:
                    return True  # 空值跳过格式检查
                import re
                # 简化的正则表达式检查
                if 'REGEXP_LIKE' in expression:
                    pattern = expression.split("'")[1]  # 提取正则表达式
                    return bool(re.match(pattern, str(value)))
                return True
            elif rule_type == 'range':
                if value is None:
                    return True  # 空值跳过范围检查
                try:
                    num_value = float(value)
                    # 简化的范围检查
                    if '>=' in expression and '<=' in expression:
                        parts = expression.split(' AND ')
                        min_val = float(parts[0].split('>=')[1].strip())
                        max_val = float(parts[1].split('<=')[1].strip())
                        return min_val <= num_value <= max_val
                    return True
                except (ValueError, TypeError):
                    return False
            else:
                return True  # 未知规则类型默认通过

        except Exception as e:
            logger.warning(f"验证规则执行失败: {str(e)}")
            return True  # 规则执行失败时默认通过

    @staticmethod
    def _batch_insert_data(upload_id: int, batch_id: str, data: List[Dict[str, Any]], data_source: str) -> int:
        """批量插入数据"""
        try:
            logger.info(f"开始批量插入数据，共 {len(data)} 条记录")

            if not data:
                return 0

            db_manager = get_db_manager()
            inserted_count = 0
            batch_size = 1000  # 每批处理1000条记录

            # 准备插入SQL
            insert_sql = """
            INSERT INTO selfcheck_data_import (
                id, upload_id, batch_id, row_number,
                patient_name, medical_record_no, id_card, settlement_no,
                hospital_code, hospital_name, gender, age,
                admission_date, discharge_date, treatment_date, settlement_date,
                insurance_item_code, insurance_item_name,
                unit_price, quantity, amount, insurance_amount,
                original_data, data_source, import_time
            ) VALUES (
                selfcheck_data_import_seq.NEXTVAL, :upload_id, :batch_id, :row_number,
                :patient_name, :medical_record_no, :id_card, :settlement_no,
                :hospital_code, :hospital_name, :gender, :age,
                :admission_date, :discharge_date, :treatment_date, :settlement_date,
                :insurance_item_code, :insurance_item_name,
                :unit_price, :quantity, :amount, :insurance_amount,
                :original_data, :data_source, CURRENT_TIMESTAMP
            )
            """

            # 分批插入
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i + batch_size]
                batch_params = []

                for record in batch_data:
                    params = {
                        'upload_id': upload_id,
                        'batch_id': batch_id,
                        'row_number': record.get('row_number', 0),
                        'patient_name': record.get('patient_name'),
                        'medical_record_no': record.get('medical_record_no'),
                        'id_card': record.get('id_card'),
                        'settlement_no': record.get('settlement_no'),
                        'hospital_code': record.get('hospital_code'),
                        'hospital_name': record.get('hospital_name'),
                        'gender': record.get('gender'),
                        'age': record.get('age'),
                        'admission_date': record.get('admission_date'),
                        'discharge_date': record.get('discharge_date'),
                        'treatment_date': record.get('treatment_date'),
                        'settlement_date': record.get('settlement_date'),
                        'insurance_item_code': record.get('insurance_item_code'),
                        'insurance_item_name': record.get('insurance_item_name'),
                        'unit_price': record.get('unit_price'),
                        'quantity': record.get('quantity'),
                        'amount': record.get('amount'),
                        'insurance_amount': record.get('insurance_amount'),
                        'original_data': record.get('original_data'),
                        'data_source': data_source
                    }
                    batch_params.append(params)

                # 执行批量插入
                try:
                    with db_manager.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.executemany(insert_sql, batch_params)
                        conn.commit()
                        inserted_count += len(batch_params)
                        logger.info(f"批量插入完成: {inserted_count}/{len(data)}")

                except Exception as e:
                    logger.error(f"批量插入失败: {str(e)}")
                    # 尝试单条插入
                    for params in batch_params:
                        try:
                            execute_update(insert_sql, params)
                            inserted_count += 1
                        except Exception as single_error:
                            logger.error(f"单条插入失败: {str(single_error)}, 数据: {params}")

            logger.info(f"数据插入完成，成功插入 {inserted_count} 条记录")
            return inserted_count

        except Exception as e:
            logger.error(f"批量插入数据失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def _log_import_operation(upload_id: int, batch_id: str, operation_type: str,
                            status: str, message: str, processed_count: int = 0,
                            error_count: int = 0, warning_count: int = 0):
        """记录导入操作日志"""
        try:
            log_sql = """
            INSERT INTO selfcheck_import_logs (
                id, upload_id, batch_id, operation_type, status, message,
                processed_count, error_count, warning_count, start_time
            ) VALUES (
                selfcheck_import_logs_seq.NEXTVAL, :upload_id, :batch_id,
                :operation_type, :status, :message,
                :processed_count, :error_count, :warning_count, CURRENT_TIMESTAMP
            )
            """

            params = {
                'upload_id': upload_id,
                'batch_id': batch_id,
                'operation_type': operation_type,
                'status': status,
                'message': message,
                'processed_count': processed_count,
                'error_count': error_count,
                'warning_count': warning_count
            }

            execute_update(log_sql, params)

        except Exception as e:
            logger.error(f"记录导入日志失败: {str(e)}")

    @staticmethod
    def get_import_data(upload_id: int, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """获取导入的数据"""
        try:
            db_manager = get_db_manager()

            # 获取总数
            count_query = "SELECT COUNT(*) as total FROM selfcheck_data_import WHERE upload_id = :upload_id"
            count_result = execute_query(count_query, {'upload_id': upload_id})
            total = count_result[0]['total'] if count_result else 0

            # 分页查询数据
            offset = (page - 1) * per_page
            data_query = """
            SELECT id, row_number, patient_name, medical_record_no, settlement_no,
                   hospital_name, gender, age, insurance_item_code, insurance_item_name,
                   unit_price, quantity, amount, import_time
            FROM selfcheck_data_import
            WHERE upload_id = :upload_id
            ORDER BY row_number
            OFFSET :offset ROWS FETCH NEXT :per_page ROWS ONLY
            """

            data = execute_query(data_query, {
                'upload_id': upload_id,
                'offset': offset,
                'per_page': per_page
            })

            return {
                'data': data,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page if total > 0 else 0
            }

        except Exception as e:
            logger.error(f"获取导入数据失败: {str(e)}")
            raise

    @staticmethod
    def delete_import_data(upload_id: int) -> bool:
        """删除导入的数据"""
        try:
            logger.info(f"开始删除导入数据: {upload_id}")

            # 删除导入数据
            delete_data_sql = "DELETE FROM selfcheck_data_import WHERE upload_id = :upload_id"
            execute_update(delete_data_sql, {'upload_id': upload_id})

            # 删除导入日志
            delete_logs_sql = "DELETE FROM selfcheck_import_logs WHERE upload_id = :upload_id"
            execute_update(delete_logs_sql, {'upload_id': upload_id})

            logger.info(f"导入数据删除完成: {upload_id}")
            return True

        except Exception as e:
            logger.error(f"删除导入数据失败: {str(e)}")
            return False
