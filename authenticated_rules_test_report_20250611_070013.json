{"timestamp": "2025-06-11T07:00:13.313358", "test_type": "authenticated", "logged_in": true, "summary": {"total": 9, "passed": 9, "failed": 0, "errors": 0, "pass_rate": 100.0}, "results": [{"name": "规则列表API", "status": "PASS", "duration": 0.7546076774597168, "details": "返回JSON数据，keys: ['page', 'pages', 'per_page', 'rules', 'success', 'total']", "error": null}, {"name": "规则详情API", "status": "PASS", "duration": 0.01922607421875, "details": "返回JSON数据，keys: ['data', 'success']", "error": null}, {"name": "规则搜索API", "status": "PASS", "duration": 0.15540504455566406, "details": "返回JSON数据，keys: ['rules', 'success']", "error": null}, {"name": "规则参数搜索API", "status": "PASS", "duration": 0.018065214157104492, "details": "返回JSON数据，keys: ['rules', 'success']", "error": null}, {"name": "规则管理首页", "status": "PASS", "duration": 0.019159793853759766, "details": "返回HTML页面，长度: 38535", "error": null}, {"name": "飞检规则知识库", "status": "PASS", "duration": 0.02472233772277832, "details": "返回HTML页面，长度: 78134", "error": null}, {"name": "SQL生成器", "status": "PASS", "duration": 0.020476579666137695, "details": "返回HTML页面，长度: 52108", "error": null}, {"name": "系统规则", "status": "PASS", "duration": 0.02360987663269043, "details": "返回HTML页面，长度: 54957", "error": null}, {"name": "数据库查询", "status": "PASS", "duration": 0.16760683059692383, "details": "返回HTML页面，长度: 76116", "error": null}]}