<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审计日志测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log-item { padding: 10px; margin: 5px 0; background: #f8f9fa; border-left: 3px solid #007bff; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { padding: 15px; background: #e9ecef; border-radius: 5px; text-align: center; flex: 1; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .loading { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <h1>MICRA 审计日志功能测试</h1>
        
        <div class="test-section">
            <h2>连接测试</h2>
            <button onclick="testConnection()">测试应用连接</button>
            <div id="connectionResult"></div>
        </div>
        
        <div class="test-section">
            <h2>统计信息</h2>
            <button onclick="loadStatistics()">加载统计信息</button>
            <div class="stats" id="statisticsResult">
                <div class="stat-card">
                    <h3 id="totalLogs">-</h3>
                    <p>总日志数</p>
                </div>
                <div class="stat-card">
                    <h3 id="todayLogs">-</h3>
                    <p>今日日志</p>
                </div>
                <div class="stat-card">
                    <h3 id="activeUsers">-</h3>
                    <p>活跃用户</p>
                </div>
                <div class="stat-card">
                    <h3 id="successRate">-</h3>
                    <p>成功率</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>审计日志列表</h2>
            <button onclick="loadAuditLogs()">加载审计日志</button>
            <button onclick="createTestData()">创建测试数据</button>
            <div id="auditLogsResult"></div>
        </div>
        
        <div class="test-section">
            <h2>API测试结果</h2>
            <div id="apiTestResult"></div>
        </div>
    </div>

    <script>
        const baseUrl = 'http://localhost:5002';
        
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('apiTestResult');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            resultDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(message);
        }
        
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div class="loading">测试连接中...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/`);
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ 应用连接正常</div>';
                    log('应用连接测试成功', 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 应用响应异常: ${response.status}</div>`;
                    log(`应用连接测试失败: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 连接失败: ${error.message}</div>`;
                log(`应用连接测试失败: ${error.message}`, 'error');
            }
        }
        
        async function loadStatistics() {
            log('开始加载统计信息...');
            
            try {
                const response = await fetch(`${baseUrl}/admin/api/audit_logs/statistics`);
                
                if (response.ok) {
                    const data = await response.json();
                    
                    document.getElementById('totalLogs').textContent = data.total_logs || 0;
                    document.getElementById('todayLogs').textContent = data.today_logs || 0;
                    document.getElementById('activeUsers').textContent = data.active_users || 0;
                    document.getElementById('successRate').textContent = (data.success_rate || 0) + '%';
                    
                    log('统计信息加载成功', 'success');
                } else if (response.status === 401 || response.status === 403) {
                    log('需要登录或权限不足，请先登录管理员账户', 'error');
                } else {
                    log(`统计信息加载失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`统计信息加载失败: ${error.message}`, 'error');
            }
        }
        
        async function loadAuditLogs() {
            const resultDiv = document.getElementById('auditLogsResult');
            resultDiv.innerHTML = '<div class="loading">加载审计日志中...</div>';
            log('开始加载审计日志...');
            
            try {
                const response = await fetch(`${baseUrl}/admin/api/audit_logs?page=1&per_page=10`);
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.logs && data.logs.length > 0) {
                        let html = `<h3>审计日志 (总计: ${data.total})</h3>`;
                        html += '<table><thead><tr><th>时间</th><th>用户</th><th>操作</th><th>资源</th><th>详情</th></tr></thead><tbody>';
                        
                        data.logs.forEach(log => {
                            const timestamp = new Date(log.timestamp).toLocaleString();
                            html += `<tr>
                                <td>${timestamp}</td>
                                <td>${log.user_name || '系统'}</td>
                                <td>${log.action}</td>
                                <td>${log.resource}</td>
                                <td>${log.details || '-'}</td>
                            </tr>`;
                        });
                        
                        html += '</tbody></table>';
                        resultDiv.innerHTML = html;
                        log(`审计日志加载成功，共 ${data.logs.length} 条记录`, 'success');
                    } else {
                        resultDiv.innerHTML = '<div class="info">暂无审计日志数据</div>';
                        log('暂无审计日志数据', 'info');
                    }
                } else if (response.status === 401 || response.status === 403) {
                    resultDiv.innerHTML = '<div class="error">需要登录或权限不足</div>';
                    log('需要登录或权限不足，请先登录管理员账户', 'error');
                } else {
                    resultDiv.innerHTML = `<div class="error">加载失败: ${response.status}</div>`;
                    log(`审计日志加载失败: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
                log(`审计日志加载失败: ${error.message}`, 'error');
            }
        }
        
        async function createTestData() {
            log('开始创建测试数据...');
            
            try {
                // 这里我们通过访问一些页面来生成审计日志
                const pages = [
                    '/admin/',
                    '/admin/users',
                    '/admin/roles',
                    '/admin/audit_logs'
                ];
                
                for (const page of pages) {
                    try {
                        await fetch(`${baseUrl}${page}`);
                        log(`访问页面: ${page}`);
                    } catch (e) {
                        // 忽略单个页面的错误
                    }
                }
                
                log('测试数据创建完成，请重新加载审计日志查看', 'success');
            } catch (error) {
                log(`创建测试数据失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试连接
        window.onload = function() {
            log('页面加载完成，开始自动测试...');
            testConnection();
        };
    </script>
</body>
</html>
