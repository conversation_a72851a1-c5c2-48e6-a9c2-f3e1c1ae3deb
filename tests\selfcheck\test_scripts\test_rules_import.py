#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则导入模块自动化测试脚本
"""

import unittest
import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from tests.selfcheck.utils.test_base import SelfCheckTestBase, PerformanceTestMixin, DataValidationMixin
from tests.selfcheck.utils.api_client import SelfCheckAPIClient
from tests.selfcheck.utils.data_generator import TestDataGenerator


class TestRulesImport(SelfCheckTestBase, PerformanceTestMixin, DataValidationMixin):
    """规则导入模块测试类"""
    
    def setUp(self):
        """测试前置设置"""
        super().setUp()
        self.api_client = SelfCheckAPIClient(self.base_url)
        self.api_client.login("admin", "Admin123!")
        self.data_generator = TestDataGenerator()
    
    def tearDown(self):
        """测试后清理"""
        self.api_client.close()
        super().tearDown()
    
    def test_TC017_importable_rules_list(self):
        """TC017 - 可导入规则列表查询测试"""
        print("\n=== TC017 - 可导入规则列表查询测试 ===")
        
        # 获取可导入规则列表
        response = self.api_client.get_importable_rules(page=1, per_page=20)
        
        # 验证响应结构
        self.assertTrue(response.get('success', False), "获取可导入规则列表失败")
        self.assertIn('rules', response, "响应中缺少rules字段")
        self.assertIn('total', response, "响应中缺少total字段")
        
        # 验证分页数据
        self.assert_pagination_data(response)
        
        # 验证规则数据结构
        rules = response.get('rules', [])
        if rules:
            rule = rules[0]
            required_fields = ['rule_id', 'compare_id', 'visit_type', 'rule_name', 'rule_type']
            self.assert_required_fields(rule, required_fields)
            
            # 验证导入状态字段
            self.assertIn('is_imported', rule, "缺少导入状态字段")
        
        print(f"✅ 可导入规则列表查询测试通过，共获取到 {len(rules)} 条规则")
    
    def test_TC018_rules_search_and_filter(self):
        """TC018 - 规则搜索和过滤测试"""
        print("\n=== TC018 - 规则搜索和过滤测试 ===")
        
        # 测试规则名称搜索
        response = self.api_client.get_importable_rules(rule_name="重复")
        self.assertTrue(response.get('success', False), "按规则名称搜索失败")
        
        # 测试城市过滤
        response = self.api_client.get_importable_rules(city="北京")
        self.assertTrue(response.get('success', False), "按城市过滤失败")
        
        # 测试规则类型过滤
        response = self.api_client.get_importable_rules(rule_type="重复收费")
        self.assertTrue(response.get('success', False), "按规则类型过滤失败")
        
        # 测试用途过滤
        response = self.api_client.get_importable_rules(visit_type="住院")
        self.assertTrue(response.get('success', False), "按用途过滤失败")
        
        # 测试导入状态过滤
        response = self.api_client.get_importable_rules(is_imported="false")
        self.assertTrue(response.get('success', False), "按导入状态过滤失败")
        
        # 测试组合过滤
        response = self.api_client.get_importable_rules(
            rule_name="重复",
            city="北京",
            rule_type="重复收费",
            is_imported="false"
        )
        self.assertTrue(response.get('success', False), "组合过滤失败")
        
        print("✅ 规则搜索和过滤测试通过")
    
    def test_TC019_single_rule_import(self):
        """TC019 - 单个规则导入测试"""
        print("\n=== TC019 - 单个规则导入测试 ===")
        
        # 获取未导入的规则
        response = self.api_client.get_importable_rules(is_imported="false", per_page=1)
        self.assertTrue(response.get('success', False), "获取未导入规则失败")
        
        rules = response.get('rules', [])
        if not rules:
            print("⚠️ 没有可导入的规则，跳过测试")
            return
        
        rule = rules[0]
        compare_id = rule.get('compare_id')
        visit_type = rule.get('visit_type')
        
        self.assertIsNotNone(compare_id, "规则缺少compare_id")
        self.assertIsNotNone(visit_type, "规则缺少visit_type")
        
        # 执行导入
        import_result = self.api_client.import_rules([compare_id], [visit_type])
        self.assertTrue(import_result.get('success', False), f"导入规则失败: {import_result.get('message', '')}")
        
        # 验证导入结果
        imported_count = import_result.get('imported_count', 0)
        self.assertGreater(imported_count, 0, "导入数量应该大于0")
        
        # 验证规则状态更新
        time.sleep(1)  # 等待状态更新
        updated_response = self.api_client.get_importable_rules(
            rule_name=rule.get('rule_name', ''),
            per_page=1
        )
        
        if updated_response.get('success', False):
            updated_rules = updated_response.get('rules', [])
            if updated_rules:
                updated_rule = updated_rules[0]
                if updated_rule.get('compare_id') == compare_id:
                    self.assertEqual(updated_rule.get('is_imported'), 'Y', "规则导入状态未正确更新")
        
        print(f"✅ 单个规则导入测试通过，导入数量: {imported_count}")
    
    def test_TC020_batch_rules_import(self):
        """TC020 - 批量规则导入测试"""
        print("\n=== TC020 - 批量规则导入测试 ===")
        
        # 获取多个未导入的规则
        response = self.api_client.get_importable_rules(is_imported="false", per_page=5)
        self.assertTrue(response.get('success', False), "获取未导入规则失败")
        
        rules = response.get('rules', [])
        if len(rules) < 2:
            print("⚠️ 可导入规则数量不足，跳过批量导入测试")
            return
        
        # 准备导入数据
        compare_ids = []
        visit_types = []
        
        for rule in rules[:3]:  # 最多导入3个规则
            compare_id = rule.get('compare_id')
            visit_type = rule.get('visit_type')
            if compare_id and visit_type:
                compare_ids.append(compare_id)
                visit_types.append(visit_type)
        
        if not compare_ids:
            print("⚠️ 没有有效的导入数据，跳过测试")
            return
        
        # 执行批量导入
        import_result = self.api_client.import_rules(compare_ids, visit_types)
        self.assertTrue(import_result.get('success', False), f"批量导入失败: {import_result.get('message', '')}")
        
        # 验证导入结果
        imported_count = import_result.get('imported_count', 0)
        self.assertGreater(imported_count, 0, "批量导入数量应该大于0")
        
        errors = import_result.get('errors', [])
        print(f"导入成功: {imported_count}, 错误: {len(errors)}")
        
        if errors:
            print("导入错误信息:")
            for error in errors[:3]:  # 只显示前3个错误
                print(f"  - {error}")
        
        print(f"✅ 批量规则导入测试通过，导入数量: {imported_count}")
    
    def test_TC021_import_progress_monitoring(self):
        """TC021 - 导入进度监控测试"""
        print("\n=== TC021 - 导入进度监控测试 ===")
        
        # 获取未导入的规则
        response = self.api_client.get_importable_rules(is_imported="false", per_page=10)
        self.assertTrue(response.get('success', False), "获取未导入规则失败")
        
        rules = response.get('rules', [])
        if len(rules) < 5:
            print("⚠️ 可导入规则数量不足，跳过进度监控测试")
            return
        
        # 准备导入数据
        compare_ids = []
        visit_types = []
        
        for rule in rules[:5]:  # 导入5个规则
            compare_id = rule.get('compare_id')
            visit_type = rule.get('visit_type')
            if compare_id and visit_type:
                compare_ids.append(compare_id)
                visit_types.append(visit_type)
        
        # 测量导入时间
        start_time = time.time()
        import_result = self.api_client.import_rules(compare_ids, visit_types)
        end_time = time.time()
        
        import_time = end_time - start_time
        
        # 验证导入结果
        self.assertTrue(import_result.get('success', False), "导入失败")
        
        imported_count = import_result.get('imported_count', 0)
        
        # 计算导入速度
        if imported_count > 0:
            import_speed = imported_count / import_time
            print(f"导入速度: {import_speed:.2f} 规则/秒")
            
            # 验证性能要求（至少1规则/秒）
            self.assertGreaterEqual(import_speed, 1.0, f"导入速度过慢: {import_speed:.2f} < 1.0 规则/秒")
        
        print(f"✅ 导入进度监控测试通过，导入时间: {import_time:.2f}s")
    
    def test_TC022_import_error_handling(self):
        """TC022 - 导入错误处理测试"""
        print("\n=== TC022 - 导入错误处理测试 ===")
        
        # 测试无效的compare_id
        invalid_compare_ids = ["INVALID_ID_001", "INVALID_ID_002"]
        invalid_visit_types = ["住院", "门诊"]
        
        import_result = self.api_client.import_rules(invalid_compare_ids, invalid_visit_types)
        
        # 验证错误处理
        if import_result.get('success', False):
            # 如果请求成功，应该有错误信息
            errors = import_result.get('errors', [])
            imported_count = import_result.get('imported_count', 0)
            
            # 无效ID应该导致导入失败
            self.assertEqual(imported_count, 0, "无效ID不应该导入成功")
            self.assertGreater(len(errors), 0, "应该有错误信息")
        else:
            # 如果请求失败，应该有错误消息
            self.assertIn('message', import_result, "应该有错误消息")
        
        # 测试参数不匹配
        mismatched_result = self.api_client.import_rules(["ID1"], ["TYPE1", "TYPE2"])
        self.assertFalse(mismatched_result.get('success', True), "参数不匹配应该失败")
        
        print("✅ 导入错误处理测试通过")
    
    def test_TC023_import_status_management(self):
        """TC023 - 导入状态管理测试"""
        print("\n=== TC023 - 导入状态管理测试 ===")
        
        # 获取已导入的规则
        imported_response = self.api_client.get_importable_rules(is_imported="true", per_page=5)
        self.assertTrue(imported_response.get('success', False), "获取已导入规则失败")
        
        imported_rules = imported_response.get('rules', [])
        
        # 获取未导入的规则
        not_imported_response = self.api_client.get_importable_rules(is_imported="false", per_page=5)
        self.assertTrue(not_imported_response.get('success', False), "获取未导入规则失败")
        
        not_imported_rules = not_imported_response.get('rules', [])
        
        # 验证状态过滤正确性
        for rule in imported_rules:
            self.assertEqual(rule.get('is_imported'), 'Y', "已导入规则状态错误")
        
        for rule in not_imported_rules:
            self.assertNotEqual(rule.get('is_imported'), 'Y', "未导入规则状态错误")
        
        print(f"✅ 导入状态管理测试通过，已导入: {len(imported_rules)}, 未导入: {len(not_imported_rules)}")
    
    def test_TC024_data_integrity_validation(self):
        """TC024 - 数据完整性验证测试"""
        print("\n=== TC024 - 数据完整性验证测试 ===")
        
        # 获取一个未导入的规则
        response = self.api_client.get_importable_rules(is_imported="false", per_page=1)
        self.assertTrue(response.get('success', False), "获取未导入规则失败")
        
        rules = response.get('rules', [])
        if not rules:
            print("⚠️ 没有可导入的规则，跳过数据完整性测试")
            return
        
        original_rule = rules[0]
        compare_id = original_rule.get('compare_id')
        visit_type = original_rule.get('visit_type')
        
        # 执行导入
        import_result = self.api_client.import_rules([compare_id], [visit_type])
        self.assertTrue(import_result.get('success', False), "导入规则失败")
        
        imported_count = import_result.get('imported_count', 0)
        if imported_count == 0:
            print("⚠️ 规则导入失败，跳过数据完整性验证")
            return
        
        # 验证导入后的规则数据
        # 这里可以通过规则管理API获取导入的规则进行验证
        rules_response = self.api_client.get_rules(rule_name=original_rule.get('rule_name', ''))
        
        if rules_response.get('success', False):
            imported_rules = rules_response.get('rules', [])
            if imported_rules:
                imported_rule = imported_rules[0]
                
                # 验证关键字段
                self.assertEqual(imported_rule.get('rule_name'), original_rule.get('rule_name'))
                self.assertEqual(imported_rule.get('rule_type'), original_rule.get('rule_type'))
                self.assertEqual(imported_rule.get('city'), original_rule.get('city'))
                
                print("✅ 数据完整性验证通过")
            else:
                print("⚠️ 未找到导入的规则")
        else:
            print("⚠️ 获取导入规则失败")
    
    def test_TC027_large_batch_import_performance(self):
        """TC027 - 大批量导入性能测试"""
        print("\n=== TC027 - 大批量导入性能测试 ===")
        
        # 获取大量未导入规则
        response = self.api_client.get_importable_rules(is_imported="false", per_page=20)
        self.assertTrue(response.get('success', False), "获取未导入规则失败")
        
        rules = response.get('rules', [])
        if len(rules) < 10:
            print("⚠️ 可导入规则数量不足，跳过大批量导入性能测试")
            return
        
        # 准备导入数据
        compare_ids = []
        visit_types = []
        
        for rule in rules[:10]:  # 导入10个规则
            compare_id = rule.get('compare_id')
            visit_type = rule.get('visit_type')
            if compare_id and visit_type:
                compare_ids.append(compare_id)
                visit_types.append(visit_type)
        
        # 测量导入性能
        def batch_import():
            return self.api_client.import_rules(compare_ids, visit_types)
        
        result, response_time = self.measure_response_time(batch_import)
        
        # 验证性能要求
        self.assertTrue(result.get('success', False), "大批量导入失败")
        self.assertLessEqual(response_time, 30.0, f"大批量导入性能不达标: {response_time:.2f}s > 30.0s")
        
        imported_count = result.get('imported_count', 0)
        if imported_count > 0:
            import_speed = imported_count / response_time
            self.assertGreaterEqual(import_speed, 0.5, f"导入速度过慢: {import_speed:.2f} < 0.5 规则/秒")
        
        print(f"✅ 大批量导入性能测试通过，响应时间: {response_time:.2f}s，导入数量: {imported_count}")


if __name__ == '__main__':
    # 配置测试运行器
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestRulesImport)
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # 输出测试结果统计
    print(f"\n{'='*50}")
    print(f"规则导入模块测试结果统计:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"{'='*50}")
