#!/usr/bin/env python3
"""测试方案规则管理功能"""

import requests
import json

def test_rule_management():
    """测试方案规则管理功能"""
    base_url = "http://127.0.0.1:5002"
    
    print("=== 方案规则管理功能测试 ===")
    
    # 创建会话
    session = requests.Session()
    
    # 1. 测试获取方案列表
    print("\n1. 获取方案列表...")
    schemes_response = session.get(f"{base_url}/selfcheck/api/schemes")
    
    if schemes_response.status_code == 200:
        schemes_data = schemes_response.json()
        if schemes_data.get('success') and schemes_data.get('schemes'):
            schemes = schemes_data['schemes']
            print(f"✅ 获取到 {len(schemes)} 个方案")
            
            # 选择第一个方案进行测试
            test_scheme = schemes[0]
            scheme_id = test_scheme['id']
            scheme_name = test_scheme['scheme_name']
            print(f"   测试方案: {scheme_name} (ID: {scheme_id})")
            
            # 2. 测试获取方案规则
            print(f"\n2. 获取方案 {scheme_id} 的规则...")
            rules_response = session.get(f"{base_url}/selfcheck/api/schemes/{scheme_id}/rules")
            
            if rules_response.status_code == 200:
                rules_data = rules_response.json()
                print(f"✅ 方案规则API调用成功")
                print(f"   响应数据: {json.dumps(rules_data, ensure_ascii=False, indent=2)}")
                
                if rules_data.get('success'):
                    rules = rules_data.get('rules', [])
                    print(f"   方案中有 {len(rules)} 个规则")
                    for rule in rules:
                        print(f"   - {rule.get('rule_name', 'Unknown')} (ID: {rule.get('rule_id', 'N/A')}, 状态: {'启用' if rule.get('is_enabled') else '禁用'})")
                else:
                    print(f"   API返回错误: {rules_data.get('message')}")
            else:
                print(f"❌ 方案规则API调用失败: {rules_response.status_code}")
                print(f"   响应内容: {rules_response.text[:200]}")
            
            # 3. 测试获取可用规则
            print(f"\n3. 获取方案 {scheme_id} 的可用规则...")
            available_response = session.get(f"{base_url}/selfcheck/api/schemes/{scheme_id}/available-rules")
            
            if available_response.status_code == 200:
                available_data = available_response.json()
                print(f"✅ 可用规则API调用成功")
                print(f"   响应数据: {json.dumps(available_data, ensure_ascii=False, indent=2)}")
                
                if available_data.get('success'):
                    available_rules = available_data.get('rules', [])
                    print(f"   可添加的规则有 {len(available_rules)} 个")
                    for rule in available_rules[:3]:  # 只显示前3个
                        print(f"   - {rule.get('rule_name', 'Unknown')} (ID: {rule.get('id', 'N/A')}, 类型: {rule.get('rule_type', 'N/A')})")
                else:
                    print(f"   API返回错误: {available_data.get('message')}")
            else:
                print(f"❌ 可用规则API调用失败: {available_response.status_code}")
                print(f"   响应内容: {available_response.text[:200]}")
            
            # 4. 测试带过滤条件的可用规则查询
            print(f"\n4. 测试过滤条件查询...")
            filter_params = {
                'rule_name': '规则',
                'rule_type': '数据质量'
            }
            
            filter_response = session.get(
                f"{base_url}/selfcheck/api/schemes/{scheme_id}/available-rules",
                params=filter_params
            )
            
            if filter_response.status_code == 200:
                filter_data = filter_response.json()
                print(f"✅ 过滤查询API调用成功")
                
                if filter_data.get('success'):
                    filtered_rules = filter_data.get('rules', [])
                    print(f"   过滤后的规则有 {len(filtered_rules)} 个")
                else:
                    print(f"   API返回错误: {filter_data.get('message')}")
            else:
                print(f"❌ 过滤查询API调用失败: {filter_response.status_code}")
            
        else:
            print("❌ 没有获取到方案数据")
    else:
        print(f"❌ 获取方案列表失败: {schemes_response.status_code}")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_rule_management()
