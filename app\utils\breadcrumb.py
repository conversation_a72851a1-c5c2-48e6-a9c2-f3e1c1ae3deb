# -*- coding: utf-8 -*-
"""
面包屑导航工具函数
"""

from flask import url_for


def generate_breadcrumb(module, page=None, extra_items=None):
    """
    生成面包屑导航数据
    
    Args:
        module: 模块名称 ('admin', 'selfcheck', 'rules', 'data')
        page: 页面名称 (可选)
        extra_items: 额外的面包屑项目 (可选)
    
    Returns:
        list: 面包屑项目列表
    """
    breadcrumb_items = []
    
    # 首页
    breadcrumb_items.append({
        'name': '首页',
        'url': url_for('main.index'),
        'icon': 'fas fa-home'
    })
    
    # 模块级别
    if module == 'admin':
        breadcrumb_items.append({
            'name': '系统管理',
            'url': url_for('admin.index'),
            'icon': 'fas fa-cogs'
        })
        
        if page == 'users':
            breadcrumb_items.append({
                'name': '用户管理',
                'url': url_for('admin.users'),
                'icon': 'fas fa-users'
            })
        elif page == 'roles':
            breadcrumb_items.append({
                'name': '角色管理',
                'url': url_for('admin.roles'),
                'icon': 'fas fa-user-tag'
            })
        elif page == 'audit_logs':
            breadcrumb_items.append({
                'name': '审计日志',
                'url': url_for('admin.audit_logs'),
                'icon': 'fas fa-clipboard-list'
            })
        elif page == 'permissions':
            breadcrumb_items.append({
                'name': '权限管理',
                'url': url_for('admin.permissions'),
                'icon': 'fas fa-key'
            })
            
    elif module == 'selfcheck':
        breadcrumb_items.append({
            'name': '自查自纠',
            'url': url_for('selfcheck.index'),
            'icon': 'fas fa-search'
        })
        if page == 'rules':
            breadcrumb_items.append({
                'name': '规则管理',
                'url': url_for('selfcheck.rules'),
                'icon': 'fas fa-list-ul'
            })        
        elif page == 'uploads':
            breadcrumb_items.append({
                'name': '数据上传',
                'url': url_for('selfcheck.uploads'),
                'icon': 'fas fa-upload'
            })
        elif page == 'tasks':
            breadcrumb_items.append({
                'name': '自查任务',
                'url': url_for('selfcheck.tasks'),
                'icon': 'fas fa-tasks'
            })
        elif page == 'rules_import':
            breadcrumb_items.append({
                'name': '规则导入',
                'url': url_for('selfcheck.rules_import'),
                'icon': 'fas fa-file-import'
            })
        elif page == 'schemes':
            breadcrumb_items.append({
                'name': '方案管理',
                'url': url_for('selfcheck.schemes'),
                'icon': 'fas fa-project-diagram'
            })

            
    elif module == 'rules':
        breadcrumb_items.append({
            'name': '规则管理',
            'url': url_for('rules.index'),
            'icon': 'fas fa-book'
        })
        
        if page == 'knowledge_base':
            breadcrumb_items.append({
                'name': '知识库管理',
                'url': url_for('rules.knowledge_base'),
                'icon': 'fas fa-database'
            })
        elif page == 'system_rules':
            breadcrumb_items.append({
                'name': '系统规则语句',
                'url': url_for('rules.system_rules'),
                'icon': 'fas fa-cog'
            })
        elif page == 'sql_generator':
            breadcrumb_items.append({
                'name': '规则SQL生成器',
                'url': url_for('rules.sql_generator'),
                'icon': 'fas fa-code'
            })
            
    elif module == 'data':
        breadcrumb_items.append({
            'name': '数据处理',
            'url': url_for('data.index'),
            'icon': 'fas fa-tools'
        })
        
        if page == 'standardization':
            breadcrumb_items.append({
                'name': '数据标准化',
                'url': url_for('data.standardization'),
                'icon': 'fas fa-align-left'
            })
        elif page == 'validator':
            breadcrumb_items.append({
                'name': '数据验证',
                'url': url_for('data.validator'),
                'icon': 'fas fa-check-circle'
            })
        elif page == 'find_duplicates':
            breadcrumb_items.append({
                'name': '查找重复项',
                'url': url_for('data.find_duplicates'),
                'icon': 'fas fa-copy'
            })

    elif module == 'excel':

        breadcrumb_items.append({
            'name': 'Excel工具',
            'url': url_for('excel.index'),
            'icon': 'fas fa-file-excel'
        })
        if page == 'splitter':
            breadcrumb_items.append({
                'name': '文件拆分',
                'url': url_for('excel.splitter'),
                'icon': 'fas fa-cut'
            })
        elif page == 'delete':
            breadcrumb_items.append({
                'name': '内容删除',
                'url': url_for('excel.delete'),
                'icon': 'fas fa-trash'
            })
        elif page == 'compare':
            breadcrumb_items.append({
                'name': '文件比对',
                'url': url_for('excel.compare'),
                'icon': 'fas fa-balance-scale'
            })
        elif page == 'to_sql':
            breadcrumb_items.append({
                'name': 'Excel转SQL',
                'url': url_for('excel.to_sql'),
                'icon': 'fas fa-code'
            })

    elif module == 'database':
        breadcrumb_items.append({
            'name': '数据库工具',
            'url': url_for('database.index'),
            'icon': 'fas fa-database'
        })

        if page == 'query':
            breadcrumb_items.append({
                'name': '数据库查询',
                'url': url_for('database.query'),
                'icon': 'fas fa-search'
            })
        elif page == 'batch_query':
            breadcrumb_items.append({
                'name': '批量SQL查询',
                'url': url_for('database.batch_query'),
                'icon': 'fas fa-list'
            })
        elif page == 'performance':
            breadcrumb_items.append({
                'name': '性能监控',
                'url': url_for('database.performance'),
                'icon': 'fas fa-chart-line'
            })
        elif page == 'sql_generator':
            breadcrumb_items.append({
                'name': 'SQL生成器',
                'url': url_for('database.sql_generator'),
                'icon': 'fas fa-code'
            })
        elif page == 'performance_test':
            breadcrumb_items.append({
                'name': 'SQL性能测试',
                'url': url_for('database.performance_test'),
                'icon': 'fas fa-stopwatch'
            })

    # 添加额外的面包屑项目
    if extra_items:
        breadcrumb_items.extend(extra_items)

    return breadcrumb_items


def get_breadcrumb_for_auth(page):
    """
    获取认证相关页面的面包屑
    """
    breadcrumb_items = []
    
    if page == 'login':
        breadcrumb_items.append({
            'name': '用户登录',
            'url': None,
            'icon': 'fas fa-sign-in-alt'
        })
    elif page == 'change_password':
        breadcrumb_items.extend([
            {
                'name': '首页',
                'url': url_for('main.index'),
                'icon': 'fas fa-home'
            },
            {
                'name': '修改密码',
                'url': None,
                'icon': 'fas fa-key'
            }
        ])
    
    return breadcrumb_items
