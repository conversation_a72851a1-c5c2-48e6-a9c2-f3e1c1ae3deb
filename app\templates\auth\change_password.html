{% extends "base.html" %}

{% block title %}修改密码 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-key"></i> 修改密码</h4>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        修改密码
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="changePasswordForm">
                        {{ form.hidden_tag() }}
                        
                        <!-- 当前密码 -->
                        <div class="mb-3">
                            {{ form.old_password.label(class="form-label") }}
                            {{ form.old_password(class="form-control", placeholder="请输入当前密码") }}
                            {% if form.old_password.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.old_password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- 新密码 -->
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control", placeholder="请输入新密码") }}
                            {% if form.password.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                密码长度至少8位，包含字母和数字
                            </div>
                        </div>

                        <!-- 确认新密码 -->
                        <div class="mb-3">
                            {{ form.password2.label(class="form-label") }}
                            {{ form.password2(class="form-control", placeholder="请再次输入新密码") }}
                            {% if form.password2.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.password2.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- 按钮组 -->
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                返回首页
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 密码安全提示 -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        密码安全提示
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small mb-0">
                        <li><i class="fas fa-check text-success me-1"></i> 密码长度至少8位字符</li>
                        <li><i class="fas fa-check text-success me-1"></i> 包含大小写字母和数字</li>
                        <li><i class="fas fa-check text-success me-1"></i> 避免使用个人信息作为密码</li>
                        <li><i class="fas fa-check text-success me-1"></i> 定期更换密码，提高安全性</li>
                        <li><i class="fas fa-check text-success me-1"></i> 不要在多个系统使用相同密码</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 表单验证
    $('#changePasswordForm').on('submit', function(e) {
        const newPassword = $('#password').val();
        const confirmPassword = $('#password2').val();

        if (newPassword !== confirmPassword) {
            e.preventDefault();
            showAlert('新密码和确认密码不匹配', 'error');
            return false;
        }

        if (newPassword.length < 8) {
            e.preventDefault();
            showAlert('密码长度至少8位字符', 'error');
            return false;
        }

        // 检查密码复杂度
        const hasLetter = /[a-zA-Z]/.test(newPassword);
        const hasNumber = /\d/.test(newPassword);

        if (!hasLetter || !hasNumber) {
            e.preventDefault();
            showAlert('密码必须包含字母和数字', 'error');
            return false;
        }
    });

    // 实时密码强度检查
    $('#password').on('input', function() {
        const password = $(this).val();
        const strength = checkPasswordStrength(password);
        updatePasswordStrengthIndicator(strength);
    });
});

function checkPasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[^a-zA-Z\d]/.test(password)) score++;
    
    return score;
}

function updatePasswordStrengthIndicator(strength) {
    const indicator = $('#passwordStrength');
    if (indicator.length === 0) {
        $('#password').after('<div id="passwordStrength" class="mt-1"></div>');
    }

    let text = '';
    let className = '';

    switch (strength) {
        case 0:
        case 1:
            text = '密码强度：弱';
            className = 'text-danger';
            break;
        case 2:
        case 3:
            text = '密码强度：中等';
            className = 'text-warning';
            break;
        case 4:
        case 5:
            text = '密码强度：强';
            className = 'text-success';
            break;
    }

    $('#passwordStrength').html(`<small class="${className}">${text}</small>`);
}
</script>
{% endblock %}
