"""
文件验证服务
用于验证上传文件的表结构是否符合标准
"""

import os
import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
import json
import struct
from pathlib import Path

logger = logging.getLogger(__name__)


class ValidationService:
    """文件验证服务"""
    
    # 标准表结构配置
    STANDARD_TABLES = {
        'SETTLE_ZY': {
            'name': '住院结算主单',
            'required_fields': [
                'HOSPITAL_ID', 'HOSPITAL_NAME', 'SETTLE_ID', 'PATIENT_ID',
                'PATIENT_NAME', 'GENDER', 'BIRTHDAY', 'ID_CARD', 'PHONE',
                'ADMISSION_DATE', 'DISCHARGE_DATE', 'DEPT_NAME', 'DOCTOR_NAME',
                'DIAGNOSIS_CODE', 'DIAGNOSIS_NAME', 'TOTAL_AMOUNT', 'MEDICAL_AMOUNT',
                'SELF_AMOUNT', 'INSURANCE_AMOUNT'
            ],
            'field_types': {
                'HOSPITAL_ID': 'VARCHAR(100)',
                'HOSPITAL_NAME': 'VARCHAR(100)',
                'SETTLE_ID': 'VARCHAR(100)',
                'PATIENT_ID': 'VARCHAR(100)',
                'PATIENT_NAME': 'VARCHAR(100)',
                'GENDER': 'VARCHAR(10)',
                'BIRTHDAY': 'DATE',
                'ID_CARD': 'VARCHAR(50)',
                'PHONE': 'VARCHAR(20)',
                'ADMISSION_DATE': 'DATE',
                'DISCHARGE_DATE': 'DATE',
                'DEPT_NAME': 'VARCHAR(100)',
                'DOCTOR_NAME': 'VARCHAR(100)',
                'DIAGNOSIS_CODE': 'VARCHAR(50)',
                'DIAGNOSIS_NAME': 'VARCHAR(200)',
                'TOTAL_AMOUNT': 'NUMBER(10,2)',
                'MEDICAL_AMOUNT': 'NUMBER(10,2)',
                'SELF_AMOUNT': 'NUMBER(10,2)',
                'INSURANCE_AMOUNT': 'NUMBER(10,2)'
            }
        },
        'SETTLE_ZY_DETAIL': {
            'name': '住院结算明细',
            'required_fields': [
                'DETAIL_ID', 'HOSPITAL_ID', 'SETTLE_ID', 'ITEM_CODE',
                'ITEM_NAME', 'ITEM_SPEC', 'UNIT', 'QUANTITY', 'PRICE',
                'TOTAL_AMOUNT', 'MEDICAL_AMOUNT', 'SELF_AMOUNT'
            ],
            'field_types': {
                'DETAIL_ID': 'VARCHAR(100)',
                'HOSPITAL_ID': 'VARCHAR(100)',
                'SETTLE_ID': 'VARCHAR(100)',
                'ITEM_CODE': 'VARCHAR(50)',
                'ITEM_NAME': 'VARCHAR(200)',
                'ITEM_SPEC': 'VARCHAR(100)',
                'UNIT': 'VARCHAR(20)',
                'QUANTITY': 'NUMBER(10,3)',
                'PRICE': 'NUMBER(10,2)',
                'TOTAL_AMOUNT': 'NUMBER(10,2)',
                'MEDICAL_AMOUNT': 'NUMBER(10,2)',
                'SELF_AMOUNT': 'NUMBER(10,2)'
            }
        },
        'SETTLE_MZ': {
            'name': '门诊结算主单',
            'required_fields': [
                'HOSPITAL_ID', 'HOSPITAL_NAME', 'SETTLE_ID', 'PATIENT_ID',
                'PATIENT_NAME', 'GENDER', 'BIRTHDAY', 'ID_CARD', 'PHONE',
                'VISIT_DATE', 'DEPT_NAME', 'DOCTOR_NAME', 'DIAGNOSIS_CODE',
                'DIAGNOSIS_NAME', 'TOTAL_AMOUNT', 'MEDICAL_AMOUNT', 'SELF_AMOUNT'
            ],
            'field_types': {
                'HOSPITAL_ID': 'VARCHAR(100)',
                'HOSPITAL_NAME': 'VARCHAR(100)',
                'SETTLE_ID': 'VARCHAR(100)',
                'PATIENT_ID': 'VARCHAR(100)',
                'PATIENT_NAME': 'VARCHAR(100)',
                'GENDER': 'VARCHAR(10)',
                'BIRTHDAY': 'DATE',
                'ID_CARD': 'VARCHAR(50)',
                'PHONE': 'VARCHAR(20)',
                'VISIT_DATE': 'DATE',
                'DEPT_NAME': 'VARCHAR(100)',
                'DOCTOR_NAME': 'VARCHAR(100)',
                'DIAGNOSIS_CODE': 'VARCHAR(50)',
                'DIAGNOSIS_NAME': 'VARCHAR(200)',
                'TOTAL_AMOUNT': 'NUMBER(10,2)',
                'MEDICAL_AMOUNT': 'NUMBER(10,2)',
                'SELF_AMOUNT': 'NUMBER(10,2)'
            }
        },
        'SETTLE_MZ_DETAIL': {
            'name': '门诊结算明细',
            'required_fields': [
                'DETAIL_ID', 'HOSPITAL_ID', 'SETTLE_ID', 'ITEM_CODE',
                'ITEM_NAME', 'ITEM_SPEC', 'UNIT', 'QUANTITY', 'PRICE',
                'TOTAL_AMOUNT', 'MEDICAL_AMOUNT', 'SELF_AMOUNT'
            ],
            'field_types': {
                'DETAIL_ID': 'VARCHAR(100)',
                'HOSPITAL_ID': 'VARCHAR(100)',
                'SETTLE_ID': 'VARCHAR(100)',
                'ITEM_CODE': 'VARCHAR(50)',
                'ITEM_NAME': 'VARCHAR(200)',
                'ITEM_SPEC': 'VARCHAR(100)',
                'UNIT': 'VARCHAR(20)',
                'QUANTITY': 'NUMBER(10,3)',
                'PRICE': 'NUMBER(10,2)',
                'TOTAL_AMOUNT': 'NUMBER(10,2)',
                'MEDICAL_AMOUNT': 'NUMBER(10,2)',
                'SELF_AMOUNT': 'NUMBER(10,2)'
            }
        },
        'MEDICAL_ORDER_PACKAGE': {
            'name': '医嘱套餐表',
            'required_fields': [
                'HOSPITAL_ID', 'HOSPITAL_NAME', 'PACKAGE_ID', 'PACKAGE_NAME',
                'PACKAGE_TYPE', 'DEPT_NAME', 'DOCTOR_NAME', 'CREATE_DATE',
                'STATUS'
            ],
            'field_types': {
                'HOSPITAL_ID': 'VARCHAR(100)',
                'HOSPITAL_NAME': 'VARCHAR(100)',
                'PACKAGE_ID': 'VARCHAR(100)',
                'PACKAGE_NAME': 'VARCHAR(200)',
                'PACKAGE_TYPE': 'VARCHAR(50)',
                'DEPT_NAME': 'VARCHAR(100)',
                'DOCTOR_NAME': 'VARCHAR(100)',
                'CREATE_DATE': 'DATE',
                'STATUS': 'VARCHAR(20)'
            }
        }
    }

    @staticmethod
    def load_standard_tables() -> Dict[str, Any]:
        """从标准文件加载表结构"""
        try:
            standard_file = '2025国飞-提数标准-医院端v1-5表.xlsx'
            if not os.path.exists(standard_file):
                logger.warning(f"标准文件不存在: {standard_file}")
                return ValidationService.STANDARD_TABLES
            
            # 读取Excel文件
            excel_file = pd.ExcelFile(standard_file)
            tables = {}
            
            # 解析每个工作表
            for sheet_name in excel_file.sheet_names:
                if sheet_name in ['数据采集说明', '医疗机构端数据采集表清单']:
                    continue
                    
                try:
                    df = pd.read_excel(standard_file, sheet_name=sheet_name)
                    table_info = ValidationService._parse_table_structure(df, sheet_name)
                    if table_info:
                        tables[table_info['table_name']] = table_info
                except Exception as e:
                    logger.error(f"解析工作表 {sheet_name} 失败: {str(e)}")
                    continue
            
            # 如果解析成功，返回解析结果，否则返回默认配置
            return tables if tables else ValidationService.STANDARD_TABLES
            
        except Exception as e:
            logger.error(f"加载标准表结构失败: {str(e)}")
            return ValidationService.STANDARD_TABLES

    @staticmethod
    def _parse_table_structure(df: pd.DataFrame, sheet_name: str) -> Dict[str, Any]:
        """解析表结构"""
        try:
            # 根据工作表名称确定表名
            table_name_map = {
                '住院结算主单': 'SETTLE_ZY',
                '住院结算明细': 'SETTLE_ZY_DETAIL',
                '门诊结算主单': 'SETTLE_MZ',
                '门诊结算明细': 'SETTLE_MZ_DETAIL',
                '医嘱套餐表': 'MEDICAL_ORDER_PACKAGE',
                '住院医嘱表': 'MEDICAL_ORDER'
            }
            
            table_name = None
            for key, value in table_name_map.items():
                if key in sheet_name:
                    table_name = value
                    break
            
            if not table_name:
                return None
            
            # 查找字段定义行
            field_rows = []
            for idx, row in df.iterrows():
                # 查找包含字段名的行
                if any(col for col in df.columns if '字段' in str(row[col]) or '程序字段名' in str(row[col])):
                    field_rows = df.iloc[idx+1:].copy()
                    break
            
            if field_rows.empty:
                return None
            
            # 解析字段信息
            required_fields = []
            field_types = {}
            
            for _, row in field_rows.iterrows():
                # 跳过空行
                if pd.isna(row.iloc[1]):
                    continue
                    
                field_name = str(row.iloc[1]).strip()
                field_type = str(row.iloc[3]).strip() if len(row) > 3 else 'VARCHAR(100)'
                
                if field_name and field_name != 'nan':
                    required_fields.append(field_name)
                    field_types[field_name] = field_type
            
            return {
                'table_name': table_name,
                'name': sheet_name,
                'required_fields': required_fields,
                'field_types': field_types
            }
            
        except Exception as e:
            logger.error(f"解析表结构失败: {str(e)}")
            return None

    @staticmethod
    def validate_csv_file(file_path: str) -> Dict[str, Any]:
        """验证CSV文件"""
        try:
            # 读取CSV文件头部
            df = pd.read_csv(file_path, nrows=0)  # 只读取表头
            file_headers = list(df.columns)
            
            # 加载标准表结构
            standard_tables = ValidationService.load_standard_tables()
            
            # 尝试匹配表结构
            best_match = None
            best_score = 0
            
            for table_name, table_info in standard_tables.items():
                required_fields = table_info['required_fields']
                matched_fields = set(file_headers) & set(required_fields)
                score = len(matched_fields) / len(required_fields)
                
                if score > best_score:
                    best_score = score
                    best_match = table_name
            
            if not best_match or best_score < 0.5:
                return {
                    'success': False,
                    'error': '无法识别CSV文件的表结构',
                    'file_headers': file_headers,
                    'suggestions': list(standard_tables.keys())
                }
            
            # 详细验证最佳匹配的表
            table_info = standard_tables[best_match]
            required_fields = table_info['required_fields']
            missing_fields = set(required_fields) - set(file_headers)
            extra_fields = set(file_headers) - set(required_fields)
            
            if missing_fields:
                return {
                    'success': False,
                    'error': f'CSV文件缺少必需字段',
                    'table_name': best_match,
                    'table_display_name': table_info['name'],
                    'file_headers': file_headers,
                    'required_fields': required_fields,
                    'missing_fields': list(missing_fields),
                    'extra_fields': list(extra_fields),
                    'match_score': best_score
                }
            
            return {
                'success': True,
                'message': f'CSV文件表头验证通过，匹配表: {table_info["name"]}',
                'table_name': best_match,
                'table_display_name': table_info['name'],
                'file_headers': file_headers,
                'required_fields': required_fields,
                'missing_fields': [],
                'extra_fields': list(extra_fields),
                'match_score': best_score,
                'total_headers': len(file_headers),
                'matched_headers': len(set(file_headers) & set(required_fields))
            }
            
        except Exception as e:
            logger.error(f"CSV文件验证失败: {str(e)}")
            return {
                'success': False,
                'error': f'CSV文件验证失败: {str(e)}'
            }

    @staticmethod
    def validate_oracle_dump_file(file_path: str) -> Dict[str, Any]:
        """验证Oracle导出文件（DMP/DP格式）"""
        try:
            # 这里需要实现Oracle dump文件的解析
            # 由于Oracle dump文件格式复杂，这里提供一个基础实现
            
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return {
                    'success': False,
                    'error': 'Oracle导出文件为空'
                }
            
            # 检查文件头部标识
            with open(file_path, 'rb') as f:
                header = f.read(100)
                
            # 简单的文件格式检查
            if b'EXPORT' in header or b'Data Pump' in header:
                return {
                    'success': True,
                    'message': 'Oracle导出文件格式验证通过',
                    'file_type': 'Oracle Export',
                    'file_size': file_size,
                    'note': '详细的表结构验证需要导入到Oracle数据库后进行'
                }
            else:
                return {
                    'success': False,
                    'error': '文件不是有效的Oracle导出格式'
                }
                
        except Exception as e:
            logger.error(f"Oracle导出文件验证失败: {str(e)}")
            return {
                'success': False,
                'error': f'Oracle导出文件验证失败: {str(e)}'
            }

    @staticmethod
    def validate_sqlserver_backup_file(file_path: str) -> Dict[str, Any]:
        """验证SQL Server备份文件（BAK格式）"""
        try:
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return {
                    'success': False,
                    'error': 'SQL Server备份文件为空'
                }
            
            # 检查BAK文件头部
            with open(file_path, 'rb') as f:
                header = f.read(100)
            
            # SQL Server备份文件的简单检查
            # BAK文件通常以特定的字节序列开始
            if b'TAPE' in header or b'DISK' in header or file_path.lower().endswith('.bak'):
                return {
                    'success': True,
                    'message': 'SQL Server备份文件格式验证通过',
                    'file_type': 'SQL Server Backup',
                    'file_size': file_size,
                    'note': '详细的表结构验证需要恢复到SQL Server数据库后进行'
                }
            else:
                return {
                    'success': False,
                    'error': '文件不是有效的SQL Server备份格式'
                }
                
        except Exception as e:
            logger.error(f"SQL Server备份文件验证失败: {str(e)}")
            return {
                'success': False,
                'error': f'SQL Server备份文件验证失败: {str(e)}'
            }

    @staticmethod
    def validate_file_by_type(file_path: str, file_type: str) -> Dict[str, Any]:
        """根据文件类型进行验证"""
        try:
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': '文件不存在'
                }
            
            file_type = file_type.lower()
            
            if file_type == 'csv':
                return ValidationService.validate_csv_file(file_path)
            elif file_type in ['dmp', 'dp']:
                return ValidationService.validate_oracle_dump_file(file_path)
            elif file_type == 'bak':
                return ValidationService.validate_sqlserver_backup_file(file_path)
            else:
                return {
                    'success': False,
                    'error': f'不支持的文件类型: {file_type}'
                }
                
        except Exception as e:
            logger.error(f"文件验证失败: {str(e)}")
            return {
                'success': False,
                'error': f'文件验证失败: {str(e)}'
            }
