"""
文件验证服务
用于验证上传文件的表结构是否符合标准
"""

import os
import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
import json
import struct
from pathlib import Path

logger = logging.getLogger(__name__)


class ValidationService:
    """文件验证服务"""
    
    # 标准表结构配置
    STANDARD_TABLES = {
        'SETTLE_ZY': {
            'name': '住院结算主单',
            'required_fields': [
                'HOSPITAL_ID', 'HOSPITAL_NAME', 'SETTLE_ID', 'PATIENT_ID',
                'PATIENT_NAME', 'GENDER', 'BIRTHDAY', 'ID_CARD', 'PHONE',
                'ADMISSION_DATE', 'DISCHARGE_DATE', 'DEPT_NAME', 'DOCTOR_NAME',
                'DIAGNOSIS_CODE', 'DIAGNOSIS_NAME', 'TOTAL_AMOUNT', 'MEDICAL_AMOUNT',
                'SELF_AMOUNT', 'INSURANCE_AMOUNT'
            ],
            'field_types': {
                'HOSPITAL_ID': 'VARCHAR(100)',
                'HOSPITAL_NAME': 'VARCHAR(100)',
                'SETTLE_ID': 'VARCHAR(100)',
                'PATIENT_ID': 'VARCHAR(100)',
                'PATIENT_NAME': 'VARCHAR(100)',
                'GENDER': 'VARCHAR(10)',
                'BIRTHDAY': 'DATE',
                'ID_CARD': 'VARCHAR(50)',
                'PHONE': 'VARCHAR(20)',
                'ADMISSION_DATE': 'DATE',
                'DISCHARGE_DATE': 'DATE',
                'DEPT_NAME': 'VARCHAR(100)',
                'DOCTOR_NAME': 'VARCHAR(100)',
                'DIAGNOSIS_CODE': 'VARCHAR(50)',
                'DIAGNOSIS_NAME': 'VARCHAR(200)',
                'TOTAL_AMOUNT': 'NUMBER(10,2)',
                'MEDICAL_AMOUNT': 'NUMBER(10,2)',
                'SELF_AMOUNT': 'NUMBER(10,2)',
                'INSURANCE_AMOUNT': 'NUMBER(10,2)'
            }
        },
        'SETTLE_ZY_DETAIL': {
            'name': '住院结算明细',
            'required_fields': [
                'DETAIL_ID', 'HOSPITAL_ID', 'SETTLE_ID', 'ITEM_CODE',
                'ITEM_NAME', 'ITEM_SPEC', 'UNIT', 'QUANTITY', 'PRICE',
                'TOTAL_AMOUNT', 'MEDICAL_AMOUNT', 'SELF_AMOUNT'
            ],
            'field_types': {
                'DETAIL_ID': 'VARCHAR(100)',
                'HOSPITAL_ID': 'VARCHAR(100)',
                'SETTLE_ID': 'VARCHAR(100)',
                'ITEM_CODE': 'VARCHAR(50)',
                'ITEM_NAME': 'VARCHAR(200)',
                'ITEM_SPEC': 'VARCHAR(100)',
                'UNIT': 'VARCHAR(20)',
                'QUANTITY': 'NUMBER(10,3)',
                'PRICE': 'NUMBER(10,2)',
                'TOTAL_AMOUNT': 'NUMBER(10,2)',
                'MEDICAL_AMOUNT': 'NUMBER(10,2)',
                'SELF_AMOUNT': 'NUMBER(10,2)'
            }
        },
        'SETTLE_MZ': {
            'name': '门诊结算主单',
            'required_fields': [
                'HOSPITAL_ID', 'HOSPITAL_NAME', 'SETTLE_ID', 'PATIENT_ID',
                'PATIENT_NAME', 'GENDER', 'BIRTHDAY', 'ID_CARD', 'PHONE',
                'VISIT_DATE', 'DEPT_NAME', 'DOCTOR_NAME', 'DIAGNOSIS_CODE',
                'DIAGNOSIS_NAME', 'TOTAL_AMOUNT', 'MEDICAL_AMOUNT', 'SELF_AMOUNT'
            ],
            'field_types': {
                'HOSPITAL_ID': 'VARCHAR(100)',
                'HOSPITAL_NAME': 'VARCHAR(100)',
                'SETTLE_ID': 'VARCHAR(100)',
                'PATIENT_ID': 'VARCHAR(100)',
                'PATIENT_NAME': 'VARCHAR(100)',
                'GENDER': 'VARCHAR(10)',
                'BIRTHDAY': 'DATE',
                'ID_CARD': 'VARCHAR(50)',
                'PHONE': 'VARCHAR(20)',
                'VISIT_DATE': 'DATE',
                'DEPT_NAME': 'VARCHAR(100)',
                'DOCTOR_NAME': 'VARCHAR(100)',
                'DIAGNOSIS_CODE': 'VARCHAR(50)',
                'DIAGNOSIS_NAME': 'VARCHAR(200)',
                'TOTAL_AMOUNT': 'NUMBER(10,2)',
                'MEDICAL_AMOUNT': 'NUMBER(10,2)',
                'SELF_AMOUNT': 'NUMBER(10,2)'
            }
        },
        'SETTLE_MZ_DETAIL': {
            'name': '门诊结算明细',
            'required_fields': [
                'DETAIL_ID', 'HOSPITAL_ID', 'SETTLE_ID', 'ITEM_CODE',
                'ITEM_NAME', 'ITEM_SPEC', 'UNIT', 'QUANTITY', 'PRICE',
                'TOTAL_AMOUNT', 'MEDICAL_AMOUNT', 'SELF_AMOUNT'
            ],
            'field_types': {
                'DETAIL_ID': 'VARCHAR(100)',
                'HOSPITAL_ID': 'VARCHAR(100)',
                'SETTLE_ID': 'VARCHAR(100)',
                'ITEM_CODE': 'VARCHAR(50)',
                'ITEM_NAME': 'VARCHAR(200)',
                'ITEM_SPEC': 'VARCHAR(100)',
                'UNIT': 'VARCHAR(20)',
                'QUANTITY': 'NUMBER(10,3)',
                'PRICE': 'NUMBER(10,2)',
                'TOTAL_AMOUNT': 'NUMBER(10,2)',
                'MEDICAL_AMOUNT': 'NUMBER(10,2)',
                'SELF_AMOUNT': 'NUMBER(10,2)'
            }
        },
        'MEDICAL_ORDER_PACKAGE': {
            'name': '医嘱套餐表',
            'required_fields': [
                'HOSPITAL_ID', 'HOSPITAL_NAME', 'PACKAGE_ID', 'PACKAGE_NAME',
                'PACKAGE_TYPE', 'DEPT_NAME', 'DOCTOR_NAME', 'CREATE_DATE',
                'STATUS'
            ],
            'field_types': {
                'HOSPITAL_ID': 'VARCHAR(100)',
                'HOSPITAL_NAME': 'VARCHAR(100)',
                'PACKAGE_ID': 'VARCHAR(100)',
                'PACKAGE_NAME': 'VARCHAR(200)',
                'PACKAGE_TYPE': 'VARCHAR(50)',
                'DEPT_NAME': 'VARCHAR(100)',
                'DOCTOR_NAME': 'VARCHAR(100)',
                'CREATE_DATE': 'DATE',
                'STATUS': 'VARCHAR(20)'
            }
        }
    }

    @staticmethod
    def load_standard_tables() -> Dict[str, Any]:
        """从标准文件加载表结构"""
        try:
            standard_file = '2025国飞-提数标准-医院端v1-5表.xlsx'
            if not os.path.exists(standard_file):
                logger.warning(f"标准文件不存在: {standard_file}")
                return ValidationService.STANDARD_TABLES

            # 读取Excel文件
            excel_file = pd.ExcelFile(standard_file)
            tables = {}

            # 解析每个工作表
            for sheet_name in excel_file.sheet_names:
                if sheet_name in ['数据采集说明', '医疗机构端数据采集表清单']:
                    continue

                try:
                    df = pd.read_excel(standard_file, sheet_name=sheet_name)
                    table_info = ValidationService._parse_table_structure(df, sheet_name)
                    if table_info:
                        tables[table_info['table_name']] = table_info
                except Exception as e:
                    logger.error(f"解析工作表 {sheet_name} 失败: {str(e)}")
                    continue

            # 如果解析成功，返回解析结果，否则返回默认配置
            return tables if tables else ValidationService.STANDARD_TABLES

        except Exception as e:
            logger.error(f"加载标准表结构失败: {str(e)}")
            return ValidationService.STANDARD_TABLES

    @staticmethod
    def _parse_table_structure(df: pd.DataFrame, sheet_name: str) -> Dict[str, Any]:
        """解析表结构"""
        try:
            # 根据工作表名称确定表名
            table_name_map = {
                '住院结算主单': 'SETTLE_ZY',
                '住院结算明细': 'SETTLE_ZY_DETAIL',
                '门诊结算主单': 'SETTLE_MZ',
                '门诊结算明细': 'SETTLE_MZ_DETAIL',
                '医嘱套餐表': 'MEDICAL_ORDER_PACKAGE',
                '住院医嘱表': 'MEDICAL_ORDER'
            }

            table_name = table_name_map.get(sheet_name)
            if not table_name:
                return None

            # 解析字段信息 - 从第2行开始（第1行是表头）
            required_fields = []
            field_types = {}

            # 跳过第一行表头，从第二行开始解析
            for idx in range(1, len(df)):
                row = df.iloc[idx]

                # 跳过空行或无效行
                if pd.isna(row.iloc[1]) or str(row.iloc[1]).strip() == 'nan':
                    continue

                # 获取字段名（第2列，索引1）
                field_name = str(row.iloc[1]).strip()

                # 获取字段类型（第4列，索引3）
                field_type = str(row.iloc[3]).strip() if len(row) > 3 and not pd.isna(row.iloc[3]) else 'VARCHAR(100)'

                if field_name and field_name != 'nan' and field_name != '程序字段名':
                    required_fields.append(field_name)
                    field_types[field_name] = field_type

            if not required_fields:
                logger.warning(f"工作表 {sheet_name} 没有解析到有效字段")
                return None

            return {
                'table_name': table_name,
                'name': sheet_name,
                'required_fields': required_fields,
                'field_types': field_types
            }

        except Exception as e:
            logger.error(f"解析表结构失败: {str(e)}")
            return None

    @staticmethod
    def validate_csv_file(file_path: str) -> Dict[str, Any]:
        """验证CSV文件"""
        try:
            # 读取CSV文件头部
            df = pd.read_csv(file_path, nrows=0)  # 只读取表头
            file_headers = list(df.columns)
            
            # 加载标准表结构
            standard_tables = ValidationService.load_standard_tables()
            
            # 尝试匹配表结构 - 改进匹配算法
            best_match = None
            best_score = 0
            best_matched_count = 0

            for table_name, table_info in standard_tables.items():
                required_fields = table_info['required_fields']
                matched_fields = set(file_headers) & set(required_fields)
                matched_count = len(matched_fields)

                # 计算两种分数：覆盖率和匹配率
                coverage_score = matched_count / len(required_fields)  # 覆盖了多少必需字段
                match_score = matched_count / len(file_headers) if file_headers else 0  # 文件字段有多少是有效的

                # 综合分数：优先考虑匹配的字段数量
                combined_score = matched_count * 0.6 + coverage_score * 0.3 + match_score * 0.1

                if matched_count > best_matched_count or (matched_count == best_matched_count and combined_score > best_score):
                    best_score = combined_score
                    best_match = table_name
                    best_matched_count = matched_count

            # 降低阈值：只要有匹配的字段就认为可能是该表类型
            if not best_match or best_matched_count == 0:
                # 提供详细的错误信息和指导
                suggestions = []
                for table_name, table_info in standard_tables.items():
                    suggestions.append({
                        'table_name': table_name,
                        'display_name': table_info['name'],
                        'sample_fields': table_info['required_fields'][:10]  # 前10个字段作为示例
                    })

                return {
                    'success': False,
                    'error': '无法识别CSV文件的表结构，请检查文件表头是否符合标准格式',
                    'file_headers': file_headers,
                    'file_headers_count': len(file_headers),
                    'suggestions': suggestions,
                    'guidance': {
                        'title': '如何修正CSV文件',
                        'steps': [
                            '1. 检查CSV文件的第一行是否为标准表头',
                            '2. 确保表头字段名与标准格式完全一致（区分大小写）',
                            '3. 删除多余的空列或无关列',
                            '4. 确保必需字段都已包含',
                            '5. 重新保存CSV文件并上传'
                        ]
                    }
                }
            
            # 详细验证最佳匹配的表
            table_info = standard_tables[best_match]
            required_fields = table_info['required_fields']
            matched_fields = set(file_headers) & set(required_fields)
            missing_fields = set(required_fields) - set(file_headers)
            extra_fields = set(file_headers) - set(required_fields)

            # 重新计算准确的匹配分数
            coverage_score = len(matched_fields) / len(required_fields)
            
            # 判断是否为完整匹配（所有必需字段都有）
            is_complete_match = len(missing_fields) == 0

            if is_complete_match:
                return {
                    'success': True,
                    'message': f'CSV文件表头验证通过，完全匹配表: {table_info["name"]}',
                    'table_name': best_match,
                    'table_display_name': table_info['name'],
                    'file_headers': file_headers,
                    'file_headers_count': len(file_headers),
                    'required_fields': required_fields,
                    'required_fields_count': len(required_fields),
                    'missing_fields': [],
                    'missing_fields_count': 0,
                    'extra_fields': list(extra_fields),
                    'extra_fields_count': len(extra_fields),
                    'match_score': coverage_score,
                    'matched_fields': list(matched_fields),
                    'matched_fields_count': len(matched_fields),
                    'total_headers': len(file_headers),
                    'matched_headers': len(matched_fields)
                }
            else:
                # 部分匹配，但缺少字段
                return {
                    'success': False,
                    'error': f'CSV文件缺少 {len(missing_fields)} 个必需字段',
                    'table_name': best_match,
                    'table_display_name': table_info['name'],
                    'file_headers': file_headers,
                    'file_headers_count': len(file_headers),
                    'required_fields': required_fields,
                    'required_fields_count': len(required_fields),
                    'missing_fields': list(missing_fields),
                    'missing_fields_count': len(missing_fields),
                    'extra_fields': list(extra_fields),
                    'extra_fields_count': len(extra_fields),
                    'match_score': coverage_score,
                    'matched_fields': list(matched_fields),
                    'matched_fields_count': len(matched_fields),
                    'guidance': {
                        'title': f'如何修正 {table_info["name"]} CSV文件',
                        'missing_fields_guidance': f'请在CSV文件中添加以下 {len(missing_fields)} 个必需字段：',
                        'steps': [
                            '1. 打开CSV文件编辑器（如Excel、记事本等）',
                            '2. 在第一行添加缺少的字段名',
                            '3. 确保字段名与标准格式完全一致（区分大小写）',
                            '4. 为新增字段填入相应的数据',
                            '5. 保存文件并重新上传'
                        ]
                    }
                }
            
        except Exception as e:
            logger.error(f"CSV文件验证失败: {str(e)}")
            return {
                'success': False,
                'error': f'CSV文件验证失败: {str(e)}'
            }

    @staticmethod
    def validate_oracle_dump_file(file_path: str) -> Dict[str, Any]:
        """验证Oracle导出文件（DMP/DP格式）"""
        try:
            # 这里需要实现Oracle dump文件的解析
            # 由于Oracle dump文件格式复杂，这里提供一个基础实现
            
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return {
                    'success': False,
                    'error': 'Oracle导出文件为空'
                }
            
            # 检查文件头部标识
            with open(file_path, 'rb') as f:
                header = f.read(100)
                
            # 简单的文件格式检查
            if b'EXPORT' in header or b'Data Pump' in header:
                return {
                    'success': True,
                    'message': 'Oracle导出文件格式验证通过',
                    'file_type': 'Oracle Export',
                    'file_size': file_size,
                    'note': '详细的表结构验证需要导入到Oracle数据库后进行'
                }
            else:
                return {
                    'success': False,
                    'error': '文件不是有效的Oracle导出格式'
                }
                
        except Exception as e:
            logger.error(f"Oracle导出文件验证失败: {str(e)}")
            return {
                'success': False,
                'error': f'Oracle导出文件验证失败: {str(e)}'
            }

    @staticmethod
    def validate_sqlserver_backup_file(file_path: str) -> Dict[str, Any]:
        """验证SQL Server备份文件（BAK格式）"""
        try:
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return {
                    'success': False,
                    'error': 'SQL Server备份文件为空'
                }
            
            # 检查BAK文件头部
            with open(file_path, 'rb') as f:
                header = f.read(100)
            
            # SQL Server备份文件的简单检查
            # BAK文件通常以特定的字节序列开始
            if b'TAPE' in header or b'DISK' in header or file_path.lower().endswith('.bak'):
                return {
                    'success': True,
                    'message': 'SQL Server备份文件格式验证通过',
                    'file_type': 'SQL Server Backup',
                    'file_size': file_size,
                    'note': '详细的表结构验证需要恢复到SQL Server数据库后进行'
                }
            else:
                return {
                    'success': False,
                    'error': '文件不是有效的SQL Server备份格式'
                }
                
        except Exception as e:
            logger.error(f"SQL Server备份文件验证失败: {str(e)}")
            return {
                'success': False,
                'error': f'SQL Server备份文件验证失败: {str(e)}'
            }

    @staticmethod
    def validate_file_by_type(file_path: str, file_type: str) -> Dict[str, Any]:
        """根据文件类型进行验证"""
        try:
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': '文件不存在'
                }
            
            file_type = file_type.lower()
            
            if file_type == 'csv':
                return ValidationService.validate_csv_file(file_path)
            elif file_type in ['dmp', 'dp']:
                return ValidationService.validate_oracle_dump_file(file_path)
            elif file_type == 'bak':
                return ValidationService.validate_sqlserver_backup_file(file_path)
            else:
                return {
                    'success': False,
                    'error': f'不支持的文件类型: {file_type}'
                }
                
        except Exception as e:
            logger.error(f"文件验证失败: {str(e)}")
            return {
                'success': False,
                'error': f'文件验证失败: {str(e)}'
            }
