#!/usr/bin/env python3
"""
最终测试规则管理模块的所有功能
"""

import requests
import json
import re

def final_test():
    base_url = "http://127.0.0.1:5002"
    session = requests.Session()
    
    # 先登录
    print("🔐 正在登录...")
    login_page = session.get(f"{base_url}/auth/login")
    
    csrf_patterns = [
        r'name="csrf_token" value="([^"]+)"',
        r'<input[^>]*name="csrf_token"[^>]*value="([^"]+)"',
    ]
    
    csrf_token = None
    for pattern in csrf_patterns:
        csrf_match = re.search(pattern, login_page.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            break
    
    login_data = {
        'username': 'admin',
        'password': 'Admin123!',
        'remember_me': False
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    response = session.post(f"{base_url}/auth/login", data=login_data, allow_redirects=True)
    
    if response.status_code == 200 and '/auth/login' not in response.url:
        print("✅ 登录成功")
    else:
        print("❌ 登录失败")
        return
    
    # 测试所有页面
    pages = [
        ('/rules/', '规则管理首页'),
        ('/rules/knowledge_base', '飞检规则知识库'),
        ('/rules/sql_generator', 'SQL生成器'),
        ('/rules/system_rules', '系统规则语句')
    ]
    
    print("\n📋 测试页面访问...")
    for url, name in pages:
        try:
            response = session.get(f"{base_url}{url}")
            if response.status_code == 200:
                # 检查页面是否包含预期内容
                content = response.text
                if 'Error:' in content or 'Traceback' in content:
                    print(f"❌ {name} - 页面有错误")
                else:
                    print(f"✅ {name} - 正常")
            else:
                print(f"❌ {name} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {name} - 异常: {str(e)}")
    
    # 测试API端点
    print("\n🔧 测试API端点...")
    api_tests = [
        ('/rules/api/rules?page=1&per_page=5', '规则列表'),
        ('/rules/api/search?city=北京', '规则搜索'),
        ('/rules/api/behavior_types', '行为认定类型'),
        ('/rules/api/city_types', '城市类型'),
        ('/rules/api/rule_sources', '规则来源'),
        ('/rules/api/type_types', '类型'),
        ('/rules/api/rule_type_types', '规则类型')
    ]
    
    for url, name in api_tests:
        try:
            response = session.get(f"{base_url}{url}")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ {name} - 成功")
                else:
                    print(f"❌ {name} - API返回失败: {data.get('error', '未知错误')}")
            else:
                print(f"❌ {name} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {name} - 异常: {str(e)}")
    
    # 测试功能性API
    print("\n⚙️ 测试功能性API...")
    
    # 测试规则详情
    try:
        response = session.get(f"{base_url}/rules/api/rules/3226")
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                print("✅ 规则详情API - 成功")
            else:
                print("❌ 规则详情API - 无数据")
        else:
            print(f"❌ 规则详情API - HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 规则详情API - 异常: {str(e)}")
    
    print("\n🎉 测试完成！")

if __name__ == '__main__':
    final_test()
