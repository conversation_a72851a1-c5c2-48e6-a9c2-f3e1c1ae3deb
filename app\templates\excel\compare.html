{% extends "base.html" %}

{% block title %}Excel比对工具 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-balance-scale"></i> Excel比对工具</h4>
{% endblock %}

{% block content %}
<!-- 文件上传区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-upload"></i> 文件上传
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="file1Input" class="form-label">原始文件</label>
                            <input type="file" class="form-control" id="file1Input" accept=".xlsx,.xls" onchange="uploadFile1()">
                            <div class="form-text">选择作为比对基准的Excel文件</div>
                        </div>
                        
                        <div id="file1Info" class="mt-3 d-none">
                            <div class="alert alert-info">
                                <strong>原始文件信息：</strong>
                                <div id="file1Name"></div>
                                <div id="file1Size"></div>
                                <div id="file1Rows"></div>
                                <div id="file1Columns"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="file2Input" class="form-label">对比文件</label>
                            <input type="file" class="form-control" id="file2Input" accept=".xlsx,.xls" onchange="uploadFile2()">
                            <div class="form-text">选择要与原始文件比对的Excel文件</div>
                        </div>
                        
                        <div id="file2Info" class="mt-3 d-none">
                            <div class="alert alert-warning">
                                <strong>对比文件信息：</strong>
                                <div id="file2Name"></div>
                                <div id="file2Size"></div>
                                <div id="file2Rows"></div>
                                <div id="file2Columns"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 比对配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 比对配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">比对模式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="compareMode" id="fullCompare" value="full" checked>
                                <label class="form-check-label" for="fullCompare">
                                    完整比对
                                </label>
                                <div class="form-text">比对所有行和列的差异</div>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="compareMode" id="keyCompare" value="key">
                                <label class="form-check-label" for="keyCompare">
                                    基于关键列比对
                                </label>
                                <div class="form-text">基于指定的关键列进行匹配比对</div>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="compareMode" id="structureCompare" value="structure">
                                <label class="form-check-label" for="structureCompare">
                                    结构比对
                                </label>
                                <div class="form-text">仅比对文件结构（列名、数据类型等）</div>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="keyColumnsConfig" style="display: none;">
                            <label class="form-label">关键列配置</label>
                            <div id="keyColumnsContainer">
                                <!-- 关键列选择将动态添加 -->
                            </div>
                            <button type="button" class="btn btn-sm btn-success" onclick="addKeyColumn()">
                                <i class="fas fa-plus"></i> 添加关键列
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">比对选项</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="ignoreCase" checked>
                                <label class="form-check-label" for="ignoreCase">
                                    忽略大小写
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="ignoreWhitespace" checked>
                                <label class="form-check-label" for="ignoreWhitespace">
                                    忽略空格
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="ignoreEmptyRows">
                                <label class="form-check-label" for="ignoreEmptyRows">
                                    忽略空行
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showOnlyDifferences" checked>
                                <label class="form-check-label" for="showOnlyDifferences">
                                    仅显示差异
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">输出格式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="generateReport" checked>
                                <label class="form-check-label" for="generateReport">
                                    生成比对报告
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="highlightDifferences" checked>
                                <label class="form-check-label" for="highlightDifferences">
                                    高亮显示差异
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="exportDifferences">
                                <label class="form-check-label" for="exportDifferences">
                                    导出差异到Excel
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="startComparison()" disabled id="compareBtn">
                            <i class="fas fa-balance-scale"></i> 开始比对
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="previewComparison()" disabled id="previewBtn">
                            <i class="fas fa-eye"></i> 预览比对
                        </button>
                        <button type="button" class="btn btn-info ms-2" onclick="resetForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 比对进度区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks"></i> 比对进度
                </h6>
            </div>
            <div class="card-body">
                <div id="comparisonProgress" class="d-none">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>比对进度</span>
                            <span id="comparisonStatus">比对中...</span>
                        </div>
                        <div class="progress">
                            <div id="comparisonProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div id="comparisonInfo" class="alert alert-info">
                        <strong>当前状态：</strong><span id="currentComparisonStatus">正在比对...</span>
                    </div>
                </div>
                
                <div id="noComparisonProgress" class="text-center text-muted py-4">
                    <i class="fas fa-balance-scale fa-3x mb-3"></i>
                    <p>上传两个Excel文件并配置比对选项后，点击"开始比对"进行文件比对</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 比对结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list-alt"></i> 比对结果
                </h6>
                <div id="resultActions" class="d-none">
                    <button type="button" class="btn btn-success btn-sm" onclick="downloadComparisonReport()">
                        <i class="fas fa-download"></i> 下载报告
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="exportDifferencesToExcel()">
                        <i class="fas fa-file-excel"></i> 导出差异
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="showComparisonDetails()">
                        <i class="fas fa-list"></i> 详细信息
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="comparisonResultsContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                        <p>比对完成后结果将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 比对详情模态框 -->
<div class="modal fade" id="comparisonDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list"></i> 比对详细信息
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="comparisonDetailsContent">
                <!-- 比对详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportComparisonDetails()">导出详情</button>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye"></i> 比对预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewModalContent">
                <!-- 预览内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmComparison()">确认比对</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let file1Path = '';
let file2Path = '';
let comparisonResults = null;
let keyColumnCount = 0;

// 页面加载完成后初始化
$(document).ready(function() {
    // 绑定比对模式变化事件
    $('input[name="compareMode"]').change(function() {
        const mode = $(this).val();
        if (mode === 'key') {
            $('#keyColumnsConfig').show();
        } else {
            $('#keyColumnsConfig').hide();
        }
    });
});

// 上传文件1
function uploadFile1() {
    const fileInput = document.getElementById('file1Input');
    const file = fileInput.files[0];
    
    if (!file) return;
    
    uploadFile(file, 'file1');
}

// 上传文件2
function uploadFile2() {
    const fileInput = document.getElementById('file2Input');
    const file = fileInput.files[0];
    
    if (!file) return;
    
    uploadFile(file, 'file2');
}

// 通用文件上传函数
function uploadFile(file, type) {
    const formData = new FormData();
    formData.append('file', file);
    
    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    fetch('/excel/api/upload', {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (type === 'file1') {
                file1Path = data.file_path;
                document.getElementById('file1Name').textContent = `文件名: ${data.filename}`;
                document.getElementById('file1Size').textContent = `大小: ${formatFileSize(data.file_size)}`;
                // 这里应该获取实际的行列数
                document.getElementById('file1Rows').textContent = `行数: 加载中...`;
                document.getElementById('file1Columns').textContent = `列数: 加载中...`;
                document.getElementById('file1Info').classList.remove('d-none');
            } else {
                file2Path = data.file_path;
                document.getElementById('file2Name').textContent = `文件名: ${data.filename}`;
                document.getElementById('file2Size').textContent = `大小: ${formatFileSize(data.file_size)}`;
                document.getElementById('file2Rows').textContent = `行数: 加载中...`;
                document.getElementById('file2Columns').textContent = `列数: 加载中...`;
                document.getElementById('file2Info').classList.remove('d-none');
            }
            
            checkFormComplete();
            loadFileColumns();
        } else {
            showAlert('上传失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('网络错误: ' + error.message, 'danger');
    });
}

// 加载文件列信息
function loadFileColumns() {
    if (!file1Path || !file2Path) return;
    
    // 这里应该调用API获取文件列信息
    // 暂时使用示例数据
    const sampleColumns = ['患者姓名', '身份证号', '就诊日期', '科室名称', '费用金额', '医生姓名'];
    
    // 更新关键列选择器
    updateKeyColumnSelectors(sampleColumns);
}

// 更新关键列选择器
function updateKeyColumnSelectors(columns) {
    const container = document.getElementById('keyColumnsContainer');
    container.innerHTML = '';
    
    // 添加一个默认的关键列选择器
    addKeyColumn(columns);
}

// 添加关键列选择器
function addKeyColumn(columns = null) {
    const container = document.getElementById('keyColumnsContainer');
    
    if (!columns) {
        // 如果没有提供列信息，使用示例数据
        columns = ['患者姓名', '身份证号', '就诊日期', '科室名称', '费用金额', '医生姓名'];
    }
    
    const keyColumnHtml = `
        <div class="row mb-2" id="keyColumn_${keyColumnCount}">
            <div class="col-md-10">
                <select class="form-select" name="key_column_${keyColumnCount}">
                    <option value="">选择关键列</option>
                    ${columns.map(col => `<option value="${col}">${col}</option>`).join('')}
                </select>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeKeyColumn(${keyColumnCount})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', keyColumnHtml);
    keyColumnCount++;
}

// 删除关键列选择器
function removeKeyColumn(index) {
    document.getElementById(`keyColumn_${index}`).remove();
}

// 检查表单是否完整
function checkFormComplete() {
    const isComplete = file1Path && file2Path;
    
    document.getElementById('compareBtn').disabled = !isComplete;
    document.getElementById('previewBtn').disabled = !isComplete;
}

// 预览比对
function previewComparison() {
    const config = getComparisonConfig();
    
    // 这里应该调用API进行预览
    showAlert('预览功能开发中...', 'info');
}

// 开始比对
function startComparison() {
    const config = getComparisonConfig();
    
    // 显示进度
    document.getElementById('comparisonProgress').classList.remove('d-none');
    document.getElementById('noComparisonProgress').classList.add('d-none');
    
    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // 调用比对API
    fetch('/excel/api/compare', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('comparisonProgress').classList.add('d-none');
        document.getElementById('noComparisonProgress').classList.remove('d-none');
        
        if (data.success) {
            comparisonResults = data.comparison_result;
            displayComparisonResults(data.comparison_result);
            showAlert('比对完成！', 'success');
        } else {
            showAlert('比对失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        document.getElementById('comparisonProgress').classList.add('d-none');
        document.getElementById('noComparisonProgress').classList.remove('d-none');
        showAlert('网络错误: ' + error.message, 'danger');
    });
}

// 获取比对配置
function getComparisonConfig() {
    const compareMode = document.querySelector('input[name="compareMode"]:checked').value;
    const keyColumns = [];
    
    if (compareMode === 'key') {
        for (let i = 0; i < keyColumnCount; i++) {
            const select = document.querySelector(`select[name="key_column_${i}"]`);
            if (select && select.value) {
                keyColumns.push(select.value);
            }
        }
    }
    
    return {
        file1_path: file1Path,
        file2_path: file2Path,
        compare_mode: compareMode,
        key_columns: keyColumns,
        ignore_case: document.getElementById('ignoreCase').checked,
        ignore_whitespace: document.getElementById('ignoreWhitespace').checked,
        ignore_empty_rows: document.getElementById('ignoreEmptyRows').checked,
        show_only_differences: document.getElementById('showOnlyDifferences').checked,
        generate_report: document.getElementById('generateReport').checked,
        highlight_differences: document.getElementById('highlightDifferences').checked,
        export_differences: document.getElementById('exportDifferences').checked
    };
}

// 显示比对结果
function displayComparisonResults(results) {
    const { summary, differences, statistics } = results;
    
    let resultsHtml = `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5>${statistics.total_rows_file1}</h5>
                        <p class="mb-0">原始文件行数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h5>${statistics.total_rows_file2}</h5>
                        <p class="mb-0">对比文件行数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-${statistics.different_rows > 0 ? 'warning' : 'success'} text-white">
                    <div class="card-body text-center">
                        <h5>${statistics.different_rows}</h5>
                        <p class="mb-0">差异行数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-${statistics.similarity_percentage < 90 ? 'danger' : 'success'} text-white">
                    <div class="card-body text-center">
                        <h5>${statistics.similarity_percentage.toFixed(1)}%</h5>
                        <p class="mb-0">相似度</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-${summary.has_differences ? 'warning' : 'success'}">
            <h6><i class="fas fa-info-circle"></i> 比对摘要</h6>
            <p>${summary.message}</p>
        </div>
    `;
    
    if (differences && differences.length > 0) {
        resultsHtml += `
            <h6>主要差异：</h6>
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>行号</th>
                            <th>列名</th>
                            <th>原始值</th>
                            <th>对比值</th>
                            <th>差异类型</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        differences.slice(0, 10).forEach(diff => {
            resultsHtml += `
                <tr>
                    <td>${diff.row}</td>
                    <td>${diff.column}</td>
                    <td>${diff.value1 || '<空>'}</td>
                    <td>${diff.value2 || '<空>'}</td>
                    <td><span class="badge bg-warning">${diff.type}</span></td>
                </tr>
            `;
        });
        
        resultsHtml += `
                    </tbody>
                </table>
            </div>
        `;
        
        if (differences.length > 10) {
            resultsHtml += `<p class="text-muted">显示前10个差异，共${differences.length}个差异。</p>`;
        }
    }
    
    document.getElementById('comparisonResultsContainer').innerHTML = resultsHtml;
    document.getElementById('resultActions').classList.remove('d-none');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 重置表单
function resetForm() {
    file1Path = '';
    file2Path = '';
    
    document.getElementById('file1Input').value = '';
    document.getElementById('file2Input').value = '';
    document.getElementById('file1Info').classList.add('d-none');
    document.getElementById('file2Info').classList.add('d-none');
    
    document.getElementById('fullCompare').checked = true;
    document.getElementById('keyColumnsConfig').style.display = 'none';
    document.getElementById('keyColumnsContainer').innerHTML = '';
    keyColumnCount = 0;
    
    // 重置复选框
    document.getElementById('ignoreCase').checked = true;
    document.getElementById('ignoreWhitespace').checked = true;
    document.getElementById('ignoreEmptyRows').checked = false;
    document.getElementById('showOnlyDifferences').checked = true;
    document.getElementById('generateReport').checked = true;
    document.getElementById('highlightDifferences').checked = true;
    document.getElementById('exportDifferences').checked = false;
    
    checkFormComplete();
    
    showAlert('表单已重置', 'info');
}

// 下载比对报告
function downloadComparisonReport() {
    if (!comparisonResults) {
        showAlert('没有可下载的报告', 'warning');
        return;
    }
    
    showAlert('下载功能开发中...', 'info');
}

// 导出差异到Excel
function exportDifferencesToExcel() {
    if (!comparisonResults) {
        showAlert('没有可导出的差异', 'warning');
        return;
    }
    
    showAlert('导出功能开发中...', 'info');
}

// 显示比对详情
function showComparisonDetails() {
    if (!comparisonResults) {
        showAlert('没有可显示的详情', 'warning');
        return;
    }
    
    document.getElementById('comparisonDetailsModal').querySelector('.modal').modal('show');
}

// 导出比对详情
function exportComparisonDetails() {
    showAlert('详情导出功能开发中...', 'info');
}

// 确认比对
function confirmComparison() {
    $('#previewModal').modal('hide');
    startComparison();
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
