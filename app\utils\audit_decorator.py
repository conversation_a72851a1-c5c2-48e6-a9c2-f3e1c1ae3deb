#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
审计日志装饰器
用于自动记录API操作
"""

from functools import wraps
from flask import request, current_app
from flask_login import current_user
from app.models.audit import AuditLog


def audit_log(action=None, resource=None, get_resource_id=None, get_details=None):
    """
    审计日志装饰器
    
    Args:
        action: 操作类型，如 'create', 'update', 'delete', 'access'
        resource: 资源类型，如 'user', 'rule', 'scheme'
        get_resource_id: 函数，用于从请求或响应中获取资源ID
        get_details: 函数，用于从请求或响应中获取操作详情
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 执行原函数
            result = f(*args, **kwargs)
            
            try:
                # 确定操作类型
                actual_action = action
                if actual_action is None:
                    # 根据HTTP方法自动推断操作类型
                    method_map = {
                        'GET': 'access',
                        'POST': 'create',
                        'PUT': 'update',
                        'PATCH': 'update',
                        'DELETE': 'delete'
                    }
                    actual_action = method_map.get(request.method, 'unknown')
                
                # 确定资源类型
                actual_resource = resource
                if actual_resource is None:
                    # 从URL路径推断资源类型
                    path = request.path
                    if '/rules' in path:
                        actual_resource = 'rule'
                    elif '/schemes' in path:
                        actual_resource = 'scheme'
                    elif '/users' in path:
                        actual_resource = 'user'
                    elif '/uploads' in path:
                        actual_resource = 'upload'
                    elif '/tasks' in path:
                        actual_resource = 'task'
                    else:
                        actual_resource = 'unknown'
                
                # 获取资源ID
                resource_id = None
                if get_resource_id:
                    try:
                        resource_id = get_resource_id(result, *args, **kwargs)
                    except:
                        pass
                
                if resource_id is None:
                    # 尝试从URL参数获取ID
                    if hasattr(result, 'json') and result.json:
                        data = result.json
                        if isinstance(data, dict):
                            # 尝试从响应中获取ID
                            for key in ['id', 'rule_id', 'scheme_id', 'user_id', 'task_id']:
                                if key in data:
                                    resource_id = str(data[key])
                                    break
                            # 尝试从嵌套对象中获取ID
                            for obj_key in ['rule', 'scheme', 'user', 'task']:
                                if obj_key in data and isinstance(data[obj_key], dict):
                                    resource_id = str(data[obj_key].get('id', ''))
                                    break
                
                # 获取操作详情
                details = None
                if get_details:
                    try:
                        details = get_details(result, *args, **kwargs)
                    except:
                        pass
                
                if details is None:
                    # 自动生成操作详情
                    if actual_action == 'create':
                        details = f"创建{actual_resource}"
                    elif actual_action == 'update':
                        details = f"更新{actual_resource}"
                    elif actual_action == 'delete':
                        details = f"删除{actual_resource}"
                    elif actual_action == 'access':
                        details = f"访问{actual_resource}"
                    else:
                        details = f"{actual_action} {actual_resource}"
                    
                    if resource_id:
                        details += f" (ID: {resource_id})"
                
                # 记录审计日志
                AuditLog.log_action(
                    action=actual_action,
                    resource=actual_resource,
                    resource_id=resource_id,
                    details=details
                )
                
            except Exception as e:
                current_app.logger.error(f"审计日志记录失败: {e}")
                # 不影响主要功能
                pass
            
            return result
        
        return decorated_function
    return decorator


def audit_login(username=None, success=True, details=None):
    """记录登录审计日志"""
    try:
        action = 'login_success' if success else 'login_failed'
        actual_details = details or f"用户 {username} {'登录成功' if success else '登录失败'}"
        
        AuditLog.log_action(
            action=action,
            resource='auth',
            details=actual_details
        )
    except Exception as e:
        current_app.logger.error(f"登录审计日志记录失败: {e}")


def audit_logout(username=None):
    """记录登出审计日志"""
    try:
        details = f"用户 {username} 登出系统" if username else "用户登出系统"
        
        AuditLog.log_action(
            action='logout',
            resource='auth',
            details=details
        )
    except Exception as e:
        current_app.logger.error(f"登出审计日志记录失败: {e}")


def audit_access(resource=None, details=None):
    """记录页面访问审计日志"""
    try:
        actual_resource = resource or 'page'
        actual_details = details or f"访问页面: {request.path}"
        
        AuditLog.log_action(
            action='access',
            resource=actual_resource,
            details=actual_details
        )
    except Exception as e:
        current_app.logger.error(f"访问审计日志记录失败: {e}")


# 便捷的装饰器实例
audit_rule_operation = audit_log(resource='rule')
audit_scheme_operation = audit_log(resource='scheme')
audit_user_operation = audit_log(resource='user')
audit_upload_operation = audit_log(resource='upload')
audit_task_operation = audit_log(resource='task')
