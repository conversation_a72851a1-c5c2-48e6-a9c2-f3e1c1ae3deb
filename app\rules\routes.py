from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.rules import bp
from app.auth.decorators import permission_required
from app.models.audit import AuditLog
from app.rules.services import RuleService, SQLTemplateService
from app.utils.database import handle_db_error
from app.utils.breadcrumb import generate_breadcrumb
from datetime import datetime
import os

@bp.route('/')
@login_required
@permission_required('rules')
def index():
    """规则管理模块首页"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='rules_index',
        details='访问规则管理模块首页'
    )
    breadcrumb_items = generate_breadcrumb('rules')
    return render_template('rules/index.html', breadcrumb_items=breadcrumb_items)

@bp.route('/knowledge_base')
@login_required
@permission_required('rules.knowledge_base')
def knowledge_base():
    """飞检规则知识库"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='rules_knowledge_base',
        details='访问飞检规则知识库'
    )
    breadcrumb_items = generate_breadcrumb('rules', 'knowledge_base')
    return render_template('rules/knowledge_base.html', breadcrumb_items=breadcrumb_items)

@bp.route('/sql_generator')
@login_required
@permission_required('rules.sql_generator')
def sql_generator():
    """规则SQL生成器"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='rules_sql_generator',
        details='访问规则SQL生成器'
    )

    breadcrumb_items = generate_breadcrumb('rules', 'sql_generator')
    return render_template('rules/rule_sql_generator.html', breadcrumb_items=breadcrumb_items)

@bp.route('/system_rules')
@login_required
@permission_required('rules.system_rules')
def system_rules():
    """系统规则语句"""
    # 记录访问日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='access',
        resource='rules_system_rules',
        details='访问系统规则语句'
    )
    breadcrumb_items = generate_breadcrumb('rules', 'system_rules')
    return render_template('rules/system_rules.html', breadcrumb_items=breadcrumb_items)

# API路由
@bp.route('/api/search')
@login_required
@permission_required('rules.knowledge_base')
def api_search_rules():
    """搜索规则API"""
    try:
        # 获取搜索参数
        filters = {
            'behavior_type': request.args.get('behavior_type', ''),
            'city': request.args.get('city', ''),
            'rule_source': request.args.get('rule_source', ''),
            'rule_name': request.args.get('rule_name', ''),
            'rule_content': request.args.get('rule_content', ''),
            'type': request.args.get('type', ''),
            'rule_type': request.args.get('rule_type', ''),
            'visit_type': request.args.get('visit_type', '')
        }

        # 过滤空值
        filters = {k: v for k, v in filters.items() if v}

        # 使用RuleService搜索规则
        filtered_rules = RuleService.search_rules(filters)

        return jsonify({
            'success': True,
            'rules': filtered_rules
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/api/behavior_types')
@login_required
@permission_required('rules.knowledge_base')
def api_behavior_types():
    """获取行为认定类型API"""
    try:
        types = RuleService.get_behavior_types()
        return jsonify({
            'success': True,
            'types': types
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'types': []
        })

@bp.route('/api/city_types')
@login_required
@permission_required('rules.knowledge_base')
def api_city_types():
    """获取城市类型API"""
    try:
        types = RuleService.get_cities()
        return jsonify({
            'success': True,
            'types': types
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'types': []
        })

@bp.route('/api/rule_sources')
@login_required
@permission_required('rules.knowledge_base')
def api_rule_sources():
    """获取规则来源API"""
    try:
        types = RuleService.get_rule_sources()
        return jsonify({
            'success': True,
            'types': types
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'types': []
        })

@bp.route('/api/type_types')
@login_required
@permission_required('rules.knowledge_base')
def api_type_types():
    """获取类型API"""
    try:
        types = RuleService.get_type_types()
        return jsonify({
            'success': True,
            'types': types
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'types': []
        })

@bp.route('/api/rule_type_types')
@login_required
@permission_required('rules.knowledge_base')
def api_rule_type_types():
    """获取规则类型API"""
    try:
        types = RuleService.get_rule_type_types()
        return jsonify({
            'success': True,
            'types': types
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'types': []
        })

@bp.route('/api/generate_sql', methods=['POST'])
@login_required
@permission_required('rules.sql_generator')
def api_generate_sql():
    """生成规则SQL API"""
    try:
        data = request.get_json()
        rule_ids = data.get('rule_ids', [])
        template_path = data.get('template_path', '')
        visit_type = data.get('visit_type', '')

        if not rule_ids:
            return jsonify({
                'success': False,
                'error': '请选择至少一条规则'
            }), 400

        # 根据模板路径生成SQL
        sql_template = get_sql_template(template_path)
        generated_sql = generate_sql_from_template(sql_template, rule_ids, visit_type)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='generate_sql',
            resource='rules_sql',
            details=f'生成规则SQL，规则数量: {len(rule_ids)}, 模板: {template_path}'
        )

        return jsonify({
            'success': True,
            'sql': generated_sql
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bp.route('/api/execute_sql', methods=['POST'])
@login_required
@permission_required('rules.sql_generator')
def api_execute_sql():
    """执行SQL API"""
    try:
        data = request.get_json()
        sql = data.get('sql', '')

        if not sql.strip():
            return jsonify({
                'success': False,
                'error': 'SQL语句不能为空'
            }), 400

        # 这里应该连接到实际的数据库执行SQL
        # 为了安全起见，这里只是模拟执行
        affected_rows = 0  # 模拟影响的行数

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='execute_sql',
            resource='rules_sql',
            details=f'执行规则SQL，影响行数: {affected_rows}'
        )

        return jsonify({
            'success': True,
            'affected_rows': affected_rows
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def get_sql_template(template_path):
    """获取SQL模板"""
    templates = {
        'rule_oracle_name_inpatient': '''
SELECT * FROM medical_records
WHERE visit_type = '住院'
AND item_name IN ({rule_names})
AND check_date >= SYSDATE - 30
        ''',
        'rule_oracle_code_inpatient': '''
SELECT * FROM medical_records
WHERE visit_type = '住院'
AND item_code IN ({rule_codes})
AND check_date >= SYSDATE - 30
        ''',
        'rule_pg_name_inpatient': '''
SELECT * FROM medical_records
WHERE visit_type = '住院'
AND item_name IN ({rule_names})
AND check_date >= CURRENT_DATE - INTERVAL '30 days'
        ''',
        'rule_pg_code_inpatient': '''
SELECT * FROM medical_records
WHERE visit_type = '住院'
AND item_code IN ({rule_codes})
AND check_date >= CURRENT_DATE - INTERVAL '30 days'
        '''
    }

    return templates.get(template_path, 'SELECT 1 FROM DUAL -- 未找到对应模板')

def generate_sql_from_template(template, rule_ids, visit_type):
    """根据模板生成SQL"""
    # 这里应该根据rule_ids查询实际的规则数据
    # 为了演示，使用模拟数据
    rule_names = [f"'规则{rule_id}'" for rule_id in rule_ids]
    rule_codes = [f"'CODE{rule_id}'" for rule_id in rule_ids]

    sql = template.replace('{rule_names}', ', '.join(rule_names))
    sql = sql.replace('{rule_codes}', ', '.join(rule_codes))

    # 根据就诊类型调整SQL
    if visit_type == '门诊':
        sql = sql.replace('住院', '门诊').replace('INPATIENT', 'OUTPATIENT')
    elif visit_type == '住院':
        sql = sql.replace('门诊', '住院').replace('OUTPATIENT', 'INPATIENT')

    return sql.strip()

# API路由
@bp.route('/api/rules', methods=['GET'])
@login_required
@permission_required('rules.knowledge_base')
@handle_db_error
def api_get_rules():
    """获取规则列表API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取过滤条件
        filters = {
            'behavior_type': request.args.get('behavior_type', ''),
            'city': request.args.get('city', ''),
            'rule_source': request.args.get('rule_source', ''),
            'rule_name': request.args.get('rule_name', ''),
            'rule_content': request.args.get('rule_content', ''),
            'type': request.args.get('type', ''),
            'rule_type': request.args.get('rule_type', '')
        }

        # 过滤空值
        filters = {k: v for k, v in filters.items() if v}

        # 使用统一的get_all_rules方法，支持过滤和分页
        result = RuleService.get_all_rules(page=page, per_page=per_page, **filters)
        result['success'] = True

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='query',
            resource='rules',
            details=f'查询规则列表，页码: {page}'
        )

        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules/<int:rule_id>', methods=['GET'])
@login_required
@permission_required('rules.knowledge_base')
@handle_db_error
def api_get_rule(rule_id):
    """获取规则详情API"""
    try:
        rule = RuleService.get_rule_by_id(rule_id)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='view',
            resource='rule',
            resource_id=str(rule_id),
            details=f'查看规则详情: {rule_id}'
        )

        return jsonify({
            'success': True,
            'data': rule
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules', methods=['POST'])
@login_required
@permission_required('rules.knowledge_base.create')
@handle_db_error
def api_create_rule():
    """新增规则API"""
    try:
        data = request.get_json()

        # 验证必要字段
        required_fields = ['规则名称', '行为认定']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'缺少必要字段: {field}'
                }), 400

        # 创建规则
        result = RuleService.create_rule(data)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='create',
            resource='rule',
            resource_id=str(result.get('id', '')),
            details=f'新增规则: {data.get("规则名称", "")}'
        )

        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules/<int:rule_id>', methods=['PUT'])
@login_required
@permission_required('rules.knowledge_base.edit')
@handle_db_error
def api_update_rule(rule_id):
    """更新规则API"""
    try:
        data = request.get_json()

        # 更新规则
        result = RuleService.update_rule(rule_id, data)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='update',
            resource='rule',
            resource_id=str(rule_id),
            details=f'更新规则: {data.get("规则名称", "")}'
        )

        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules/<int:rule_id>', methods=['DELETE'])
@login_required
@permission_required('rules.knowledge_base.delete')
@handle_db_error
def api_delete_rule(rule_id):
    """删除规则API"""
    try:
        # 获取规则信息用于日志
        rule = RuleService.get_rule_by_id(rule_id)
        rule_name = rule[0].get('规则名称', '') if rule else ''

        # 删除规则
        result = RuleService.delete_rule(rule_id)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='delete',
            resource='rule',
            resource_id=str(rule_id),
            details=f'删除规则: {rule_name}'
        )

        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules/next-id', methods=['GET'])
@login_required
@permission_required('rules.knowledge_base')
@handle_db_error
def api_get_next_rule_id():
    """获取下一个规则ID"""
    try:
        next_id = RuleService.get_next_rule_id()
        return jsonify({
            'success': True,
            'next_id': next_id
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules/import', methods=['POST'])
@login_required
@permission_required('rules.knowledge_base.import')
@handle_db_error
def api_import_rules():
    """导入规则API"""
    try:
        data = request.get_json()
        rules = data.get('rules', [])

        if not rules:
            return jsonify({
                'success': False,
                'error': '没有要导入的规则数据'
            }), 400

        # 导入规则
        result = RuleService.import_rules(rules)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='import',
            resource='rules',
            details=f'导入规则，成功: {result["success_count"]}, 失败: {result["error_count"]}'
        )

        return jsonify({
            'success': True,
            'message': f'导入完成，成功: {result["success_count"]}, 失败: {result["error_count"]}',
            'details': result
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules/preview', methods=['POST'])
@login_required
@permission_required('rules.knowledge_base.import')
@handle_db_error
def api_preview_rules():
    """预览导入规则API"""
    try:
        data = request.get_json()
        rules = data.get('rules', [])

        if not rules:
            return jsonify({
                'success': False,
                'error': '没有要预览的规则数据'
            }), 400

        # 预览规则
        preview_result = RuleService.preview_import_rules(rules)

        return jsonify({
            'success': True,
            'preview': preview_result
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules/merge', methods=['POST'])
@login_required
@permission_required('rules.knowledge_base.merge')
@handle_db_error
def api_merge_rules():
    """合并规则API"""
    try:
        data = request.get_json()
        source_rule_id = data.get('source_rule_id')
        target_rule_id = data.get('target_rule_id')

        if not source_rule_id or not target_rule_id:
            return jsonify({
                'success': False,
                'error': '缺少源规则ID或目标规则ID'
            }), 400

        # 合并规则
        result = RuleService.merge_rules(source_rule_id, target_rule_id)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='merge',
            resource='rules',
            details=f'合并规则: {source_rule_id} -> {target_rule_id}'
        )

        return jsonify({
            'success': True,
            'message': '规则合并成功',
            'details': result
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules/search-import', methods=['POST'])
@login_required
@permission_required('rules.knowledge_base')
@handle_db_error
def api_search_import_rules():
    """搜索可导入的规则API"""
    try:
        data = request.get_json()
        keyword = data.get('keyword', '')

        # 搜索规则
        rules = RuleService.search_import_rules(keyword)

        return jsonify({
            'success': True,
            'rules': rules
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/rules/test-sql', methods=['POST'])
@login_required
@permission_required('rules.knowledge_base.test_sql')
@handle_db_error
def api_test_sql():
    """测试SQL功能API"""
    try:
        data = request.get_json()
        sql = data.get('sql', '')
        database_type = data.get('database_type', 'oracle')
        host_ip = data.get('host_ip', '')
        schema = data.get('schema', '')

        if not sql.strip():
            return jsonify({
                'success': False,
                'error': 'SQL语句不能为空'
            }), 400

        # 执行SQL测试
        result = RuleService.test_sql(sql, database_type, host_ip, schema)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='test_sql',
            resource='rules',
            details=f'测试SQL，数据库: {database_type}, 主机: {host_ip}'
        )

        return jsonify({
            'success': True,
            'result': result
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/database/schemas', methods=['POST'])
@login_required
@permission_required('rules.knowledge_base')
@handle_db_error
def api_get_database_schemas():
    """获取数据库Schema列表API"""
    try:
        data = request.get_json()
        database_type = data.get('database_type', 'oracle')
        host_ip = data.get('host_ip', '')

        # 获取Schema列表
        schemas = RuleService.get_database_schemas(database_type, host_ip)

        return jsonify({
            'success': True,
            'schemas': schemas
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500





@bp.route('/api/sql_templates', methods=['GET'])
@login_required
@permission_required('rules.sql_generator')
@handle_db_error
def api_get_sql_templates():
    """获取SQL模板列表API"""
    try:
        template_type = request.args.get('type', 'rule')
        templates = SQLTemplateService.list_sql_templates(template_type)
        return jsonify({'success': True, 'templates': templates})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/sql_template/<template_name>', methods=['GET'])
@login_required
@permission_required('rules.sql_generator')
@handle_db_error
def api_get_sql_template(template_name):
    """获取SQL模板内容API"""
    try:
        template_type = request.args.get('type', 'rule')
        template_path = f'templates/{template_type}/{template_name}'

        if not os.path.exists(template_path):
            return jsonify({'success': False, 'error': '模板文件不存在'}), 404

        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()

        variables = SQLTemplateService.get_template_variables(template_path, template_type)

        return jsonify({
            'success': True,
            'content': content,
            'variables': variables
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/render_sql_template', methods=['POST'])
@login_required
@permission_required('rules.sql_generator')
@handle_db_error
def api_render_sql_template():
    """渲染SQL模板API"""
    try:
        data = request.get_json()
        template_content = data.get('template', '')
        rule_data = data.get('rule_data', {})

        rendered_sql = SQLTemplateService.render_sql_template(template_content, rule_data)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='render_template',
            resource='sql_template',
            details=f'渲染SQL模板，规则ID: {rule_data.get("ID", "")}'
        )

        return jsonify({
            'success': True,
            'sql': rendered_sql
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/generate_rule_sql/<int:rule_id>', methods=['POST'])
@login_required
@permission_required('rules.system_rules')
@handle_db_error
def api_generate_rule_sql(rule_id):
    """生成规则SQL API"""
    try:
        # 获取规则详情
        rule = RuleService.get_rule_by_id(rule_id)
        if not rule:
            return jsonify({'success': False, 'error': '规则不存在'}), 404

        rule_data = rule[0] if rule else {}

        # 生成SQL（这里使用简化的模板）
        sql = generate_rule_sql_from_data(rule_data)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='generate_sql',
            resource='rule',
            resource_id=str(rule_id),
            details=f'生成规则SQL: {rule_data.get("规则名称", "")}'
        )

        return jsonify({
            'success': True,
            'sql': sql,
            'rule_name': rule_data.get('规则名称', ''),
            'rule_type': rule_data.get('类型', '') or rule_data.get('规则类型', '')
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

def generate_rule_sql_from_data(rule_data):
    """根据规则数据生成SQL"""
    rule_type = rule_data.get('类型', '') or rule_data.get('规则类型', '')
    rule_name = rule_data.get('规则名称', '')
    behavior = rule_data.get('行为认定', '')

    # 基础SQL模板
    if '药品' in rule_type:
        sql = f"""-- 药品规则SQL: {rule_name}
-- 行为认定: {behavior}
-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

SELECT
    就诊流水号,
    患者姓名,
    科室名称,
    医生姓名,
    药品名称,
    规格,
    数量,
    单价,
    金额,
    开药日期,
    '{rule_name}' as 违规规则
FROM 药品使用明细
WHERE 药品编码 = '{rule_data.get("医保编码1", "")}'"""
    elif '诊疗' in rule_type:
        sql = f"""-- 诊疗规则SQL: {rule_name}
-- 行为认定: {behavior}
-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

SELECT
    就诊流水号,
    患者姓名,
    科室名称,
    医生姓名,
    诊疗项目名称,
    项目编码,
    数量,
    单价,
    金额,
    执行日期,
    '{rule_name}' as 违规规则
FROM 诊疗项目明细
WHERE 项目编码 = '{rule_data.get("医保编码1", "")}'"""
    else:
        sql = f"""-- 通用规则SQL: {rule_name}
-- 行为认定: {behavior}
-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

SELECT
    就诊流水号,
    患者姓名,
    科室名称,
    医生姓名,
    费用项目名称,
    医保编码,
    费用金额,
    就诊日期,
    '{rule_name}' as 违规规则
FROM 费用明细表
WHERE 医保编码 = '{rule_data.get("医保编码1", "")}'"""

    # 添加条件
    conditions = []

    if rule_data.get('违规数量'):
        conditions.append(f"  AND 数量 > {rule_data['违规数量']}")

    if rule_data.get('违规金额'):
        conditions.append(f"  AND 金额 > {rule_data['违规金额']}")

    if rule_data.get('包含科室'):
        departments = [f"'{dept.strip()}'" for dept in rule_data['包含科室'].split(',')]
        conditions.append(f"  AND 科室名称 IN ({','.join(departments)})")

    if rule_data.get('排除科室'):
        departments = [f"'{dept.strip()}'" for dept in rule_data['排除科室'].split(',')]
        conditions.append(f"  AND 科室名称 NOT IN ({','.join(departments)})")

    # 添加时间条件
    conditions.append("  AND 就诊日期 BETWEEN '2024-01-01' AND '2024-12-31'")

    # 组合SQL
    if conditions:
        sql += '\n' + '\n'.join(conditions)

    sql += '\nORDER BY 金额 DESC'

    return sql


