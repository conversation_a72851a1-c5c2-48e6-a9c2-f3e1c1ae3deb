#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速查看今天的日志
"""

import os
import sys
from datetime import datetime


def view_today_logs(lines=100, level=None):
    """查看今天的日志"""
    logs_dir = 'logs'
    today = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(logs_dir, f'micra_{today}.log')
    
    if not os.path.exists(log_file):
        print(f"今天的日志文件不存在: {log_file}")
        
        # 查找最新的日志文件
        if os.path.exists(logs_dir):
            import glob
            log_files = glob.glob(os.path.join(logs_dir, 'micra_*.log'))
            if log_files:
                latest_log = max(log_files, key=os.path.getmtime)
                print(f"使用最新的日志文件: {latest_log}")
                log_file = latest_log
            else:
                print("没有找到任何日志文件")
                return
        else:
            print(f"日志目录不存在: {logs_dir}")
            return
    
    print(f"查看日志文件: {log_file}")
    if level:
        print(f"过滤级别: {level.upper()}")
    print(f"显示最后 {lines} 行")
    print("=" * 80)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            log_lines = f.readlines()
        
        # 过滤日志级别
        if level:
            log_lines = [line for line in log_lines if f' {level.upper()} ' in line]
        
        # 显示最后N行
        if lines > 0:
            log_lines = log_lines[-lines:]
        
        for line in log_lines:
            print(line.rstrip())
            
    except Exception as e:
        print(f"读取日志文件失败: {e}")


def follow_logs(level=None):
    """实时跟踪日志"""
    logs_dir = 'logs'
    today = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(logs_dir, f'micra_{today}.log')
    
    if not os.path.exists(log_file):
        print(f"日志文件不存在: {log_file}")
        return
    
    print(f"实时跟踪日志: {log_file}")
    if level:
        print(f"过滤级别: {level.upper()}")
    print("按 Ctrl+C 退出")
    print("=" * 80)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            # 移动到文件末尾
            f.seek(0, 2)
            
            while True:
                line = f.readline()
                if line:
                    # 过滤条件
                    if level and f' {level.upper()} ' not in line:
                        continue
                    
                    print(line.rstrip())
                else:
                    import time
                    time.sleep(0.1)
                    
    except KeyboardInterrupt:
        print("\n停止跟踪日志")
    except Exception as e:
        print(f"跟踪日志失败: {e}")


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='快速查看MICRA日志')
    parser.add_argument('--lines', '-n', type=int, default=100, help='显示行数 (默认100)')
    parser.add_argument('--level', '-l', choices=['debug', 'info', 'warning', 'error', 'critical'], help='过滤日志级别')
    parser.add_argument('--follow', '-f', action='store_true', help='实时跟踪日志')
    
    args = parser.parse_args()
    
    if args.follow:
        follow_logs(args.level)
    else:
        view_today_logs(args.lines, args.level)
