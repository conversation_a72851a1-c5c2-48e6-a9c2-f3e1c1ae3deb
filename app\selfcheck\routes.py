"""
自查自纠模块路由
"""
from flask import render_template, request, jsonify, flash, redirect, url_for, send_file
from flask_login import login_required, current_user
# from flask_wtf import csrf
from app.selfcheck import bp
from app.selfcheck.services import RuleService, UploadService, TaskService, SchemeService
from app.selfcheck.data_import_service import DataImportService
from app.selfcheck.validation_service import ValidationService
from app.auth.decorators import permission_required
from app.utils.breadcrumb import generate_breadcrumb
from app.utils.audit_decorator import audit_rule_operation, audit_access
from app.models.audit import AuditLog
import logging

logger = logging.getLogger(__name__)


@bp.route('/')
@login_required
def index():
    """自查自纠首页"""
    breadcrumb_items = generate_breadcrumb('selfcheck')
    return render_template('selfcheck/index.html', breadcrumb_items=breadcrumb_items)


# ==================== 规则管理 ====================

@bp.route('/rules')
@login_required
@permission_required('selfcheck.rules.view')
def rules():
    """规则管理页面"""
    breadcrumb_items = generate_breadcrumb('selfcheck', 'rules')
    return render_template('selfcheck/rules.html', breadcrumb_items=breadcrumb_items)


@bp.route('/api/rules')
@login_required
@permission_required('selfcheck.rules.view')
def api_rules():
    """获取规则列表API"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取排序参数
        sort_by = request.args.get('sort_by', 'created_at')  # 默认按创建时间排序
        sort_order = request.args.get('sort_order', 'desc')  # 默认降序

        # 获取过滤条件
        filters = {
            'rule_name': request.args.get('rule_name', '').strip(),
            'rule_type': request.args.get('rule_type', '').strip(),
            'status': request.args.get('status', '').strip(),
            'city': request.args.get('city', '').strip(),
            'visit_type': request.args.get('visit_type', '').strip(),
            'rule_source': request.args.get('rule_source', '').strip(),
            'types': request.args.get('types', '').strip()
        }

        # 移除空值
        filters = {k: v for k, v in filters.items() if v}

        # 如果搜索状态是 'deleted'，需要包含已删除的记录
        if filters.get('status') == 'deleted':
            filters['include_deleted'] = 'true'

        # 获取分页数据
        result = RuleService.get_all_rules(
            page=page,
            per_page=per_page,
            sort_by=sort_by,
            sort_order=sort_order,
            **filters
        )
        result['success'] = True

        return jsonify(result)
    except Exception as e:
        logger.error(f"获取规则列表失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取规则列表失败'})


@bp.route('/api/rules/<int:rule_id>')
@login_required
@permission_required('selfcheck.rules.view')
def api_rule_detail(rule_id):
    """获取规则详情API"""
    try:
        rule = RuleService.get_rule_by_id(rule_id)
        if not rule:
            return jsonify({'success': False, 'message': '规则不存在'})
        
        return jsonify({'success': True, 'rule': rule})
    except Exception as e:
        logger.error(f"获取规则详情失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取规则详情失败'})


@bp.route('/api/rules', methods=['POST'])
@login_required
@permission_required('selfcheck.rules.create')
def api_create_rule():
    """创建规则API"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['rule_name', 'rule_code', 'rule_type']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'{field} 不能为空'})

        rule = RuleService.create_rule(data, current_user.id)

        # 记录审计日志
        AuditLog.log_action(
            action='create',
            resource='selfcheck_rule',
            resource_id=str(rule.get('id', '')),
            details=f"创建自查规则: {data.get('rule_name', '')}"
        )

        return jsonify({'success': True, 'rule': rule, 'message': '规则创建成功'})
    except Exception as e:
        logger.error(f"创建规则失败: {str(e)}")
        return jsonify({'success': False, 'message': f'创建规则失败: {str(e)}'})


@bp.route('/api/rules/<int:rule_id>', methods=['PUT'])
@login_required
@permission_required('selfcheck.rules.edit')
def api_update_rule(rule_id):
    """更新规则API"""
    try:
        data = request.get_json()

        rule = RuleService.update_rule(rule_id, data, current_user.id)
        if not rule:
            return jsonify({'success': False, 'message': '规则不存在'})

        # 记录审计日志
        AuditLog.log_action(
            action='update',
            resource='selfcheck_rule',
            resource_id=str(rule_id),
            details=f"更新自查规则: {rule.get('rule_name', '')}"
        )

        return jsonify({'success': True, 'rule': rule, 'message': '规则更新成功'})
    except Exception as e:
        logger.error(f"更新规则失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新规则失败: {str(e)}'})


@bp.route('/api/rules/<int:rule_id>', methods=['DELETE'])
@login_required
@permission_required('selfcheck.rules.delete')
def api_delete_rule(rule_id):
    """删除规则API（软删除）"""
    try:
        # 先获取规则信息用于审计日志
        rule = RuleService.get_rule_by_id(rule_id)
        rule_name = rule.get('rule_name', '') if rule else f'ID:{rule_id}'

        success = RuleService.delete_rule(rule_id)
        if not success:
            return jsonify({'success': False, 'message': '规则不存在'})

        # 记录审计日志
        AuditLog.log_action(
            action='delete',
            resource='selfcheck_rule',
            resource_id=str(rule_id),
            details=f"删除自查规则: {rule_name}"
        )

        return jsonify({'success': True, 'message': '规则已删除（可通过恢复功能找回）'})
    except Exception as e:
        logger.error(f"删除规则失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除规则失败: {str(e)}'})


@bp.route('/api/rules/<int:rule_id>/restore', methods=['PUT'])
@login_required
@permission_required('selfcheck.rules.edit')
def api_restore_rule(rule_id):
    """恢复规则API"""
    try:
        success = RuleService.restore_rule(rule_id)
        if not success:
            return jsonify({'success': False, 'message': '规则不存在'})

        return jsonify({'success': True, 'message': '规则恢复成功'})
    except Exception as e:
        logger.error(f"恢复规则失败: {str(e)}")
        return jsonify({'success': False, 'message': f'恢复规则失败: {str(e)}'})


# ==================== 规则导入 ====================

@bp.route('/rules/import')
@login_required
@permission_required('selfcheck.rules.import')
def rules_import():
    """规则导入页面"""
    breadcrumb_items = generate_breadcrumb('selfcheck', 'rules_import')
    return render_template('selfcheck/rules_import.html', breadcrumb_items=breadcrumb_items)


@bp.route('/api/rules/importable')
@login_required
@permission_required('selfcheck.rules.import')
def api_importable_rules():
    """获取可导入的历史规则API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取排序参数
        sort_by = request.args.get('sort_by', 'create_time')  # 默认按创建时间排序
        sort_order = request.args.get('sort_order', 'desc')  # 默认降序

        # 获取过滤条件
        filters = {
            'rule_name': request.args.get('rule_name', '').strip(),
            'city': request.args.get('city', '').strip(),
            'rule_type': request.args.get('rule_type', '').strip(),
            'rule_source': request.args.get('rule_source', '').strip(),
            'visit_type': request.args.get('visit_type', '').strip(),
            'is_imported': request.args.get('is_imported', '').strip()
        }

        # 移除空值
        filters = {k: v for k, v in filters.items() if v}

        result = RuleService.get_importable_rules(
            page=page,
            per_page=per_page,
            sort_by=sort_by,
            sort_order=sort_order,
            **filters
        )
        result['success'] = True

        return jsonify(result)
    except Exception as e:
        logger.error(f"获取可导入规则失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取可导入规则失败'})


@bp.route('/api/rules/test', methods=['GET'])
def test_route():
    """测试路由"""
    print("=== 测试路由被调用 ===", flush=True)
    return jsonify({'success': True, 'message': '测试成功'})

@bp.route('/api/rules/import-from-history', methods=['POST'])
@login_required
@permission_required('selfcheck.rules.import')
def api_import_rules():
    """导入规则API"""
    print("=== 导入规则API被调用 ===", flush=True)
    try:
        print(f"当前用户: {current_user.username}", flush=True)
        logger.info(f"导入规则API被调用，用户: {current_user.username}")

        data = request.get_json()
        logger.info(f"接收到的数据: {data}")

        if not data:
            logger.warning("没有接收到JSON数据")
            return jsonify({'success': False, 'message': '请求数据格式错误'}), 400

        # 获取compare_ids和visit_types
        compare_ids = data.get('compare_ids', [])
        visit_types = data.get('visit_types', [])

        logger.info(f"接收到的compare_ids: {compare_ids}")
        logger.info(f"接收到的visit_types: {visit_types}")

        if not compare_ids or not visit_types:
            logger.warning("没有接收到compare_ids或visit_types")
            return jsonify({'success': False, 'message': '请选择要导入的规则'}), 400

        if len(compare_ids) != len(visit_types):
            logger.warning(f"compare_ids和visit_types数量不匹配: {len(compare_ids)} vs {len(visit_types)}")
            return jsonify({'success': False, 'message': '规则数据不匹配'}), 400

        logger.info(f"提取的compare_ids: {compare_ids}")
        logger.info(f"提取的visit_types: {visit_types}")

        logger.info("开始调用RuleService.import_rules_from_history")
        result = RuleService.import_rules_from_history(compare_ids, visit_types, current_user.id)
        logger.info(f"导入结果: {result}")

        # 记录审计日志
        AuditLog.log_action(
            action='import',
            resource='selfcheck_rule',
            details=f"从历史规则导入 {result['imported_count']} 个规则"
        )

        message = f"成功导入 {result['imported_count']} 个规则"
        if result['errors']:
            message += f"，{len(result['errors'])} 个失败"

        return jsonify({
            'success': True,
            'message': message,
            'imported_count': result['imported_count'],
            'errors': result['errors']
        })
    except Exception as e:
        logger.error(f"导入规则失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'导入规则失败: {str(e)}'}), 500


@bp.route('/api/rules/batch-delete', methods=['POST'])
@login_required
@permission_required('selfcheck.rules.delete')
def api_batch_delete_rules():
    """批量删除规则API"""
    try:
        data = request.get_json()
        rule_ids = data.get('rule_ids', [])

        if not rule_ids:
            return jsonify({'success': False, 'message': '请选择要删除的规则'}), 400

        # 验证rule_ids都是整数
        try:
            rule_ids = [int(rule_id) for rule_id in rule_ids]
        except (ValueError, TypeError):
            return jsonify({'success': False, 'message': '无效的规则ID'}), 400

        deleted_count = 0
        errors = []

        for rule_id in rule_ids:
            try:
                result = RuleService.delete_rule(rule_id)
                if result:
                    deleted_count += 1
                else:
                    errors.append(f"规则ID {rule_id} 删除失败")
            except Exception as e:
                errors.append(f"规则ID {rule_id} 删除失败: {str(e)}")

        message = f"成功删除 {deleted_count} 个规则"
        if errors:
            message += f"，{len(errors)} 个失败"

        return jsonify({
            'success': True,
            'message': message,
            'deleted_count': deleted_count,
            'errors': errors
        })
    except Exception as e:
        logger.error(f"批量删除规则失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'批量删除规则失败: {str(e)}'}), 500


@bp.route('/api/rules/<int:rule_id>/sql', methods=['PUT'])
@login_required
@permission_required('selfcheck.rules.edit')
def api_update_rule_sql(rule_id):
    """更新规则SQL语句API"""
    try:
        data = request.get_json()
        sql_content = data.get('sql_content', '')
        template_name = data.get('template_name', '')
        visit_type = data.get('visit_type', '')

        # 获取规则
        rule = RuleService.get_rule_by_id(rule_id)
        if not rule:
            return jsonify({'success': False, 'message': '规则不存在'}), 404

        # 更新SQL相关字段
        update_data = {
            'sql_content': sql_content,
            'template_name': template_name,
            'visit_type': visit_type,
            'updated_by': current_user.id
        }

        result = RuleService.update_rule(rule_id, update_data, current_user.id)
        if result:
            return jsonify({'success': True, 'message': 'SQL语句更新成功'})
        else:
            return jsonify({'success': False, 'message': 'SQL语句更新失败'}), 500
    except Exception as e:
        logger.error(f"更新规则SQL失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'更新规则SQL失败: {str(e)}'}), 500


@bp.route('/api/rules/test-sql', methods=['POST'])
@login_required
@permission_required('selfcheck.rules.edit')
def api_test_sql():
    """测试SQL语句API"""
    try:
        data = request.get_json()
        sql_content = data.get('sql_content', '')

        if not sql_content.strip():
            return jsonify({'success': False, 'message': 'SQL语句不能为空'}), 400

        # 简单的SQL语法检查
        sql_lower = sql_content.lower().strip()

        # 检查是否包含危险操作（使用单词边界匹配，避免误判）
        import re
        dangerous_keywords = ['drop', 'delete', 'truncate', 'alter', 'create', 'insert', 'update']
        for keyword in dangerous_keywords:
            # 使用正则表达式检查单词边界，避免误判（如 "selected" 包含 "select"）
            pattern = r'\b' + keyword + r'\b'
            if re.search(pattern, sql_lower):
                return jsonify({'success': False, 'message': f'SQL语句不能包含 {keyword.upper()} 操作'}), 400

        # 移除严格的SELECT开头检查，允许WITH、EXPLAIN等语句
        # 只要不包含危险操作即可执行

        # 这里可以添加更复杂的SQL语法检查
        # 比如使用sqlparse库进行语法分析

        return jsonify({'success': True, 'message': 'SQL语法检查通过'})
    except Exception as e:
        logger.error(f"测试SQL失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'SQL测试失败: {str(e)}'}), 500


@bp.route('/api/cities')
@login_required
@permission_required('selfcheck.rules.view')
def api_get_cities():
    """获取城市列表API"""
    try:
        cities = RuleService.get_cities()
        return jsonify({
            'success': True,
            'cities': cities
        })
    except Exception as e:
        logger.error(f"获取城市列表失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取城市列表失败: {str(e)}'}), 500


@bp.route('/api/visit-types')
@login_required
@permission_required('selfcheck.rules.view')
def api_get_visit_types():
    """获取用途列表API"""
    try:
        visit_types = RuleService.get_visit_types()
        return jsonify({
            'success': True,
            'visit_types': visit_types
        })
    except Exception as e:
        logger.error(f"获取用途列表失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取用途列表失败: {str(e)}'}), 500


@bp.route('/api/rule-sources')
@login_required
@permission_required('selfcheck.rules.view')
def api_get_rule_sources():
    """获取规则来源列表API"""
    try:
        rule_sources = RuleService.get_rule_sources()
        return jsonify({
            'success': True,
            'rule_sources': rule_sources
        })
    except Exception as e:
        logger.error(f"获取规则来源列表失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取规则来源列表失败: {str(e)}'}), 500


@bp.route('/api/rules/rule-sources')
@login_required
@permission_required('selfcheck.rules.view')
def api_get_rule_sources_alt():
    """获取规则来源列表API（备用路径）"""
    try:
        rule_sources = RuleService.get_rule_sources_from_history()
        return jsonify({
            'success': True,
            'rule_sources': rule_sources
        })
    except Exception as e:
        logger.error(f"获取规则来源列表失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取规则来源列表失败: {str(e)}'}), 500


@bp.route('/api/rules/cities')
@login_required
@permission_required('selfcheck.rules.view')
def api_get_cities_from_history():
    """获取城市列表API（从历史表）"""
    try:
        cities = RuleService.get_cities_from_history()
        return jsonify({
            'success': True,
            'cities': cities
        })
    except Exception as e:
        logger.error(f"获取城市列表失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取城市列表失败: {str(e)}'}), 500


@bp.route('/api/rules/rule-types')
@login_required
@permission_required('selfcheck.rules.view')
def api_get_rule_types_from_history():
    """获取规则类型列表API（从历史表）"""
    try:
        rule_types = RuleService.get_rule_types_from_history()
        return jsonify({
            'success': True,
            'rule_types': rule_types
        })
    except Exception as e:
        logger.error(f"获取规则类型列表失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取规则类型列表失败: {str(e)}'}), 500


@bp.route('/api/rules/visit-types')
@login_required
@permission_required('selfcheck.rules.view')
def api_get_visit_types_from_history():
    """获取用途（就诊类型）列表API（从历史表）"""
    try:
        visit_types = RuleService.get_visit_types_from_history()
        return jsonify({
            'success': True,
            'visit_types': visit_types
        })
    except Exception as e:
        logger.error(f"获取用途列表失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取用途列表失败: {str(e)}'}), 500


@bp.route('/api/rule-types')
@login_required
@permission_required('selfcheck.rules.view')
def api_get_rule_types():
    """获取规则类型列表API"""
    try:
        rule_types = RuleService.get_rule_types()
        return jsonify({
            'success': True,
            'rule_types': rule_types
        })
    except Exception as e:
        logger.error(f"获取规则类型列表失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取规则类型列表失败: {str(e)}'}), 500


@bp.route('/api/database/pool-status')
@login_required
@permission_required('selfcheck.rules.view')
def api_get_pool_status():
    """获取数据库连接池状态API"""
    try:
        from app.selfcheck.database import get_db_manager, get_batch_import_db_manager

        # 获取普通连接池状态（现在没有连接池，返回None）
        db_manager = get_db_manager()
        normal_pool_status = None  # 普通连接不使用连接池

        # 获取批量导入连接池状态
        batch_db_manager = get_batch_import_db_manager()
        batch_pool_status = batch_db_manager.get_pool_status()

        return jsonify({
            'success': True,
            'normal_pool_status': normal_pool_status,
            'batch_import_pool_status': batch_pool_status,
            'message': '连接池状态获取成功'
        })
    except Exception as e:
        logger.error(f"获取连接池状态失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取连接池状态失败: {str(e)}'}), 500


# ==================== 数据上传 ====================

@bp.route('/uploads')
@login_required
@permission_required('selfcheck.uploads.view')
def uploads():
    """数据上传页面"""
    breadcrumb_items = generate_breadcrumb('selfcheck', 'uploads')
    return render_template('selfcheck/uploads.html', breadcrumb_items=breadcrumb_items)


@bp.route('/api/uploads')
@login_required
@permission_required('selfcheck.uploads.view')
def api_uploads():
    """获取上传记录列表API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        selected_user_id = request.args.get('selected_user_id', type=int)

        # 获取过滤条件
        filters = {
            'file_name': request.args.get('file_name', '').strip(),
            'file_type': request.args.get('file_type', '').strip(),
            'status': request.args.get('status', '').strip()
        }

        # 移除空值
        filters = {k: v for k, v in filters.items() if v}

        # 权限控制：普通用户只能查看自己的文件，管理员可以查看所有用户的文件
        if current_user.is_admin and selected_user_id:
            # 管理员查看指定用户的文件
            target_user_id = selected_user_id
        elif current_user.is_admin and not selected_user_id:
            # 管理员查看所有文件
            target_user_id = None
        else:
            # 普通用户只能查看自己的文件
            target_user_id = current_user.id

        result = UploadService.get_all_uploads(
            page=page,
            per_page=per_page,
            user_id=current_user.id,  # 当前用户ID，用于权限控制
            selected_user_id=target_user_id,  # 要查看的用户ID
            **filters
        )
        result['success'] = True

        return jsonify(result)
    except Exception as e:
        logger.error(f"获取上传记录列表失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取上传记录列表失败'})


@bp.route('/api/uploads/users')
@login_required
@permission_required('selfcheck.uploads.view')
def api_upload_users():
    """获取有上传记录的用户列表API（仅管理员）"""
    try:
        if not current_user.is_admin:
            return jsonify({'success': False, 'message': '权限不足'}), 403

        users = UploadService.get_all_users_with_uploads()
        return jsonify({
            'success': True,
            'users': users
        })
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取用户列表失败'})


@bp.route('/api/uploads/limits')
@login_required
@permission_required('selfcheck.uploads.view')
def api_get_upload_limits():
    """获取当前用户的上传限制API"""
    try:
        from app.services.role_config_service import RoleConfigService

        # 获取用户上传限制
        limits = RoleConfigService.get_user_upload_limits(current_user)
        usage = RoleConfigService.get_user_current_usage(current_user)

        return jsonify({
            'success': True,
            'limits': {
                'max_size_mb': limits.get('max_size_mb', 350),
                'max_file_count': limits.get('max_file_count', 100),
                'single_file_max_mb': limits.get('upload_single_file_max_mb', 10),
                'allowed_types': limits.get('allowed_types', ['csv', 'dmp', 'dp', 'bak']),
                'current_size_mb': usage.get('current_size_mb', 0),
                'current_file_count': usage.get('current_file_count', 0),
                'usage_percentage': usage.get('usage_percentage', 0)
            }
        })
    except Exception as e:
        logger.error(f"获取上传限制失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/uploads', methods=['POST'])
@login_required
@permission_required('selfcheck.uploads.create')
def api_upload_file():
    """文件上传API"""
    try:
        logger.info(f"收到文件上传请求，用户: {current_user.username}")
        logger.info(f"请求文件: {list(request.files.keys())}")

        if 'file' not in request.files:
            logger.warning("请求中没有文件")
            return jsonify({'success': False, 'message': '没有选择文件'})

        file = request.files['file']
        logger.info(f"文件名: {file.filename}, 大小: {file.content_length}")

        # 验证文件
        is_valid, message = UploadService.validate_file(file, current_user.id)
        if not is_valid:
            logger.warning(f"文件验证失败: {message}")
            return jsonify({'success': False, 'message': message})

        # 保存文件
        upload_record = UploadService.save_upload(file, current_user.id)
        logger.info(f"文件上传成功，记录ID: {upload_record.get('id')}")

        # 记录审计日志
        AuditLog.log_action(
            action='upload',
            resource='data_file',
            resource_id=str(upload_record.get('id', '')),
            details=f"上传数据文件: {file.filename}"
        )

        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'upload': upload_record
        })
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'文件上传失败: {str(e)}'})


@bp.route('/api/uploads/folder', methods=['POST'])
@login_required
@permission_required('selfcheck.uploads.create')
def api_upload_folder():
    """文件夹上传API"""
    try:
        logger.info(f"收到文件夹上传请求，用户: {current_user.username}")
        logger.info(f"请求文件: {list(request.files.keys())}")

        if 'files' not in request.files:
            logger.warning("请求中没有文件")
            return jsonify({'success': False, 'message': '没有选择文件'})

        files = request.files.getlist('files')
        folder_name = request.form.get('folder_name', 'folder')
        is_multiple = request.form.get('is_multiple', 'false') == 'true'

        logger.info(f"文件夹名: {folder_name}, 文件数量: {len(files)}, 多文件模式: {is_multiple}")

        if not files:
            return jsonify({'success': False, 'message': '没有选择文件'})

        # 保存文件夹上传或多文件上传
        if is_multiple:
            result = UploadService.save_multiple_files(files, current_user.id)
        else:
            result = UploadService.save_folder_upload(files, folder_name, current_user.id)
        logger.info(f"文件夹上传成功，上传文件数: {result.get('uploaded_count', 0)}")

        # 记录审计日志
        AuditLog.log_action(
            action='upload_folder',
            resource='data_folder',
            resource_id=folder_name,
            details=f"上传文件夹: {folder_name}，包含 {result.get('uploaded_count', 0)} 个文件"
        )

        return jsonify({
            'success': True,
            'message': f'文件夹上传成功',
            'uploaded_count': result.get('uploaded_count', 0),
            'uploads': result.get('files', [])
        })
    except Exception as e:
        logger.error(f"文件夹上传失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'文件夹上传失败: {str(e)}'})


@bp.route('/api/uploads/<int:upload_id>')
@login_required
@permission_required('selfcheck.uploads.view')
def api_upload_detail(upload_id):
    """获取上传记录详情API"""
    try:
        upload = UploadService.get_upload_by_id(upload_id)
        if not upload:
            return jsonify({'success': False, 'message': '上传记录不存在'})
        
        # 检查权限：只能查看自己的上传记录
        if upload['user_id'] != current_user.id and not current_user.is_admin:
            return jsonify({'success': False, 'message': '权限不足'})
        
        return jsonify({'success': True, 'upload': upload})
    except Exception as e:
        logger.error(f"获取上传记录详情失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取上传记录详情失败'})


@bp.route('/api/uploads/<int:upload_id>', methods=['DELETE'])
@login_required
@permission_required('selfcheck.uploads.delete')
def api_delete_upload(upload_id):
    """删除上传记录API"""
    try:
        logger.info(f"收到删除上传记录请求: {upload_id}, 用户: {current_user.username}")

        upload = UploadService.get_upload_by_id(upload_id)
        if not upload:
            logger.warning(f"上传记录不存在: {upload_id}")
            return jsonify({'success': False, 'message': '上传记录不存在'})

        # 检查权限：只能删除自己的上传记录
        if upload['user_id'] != current_user.id and not current_user.is_admin:
            logger.warning(f"用户 {current_user.username} 尝试删除其他用户的上传记录: {upload_id}")
            return jsonify({'success': False, 'message': '权限不足'})

        # 记录审计日志
        AuditLog.log_action(
            action='delete',
            resource='upload_file',
            resource_id=str(upload_id),
            details=f"删除上传文件: {upload.get('file_name', '')}"
        )

        success = UploadService.delete_upload(upload_id)
        if not success:
            logger.error(f"删除上传记录失败: {upload_id}")
            return jsonify({'success': False, 'message': '删除操作失败'})

        logger.info(f"上传记录删除成功: {upload_id}")
        return jsonify({'success': True, 'message': '上传记录删除成功'})
    except ValueError as e:
        # 业务逻辑错误（如文件被任务使用）
        logger.warning(f"删除上传记录业务错误: {upload_id}, 错误: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})
    except Exception as e:
        logger.error(f"删除上传记录系统错误: {upload_id}, 错误: {str(e)}")
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})


# ==================== 自查任务 ====================

@bp.route('/tasks')
@login_required
@permission_required('selfcheck.tasks.view')
def tasks():
    """自查任务页面"""
    breadcrumb_items = generate_breadcrumb('selfcheck', 'tasks')
    return render_template('selfcheck/tasks.html', breadcrumb_items=breadcrumb_items)


@bp.route('/api/tasks')
@login_required
@permission_required('selfcheck.tasks.view')
def api_tasks():
    """获取任务列表API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 获取过滤条件
        filters = {
            'task_name': request.args.get('task_name', '').strip(),
            'status': request.args.get('status', '').strip()
        }
        
        # 移除空值
        filters = {k: v for k, v in filters.items() if v}
        
        result = TaskService.get_all_tasks(
            page=page, 
            per_page=per_page, 
            user_id=current_user.id,  # 只显示当前用户的任务
            **filters
        )
        result['success'] = True
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取任务列表失败'})


@bp.route('/api/tasks', methods=['POST'])
@login_required
@permission_required('selfcheck.tasks.create')
def api_create_task():
    """创建任务API"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['task_name', 'data_source', 'scheme_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必填字段: {field}'})

        # 创建任务
        task = TaskService.create_task(
            task_name=data['task_name'],
            data_source=data['data_source'],
            scheme_id=data['scheme_id'],
            user_id=current_user.id
        )

        return jsonify({
            'success': True,
            'message': '任务创建成功',
            'task': task
        })
    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        return jsonify({'success': False, 'message': f'创建任务失败: {str(e)}'})


@bp.route('/api/data-sources', methods=['GET'])
@login_required
@permission_required('selfcheck.tasks.view')
def api_get_data_sources():
    """获取可用数据源API"""
    try:
        from app.selfcheck.services import DataSourceService

        data_sources = DataSourceService.get_available_data_sources(current_user.id)
        return jsonify({'success': True, 'data_sources': data_sources})

    except Exception as e:
        logger.error(f"获取数据源失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取数据源失败'}), 500


@bp.route('/api/authorized-schemes', methods=['GET'])
@login_required
@permission_required('selfcheck.tasks.view')
def api_get_authorized_schemes():
    """获取用户授权的方案API"""
    try:
        schemes = SchemeService.get_user_authorized_schemes(current_user.id)
        return jsonify({'success': True, 'schemes': schemes})

    except Exception as e:
        logger.error(f"获取授权方案失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取授权方案失败'}), 500


@bp.route('/api/tasks/<int:task_id>', methods=['DELETE'])
@login_required
@permission_required('selfcheck.tasks.delete')
def api_delete_task(task_id):
    """删除任务API"""
    try:
        # 检查任务是否存在
        task = TaskService.get_task_by_id(task_id)
        if not task:
            return jsonify({'success': False, 'message': '任务不存在'}), 404

        # 检查任务状态，运行中的任务不能删除
        if task.get('status') == 'running':
            return jsonify({'success': False, 'message': '运行中的任务不能删除'}), 400

        # 删除任务
        TaskService.delete_task(task_id, current_user.id)

        return jsonify({'success': True, 'message': '任务删除成功'})

    except ValueError as e:
        return jsonify({'success': False, 'message': str(e)}), 400
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        return jsonify({'success': False, 'message': '删除任务失败'}), 500


@bp.route('/api/tasks/<int:task_id>/detail', methods=['GET'])
@login_required
@permission_required('selfcheck.tasks.view')
def api_get_task_detail(task_id):
    """获取任务详情API"""
    try:
        # 获取任务基本信息
        task = TaskService.get_task_by_id(task_id)
        if not task:
            return jsonify({'success': False, 'message': '任务不存在'}), 404

        # 获取任务关联的规则执行详情
        rules = TaskService.get_task_rules_detail(task_id)

        # 获取数据源名称
        from app.selfcheck.services import DataSourceService
        data_sources = DataSourceService.get_available_data_sources(current_user.id)
        data_source_info = next((ds for ds in data_sources if ds['id'] == task.get('data_source')), None)
        if data_source_info:
            task['data_source_name'] = data_source_info['name']

        return jsonify({
            'success': True,
            'task': task,
            'rules': rules
        })

    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取任务详情失败'}), 500


@bp.route('/api/tasks/<int:task_id>/report/preview', methods=['GET'])
@login_required
@permission_required('selfcheck.tasks.result')
def api_preview_task_report(task_id):
    """预览任务报告"""
    try:
        # 检查任务是否存在且已完成
        task = TaskService.get_task_by_id(task_id)
        if not task:
            return "任务不存在", 404

        if task.get('status') != 'completed':
            return "任务尚未完成，无法生成报告", 400

        # 生成报告HTML
        report_html = TaskService.generate_task_report_html(task_id)

        return report_html

    except Exception as e:
        logger.error(f"预览任务报告失败: {str(e)}")
        return f"预览报告失败: {str(e)}", 500


@bp.route('/api/tasks/<int:task_id>/report/download', methods=['GET'])
@login_required
@permission_required('selfcheck.tasks.result')
def api_download_task_report(task_id):
    """下载任务报告"""
    try:
        # 检查任务是否存在且已完成
        task = TaskService.get_task_by_id(task_id)
        if not task:
            return jsonify({'success': False, 'message': '任务不存在'}), 404

        if task.get('status') != 'completed':
            return jsonify({'success': False, 'message': '任务尚未完成，无法下载报告'}), 400

        # 生成报告文件
        report_file = TaskService.generate_task_report_file(task_id)

        return send_file(
            report_file,
            as_attachment=True,
            download_name=f"自查报告_任务{task_id}_{task['task_name']}.pdf",
            mimetype='application/pdf'
        )

    except Exception as e:
        logger.error(f"下载任务报告失败: {str(e)}")
        return jsonify({'success': False, 'message': '下载报告失败'}), 500


# ==================== 方案管理 ====================

@bp.route('/schemes')
@login_required
# @permission_required('selfcheck.schemes.view')  # 临时注释权限检查
def schemes():
    """方案管理页面"""
    breadcrumb_items = generate_breadcrumb('selfcheck', 'schemes')
    return render_template('selfcheck/schemes.html', breadcrumb_items=breadcrumb_items)


@bp.route('/api/schemes')
@login_required
# @permission_required('selfcheck.schemes.view')  # 临时注释权限检查
def api_schemes():
    """获取方案列表API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取过滤条件
        filters = {
            'scheme_name': request.args.get('scheme_name', '').strip(),
            'status': request.args.get('status', '').strip(),
            'created_by': request.args.get('created_by', '').strip()
        }

        # 移除空值
        filters = {k: v for k, v in filters.items() if v}

        result = SchemeService.get_all_schemes(
            page=page,
            per_page=per_page,
            **filters
        )
        result['success'] = True

        return jsonify(result)
    except Exception as e:
        logger.error(f"获取方案列表失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取方案列表失败'})


@bp.route('/api/schemes', methods=['POST'])
@login_required
@permission_required('selfcheck.schemes.create')
def api_create_scheme():
    """创建方案API"""
    try:
        data = request.get_json()

        # 验证必填字段
        if not data.get('scheme_name'):
            return jsonify({'success': False, 'message': '方案名称不能为空'})

        # 创建方案
        scheme = SchemeService.create_scheme(
            scheme_name=data['scheme_name'],
            description=data.get('description', ''),
            created_by=current_user.id
        )

        return jsonify({
            'success': True,
            'message': '方案创建成功',
            'scheme': scheme
        })
    except ValueError as e:
        return jsonify({'success': False, 'message': str(e)})
    except Exception as e:
        logger.error(f"创建方案失败: {str(e)}")
        return jsonify({'success': False, 'message': f'创建方案失败: {str(e)}'})


@bp.route('/api/schemes/<int:scheme_id>')
@login_required
# @permission_required('selfcheck.schemes.view')  # 临时注释权限检查
def api_get_scheme(scheme_id):
    """获取方案详情API"""
    try:
        scheme = SchemeService.get_scheme_by_id(scheme_id)

        if not scheme:
            return jsonify({'success': False, 'message': '方案不存在'})

        return jsonify({
            'success': True,
            'scheme': scheme
        })
    except Exception as e:
        logger.error(f"获取方案详情失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取方案详情失败'})


@bp.route('/api/schemes/<int:scheme_id>', methods=['PUT'])
@login_required
# @permission_required('selfcheck.schemes.edit')  # 临时注释权限检查
def api_update_scheme(scheme_id):
    """更新方案API"""
    try:
        data = request.get_json()

        # 更新方案
        scheme = SchemeService.update_scheme(
            scheme_id=scheme_id,
            scheme_name=data.get('scheme_name'),
            description=data.get('description'),
            status=data.get('status')
        )

        return jsonify({
            'success': True,
            'message': '方案更新成功',
            'scheme': scheme
        })
    except ValueError as e:
        return jsonify({'success': False, 'message': str(e)})
    except Exception as e:
        logger.error(f"更新方案失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新方案失败: {str(e)}'})


@bp.route('/api/schemes/<int:scheme_id>', methods=['DELETE'])
@login_required
# @permission_required('selfcheck.schemes.delete')  # 临时注释权限检查
def api_delete_scheme(scheme_id):
    """删除方案API"""
    try:
        SchemeService.delete_scheme(scheme_id)

        return jsonify({
            'success': True,
            'message': '方案删除成功'
        })
    except ValueError as e:
        return jsonify({'success': False, 'message': str(e)})
    except Exception as e:
        logger.error(f"删除方案失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除方案失败: {str(e)}'})


# ==================== 方案规则管理 ====================

@bp.route('/api/schemes/<int:scheme_id>/rules')
@login_required
# @permission_required('selfcheck.schemes.view')  # 临时注释权限检查
def api_scheme_rules(scheme_id):
    """获取方案中的规则列表API"""
    try:
        rules = SchemeService.get_scheme_rules(scheme_id)

        return jsonify({
            'success': True,
            'rules': rules
        })
    except Exception as e:
        logger.error(f"获取方案规则失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取方案规则失败'})


@bp.route('/api/schemes/<int:scheme_id>/rules', methods=['POST'])
@login_required
# @permission_required('selfcheck.schemes.manage_rules')  # 临时注释权限检查
def api_add_rule_to_scheme(scheme_id):
    """向方案中添加规则API"""
    try:
        data = request.get_json()

        # 验证必填字段
        if not data.get('rule_id'):
            return jsonify({'success': False, 'message': '规则ID不能为空'})

        # 添加规则到方案
        SchemeService.add_rule_to_scheme(
            scheme_id=scheme_id,
            rule_id=data['rule_id'],
            sort_order=data.get('sort_order')
        )

        return jsonify({
            'success': True,
            'message': '规则添加成功'
        })
    except ValueError as e:
        return jsonify({'success': False, 'message': str(e)})
    except Exception as e:
        logger.error(f"添加规则到方案失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加规则失败: {str(e)}'})


@bp.route('/api/schemes/<int:scheme_id>/rules/<int:rule_id>', methods=['DELETE'])
@login_required
# @permission_required('selfcheck.schemes.manage_rules')  # 临时注释权限检查
def api_remove_rule_from_scheme(scheme_id, rule_id):
    """从方案中移除规则API"""
    try:
        SchemeService.remove_rule_from_scheme(scheme_id, rule_id)

        return jsonify({
            'success': True,
            'message': '规则移除成功'
        })
    except Exception as e:
        logger.error(f"从方案中移除规则失败: {str(e)}")
        return jsonify({'success': False, 'message': f'移除规则失败: {str(e)}'})


@bp.route('/api/schemes/<int:scheme_id>/rules/<int:rule_id>', methods=['PUT'])
@login_required
# @permission_required('selfcheck.schemes.manage_rules')  # 临时注释权限检查
def api_update_rule_in_scheme(scheme_id, rule_id):
    """更新方案中规则配置API"""
    try:
        data = request.get_json()

        SchemeService.update_rule_in_scheme(
            scheme_id=scheme_id,
            rule_id=rule_id,
            sort_order=data.get('sort_order'),
            is_enabled=data.get('is_enabled')
        )

        return jsonify({
            'success': True,
            'message': '规则配置更新成功'
        })
    except Exception as e:
        logger.error(f"更新方案中规则配置失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新规则配置失败: {str(e)}'})


@bp.route('/api/schemes/<int:scheme_id>/available-rules')
@login_required
# @permission_required('selfcheck.schemes.view')  # 临时注释权限检查
def api_available_rules(scheme_id):
    """获取可添加到方案的规则列表API"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取过滤条件
        filters = {
            'rule_name': request.args.get('rule_name', '').strip(),
            'rule_type': request.args.get('rule_type', '').strip(),
            'city': request.args.get('city', '').strip(),
            'visit_type': request.args.get('visit_type', '').strip(),
            'types': request.args.get('types', '').strip(),
            'rule_source': request.args.get('rule_source', '').strip()
        }

        # 移除空值
        filters = {k: v for k, v in filters.items() if v}

        result = SchemeService.get_available_rules(
            scheme_id,
            page=page,
            per_page=per_page,
            **filters
        )

        return jsonify({
            'success': True,
            'rules': result.get('rules', []),
            'page': result.get('page', page),
            'pages': result.get('pages', 1),
            'total': result.get('total', 0),
            'per_page': result.get('per_page', per_page)
        })
    except Exception as e:
        logger.error(f"获取可用规则失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取可用规则失败'})


# ==================== 规则检索条件 ====================

@bp.route('/api/rule-filter-options')
@login_required
# @permission_required('selfcheck.rules.view')  # 临时注释权限检查
def api_rule_filter_options():
    """获取规则过滤条件选项API"""
    try:
        from app.selfcheck.database import get_db_manager

        db_manager = get_db_manager()

        # 获取门诊住院类型
        visit_types_query = """
        SELECT DISTINCT visit_type
        FROM selfcheck_rules
        WHERE visit_type IS NOT NULL
        ORDER BY visit_type
        """
        visit_types_result = db_manager.execute_query(visit_types_query)
        visit_types = [row['visit_type'] for row in visit_types_result]

        # 获取定量定性类型
        types_query = """
        SELECT DISTINCT types
        FROM selfcheck_rules
        WHERE types IS NOT NULL
        ORDER BY types
        """
        types_result = db_manager.execute_query(types_query)
        types_list = [row['types'] for row in types_result]

        # 获取规则类型
        rule_types_query = """
        SELECT DISTINCT rule_type
        FROM selfcheck_rules
        WHERE rule_type IS NOT NULL
        ORDER BY rule_type
        """
        rule_types_result = db_manager.execute_query(rule_types_query)
        rule_types = [row['rule_type'] for row in rule_types_result]

        # 获取城市列表
        cities_query = """
        SELECT DISTINCT city
        FROM selfcheck_rules
        WHERE city IS NOT NULL
        ORDER BY city
        """
        cities_result = db_manager.execute_query(cities_query)
        cities = [row['city'] for row in cities_result]

        # 获取规则来源列表（使用已有的方法，从SELFCHECK_RULES表）
        from app.selfcheck.services import RuleService
        rule_sources = RuleService.get_rule_sources()

        return jsonify({
            'success': True,
            'options': {
                'visit_types': visit_types,
                'types': types_list,
                'rule_types': rule_types,
                'cities': cities,
                'rule_sources': rule_sources
            }
        })
    except Exception as e:
        logger.error(f"获取规则过滤条件选项失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取过滤条件选项失败'})


@bp.route('/api/schemes/<int:scheme_id>/statistics')
@login_required
# @permission_required('selfcheck.schemes.view')  # 临时注释权限检查
def api_scheme_statistics(scheme_id):
    """获取方案统计信息API"""
    try:
        stats = SchemeService.get_scheme_statistics(scheme_id)

        return jsonify({
            'success': True,
            'statistics': stats
        })
    except Exception as e:
        logger.error(f"获取方案统计信息失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取方案统计信息失败'})


# ==================== 错误处理 ====================

@bp.errorhandler(403)
def forbidden(error):
    """权限不足错误处理"""
    if request.is_json:
        return jsonify({'success': False, 'message': '权限不足'}), 403
    flash('权限不足', 'error')
    return redirect(url_for('main.index'))


@bp.errorhandler(404)
def not_found(error):
    """页面不存在错误处理"""
    if request.is_json:
        return jsonify({'success': False, 'message': '页面不存在'}), 404
    flash('页面不存在', 'error')
    return redirect(url_for('selfcheck.index'))


@bp.errorhandler(500)
def internal_error(error):
    """服务器内部错误处理"""
    if request.is_json:
        return jsonify({'success': False, 'message': '服务器内部错误'}), 500
    flash('服务器内部错误', 'error')
    return redirect(url_for('selfcheck.index'))


# ==================== 数据导入API ====================

@bp.route('/api/uploads/<int:upload_id>/validate-headers', methods=['POST'])
@login_required
@permission_required('selfcheck.uploads.validate')
def api_validate_csv_headers(upload_id):
    """校验CSV文件表头"""
    try:
        # 获取上传记录
        upload = UploadService.get_upload_by_id(upload_id)
        if not upload:
            return jsonify({'success': False, 'error': '上传记录不存在'}), 404

        # 检查权限：只能验证自己的上传文件
        if upload['user_id'] != current_user.id and not current_user.is_admin:
            return jsonify({'success': False, 'error': '权限不足'}), 403

        # 检查文件类型
        if upload['file_type'].lower() != 'csv':
            return jsonify({'success': False, 'error': '只支持CSV文件的表头验证'}), 400

        # 执行验证
        result = ValidationService.validate_file_by_type(upload['file_path'], upload['file_type'])

        # 更新上传记录状态
        if result['success']:
            UploadService._update_upload_status(upload_id, 'validated', '表头验证通过')
        else:
            UploadService._update_upload_status(upload_id, 'failed', f'表头验证失败: {result["error"]}')

        # 记录审计日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='validate',
            resource='csv_headers',
            resource_id=str(upload_id),
            details=f'CSV表头验证: {upload["file_name"]} - {"成功" if result["success"] else "失败"}'
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"CSV表头验证失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'验证失败: {str(e)}'}), 500


@bp.route('/api/uploads/<int:upload_id>/validate-structure', methods=['POST'])
@login_required
@permission_required('selfcheck.uploads.validate')
def api_validate_file_structure(upload_id):
    """验证文件结构（支持CSV、DMP、DP、BAK格式）"""
    try:
        # 获取上传记录
        upload = UploadService.get_upload_by_id(upload_id)
        if not upload:
            return jsonify({'success': False, 'error': '上传记录不存在'}), 404

        # 检查权限：只能验证自己的上传文件
        if upload['user_id'] != current_user.id and not current_user.is_admin:
            return jsonify({'success': False, 'error': '权限不足'}), 403

        # 执行验证
        result = ValidationService.validate_file_by_type(upload['file_path'], upload['file_type'])

        # 更新上传记录状态
        if result['success']:
            UploadService._update_upload_status(upload_id, 'validated', '文件结构验证通过')
        else:
            UploadService._update_upload_status(upload_id, 'failed', f'文件结构验证失败: {result["error"]}')

        # 记录审计日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='validate',
            resource='file_structure',
            resource_id=str(upload_id),
            details=f'文件结构验证: {upload["file_name"]} ({upload["file_type"]}) - {"成功" if result["success"] else "失败"}'
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"文件结构验证失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'验证失败: {str(e)}'}), 500


@bp.route('/api/uploads/<int:upload_id>/import', methods=['POST'])
@login_required
@permission_required('selfcheck.uploads.import')
def api_import_upload_data(upload_id):
    """手动触发数据导入"""
    try:
        result = DataImportService.import_upload_data(upload_id)

        # 记录操作日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='import',
            resource='upload_data',
            resource_id=str(upload_id),
            details=f'手动导入上传数据: {upload_id}'
        )

        return jsonify(result)
    except Exception as e:
        logger.error(f"手动导入数据失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/uploads/<int:upload_id>/data', methods=['GET'])
@login_required
@permission_required('selfcheck.uploads.view')
def api_get_import_data(upload_id):
    """获取导入的数据"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        result = DataImportService.get_import_data(upload_id, page, per_page)
        return jsonify({'success': True, **result})
    except Exception as e:
        logger.error(f"获取导入数据失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/api/uploads/<int:upload_id>/data', methods=['DELETE'])
@login_required
@permission_required('selfcheck.uploads.delete')
def api_delete_import_data(upload_id):
    """删除导入的数据"""
    try:
        success = DataImportService.delete_import_data(upload_id)

        if success:
            # 记录操作日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='delete',
                resource='import_data',
                resource_id=str(upload_id),
                details=f'删除导入数据: {upload_id}'
            )

            return jsonify({'success': True, 'message': '导入数据删除成功'})
        else:
            return jsonify({'success': False, 'error': '删除导入数据失败'}), 500
    except Exception as e:
        logger.error(f"删除导入数据失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': str(e)}), 500



