from flask import render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_user, logout_user, current_user, login_required
from app.auth import bp
from app.auth.forms import LoginForm, ChangePasswordForm
from app.auth.wechat import WeChatAuth
from app.models.user import User
from app.models.audit import AuditLog
from app.utils.breadcrumb import get_breadcrumb_for_auth
from app import db
from datetime import datetime
from config.wechat import WeChatConfig

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        # 支持用户名或邮箱登录
        user = User.query.filter(
            (User.username == form.username.data) | 
            (User.email == form.username.data)
        ).first()
        
        if user is None:
            flash('用户名或密码错误', 'error')
            return render_template('auth/login.html', form=form)
        
        # 检查账户是否被锁定
        if user.is_locked():
            flash('账户已被锁定，请稍后再试', 'error')
            return render_template('auth/login.html', form=form)
        
        # 检查账户是否激活
        if not user.is_active:
            flash('账户已被禁用，请联系管理员', 'error')
            return render_template('auth/login.html', form=form)
        
        # 验证密码
        if user.check_password(form.password.data):
            # 登录成功
            user.last_login = datetime.utcnow()
            user.login_attempts = 0
            user.locked_until = None
            db.session.commit()
            
            login_user(user, remember=form.remember_me.data)
            
            # 记录登录日志
            AuditLog.log_action(
                user_id=user.id,
                action='login',
                resource='system',
                details='用户登录成功'
            )
            
            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('main.index')
            return redirect(next_page)
        else:
            # 登录失败
            user.login_attempts += 1
            if user.login_attempts >= current_app.config.get('MAX_LOGIN_ATTEMPTS', 5):
                user.lock_account(30)  # 锁定30分钟
                flash('登录失败次数过多，账户已被锁定30分钟', 'error')
            else:
                remaining = current_app.config.get('MAX_LOGIN_ATTEMPTS', 5) - user.login_attempts
                flash(f'用户名或密码错误，还有{remaining}次尝试机会', 'error')
            
            db.session.commit()
            
            # 记录失败日志
            AuditLog.log_action(
                user_id=user.id,
                action='login_failed',
                resource='system',
                details=f'登录失败，尝试次数: {user.login_attempts}'
            )
    
    return render_template('auth/login.html', form=form)

@bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    # 记录登出日志
    AuditLog.log_action(
        user_id=current_user.id,
        action='logout',
        resource='system',
        details='用户登出'
    )
    
    logout_user()
    flash('您已成功登出', 'info')
    return redirect(url_for('auth.login'))

@bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if current_user.check_password(form.old_password.data):
            current_user.set_password(form.password.data)
            db.session.commit()

            # 记录密码修改日志
            AuditLog.log_action(
                user_id=current_user.id,
                action='change_password',
                resource='user',
                details='用户修改密码'
            )

            flash('密码修改成功', 'success')
            return redirect(url_for('main.index'))
        else:
            flash('当前密码错误', 'error')

    breadcrumb_items = get_breadcrumb_for_auth('change_password')
    return render_template('auth/change_password.html', form=form, breadcrumb_items=breadcrumb_items)

@bp.route('/wechat/login')
def wechat_login():
    """微信登录入口"""
    if not WeChatConfig.is_configured():
        flash('微信登录功能未配置', 'error')
        return redirect(url_for('auth.login'))

    # 生成state参数
    state = WeChatAuth.generate_state('login')

    # 获取微信授权URL
    oauth_url = WeChatConfig.get_oauth_url(state)

    return redirect(oauth_url)

@bp.route('/wechat/bind')
@login_required
def wechat_bind():
    """微信绑定入口"""
    if not WeChatConfig.is_configured():
        flash('微信绑定功能未配置', 'error')
        return redirect(url_for('main.index'))

    if current_user.is_wechat_bound():
        flash('您已绑定微信账号', 'info')
        return redirect(url_for('main.index'))

    # 生成state参数
    state = WeChatAuth.generate_state('bind', current_user.id)

    # 获取微信授权URL
    oauth_url = WeChatConfig.get_oauth_url(state)

    return redirect(oauth_url)

@bp.route('/wechat/callback')
def wechat_callback():
    """微信授权回调"""
    code = request.args.get('code')
    state = request.args.get('state')

    if not code:
        flash('微信授权失败', 'error')
        return redirect(url_for('auth.login'))

    # 验证state
    state_data = WeChatAuth.verify_state(state) if state else None
    if not state_data:
        flash('授权状态无效', 'error')
        return redirect(url_for('auth.login'))

    # 处理微信登录
    result = WeChatAuth.process_wechat_login(code, state_data.get('action', 'login'))

    if not result['success']:
        flash(result['message'], 'error')
        return redirect(url_for('auth.login'))

    wechat_data = result['data']
    action = state_data.get('action', 'login')

    if action == 'bind':
        # 绑定微信到当前用户
        return handle_wechat_bind(wechat_data, state_data.get('user_id'))
    else:
        # 微信登录或注册
        return handle_wechat_login(wechat_data)

def handle_wechat_login(wechat_data):
    """处理微信登录"""
    openid = wechat_data['openid']

    # 查找已存在的用户
    user = User.find_by_wechat_openid(openid)

    if user:
        # 用户已存在，直接登录
        if not user.is_active:
            flash('账户已被禁用，请联系管理员', 'error')
            return redirect(url_for('auth.login'))

        # 更新用户信息
        user.wechat_nickname = wechat_data.get('nickname', user.wechat_nickname)
        user.wechat_avatar = wechat_data.get('avatar', user.wechat_avatar)
        user.wechat_sex = wechat_data.get('sex', user.wechat_sex)
        user.last_login = datetime.utcnow()
        db.session.commit()

        login_user(user, remember=True)

        # 记录登录日志
        AuditLog.log_action(
            user_id=user.id,
            action='wechat_login',
            resource='system',
            details='微信登录成功'
        )

        flash(f'欢迎回来，{user.wechat_nickname or user.real_name}！', 'success')
        return redirect(url_for('main.index'))

    else:
        # 新用户，创建账号
        try:
            user = User.create_from_wechat(
                openid=openid,
                unionid=wechat_data.get('unionid'),
                nickname=wechat_data.get('nickname'),
                avatar=wechat_data.get('avatar'),
                sex=wechat_data.get('sex')
            )

            login_user(user, remember=True)

            # 记录注册日志
            AuditLog.log_action(
                user_id=user.id,
                action='wechat_register',
                resource='system',
                details='微信注册成功'
            )

            flash(f'欢迎加入，{user.wechat_nickname or user.real_name}！', 'success')
            return redirect(url_for('main.index'))

        except Exception as e:
            flash('注册失败，请稍后重试', 'error')
            current_app.logger.error(f'微信注册失败: {str(e)}')
            return redirect(url_for('auth.login'))

def handle_wechat_bind(wechat_data, user_id):
    """处理微信绑定"""
    if not user_id:
        flash('绑定失败：用户信息无效', 'error')
        return redirect(url_for('auth.login'))

    user = User.query.get(user_id)
    if not user:
        flash('绑定失败：用户不存在', 'error')
        return redirect(url_for('auth.login'))

    openid = wechat_data['openid']

    # 检查该微信是否已被其他用户绑定
    existing_user = User.find_by_wechat_openid(openid)
    if existing_user and existing_user.id != user.id:
        flash('该微信账号已被其他用户绑定', 'error')
        return redirect(url_for('main.index'))

    # 绑定微信
    try:
        user.bind_wechat(
            openid=openid,
            unionid=wechat_data.get('unionid'),
            nickname=wechat_data.get('nickname'),
            avatar=wechat_data.get('avatar'),
            sex=wechat_data.get('sex')
        )

        # 记录绑定日志
        AuditLog.log_action(
            user_id=user.id,
            action='wechat_bind',
            resource='user',
            details='微信绑定成功'
        )

        flash('微信绑定成功！', 'success')
        return redirect(url_for('main.index'))

    except Exception as e:
        flash('绑定失败，请稍后重试', 'error')
        current_app.logger.error(f'微信绑定失败: {str(e)}')
        return redirect(url_for('main.index'))

@bp.route('/wechat/unbind', methods=['POST'])
@login_required
def wechat_unbind():
    """解绑微信"""
    if not current_user.is_wechat_bound():
        return jsonify({'success': False, 'message': '您尚未绑定微信账号'})

    try:
        current_user.unbind_wechat()

        # 记录解绑日志
        AuditLog.log_action(
            user_id=current_user.id,
            action='wechat_unbind',
            resource='user',
            details='微信解绑成功'
        )

        return jsonify({'success': True, 'message': '微信解绑成功'})

    except Exception as e:
        current_app.logger.error(f'微信解绑失败: {str(e)}')
        return jsonify({'success': False, 'message': '解绑失败，请稍后重试'})
