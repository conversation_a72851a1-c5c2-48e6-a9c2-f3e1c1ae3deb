# 微信登录功能配置指南

## 1. 微信公众平台准备工作

### 1.1 注册微信公众号
1. 访问 [微信公众平台](https://mp.weixin.qq.com/)
2. 点击"立即注册"
3. 选择"订阅号"或"服务号"（推荐服务号，功能更全）
4. 填写基本信息并完成注册
5. 完成微信认证（需要营业执照等资料）

### 1.2 获取开发者信息
1. 登录微信公众平台
2. 进入"开发" -> "基本配置"
3. 记录以下信息：
   - **AppID (应用ID)**
   - **AppSecret (应用密钥)**

### 1.3 配置网页授权域名
1. 在微信公众平台，进入"开发" -> "接口权限" -> "网页服务" -> "网页帐号" -> "网页授权获取用户基本信息"
2. 点击"修改"
3. 添加授权回调页面域名，例如：
   - 本地测试：`localhost:5002` 或 `127.0.0.1:5002`
   - 生产环境：`yourdomain.com`

## 2. 配置应用环境变量

### 2.1 修改 .env 文件
在项目根目录的 `.env` 文件中添加以下配置：

```bash
# 微信公众号配置
WECHAT_APP_ID=你的微信AppID
WECHAT_APP_SECRET=你的微信AppSecret
WECHAT_REDIRECT_URI=http://localhost:5002/auth/wechat/callback
```

### 2.2 生产环境配置
如果部署到服务器，需要修改回调地址：
```bash
WECHAT_REDIRECT_URI=https://yourdomain.com/auth/wechat/callback
```

## 3. 测试微信登录功能

### 3.1 本地测试步骤
1. 确保已配置好微信AppID和AppSecret
2. 启动应用：`python run.py`
3. 访问登录页面：`http://localhost:5002/auth/login`
4. 点击"微信快速注册/登录"按钮
5. 使用微信扫码或在微信内置浏览器中测试

### 3.2 微信内测试
由于微信网页授权需要在微信环境中进行，建议：
1. 使用内网穿透工具（如ngrok）将本地服务暴露到公网
2. 或者部署到有域名的服务器进行测试

## 4. 常见问题解决

### 4.1 "微信登录功能未配置"错误
- 检查 `.env` 文件中的 `WECHAT_APP_ID` 和 `WECHAT_APP_SECRET` 是否正确设置
- 确保不是默认值 `your_app_id` 和 `your_app_secret`

### 4.2 授权回调失败
- 检查微信公众平台中的授权回调域名配置
- 确保 `WECHAT_REDIRECT_URI` 与实际访问域名一致

### 4.3 获取用户信息失败
- 确保公众号已通过微信认证
- 检查scope权限设置（当前使用 `snsapi_userinfo`）

## 5. 开发调试技巧

### 5.1 查看日志
应用会记录微信登录相关的日志，可以查看：
```bash
tail -f logs/micra_*.log | grep -i wechat
```

### 5.2 测试配置
可以在Python控制台中测试配置：
```python
from config.wechat import WeChatConfig
print(f"AppID: {WeChatConfig.WECHAT_APP_ID}")
print(f"配置状态: {WeChatConfig.is_configured()}")
print(f"授权URL: {WeChatConfig.get_oauth_url('test')}")
```

## 6. 安全注意事项

1. **保护AppSecret**：不要将AppSecret提交到代码仓库
2. **使用HTTPS**：生产环境必须使用HTTPS
3. **验证来源**：确保回调请求来自微信服务器
4. **定期更新**：定期更新AppSecret

## 7. 下一步配置

配置完成后，重启应用即可使用微信登录功能：
```bash
python run.py
```

访问登录页面，应该能看到微信登录按钮正常工作。
