# 规则管理模块测试用例

## 测试模块：规则管理
**测试负责人：** AI测试工程师  
**测试时间：** 2025-06-10  
**测试版本：** V1.0  

## 1. 功能概述

规则管理模块包含以下主要功能：
- 飞检规则知识库管理
- 规则搜索和过滤
- 规则详情查看
- 规则创建和编辑
- 规则删除
- SQL生成器
- 系统规则管理

## 2. 测试用例

### TC001 - 规则管理首页访问
**测试目标：** 验证规则管理首页正常访问  
**前置条件：** 用户已登录且有规则管理权限  
**测试步骤：**
1. 访问 `/rules/` 页面
2. 验证页面正常加载
3. 验证导航菜单显示正确
4. 验证权限控制正常

**预期结果：**
- 页面正常加载
- 显示规则管理相关功能入口
- 审计日志正确记录

### TC002 - 飞检规则知识库页面
**测试目标：** 验证飞检规则知识库页面功能  
**前置条件：** 用户有knowledge_base权限  
**测试步骤：**
1. 访问 `/rules/knowledge_base` 页面
2. 验证规则列表正常显示
3. 验证分页功能
4. 验证表格排序功能

**预期结果：**
- 页面正常加载
- 规则列表正确显示
- 分页和排序功能正常

### TC003 - 规则搜索功能
**测试目标：** 验证规则搜索和过滤功能  
**前置条件：** 规则数据充足  
**测试步骤：**
1. 测试按行为认定搜索
2. 测试按城市过滤
3. 测试按规则来源过滤
4. 测试按规则名称搜索
5. 测试按规则类型过滤
6. 测试组合搜索条件

**预期结果：**
- 所有搜索条件正确生效
- 搜索结果准确
- 组合搜索结果正确

### TC004 - 规则详情查看
**测试目标：** 验证规则详情查看功能  
**前置条件：** 存在有效的规则记录  
**测试步骤：**
1. 点击规则列表中的查看按钮
2. 验证规则详情正确显示
3. 验证所有字段信息完整
4. 验证关闭功能正常

**预期结果：**
- 规则详情正确显示
- 所有字段信息完整
- 审计日志正确记录

### TC005 - 规则创建功能
**测试目标：** 验证新规则创建功能  
**前置条件：** 用户有创建权限  
**测试步骤：**
1. 点击新增规则按钮
2. 填写规则基本信息
3. 填写规则内容
4. 保存规则
5. 验证规则创建成功

**预期结果：**
- 创建表单正常显示
- 数据验证正确
- 规则创建成功
- 审计日志正确记录

### TC006 - 规则编辑功能
**测试目标：** 验证规则编辑功能  
**前置条件：** 存在可编辑的规则  
**测试步骤：**
1. 选择规则进行编辑
2. 修改规则信息
3. 保存修改
4. 验证修改生效

**预期结果：**
- 编辑表单正确填充原数据
- 修改保存成功
- 数据更新正确

### TC007 - 规则删除功能
**测试目标：** 验证规则删除功能  
**前置条件：** 存在可删除的规则  
**测试步骤：**
1. 选择规则进行删除
2. 确认删除操作
3. 验证规则删除成功
4. 验证删除后列表更新

**预期结果：**
- 删除确认提示正常
- 规则删除成功
- 列表正确更新

### TC008 - SQL生成器功能
**测试目标：** 验证SQL生成器功能  
**前置条件：** 存在规则数据  
**测试步骤：**
1. 访问SQL生成器页面
2. 选择规则
3. 选择模板类型
4. 生成SQL语句
5. 验证SQL正确性

**预期结果：**
- 规则选择正常
- SQL生成成功
- 生成的SQL语法正确

### TC009 - 系统规则管理
**测试目标：** 验证系统规则管理功能  
**前置条件：** 用户有系统规则权限  
**测试步骤：**
1. 访问系统规则页面
2. 验证规则列表显示
3. 测试规则编辑功能
4. 测试SQL编辑功能

**预期结果：**
- 系统规则正确显示
- 编辑功能正常
- SQL编辑功能正常

### TC010 - 权限控制测试
**测试目标：** 验证权限控制功能  
**前置条件：** 不同权限的测试用户  
**测试步骤：**
1. 使用只有查看权限的用户登录
2. 验证只能查看，不能编辑/删除
3. 使用有编辑权限的用户登录
4. 验证可以编辑但不能删除
5. 使用管理员用户登录
6. 验证所有操作权限

**预期结果：**
- 权限控制正确生效
- 无权限操作被正确阻止
- 权限提示信息友好

## 3. API测试用例

### TC011 - 规则列表API测试
**API路径：** `/rules/api/rules`  
**方法：** GET  
**测试内容：**
- 无参数查询
- 分页参数测试
- 过滤参数测试
- 错误处理测试

### TC012 - 规则详情API测试
**API路径：** `/rules/api/rules/{id}`  
**方法：** GET  
**测试内容：**
- 有效ID查询
- 无效ID查询
- 权限验证

### TC013 - 规则创建API测试
**API路径：** `/rules/api/rules`  
**方法：** POST  
**测试内容：**
- 有效数据创建
- 无效数据验证
- 必填字段验证

### TC014 - 规则更新API测试
**API路径：** `/rules/api/rules/{id}`  
**方法：** PUT  
**测试内容：**
- 有效数据更新
- 部分字段更新
- 数据验证

### TC015 - 规则删除API测试
**API路径：** `/rules/api/rules/{id}`  
**方法：** DELETE  
**测试内容：**
- 有效删除
- 权限验证
- 关联数据处理

## 4. 性能测试用例

### TC016 - 大数据量加载性能
**测试目标：** 验证大量规则数据的加载性能  
**测试数据：** 1000+条规则记录  
**性能指标：** 页面加载时间 < 3秒

### TC017 - 搜索性能测试
**测试目标：** 验证复杂搜索条件的响应性能  
**性能指标：** 搜索响应时间 < 2秒

### TC018 - 并发操作测试
**测试目标：** 验证多用户并发操作的稳定性  
**测试场景：** 10个用户同时进行规则操作

## 5. 数据完整性测试

### TC019 - 数据库约束测试
**测试目标：** 验证数据库约束正确性  
**测试内容：**
- 主键约束
- 外键约束
- 唯一性约束
- 非空约束

### TC020 - 事务一致性测试
**测试目标：** 验证事务处理的一致性  
**测试场景：**
- 创建操作回滚
- 更新操作回滚
- 删除操作回滚
