#!/usr/bin/env python3
"""直接测试方案管理API功能"""

import requests
import json
import time

def test_scheme_api():
    """测试方案API功能"""
    base_url = "http://127.0.0.1:5002"
    
    print("=== 方案管理API测试 ===")
    
    # 1. 测试获取方案列表
    print("\n1. 测试获取方案列表...")
    list_response = requests.get(f"{base_url}/selfcheck/api/schemes")
    
    if list_response.status_code == 200:
        try:
            list_result = list_response.json()
            print("✅ 获取方案列表成功")
            print(f"   响应数据: {json.dumps(list_result, ensure_ascii=False, indent=2)}")
            
            if list_result.get('success'):
                schemes = list_result.get('schemes', [])
                print(f"   当前方案数量: {len(schemes)}")
                
                # 如果有方案，测试获取详情
                if schemes:
                    test_scheme_id = schemes[0]['id']
                    print(f"\n2. 测试获取方案详情 (ID: {test_scheme_id})...")
                    
                    detail_response = requests.get(f"{base_url}/selfcheck/api/schemes/{test_scheme_id}")
                    
                    if detail_response.status_code == 200:
                        detail_result = detail_response.json()
                        print("✅ 获取方案详情成功")
                        print(f"   详情数据: {json.dumps(detail_result, ensure_ascii=False, indent=2)}")
                    else:
                        print(f"❌ 获取方案详情失败: {detail_response.status_code}")
                        print(f"   响应内容: {detail_response.text[:200]}")
                else:
                    print("   没有方案数据，跳过详情测试")
            else:
                print(f"   API返回错误: {list_result.get('message')}")
        except json.JSONDecodeError:
            print(f"❌ 响应不是有效的JSON: {list_response.text[:200]}")
    else:
        print(f"❌ 获取方案列表失败: {list_response.status_code}")
        print(f"   响应内容: {list_response.text[:200]}")
    
    # 3. 测试创建方案（不需要登录的话）
    print("\n3. 测试创建方案...")
    test_scheme_name = f"API测试方案_{int(time.time())}"
    create_data = {
        'scheme_name': test_scheme_name,
        'description': '这是一个通过API创建的测试方案'
    }
    
    create_response = requests.post(
        f"{base_url}/selfcheck/api/schemes",
        json=create_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"   创建方案响应状态: {create_response.status_code}")
    print(f"   响应内容: {create_response.text[:500]}")
    
    if create_response.status_code == 200:
        try:
            create_result = create_response.json()
            if create_result.get('success'):
                print("✅ 创建方案成功")
                new_scheme_id = create_result['scheme']['id']
                print(f"   新方案ID: {new_scheme_id}")
                
                # 测试更新
                print(f"\n4. 测试更新方案 (ID: {new_scheme_id})...")
                update_data = {
                    'scheme_name': test_scheme_name + "_已更新",
                    'description': '这是一个已更新的API测试方案',
                    'status': 'inactive'
                }
                
                update_response = requests.put(
                    f"{base_url}/selfcheck/api/schemes/{new_scheme_id}",
                    json=update_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                print(f"   更新方案响应状态: {update_response.status_code}")
                print(f"   响应内容: {update_response.text[:500]}")
                
                # 测试删除
                print(f"\n5. 测试删除方案 (ID: {new_scheme_id})...")
                delete_response = requests.delete(f"{base_url}/selfcheck/api/schemes/{new_scheme_id}")
                
                print(f"   删除方案响应状态: {delete_response.status_code}")
                print(f"   响应内容: {delete_response.text[:500]}")
                
            else:
                print(f"❌ 创建方案失败: {create_result.get('message')}")
        except json.JSONDecodeError:
            print(f"❌ 创建响应不是有效的JSON")
    
    print("\n=== API测试完成 ===")

if __name__ == '__main__':
    test_scheme_api()
