#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def verify_permission_structure():
    """验证权限结构"""
    app = create_app()
    
    with app.app_context():
        try:
            print('=== 验证完整的三级权限结构 ===')
            
            # 查询所有权限，按层级显示
            result = db.session.execute(text("""
                SELECT 
                    p1.id as level1_id,
                    p1.name as level1_name,
                    p1.code as level1_code,
                    p1.module as level1_module,
                    p1.resource_type as level1_type,
                    p1.sort_order as level1_sort,
                    p2.id as level2_id,
                    p2.name as level2_name,
                    p2.code as level2_code,
                    p2.resource_type as level2_type,
                    p2.sort_order as level2_sort,
                    p3.id as level3_id,
                    p3.name as level3_name,
                    p3.code as level3_code,
                    p3.resource_type as level3_type,
                    p3.sort_order as level3_sort
                FROM permissions p1
                LEFT JOIN permissions p2 ON p2.parent_id = p1.id
                LEFT JOIN permissions p3 ON p3.parent_id = p2.id
                WHERE p1.parent_id IS NULL
                ORDER BY p1.sort_order, p2.sort_order, p3.sort_order
            """))
            
            permissions = result.fetchall()
            
            current_level1 = None
            current_level2 = None
            
            for perm in permissions:
                # 第一级权限（大模块）
                if perm[0] != current_level1:
                    current_level1 = perm[0]
                    current_level2 = None
                    print(f'\n🏢 【{perm[4]}】{perm[1]} ({perm[2]}) - 模块: {perm[3]} - 排序: {perm[5]}')
                
                # 第二级权限（页面）
                if perm[6] and perm[6] != current_level2:
                    current_level2 = perm[6]
                    print(f'  📄 【{perm[9]}】{perm[7]} ({perm[8]}) - 排序: {perm[10]}')
                
                # 第三级权限（功能按钮）
                if perm[11]:
                    print(f'    🔘 【{perm[14]}】{perm[12]} ({perm[13]}) - 排序: {perm[15]}')
            
            # 统计信息
            print('\n=== 权限统计信息 ===')
            stats_result = db.session.execute(text("""
                SELECT 
                    module,
                    COUNT(*) as total_count,
                    SUM(CASE WHEN parent_id IS NULL THEN 1 ELSE 0 END) as level1_count,
                    SUM(CASE WHEN parent_id IS NOT NULL AND 
                        EXISTS(SELECT 1 FROM permissions p2 WHERE p2.id = permissions.parent_id AND p2.parent_id IS NULL) 
                        THEN 1 ELSE 0 END) as level2_count,
                    SUM(CASE WHEN parent_id IS NOT NULL AND 
                        EXISTS(SELECT 1 FROM permissions p2 WHERE p2.id = permissions.parent_id AND p2.parent_id IS NOT NULL) 
                        THEN 1 ELSE 0 END) as level3_count,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count
                FROM permissions 
                GROUP BY module
                ORDER BY module
            """))
            
            stats = stats_result.fetchall()
            print(f'{"模块":<15} {"总计":<6} {"一级":<6} {"二级":<6} {"三级":<6} {"激活":<6}')
            print('-' * 60)
            for stat in stats:
                print(f'{stat[0]:<15} {stat[1]:<6} {stat[2]:<6} {stat[3]:<6} {stat[4]:<6} {stat[5]:<6}')
            
            # 检查权限完整性
            print('\n=== 权限完整性检查 ===')
            
            # 检查是否有孤立的权限
            orphan_result = db.session.execute(text("""
                SELECT id, name, code, parent_id
                FROM permissions 
                WHERE parent_id IS NOT NULL 
                AND parent_id NOT IN (SELECT id FROM permissions)
            """))
            
            orphans = orphan_result.fetchall()
            if orphans:
                print('❌ 发现孤立权限:')
                for orphan in orphans:
                    print(f'  - {orphan[1]} ({orphan[2]}) 父ID: {orphan[3]}')
            else:
                print('✅ 没有孤立权限')
            
            # 检查是否有重复的权限代码
            duplicate_result = db.session.execute(text("""
                SELECT code, COUNT(*) as count
                FROM permissions 
                GROUP BY code
                HAVING COUNT(*) > 1
            """))
            
            duplicates = duplicate_result.fetchall()
            if duplicates:
                print('❌ 发现重复权限代码:')
                for dup in duplicates:
                    print(f'  - {dup[0]} 出现 {dup[1]} 次')
            else:
                print('✅ 没有重复权限代码')
            
            # 检查权限层级深度
            depth_result = db.session.execute(text("""
                WITH RECURSIVE permission_depth AS (
                    SELECT id, name, code, parent_id, 1 as depth
                    FROM permissions 
                    WHERE parent_id IS NULL
                    
                    UNION ALL
                    
                    SELECT p.id, p.name, p.code, p.parent_id, pd.depth + 1
                    FROM permissions p
                    JOIN permission_depth pd ON p.parent_id = pd.id
                )
                SELECT MAX(depth) as max_depth, MIN(depth) as min_depth
                FROM permission_depth
            """))
            
            depth = depth_result.fetchone()
            if depth:
                print(f'✅ 权限层级深度: 最小 {depth[1]} 级, 最大 {depth[0]} 级')
                if depth[0] > 3:
                    print(f'⚠️  警告: 发现超过3级的权限层级')
            
            print('\n✅ 权限结构验证完成！')
                
        except Exception as e:
            print(f"验证失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    verify_permission_structure()
