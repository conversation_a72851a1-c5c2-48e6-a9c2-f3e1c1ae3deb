"""
自查自纠模块数据库工具 - 使用纯oracledb，不依赖SQLAlchemy
"""
import oracledb
import logging
from contextlib import contextmanager
from typing import List, Dict, Any, Optional, Union
import threading
import time

logger = logging.getLogger(__name__)


class OracleDBManager:
    """Oracle数据库管理器 - 原始版本（兼容现有代码）"""

    def __init__(self, user: str, password: str, dsn: str):
        self.user = user
        self.password = password
        self.dsn = dsn
        self._init_oracle_client()

    def _init_oracle_client(self):
        """初始化Oracle客户端"""
        try:
            oracledb.init_oracle_client()
        except Exception:
            # 可能已经初始化过了
            pass
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = oracledb.connect(
                user=self.user,
                password=self.password,
                dsn=self.dsn
            )
            yield connection
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
        finally:
            if connection:
                connection.close()
    
    def execute_query(self, query: str, params: Optional[Union[List, Dict]] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if params:
                    # 将?占位符替换为:n形式，适配Oracle
                    if isinstance(params, list) and '?' in query:
                        # 计算?的数量
                        placeholders_count = query.count('?')
                        # 创建参数字典
                        params_dict = {}

                        # 逐个替换?为:n
                        for i in range(placeholders_count):
                            if i < len(params):
                                param_name = f"p{i+1}"
                                # 替换第一个?
                                query = query.replace('?', f":{param_name}", 1)
                                # 添加到参数字典
                                params_dict[param_name] = params[i]

                        # 使用命名参数执行
                        cursor.execute(query, params_dict)
                    else:
                        # 直接使用参数（列表或字典）
                        cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                # 获取列名
                columns = [desc[0].lower() for desc in cursor.description]
                
                # 获取所有结果
                rows = cursor.fetchall()
                
                # 转换为字典列表
                results = []
                for row in rows:
                    result = {}
                    for i, value in enumerate(row):
                        # 处理LOB类型
                        if hasattr(value, 'read'):
                            try:
                                # 读取LOB内容并转换为字符串
                                result[columns[i]] = value.read() if value else ''
                            except Exception:
                                result[columns[i]] = str(value) if value else ''
                        else:
                            result[columns[i]] = value if value is not None else ''
                    results.append(result)
                
                cursor.close()
                return results
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}")
            raise
    
    def execute_update(self, query: str, params: Optional[Union[List, Dict]] = None) -> int:
        """执行更新操作并返回影响的行数"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if params:
                    # 将?占位符替换为:n形式，适配Oracle
                    if isinstance(params, list) and '?' in query:
                        # 计算?的数量
                        placeholders_count = query.count('?')
                        # 创建参数字典
                        params_dict = {}

                        # 逐个替换?为:n
                        for i in range(placeholders_count):
                            if i < len(params):
                                param_name = f"p{i+1}"
                                # 替换第一个?
                                query = query.replace('?', f":{param_name}", 1)
                                # 添加到参数字典
                                params_dict[param_name] = params[i]

                        # 使用命名参数执行
                        cursor.execute(query, params_dict)
                    else:
                        # 直接使用参数（列表或字典）
                        cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                affected_rows = cursor.rowcount
                conn.commit()
                
                cursor.close()
                return affected_rows
        except Exception as e:
            logger.error(f"执行更新失败: {str(e)}")
            raise
    
    def execute_batch(self, query: str, params_list: List[List]) -> int:
        """批量执行操作"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查是否需要替换?占位符
                if params_list and '?' in query:
                    # 计算?的数量
                    placeholders_count = query.count('?')
                    # 创建新的查询和参数列表
                    new_query = query
                    new_params_list = []
                    
                    # 逐个替换?为:n
                    for i in range(placeholders_count):
                        param_name = f"p{i+1}"
                        # 替换第一个?
                        new_query = new_query.replace('?', f":{param_name}", 1)
                    
                    # 转换每组参数为字典
                    for params in params_list:
                        params_dict = {}
                        for i, value in enumerate(params):
                            if i < placeholders_count:
                                params_dict[f"p{i+1}"] = value
                        new_params_list.append(params_dict)
                    
                    # 使用命名参数执行
                    cursor.executemany(new_query, new_params_list)
                else:
                    cursor.executemany(query, params_list)
                
                affected_rows = cursor.rowcount
                conn.commit()
                
                cursor.close()
                return affected_rows
        except Exception as e:
            logger.error(f"批量执行失败: {str(e)}")
            raise


# 全局数据库管理器实例
_db_manager = None


def init_db_manager(user: str, password: str, dsn: str):
    """初始化数据库管理器"""
    global _db_manager
    _db_manager = OracleDBManager(user, password, dsn)


def get_db_manager() -> OracleDBManager:
    """获取数据库管理器实例"""
    if _db_manager is None:
        # 使用默认配置
        init_db_manager('datachange', 'drgs2019', '127.0.0.1/orcl')
    return _db_manager


def execute_query(query: str, params: Optional[Union[List, Dict]] = None) -> List[Dict[str, Any]]:
    """执行查询的便捷函数"""
    return get_db_manager().execute_query(query, params)


def execute_update(query: str, params: Optional[Union[List, Dict]] = None) -> int:
    """执行更新的便捷函数"""
    return get_db_manager().execute_update(query, params)


def execute_batch(query: str, params_list: List[List]) -> int:
    """批量执行的便捷函数"""
    return get_db_manager().execute_batch(query, params_list)


# ==================== 多线程批量导入专用连接池 ====================

class BatchImportDBManager:
    """专门用于批量导入的数据库管理器 - 支持连接池"""

    def __init__(self, user: str, password: str, dsn: str):
        self.user = user
        self.password = password
        self.dsn = dsn
        self.pool = None
        self._pool_lock = threading.Lock()
        self._init_oracle_client()
        self._init_connection_pool()

    def _init_oracle_client(self):
        """初始化Oracle客户端"""
        try:
            oracledb.init_oracle_client()
        except Exception:
            # 可能已经初始化过了
            pass

    def _init_connection_pool(self):
        """初始化连接池"""
        try:
            with self._pool_lock:
                if self.pool is None:
                    self.pool = oracledb.create_pool(
                        user=self.user,
                        password=self.password,
                        dsn=self.dsn,
                        min=2,          # 最小连接数
                        max=8,          # 最大连接数（降低以避免超过Oracle限制）
                        increment=1,    # 连接增量
                        getmode=oracledb.POOL_GETMODE_WAIT,  # 等待可用连接
                        timeout=30      # 获取连接超时时间（秒）
                    )
                    logger.info(f"批量导入连接池创建成功: min=2, max=8, increment=1")
        except Exception as e:
            logger.error(f"创建批量导入连接池失败: {str(e)}")
            # 不抛出异常，允许回退到直接连接
            self.pool = None

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器（使用连接池）"""
        connection = None
        start_time = time.time()
        try:
            # 优先使用连接池
            if self.pool is not None:
                logger.debug("正在从批量导入连接池获取数据库连接...")
                connection = self.pool.acquire()
                acquire_time = time.time() - start_time
                logger.debug(f"从批量导入连接池获取连接成功，耗时: {acquire_time:.3f}秒")
            else:
                # 连接池不可用时，使用直接连接
                logger.debug("批量导入连接池不可用，使用直接连接...")
                connection = oracledb.connect(
                    user=self.user,
                    password=self.password,
                    dsn=self.dsn
                )
                logger.debug("直接数据库连接成功")

            yield connection
        except Exception as e:
            logger.error(f"获取批量导入数据库连接失败: {str(e)}")
            # 如果连接池出问题，尝试直接连接
            if self.pool is not None and connection is None:
                try:
                    logger.warning("批量导入连接池获取失败，尝试直接连接...")
                    connection = oracledb.connect(
                        user=self.user,
                        password=self.password,
                        dsn=self.dsn
                    )
                    logger.debug("直接连接成功")
                    yield connection
                except Exception as direct_e:
                    logger.error(f"直接连接也失败: {str(direct_e)}")
                    raise
            else:
                raise
        finally:
            if connection:
                try:
                    connection.close()  # 对于连接池，这会将连接返回池中
                    logger.debug("批量导入连接已关闭/返回连接池")
                except Exception as e:
                    logger.error(f"关闭批量导入连接失败: {str(e)}")

    def execute_query(self, query: str, params: Optional[Union[List, Dict]] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果（支持命名参数）"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if params:
                    # 将?占位符替换为:n形式，适配Oracle
                    if isinstance(params, list) and '?' in query:
                        # 计算?的数量
                        placeholders_count = query.count('?')
                        # 创建参数字典
                        params_dict = {}

                        # 逐个替换?为:n
                        for i in range(placeholders_count):
                            if i < len(params):
                                param_name = f"p{i+1}"
                                # 替换第一个?
                                query = query.replace('?', f":{param_name}", 1)
                                # 添加到参数字典
                                params_dict[param_name] = params[i]

                        # 使用命名参数执行
                        cursor.execute(query, params_dict)
                    else:
                        # 直接使用参数（列表或字典）
                        cursor.execute(query, params)
                else:
                    cursor.execute(query)

                # 获取列名
                columns = [desc[0].lower() for desc in cursor.description]

                # 获取所有结果
                rows = cursor.fetchall()

                # 转换为字典列表
                results = []
                for row in rows:
                    result = {}
                    for i, value in enumerate(row):
                        # 处理LOB类型
                        if hasattr(value, 'read'):
                            try:
                                # 读取LOB内容并转换为字符串
                                result[columns[i]] = value.read() if value else ''
                            except Exception:
                                result[columns[i]] = str(value) if value else ''
                        else:
                            result[columns[i]] = value if value is not None else ''
                    results.append(result)

                cursor.close()
                return results
        except Exception as e:
            logger.error(f"批量导入执行查询失败: {str(e)}")
            raise

    def execute_update(self, query: str, params: Optional[Union[List, Dict]] = None) -> int:
        """执行更新操作并返回影响的行数（支持命名参数）"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if params:
                    # 将?占位符替换为:n形式，适配Oracle
                    if isinstance(params, list) and '?' in query:
                        # 计算?的数量
                        placeholders_count = query.count('?')
                        # 创建参数字典
                        params_dict = {}

                        # 逐个替换?为:n
                        for i in range(placeholders_count):
                            if i < len(params):
                                param_name = f"p{i+1}"
                                # 替换第一个?
                                query = query.replace('?', f":{param_name}", 1)
                                # 添加到参数字典
                                params_dict[param_name] = params[i]

                        # 使用命名参数执行
                        cursor.execute(query, params_dict)
                    else:
                        # 直接使用参数（列表或字典）
                        cursor.execute(query, params)
                else:
                    cursor.execute(query)

                affected_rows = cursor.rowcount
                conn.commit()

                cursor.close()
                return affected_rows
        except Exception as e:
            logger.error(f"批量导入执行更新失败: {str(e)}")
            raise

    def get_pool_status(self):
        """获取连接池状态"""
        if self.pool:
            try:
                return {
                    'opened': self.pool.opened,
                    'busy': self.pool.busy,
                    'max': self.pool.max,
                    'min': self.pool.min
                }
            except Exception as e:
                logger.error(f"获取批量导入连接池状态失败: {str(e)}")
                return None
        return None

    def close_pool(self):
        """关闭连接池"""
        try:
            with self._pool_lock:
                if self.pool:
                    self.pool.close()
                    self.pool = None
                    logger.info("批量导入连接池已关闭")
        except Exception as e:
            logger.error(f"关闭批量导入连接池失败: {str(e)}")


# 全局批量导入数据库管理器实例
_batch_import_db_manager = None


def get_batch_import_db_manager() -> BatchImportDBManager:
    """获取批量导入数据库管理器实例"""
    global _batch_import_db_manager
    if _batch_import_db_manager is None:
        # 使用默认配置
        _batch_import_db_manager = BatchImportDBManager('datachange', 'drgs2019', '127.0.0.1/orcl')
    return _batch_import_db_manager


def batch_import_execute_query(query: str, params: Optional[Union[List, Dict]] = None) -> List[Dict[str, Any]]:
    """批量导入执行查询的便捷函数"""
    return get_batch_import_db_manager().execute_query(query, params)


def batch_import_execute_update(query: str, params: Optional[Union[List, Dict]] = None) -> int:
    """批量导入执行更新的便捷函数"""
    return get_batch_import_db_manager().execute_update(query, params)
