{% extends "base.html" %}

{% block title %}系统规则语句 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-cogs"></i> 系统规则语句</h4>
{% endblock %}

{% block content %}
<!-- 规则分类选择区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-filter"></i> 规则分类
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">规则类型</label>
                        <select class="form-select" id="ruleType" onchange="loadSystemRules()">
                            <option value="">全部类型</option>
                            <option value="药品规则">药品规则</option>
                            <option value="诊疗规则">诊疗规则</option>
                            <option value="耗材规则">耗材规则</option>
                            <option value="综合规则">综合规则</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">行为认定</label>
                        <select class="form-select" id="behaviorType" onchange="loadSystemRules()">
                            <option value="">全部行为</option>
                            <option value="超量使用">超量使用</option>
                            <option value="重复收费">重复收费</option>
                            <option value="违规使用">违规使用</option>
                            <option value="套餐收费">套餐收费</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">适用范围</label>
                        <select class="form-select" id="scopeType" onchange="loadSystemRules()">
                            <option value="">全部范围</option>
                            <option value="门诊">门诊</option>
                            <option value="住院">住院</option>
                            <option value="门诊+住院">门诊+住院</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn btn-primary" onclick="loadSystemRules()">
                            <i class="fas fa-search"></i> 筛选
                        </button>
                        <button type="button" class="btn btn-success ms-2" onclick="exportSystemRules()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统规则列表区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list"></i> 系统规则列表
                </h6>
                <div>
                    <span id="ruleCount" class="badge bg-info">共 0 条规则</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="systemRulesTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="8%">规则ID</th>
                                <th width="20%">规则名称</th>
                                <th width="12%">规则类型</th>
                                <th width="12%">行为认定</th>
                                <th width="10%">适用范围</th>
                                <th width="10%">城市</th>
                                <th width="12%">规则来源</th>
                                <th width="16%">操作</th>
                            </tr>
                        </thead>
                        <tbody id="systemRulesTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="系统规则分页">
                    <ul class="pagination justify-content-center" id="systemRulesPagination">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 规则SQL语句显示区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-code"></i> 规则SQL语句
                </h6>
                <div>
                    <button type="button" class="btn btn-primary btn-sm" onclick="copyRuleSQL()">
                        <i class="fas fa-copy"></i> 复制SQL
                    </button>
                    <button type="button" class="btn btn-success btn-sm" onclick="executeRuleSQL()">
                        <i class="fas fa-play"></i> 执行SQL
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="exportRuleSQL()">
                        <i class="fas fa-download"></i> 导出Excel
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="ruleSQLContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-mouse-pointer fa-3x mb-3"></i>
                        <p>请点击上方规则列表中的"查看SQL"按钮</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 规则详情模态框 -->
<div class="modal fade" id="ruleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> 规则详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="ruleDetailContent">
                <!-- 规则详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="generateRuleSQL()">生成SQL</button>
            </div>
        </div>
    </div>
</div>

<!-- SQL执行结果模态框 -->
<div class="modal fade" id="sqlResultModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-table"></i> SQL执行结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="sqlResultContent">
                <!-- SQL执行结果将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" onclick="exportSQLResult()">导出Excel</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let currentRuleSQL = '';
let currentRuleId = null;
let sqlResultData = null;

// 页面加载完成后初始化
$(document).ready(function() {
    loadSystemRules();
});

// 加载系统规则列表
function loadSystemRules(page = 1) {
    currentPage = page;
    
    const filters = {
        rule_type: $('#ruleType').val(),
        behavior_type: $('#behaviorType').val(),
        scope_type: $('#scopeType').val(),
        page: page,
        per_page: 20
    };
    
    // 移除空值
    Object.keys(filters).forEach(key => {
        if (filters[key] === '') {
            delete filters[key];
        }
    });
    
    $.get('/rules/api/search', filters, function(response) {
        if (response.success) {
            displaySystemRules(response.rules || []);
            updateRuleCount((response.rules || []).length);
        } else {
            showAlert('加载系统规则失败: ' + response.error, 'danger');
        }
    }).fail(function() {
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

// 显示系统规则列表
function displaySystemRules(rules) {
    const tbody = $('#systemRulesTableBody');
    tbody.empty();
    
    if (rules.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted">暂无符合条件的规则</td>
            </tr>
        `);
        return;
    }
    
    rules.forEach(rule => {
        tbody.append(`
            <tr>
                <td>${rule.rule_id || rule.id || ''}</td>
                <td>${rule.规则名称 || ''}</td>
                <td>${rule.类型 || rule.规则类型 || ''}</td>
                <td>${rule.行为认定 || ''}</td>
                <td>${rule.适用范围 || ''}</td>
                <td>${rule.城市 || ''}</td>
                <td>${rule.规则来源 || ''}</td>
                <td>
                    <button class="btn btn-sm btn-info me-1" onclick="viewRuleDetail(${rule.rule_id || rule.id})" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-primary me-1" onclick="viewRuleSQL(${rule.rule_id || rule.id})" title="查看SQL">
                        <i class="fas fa-code"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="generateRuleSQL(${rule.rule_id || rule.id})" title="生成SQL">
                        <i class="fas fa-magic"></i>
                    </button>
                </td>
            </tr>
        `);
    });
}

// 更新规则数量
function updateRuleCount(count) {
    $('#ruleCount').text(`共 ${count} 条规则`);
}

// 查看规则详情
function viewRuleDetail(ruleId) {
    $.get(`/rules/api/rules/${ruleId}`, function(response) {
        if (response.success && response.data.length > 0) {
            const rule = response.data[0];
            displayRuleDetail(rule);
            $('#ruleDetailModal').modal('show');
        } else {
            showAlert('获取规则详情失败', 'danger');
        }
    });
}

// 显示规则详情
function displayRuleDetail(rule) {
    const content = $('#ruleDetailContent');
    content.html(`
        <div class="row">
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr><th width="30%">规则ID:</th><td>${rule.ID || ''}</td></tr>
                    <tr><th>规则名称:</th><td>${rule.规则名称 || ''}</td></tr>
                    <tr><th>行为认定:</th><td>${rule.行为认定 || ''}</td></tr>
                    <tr><th>适用范围:</th><td>${rule.适用范围 || ''}</td></tr>
                    <tr><th>城市:</th><td>${rule.城市 || ''}</td></tr>
                    <tr><th>规则来源:</th><td>${rule.规则来源 || ''}</td></tr>
                    <tr><th>类型:</th><td>${rule.类型 || ''}</td></tr>
                    <tr><th>规则类型:</th><td>${rule.规则类型 || ''}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr><th width="30%">医保编码1:</th><td>${rule.医保编码1 || ''}</td></tr>
                    <tr><th>医保名称1:</th><td>${rule.医保名称1 || ''}</td></tr>
                    <tr><th>医保编码2:</th><td>${rule.医保编码2 || ''}</td></tr>
                    <tr><th>医保名称2:</th><td>${rule.医保名称2 || ''}</td></tr>
                    <tr><th>违规数量:</th><td>${rule.违规数量 || ''}</td></tr>
                    <tr><th>违规金额:</th><td>${rule.违规金额 || ''}</td></tr>
                    <tr><th>年龄:</th><td>${rule.年龄 || ''}</td></tr>
                    <tr><th>性别:</th><td>${rule.性别 || ''}</td></tr>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <h6>规则内涵:</h6>
                <div class="border rounded p-3 bg-light">
                    ${rule.规则内涵 || ''}
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <h6>包含科室:</h6>
                <div class="border rounded p-2">${rule.包含科室 || ''}</div>
            </div>
            <div class="col-md-6">
                <h6>排除科室:</h6>
                <div class="border rounded p-2">${rule.排除科室 || ''}</div>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-6">
                <h6>包含诊断:</h6>
                <div class="border rounded p-2">${rule.包含诊断 || ''}</div>
            </div>
            <div class="col-md-6">
                <h6>排除诊断:</h6>
                <div class="border rounded p-2">${rule.排除诊断 || ''}</div>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-12">
                <h6>备注:</h6>
                <div class="border rounded p-2">${rule.备注 || ''}</div>
            </div>
        </div>
    `);
    
    currentRuleId = rule.ID;
}

// 查看规则SQL
function viewRuleSQL(ruleId) {
    // 这里应该调用API获取规则对应的SQL语句
    // 暂时使用示例SQL
    const exampleSQL = generateExampleSQL(ruleId);
    displayRuleSQL(exampleSQL);
    currentRuleSQL = exampleSQL;
    currentRuleId = ruleId;
}

// 生成示例SQL
function generateExampleSQL(ruleId) {
    return `-- 规则ID: ${ruleId} 对应的SQL语句
-- 生成时间: ${new Date().toLocaleString()}

SELECT 
    就诊流水号,
    患者姓名,
    科室名称,
    医生姓名,
    费用项目名称,
    医保编码,
    费用金额,
    就诊日期,
    '规则${ruleId}' as 规则标识
FROM 费用明细表 
WHERE 医保编码 IN (
    SELECT 医保编码1 
    FROM 飞检规则知识库 
    WHERE id = ${ruleId}
)
AND 费用金额 > (
    SELECT COALESCE(违规金额, 0) 
    FROM 飞检规则知识库 
    WHERE id = ${ruleId}
)
AND 就诊日期 BETWEEN '2024-01-01' AND '2024-12-31'
ORDER BY 费用金额 DESC`;
}

// 显示规则SQL
function displayRuleSQL(sql) {
    $('#ruleSQLContainer').html(`
        <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">SQL语句:</h6>
                <span class="badge bg-success">规则ID: ${currentRuleId}</span>
            </div>
        </div>
        <pre class="bg-light p-3 rounded"><code>${sql}</code></pre>
    `);
}

// 生成规则SQL
function generateRuleSQL(ruleId) {
    if (ruleId) {
        currentRuleId = ruleId;
    }
    
    if (!currentRuleId) {
        showAlert('请先选择一个规则', 'warning');
        return;
    }
    
    // 获取规则详情并生成SQL
    $.get(`/rules/api/rules/${currentRuleId}`, function(response) {
        if (response.success && response.data.length > 0) {
            const rule = response.data[0];
            const sql = generateSQLFromRule(rule);
            displayRuleSQL(sql);
            currentRuleSQL = sql;
            showAlert('SQL生成成功', 'success');
        } else {
            showAlert('获取规则信息失败', 'danger');
        }
    });
}

// 根据规则生成SQL
function generateSQLFromRule(rule) {
    const ruleType = rule.类型 || rule.规则类型 || '';
    let sql = '';
    
    if (ruleType.includes('药品')) {
        sql = generateDrugSQL(rule);
    } else if (ruleType.includes('诊疗')) {
        sql = generateTreatmentSQL(rule);
    } else {
        sql = generateGeneralSQL(rule);
    }
    
    return sql;
}

// 生成药品规则SQL
function generateDrugSQL(rule) {
    return `-- 药品规则SQL: ${rule.规则名称}
-- 行为认定: ${rule.行为认定}
-- 生成时间: ${new Date().toLocaleString()}

SELECT 
    就诊流水号,
    患者姓名,
    科室名称,
    医生姓名,
    药品名称,
    规格,
    数量,
    单价,
    金额,
    开药日期,
    '${rule.规则名称}' as 违规规则
FROM 药品使用明细
WHERE 药品编码 = '${rule.医保编码1 || ''}'
  ${rule.违规数量 ? `AND 数量 > ${rule.违规数量}` : ''}
  ${rule.违规金额 ? `AND 金额 > ${rule.违规金额}` : ''}
  AND 开药日期 BETWEEN '2024-01-01' AND '2024-12-31'
  ${rule.包含科室 ? `AND 科室名称 IN (${rule.包含科室.split(',').map(s => `'${s.trim()}'`).join(',')})` : ''}
  ${rule.排除科室 ? `AND 科室名称 NOT IN (${rule.排除科室.split(',').map(s => `'${s.trim()}'`).join(',')})` : ''}
ORDER BY 金额 DESC`;
}

// 生成诊疗规则SQL
function generateTreatmentSQL(rule) {
    return `-- 诊疗规则SQL: ${rule.规则名称}
-- 行为认定: ${rule.行为认定}
-- 生成时间: ${new Date().toLocaleString()}

SELECT 
    就诊流水号,
    患者姓名,
    科室名称,
    医生姓名,
    诊疗项目名称,
    项目编码,
    数量,
    单价,
    金额,
    执行日期,
    '${rule.规则名称}' as 违规规则
FROM 诊疗项目明细
WHERE 项目编码 = '${rule.医保编码1 || ''}'
  ${rule.违规数量 ? `AND 数量 > ${rule.违规数量}` : ''}
  ${rule.违规金额 ? `AND 金额 > ${rule.违规金额}` : ''}
  AND 执行日期 BETWEEN '2024-01-01' AND '2024-12-31'
  ${rule.包含科室 ? `AND 科室名称 IN (${rule.包含科室.split(',').map(s => `'${s.trim()}'`).join(',')})` : ''}
  ${rule.排除科室 ? `AND 科室名称 NOT IN (${rule.排除科室.split(',').map(s => `'${s.trim()}'`).join(',')})` : ''}
ORDER BY 金额 DESC`;
}

// 生成通用规则SQL
function generateGeneralSQL(rule) {
    return `-- 通用规则SQL: ${rule.规则名称}
-- 行为认定: ${rule.行为认定}
-- 生成时间: ${new Date().toLocaleString()}

SELECT 
    就诊流水号,
    患者姓名,
    科室名称,
    医生姓名,
    费用项目名称,
    医保编码,
    费用金额,
    就诊日期,
    '${rule.规则名称}' as 违规规则
FROM 费用明细表
WHERE 医保编码 = '${rule.医保编码1 || ''}'
  ${rule.违规金额 ? `AND 费用金额 > ${rule.违规金额}` : ''}
  AND 就诊日期 BETWEEN '2024-01-01' AND '2024-12-31'
  ${rule.包含科室 ? `AND 科室名称 IN (${rule.包含科室.split(',').map(s => `'${s.trim()}'`).join(',')})` : ''}
  ${rule.排除科室 ? `AND 科室名称 NOT IN (${rule.排除科室.split(',').map(s => `'${s.trim()}'`).join(',')})` : ''}
ORDER BY 费用金额 DESC`;
}

// 复制规则SQL
function copyRuleSQL() {
    if (!currentRuleSQL) {
        showAlert('没有可复制的SQL语句', 'warning');
        return;
    }
    
    navigator.clipboard.writeText(currentRuleSQL).then(function() {
        showAlert('SQL已复制到剪贴板', 'success');
    }, function() {
        showAlert('复制失败，请手动复制', 'danger');
    });
}

// 执行规则SQL
function executeRuleSQL() {
    if (!currentRuleSQL) {
        showAlert('没有可执行的SQL语句', 'warning');
        return;
    }
    
    $('#sqlResultContent').html(`
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">执行中...</span>
            </div>
            <p class="mt-2">正在执行SQL查询...</p>
        </div>
    `);
    
    $('#sqlResultModal').modal('show');
    
    $.ajax({
        url: '/database/api/execute_query',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            query: currentRuleSQL,
            export_format: 'json'
        }),
        success: function(response) {
            if (response.success) {
                sqlResultData = response;
                displaySQLExecutionResult(response);
            } else {
                $('#sqlResultContent').html(
                    '<div class="alert alert-danger">' +
                        '<i class="fas fa-exclamation-triangle"></i>' +
                        '执行失败: ' + response.error +
                    '</div>'
                );
            }
        },
        error: function() {
            $('#sqlResultContent').html(
                '<div class="alert alert-danger">' +
                    '<i class="fas fa-exclamation-triangle"></i>' +
                    '网络错误，请稍后重试' +
                '</div>'
            );
        }
    });
}

// 显示SQL执行结果
function displaySQLExecutionResult(response) {
    const { data, columns, total, displayed, truncated } = response;
    
    if (data.length === 0) {
        $('#sqlResultContent').html(`
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                查询结果为空
            </div>
        `);
        return;
    }
    
    // 生成表格
    let tableHtml = `
        <div class="mb-3">
            <span class="badge bg-primary">共 ${total} 条记录</span>
            ${truncated ? `<span class="badge bg-warning">显示前 ${displayed} 条</span>` : ''}
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover table-sm">
                <thead>
                    <tr>
    `;
    
    columns.forEach(column => {
        tableHtml += `<th>${column}</th>`;
    });
    
    tableHtml += `
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.forEach(row => {
        tableHtml += '<tr>';
        columns.forEach(column => {
            const value = row[column] || '';
            tableHtml += `<td>${value}</td>`;
        });
        tableHtml += '</tr>';
    });
    
    tableHtml += `
                </tbody>
            </table>
        </div>
    `;
    
    $('#sqlResultContent').html(tableHtml);
}

// 导出规则SQL
function exportRuleSQL() {
    if (!currentRuleSQL) {
        showAlert('没有可导出的SQL语句', 'warning');
        return;
    }
    
    $.ajax({
        url: '/database/api/execute_query',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            query: currentRuleSQL,
            export_format: 'excel'
        }),
        success: function(response) {
            if (response.success) {
                window.location.href = response.export_url;
                showAlert('Excel文件导出成功', 'success');
            } else {
                showAlert('导出失败: ' + response.error, 'danger');
            }
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'danger');
        }
    });
}

// 导出SQL执行结果
function exportSQLResult() {
    if (!sqlResultData) {
        showAlert('没有可导出的结果数据', 'warning');
        return;
    }
    
    exportRuleSQL(); // 重用导出SQL功能
}

// 导出系统规则
function exportSystemRules() {
    const filters = {
        rule_type: $('#ruleType').val(),
        behavior_type: $('#behaviorType').val(),
        scope_type: $('#scopeType').val()
    };
    
    // 构建查询参数
    const params = new URLSearchParams();
    Object.keys(filters).forEach(key => {
        if (filters[key]) {
            params.append(key, filters[key]);
        }
    });
    
    // 这里应该调用导出API
    showAlert('导出功能开发中...', 'info');
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部显示提示
    const container = $('.main-content');
    container.prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
