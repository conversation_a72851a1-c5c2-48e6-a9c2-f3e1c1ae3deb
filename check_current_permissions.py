#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def check_current_permissions():
    """检查当前权限表结构"""
    app = create_app()
    
    with app.app_context():
        try:
            print('=== 当前权限表完整结构 ===')
            
            # 直接使用SQL查询，避免ORM的复杂性
            result = db.session.execute(text("""
                SELECT id, name, code, module, resource_type, parent_id, sort_order, is_active
                FROM permissions 
                ORDER BY module, parent_id NULLS FIRST, sort_order
            """))
            
            permissions = result.fetchall()
            
            current_module = None
            current_parent = None
            
            for perm in permissions:
                # 模块分组显示
                if perm[3] != current_module:  # module
                    current_module = perm[3]
                    print(f'\n【{current_module.upper() if current_module else "UNKNOWN"}模块】')
                    current_parent = None
                
                # 父权限分组显示
                if perm[5] != current_parent:  # parent_id
                    current_parent = perm[5]
                    if perm[5] is None:
                        print(f'  ├─ 顶级权限:')
                    else:
                        # 查找父权限名称
                        parent_result = db.session.execute(text(
                            "SELECT name FROM permissions WHERE id = :parent_id"
                        ), {'parent_id': perm[5]})
                        parent_row = parent_result.fetchone()
                        parent_name = parent_row[0] if parent_row else "未知父权限"
                        print(f'  ├─ {parent_name} 的子权限:')
                
                # 显示权限详情
                indent = '    ' if perm[5] else '  '  # parent_id
                type_text = f'[{perm[4]}]' if perm[4] else '[未知类型]'  # resource_type
                active_text = '✅' if perm[7] else '❌'  # is_active
                print(f'{indent}│  {active_text} {perm[1]} ({perm[2]}) {type_text} 排序:{perm[6]}')
            
            # 统计信息
            print('\n=== 权限统计信息 ===')
            stats_result = db.session.execute(text("""
                SELECT 
                    module,
                    COUNT(*) as total_count,
                    SUM(CASE WHEN parent_id IS NULL THEN 1 ELSE 0 END) as top_level_count,
                    SUM(CASE WHEN parent_id IS NOT NULL THEN 1 ELSE 0 END) as child_count,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count
                FROM permissions 
                GROUP BY module
                ORDER BY module
            """))
            
            stats = stats_result.fetchall()
            for stat in stats:
                print(f'模块 {stat[0]}: 总计{stat[1]}个权限, 顶级{stat[2]}个, 子权限{stat[3]}个, 激活{stat[4]}个')
            
            # 检查是否有方案管理权限
            print('\n=== 方案管理权限检查 ===')
            schemes_result = db.session.execute(text("""
                SELECT id, name, code, parent_id, is_active
                FROM permissions 
                WHERE code LIKE 'selfcheck.schemes%'
                ORDER BY code
            """))
            
            schemes_perms = schemes_result.fetchall()
            if schemes_perms:
                print('✅ 方案管理权限已存在:')
                for perm in schemes_perms:
                    active_text = '✅' if perm[4] else '❌'
                    print(f'  {active_text} {perm[1]} ({perm[2]}) 父ID:{perm[3]}')
            else:
                print('❌ 方案管理权限不存在，需要创建')
                
        except Exception as e:
            print(f"检查失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_current_permissions()
