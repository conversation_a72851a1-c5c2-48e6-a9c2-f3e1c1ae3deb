import os
import logging
from datetime import datetime
from logging.handlers import RotatingFileHandler
from app import create_app, db
from app.models import User, Role, Permission, AuditLog
from flask_migrate import upgrade

# 创建logs目录
logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir)

# 生成日志文件名（按日期）
today = datetime.now().strftime('%Y-%m-%d')
log_file = os.path.join(logs_dir, f'micra_{today}.log')

# 配置日志格式
log_formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建文件处理器（按日期轮转）
file_handler = RotatingFileHandler(
    log_file,
    maxBytes=50*1024*1024,  # 50MB
    backupCount=10,
    encoding='utf-8'
)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(log_formatter)

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
console_handler.setFormatter(log_formatter)

# 配置根日志记录器
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# 设置特定模块的日志级别
logging.getLogger('werkzeug').setLevel(logging.INFO)  # 减少werkzeug的日志
logging.getLogger('app').setLevel(logging.DEBUG)  # 应用日志设为DEBUG级别

app = create_app(os.getenv('FLASK_CONFIG') or 'development')

# 设置应用日志级别
app.logger.setLevel(logging.DEBUG)

# 为Flask应用添加文件日志处理器
if not app.debug:
    app.logger.addHandler(file_handler)

@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User, 'Role': Role, 'Permission': Permission, 'AuditLog': AuditLog}

@app.cli.command()
def deploy():
    """运行部署任务"""
    # 创建数据库表
    #upgrade()
    
    # 创建基础权限数据
    create_permissions()
    
    # 创建默认角色
    create_roles()
    
    # 创建管理员用户
    create_admin_user()

def create_permissions():
    """创建基础权限数据"""
    permissions_data = [
        # 系统管理
        {'id': 1, 'name': '系统管理', 'code': 'system', 'description': '系统管理模块', 'module': 'system', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 1},
        {'id': 2, 'name': '用户管理', 'code': 'system.user', 'description': '用户管理', 'module': 'system', 'resource_type': 'menu', 'parent_id': 1, 'sort_order': 1},
        {'id': 3, 'name': '角色管理', 'code': 'system.role', 'description': '角色管理', 'module': 'system', 'resource_type': 'menu', 'parent_id': 1, 'sort_order': 2},
        {'id': 4, 'name': '权限管理', 'code': 'system.permission', 'description': '权限管理', 'module': 'system', 'resource_type': 'menu', 'parent_id': 1, 'sort_order': 3},
        
        # 用户管理权限
        {'id': 10, 'name': '查看用户', 'code': 'system.user.view', 'description': '查看用户列表', 'module': 'system', 'resource_type': 'page', 'parent_id': 2, 'sort_order': 1},
        {'id': 11, 'name': '创建用户', 'code': 'system.user.create', 'description': '创建新用户', 'module': 'system', 'resource_type': 'button', 'parent_id': 2, 'sort_order': 2},
        {'id': 12, 'name': '编辑用户', 'code': 'system.user.edit', 'description': '编辑用户信息', 'module': 'system', 'resource_type': 'button', 'parent_id': 2, 'sort_order': 3},
        {'id': 13, 'name': '删除用户', 'code': 'system.user.delete', 'description': '删除用户', 'module': 'system', 'resource_type': 'button', 'parent_id': 2, 'sort_order': 4},
        {'id': 14, 'name': '重置密码', 'code': 'system.user.reset_password', 'description': '重置用户密码', 'module': 'system', 'resource_type': 'button', 'parent_id': 2, 'sort_order': 5},
        
        # 规则管理
        {'id': 20, 'name': '规则管理', 'code': 'rules', 'description': '规则管理模块', 'module': 'rules', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 2},
        {'id': 21, 'name': '飞检规则知识库', 'code': 'rules.knowledge_base', 'description': '飞检规则知识库', 'module': 'rules', 'resource_type': 'menu', 'parent_id': 20, 'sort_order': 1},
        {'id': 22, 'name': '规则SQL生成器', 'code': 'rules.sql_generator', 'description': '规则SQL生成器', 'module': 'rules', 'resource_type': 'menu', 'parent_id': 20, 'sort_order': 2},
        {'id': 23, 'name': '系统规则语句', 'code': 'rules.system_rules', 'description': '系统规则语句', 'module': 'rules', 'resource_type': 'menu', 'parent_id': 20, 'sort_order': 3},
        
        # 数据库工具
        {'id': 30, 'name': '数据库工具', 'code': 'database', 'description': '数据库工具模块', 'module': 'database', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 3},
        {'id': 31, 'name': 'SQL生成器', 'code': 'database.sql_generator', 'description': 'SQL生成器', 'module': 'database', 'resource_type': 'menu', 'parent_id': 30, 'sort_order': 1},
        {'id': 32, 'name': '数据库查询生成Excel', 'code': 'database.query', 'description': '数据库查询生成Excel', 'module': 'database', 'resource_type': 'menu', 'parent_id': 30, 'sort_order': 2},
        {'id': 33, 'name': '批量SQL查询生成Excel', 'code': 'database.batch_query', 'description': '批量SQL查询生成Excel', 'module': 'database', 'resource_type': 'menu', 'parent_id': 30, 'sort_order': 3},
        {'id': 34, 'name': 'SQL性能测试', 'code': 'database.performance', 'description': 'SQL性能测试', 'module': 'database', 'resource_type': 'menu', 'parent_id': 30, 'sort_order': 4},
        
        # Excel工具
        {'id': 40, 'name': 'Excel工具', 'code': 'excel', 'description': 'Excel工具模块', 'module': 'excel', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 4},
        {'id': 41, 'name': 'Excel文件拆分', 'code': 'excel.splitter', 'description': 'Excel文件拆分', 'module': 'excel', 'resource_type': 'menu', 'parent_id': 40, 'sort_order': 1},
        {'id': 42, 'name': 'Excel内容删除', 'code': 'excel.delete', 'description': 'Excel内容删除', 'module': 'excel', 'resource_type': 'menu', 'parent_id': 40, 'sort_order': 2},
        {'id': 43, 'name': 'Excel比对工具', 'code': 'excel.compare', 'description': 'Excel比对工具', 'module': 'excel', 'resource_type': 'menu', 'parent_id': 40, 'sort_order': 3},
        {'id': 44, 'name': 'Excel转SQL工具', 'code': 'excel.to_sql', 'description': 'Excel转SQL工具', 'module': 'excel', 'resource_type': 'menu', 'parent_id': 40, 'sort_order': 4},
        
        # 数据处理工具
        {'id': 50, 'name': '数据处理', 'code': 'data', 'description': '数据处理模块', 'module': 'data', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 5},
        {'id': 51, 'name': '查找重复文件', 'code': 'data.find_duplicates', 'description': '查找重复文件', 'module': 'data', 'resource_type': 'menu', 'parent_id': 50, 'sort_order': 1},
        {'id': 52, 'name': '数据校验', 'code': 'data.validator', 'description': '数据校验', 'module': 'data', 'resource_type': 'menu', 'parent_id': 50, 'sort_order': 2},
        {'id': 53, 'name': '数据标准化', 'code': 'data.standardization', 'description': '数据标准化', 'module': 'data', 'resource_type': 'menu', 'parent_id': 50, 'sort_order': 3},

        # 自查自纠模块
        {'id': 60, 'name': '自查自纠', 'code': 'selfcheck', 'description': '自查自纠模块', 'module': 'selfcheck', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 6},
        {'id': 61, 'name': '规则管理', 'code': 'selfcheck.rules', 'description': '自查规则管理', 'module': 'selfcheck', 'resource_type': 'menu', 'parent_id': 60, 'sort_order': 1},
        {'id': 62, 'name': '规则导入', 'code': 'selfcheck.rules.import', 'description': '规则导入', 'module': 'selfcheck', 'resource_type': 'menu', 'parent_id': 60, 'sort_order': 2},
        {'id': 63, 'name': '数据上传', 'code': 'selfcheck.upload', 'description': '数据上传', 'module': 'selfcheck', 'resource_type': 'menu', 'parent_id': 60, 'sort_order': 3},
        {'id': 64, 'name': '自查任务', 'code': 'selfcheck.tasks', 'description': '自查任务', 'module': 'selfcheck', 'resource_type': 'menu', 'parent_id': 60, 'sort_order': 4},

        # 自查自纠权限
        {'id': 70, 'name': '查看规则', 'code': 'selfcheck.rules.view', 'description': '查看规则列表', 'module': 'selfcheck', 'resource_type': 'page', 'parent_id': 61, 'sort_order': 1},
        {'id': 71, 'name': '创建规则', 'code': 'selfcheck.rules.create', 'description': '创建新规则', 'module': 'selfcheck', 'resource_type': 'button', 'parent_id': 61, 'sort_order': 2},
        {'id': 72, 'name': '编辑规则', 'code': 'selfcheck.rules.edit', 'description': '编辑规则信息', 'module': 'selfcheck', 'resource_type': 'button', 'parent_id': 61, 'sort_order': 3},
        {'id': 73, 'name': '删除规则', 'code': 'selfcheck.rules.delete', 'description': '删除规则', 'module': 'selfcheck', 'resource_type': 'button', 'parent_id': 61, 'sort_order': 4},
        {'id': 74, 'name': '导入规则', 'code': 'selfcheck.rules.import', 'description': '从历史规则导入', 'module': 'selfcheck', 'resource_type': 'button', 'parent_id': 62, 'sort_order': 1},
        {'id': 75, 'name': '查看上传', 'code': 'selfcheck.upload.view', 'description': '查看上传记录', 'module': 'selfcheck', 'resource_type': 'page', 'parent_id': 63, 'sort_order': 1},
        {'id': 76, 'name': '创建上传', 'code': 'selfcheck.upload.create', 'description': '上传文件', 'module': 'selfcheck', 'resource_type': 'button', 'parent_id': 63, 'sort_order': 2},
        {'id': 77, 'name': '删除上传', 'code': 'selfcheck.upload.delete', 'description': '删除上传记录', 'module': 'selfcheck', 'resource_type': 'button', 'parent_id': 63, 'sort_order': 3},
        {'id': 78, 'name': '查看任务', 'code': 'selfcheck.tasks.view', 'description': '查看任务列表', 'module': 'selfcheck', 'resource_type': 'page', 'parent_id': 64, 'sort_order': 1},
    ]
    
    for perm_data in permissions_data:
        permission = Permission.query.filter_by(code=perm_data['code']).first()
        if not permission:
            permission = Permission(**perm_data)
            db.session.add(permission)
    
    db.session.commit()

def create_roles():
    """创建默认角色"""
    roles_data = [
        {'name': '超级管理员', 'description': '系统最高权限'},
        {'name': '系统管理员', 'description': '用户和权限管理'},
        {'name': '规则管理员', 'description': '规则相关功能'},
        {'name': '数据分析师', 'description': '数据处理和分析'},
        {'name': '普通用户', 'description': '基本功能使用'},
    ]
    
    for role_data in roles_data:
        role = Role.query.filter_by(name=role_data['name']).first()
        if not role:
            role = Role(**role_data)
            db.session.add(role)
    
    db.session.commit()

def create_admin_user():
    """创建管理员用户"""
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            real_name='系统管理员',
            is_admin=True
        )
        admin.set_password('Admin123!')
        db.session.add(admin)
        db.session.commit()
        print('管理员用户已创建: admin / Admin123!')

if __name__ == '__main__':
    print("=== MICRA应用启动中 ===", flush=True)
    print(f"应用配置: {app.config.get('ENV', 'unknown')}", flush=True)
    print(f"调试模式: {app.debug}", flush=False)
    print(f"日志级别: {app.logger.level}", flush=True)
    print(f"日志文件: {log_file}", flush=False)

    # 测试日志输出
    app.logger.info("=== MICRA应用启动 ===")
    app.logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    app.logger.info(f"日志文件: {log_file}")
    app.logger.info(f"应用配置: {app.config.get('ENV', 'unknown')}")
    app.logger.info(f"调试模式: {app.debug}")
    app.logger.debug("这是调试日志")
    app.logger.warning("这是警告日志")
    app.logger.error("这是错误日志")

    print("=== 启动Flask服务器 ===", flush=True)
    app.logger.info("启动Flask开发服务器...")
    app.run(host='0.0.0.0', port=5002, debug=False)
