#!/usr/bin/env python3
"""
设置任务为已完成状态用于测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def set_task_completed():
    """设置任务为已完成状态"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            # 查看所有任务的状态
            query = "SELECT id, task_name, status FROM selfcheck_tasks ORDER BY id"
            tasks = db_manager.execute_query(query)
            
            print("所有任务状态:")
            for task in tasks:
                print(f"  ID: {task['id']}, 名称: {task['task_name']}, 状态: {task['status']}")
            
            # 将第一个任务设置为已完成状态用于测试
            if tasks:
                first_task_id = tasks[0]['id']
                update_query = "UPDATE selfcheck_tasks SET status = 'completed' WHERE id = :task_id"
                db_manager.execute_update(update_query, {'task_id': first_task_id})
                print(f"\n✅ 已将任务 {first_task_id} 设置为completed状态用于测试")
                return first_task_id
            else:
                print("❌ 没有找到任务")
                return None
                
        except Exception as e:
            print(f"❌ 设置任务状态失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == '__main__':
    set_task_completed()
