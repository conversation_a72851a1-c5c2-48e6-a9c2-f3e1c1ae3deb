# 规则导入模块测试用例

## 测试模块：规则导入
**测试负责人：** 测试工程师  
**测试时间：** 2025-06-08  
**测试版本：** V1.0  

## 1. 功能概述

规则导入模块负责从历史规则库（rule_sql_history表）导入规则到当前系统中。

## 2. 测试用例

### TC017 - 可导入规则列表查询
**测试目标：** 验证可导入规则列表的正确显示  
**前置条件：** rule_sql_history表中有测试数据  
**测试步骤：**
1. 访问规则导入页面 `/selfcheck/rules/import`
2. 验证页面正常加载
3. 验证可导入规则列表显示
4. 验证分页功能
5. 验证排序功能

**预期结果：**
- 页面正常加载
- 规则列表正确显示历史规则
- 分页和排序功能正常
- 显示规则的导入状态

**测试数据：**
- rule_sql_history表中至少50条记录
- 包含已导入和未导入的规则

---

### TC018 - 规则搜索和过滤
**测试目标：** 验证规则导入页面的搜索过滤功能  
**前置条件：** 历史规则数据充足  
**测试步骤：**
1. 测试规则名称搜索
2. 测试城市过滤
3. 测试规则来源过滤
4. 测试规则类型过滤
5. 测试用途（visit_type）过滤
6. 测试导入状态过滤
7. 测试组合过滤条件

**预期结果：**
- 所有过滤条件正确生效
- 搜索结果准确
- 过滤选项从数据库动态加载
- 组合过滤结果正确

**测试数据：**
```json
{
  "search_conditions": {
    "rule_name": "重复收费",
    "cities": ["北京", "上海", "深圳"],
    "rule_sources": ["飞检规则", "内控规则", "监管规则"],
    "rule_types": ["重复收费", "频次上限", "金额限制"],
    "visit_types": ["住院", "门诊"],
    "import_status": ["已导入", "未导入"]
  }
}
```

---

### TC019 - 单个规则导入
**测试目标：** 验证单个规则的导入功能  
**前置条件：** 存在未导入的历史规则  
**测试步骤：**
1. 在规则列表中选择一个未导入的规则
2. 点击导入按钮
3. 确认导入操作
4. 验证导入进度显示
5. 验证导入结果

**预期结果：**
- 导入操作成功执行
- 进度显示正确
- 规则成功添加到selfcheck_rules表
- 历史记录标记为已导入
- 导入后规则信息完整

---

### TC020 - 批量规则导入
**测试目标：** 验证批量规则导入功能  
**前置条件：** 存在多个未导入的历史规则  
**测试步骤：**
1. 使用全选功能选择所有规则
2. 使用复选框选择多个规则（5-20个）
3. 点击批量导入按钮
4. 监控导入进度
5. 验证导入结果

**预期结果：**
- 批量选择功能正常
- 导入进度实时更新
- 所有选中规则成功导入
- 导入统计信息准确
- 错误处理机制正常

**性能要求：**
- 20个规则导入时间 < 30秒
- 进度更新及时

---

### TC021 - 导入进度监控
**测试目标：** 验证导入进度的实时监控  
**前置条件：** 执行批量导入操作  
**测试步骤：**
1. 启动批量导入任务
2. 观察进度条更新
3. 验证已完成数量显示
4. 验证剩余时间估算
5. 验证导入速度显示

**预期结果：**
- 进度条实时更新
- 统计信息准确
- 时间估算合理
- 界面响应流畅

---

### TC022 - 导入错误处理
**测试目标：** 验证导入过程中的错误处理  
**前置条件：** 准备有问题的测试数据  
**测试步骤：**
1. 导入包含无效数据的规则
2. 导入重复的规则
3. 模拟数据库连接异常
4. 验证错误信息显示
5. 验证部分成功的处理

**预期结果：**
- 错误信息详细准确
- 部分成功的导入正确处理
- 失败的规则不影响成功的规则
- 错误日志记录完整

**测试数据：**
```json
{
  "error_cases": [
    {
      "type": "invalid_sql",
      "description": "包含无效SQL语句的规则"
    },
    {
      "type": "duplicate_rule",
      "description": "重复的规则代码"
    },
    {
      "type": "missing_required_field",
      "description": "缺少必填字段的规则"
    }
  ]
}
```

---

### TC023 - 导入状态管理
**测试目标：** 验证规则导入状态的正确管理  
**前置条件：** 执行导入操作  
**测试步骤：**
1. 验证导入前规则状态
2. 执行导入操作
3. 验证导入后状态更新
4. 验证重复导入的处理
5. 验证状态过滤功能

**预期结果：**
- 导入状态准确更新
- 重复导入被正确阻止
- 状态过滤功能正常
- 导入时间记录准确

---

### TC024 - 数据完整性验证
**测试目标：** 验证导入数据的完整性  
**前置条件：** 完成规则导入  
**测试步骤：**
1. 对比原始数据和导入后数据
2. 验证所有字段正确映射
3. 验证SQL语句完整性
4. 验证关联关系正确
5. 验证数据格式转换

**预期结果：**
- 数据映射100%准确
- SQL语句完整无损
- 字段格式正确转换
- 关联关系保持一致

---

### TC025 - 权限控制测试
**测试目标：** 验证规则导入的权限控制  
**前置条件：** 不同权限的测试用户  
**测试步骤：**
1. 使用无导入权限用户访问
2. 使用有导入权限用户操作
3. 验证权限检查机制
4. 验证操作日志记录

**预期结果：**
- 权限控制严格有效
- 无权限用户被正确阻止
- 操作日志完整记录
- 权限提示友好

---

### TC026 - 导入历史记录
**测试目标：** 验证导入操作的历史记录  
**前置条件：** 执行多次导入操作  
**测试步骤：**
1. 查看导入历史记录
2. 验证记录详细信息
3. 验证操作时间记录
4. 验证操作用户记录
5. 验证导入结果统计

**预期结果：**
- 历史记录完整准确
- 时间戳记录正确
- 用户信息准确
- 统计数据正确

## 3. 性能测试用例

### TC027 - 大批量导入性能
**测试目标：** 验证大批量规则导入的性能  
**测试数据：** 100+条规则同时导入  
**性能指标：** 
- 导入速度 > 5条/秒
- 内存使用稳定
- 数据库连接池正常

### TC028 - 并发导入测试
**测试目标：** 验证多用户同时导入的处理  
**测试场景：** 3个用户同时执行导入操作  
**性能指标：** 
- 系统稳定运行
- 数据一致性保证
- 无死锁现象

## 4. 异常测试用例

### TC029 - 网络中断恢复
**测试目标：** 验证导入过程中网络中断的处理  
**测试步骤：** 
1. 启动批量导入
2. 模拟网络中断
3. 恢复网络连接
4. 验证导入状态和数据一致性

### TC030 - 数据库异常恢复
**测试目标：** 验证数据库异常时的处理  
**测试步骤：** 
1. 启动导入操作
2. 模拟数据库连接异常
3. 恢复数据库连接
4. 验证事务回滚和数据一致性

## 5. 兼容性测试用例

### TC031 - 数据格式兼容性
**测试目标：** 验证不同格式历史数据的导入  
**测试数据：** 包含各种数据格式的历史规则

### TC032 - 版本兼容性
**测试目标：** 验证不同版本历史数据的兼容性  
**测试范围：** 历史版本的规则数据格式

## 6. 自动化测试脚本

对应的自动化测试脚本：`test_scripts/test_rules_import.py`

## 7. 测试数据

测试数据文件：`test_data/import_rules_data.json`
