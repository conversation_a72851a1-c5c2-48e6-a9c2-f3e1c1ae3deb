#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def cleanup_duplicate_permissions():
    """清理重复的权限"""
    app = create_app()
    
    with app.app_context():
        try:
            print('=== 清理重复权限 ===')
            
            # 删除旧的 selfcheck.upload 相关权限
            old_upload_permissions = [
                'selfcheck.upload.view',
                'selfcheck.upload.create', 
                'selfcheck.upload.delete'
            ]
            
            for perm_code in old_upload_permissions:
                result = db.session.execute(text("""
                    SELECT id, name FROM permissions WHERE code = :code
                """), {'code': perm_code})
                
                perm = result.fetchone()
                if perm:
                    print(f'删除旧权限: {perm[1]} ({perm_code})')
                    
                    # 先删除角色权限关联
                    db.session.execute(text("""
                        DELETE FROM role_permissions WHERE permission_id = :perm_id
                    """), {'perm_id': perm[0]})
                    
                    # 删除权限
                    db.session.execute(text("""
                        DELETE FROM permissions WHERE id = :perm_id
                    """), {'perm_id': perm[0]})
                else:
                    print(f'权限不存在: {perm_code}')
            
            # 修正模块排序，让自查自纠排在第一位
            print('\n=== 修正模块排序 ===')
            module_order = {
                'selfcheck': 1,
                'rules': 2,
                'database': 3,
                'excel': 4,
                'data': 5,
                'system': 6
            }
            
            for module, sort_order in module_order.items():
                db.session.execute(text("""
                    UPDATE permissions 
                    SET sort_order = :sort_order 
                    WHERE parent_id IS NULL AND module = :module
                """), {'sort_order': sort_order, 'module': module})
                print(f'更新模块排序: {module} -> {sort_order}')
            
            db.session.commit()
            print('\n✅ 权限清理完成！')
            
            # 验证清理结果
            print('\n=== 验证清理结果 ===')
            result = db.session.execute(text("""
                SELECT code, COUNT(*) as count
                FROM permissions 
                WHERE code LIKE 'selfcheck.upload%'
                GROUP BY code
                ORDER BY code
            """))
            
            upload_perms = result.fetchall()
            for perm in upload_perms:
                print(f'权限: {perm[0]} - 数量: {perm[1]}')
            
            # 显示最终的模块排序
            print('\n=== 最终模块排序 ===')
            result = db.session.execute(text("""
                SELECT name, code, module, sort_order
                FROM permissions 
                WHERE parent_id IS NULL
                ORDER BY sort_order
            """))
            
            modules = result.fetchall()
            for module in modules:
                print(f'{module[3]}. {module[0]} ({module[1]}) - 模块: {module[2]}')
                
        except Exception as e:
            print(f"清理失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    cleanup_duplicate_permissions()
