# 方案管理模块测试用例

## 测试模块：方案管理
**测试负责人：** 测试工程师  
**测试时间：** 2025-06-08  
**测试版本：** V1.0  

## 1. 功能概述

方案管理模块负责创建、编辑、删除检查方案，以及管理方案中的规则配置。

## 2. 测试用例

### TC033 - 方案列表查询
**测试目标：** 验证方案列表的正确显示和分页功能  
**前置条件：** 用户已登录，具有方案查看权限  
**测试步骤：**
1. 访问方案管理页面 `/selfcheck/schemes`
2. 验证页面正常加载
3. 验证方案列表显示
4. 验证分页功能
5. 验证排序功能

**预期结果：**
- 页面正常加载，显示方案列表
- 分页控件正常工作
- 排序功能正常
- 显示方案基本信息（名称、描述、规则数量、状态等）

**测试数据：**
- 至少10个测试方案
- 包含不同状态的方案（active、inactive）

---

### TC034 - 方案搜索功能
**测试目标：** 验证方案搜索和过滤功能  
**前置条件：** 方案列表中有测试数据  
**测试步骤：**
1. 在方案名称搜索框输入关键词
2. 点击搜索按钮
3. 验证搜索结果
4. 测试状态过滤
5. 测试创建时间范围过滤
6. 测试组合搜索

**预期结果：**
- 搜索结果准确匹配关键词
- 过滤条件正确生效
- 组合搜索结果正确
- 无结果时显示友好提示

---

### TC035 - 创建新方案
**测试目标：** 验证方案创建功能  
**前置条件：** 用户具有方案创建权限  
**测试步骤：**
1. 点击"新增方案"按钮
2. 填写方案基本信息
3. 点击保存
4. 验证方案创建成功

**预期结果：**
- 创建表单正常显示
- 必填字段验证正确
- 方案保存成功
- 返回方案列表并显示新方案

**测试数据：**
```json
{
  "scheme_name": "测试方案001",
  "scheme_code": "TEST_SCHEME_001",
  "description": "这是一个测试方案",
  "status": "active"
}
```

---

### TC036 - 编辑方案基本信息
**测试目标：** 验证方案基本信息编辑功能  
**前置条件：** 存在可编辑的方案  
**测试步骤：**
1. 在方案列表中点击"编辑"按钮
2. 修改方案信息
3. 点击保存
4. 验证修改成功

**预期结果：**
- 编辑表单正确显示原有数据
- 修改保存成功
- 方案信息更新正确

---

### TC037 - 方案规则管理
**测试目标：** 验证方案中规则的添加、删除和排序  
**前置条件：** 存在方案和可用规则  
**测试步骤：**
1. 进入方案详情页面
2. 点击"添加规则"按钮
3. 从规则列表中选择规则
4. 确认添加
5. 验证规则添加成功
6. 测试规则删除功能
7. 测试规则排序功能

**预期结果：**
- 规则选择界面正常显示
- 规则添加成功
- 规则删除功能正常
- 规则排序功能正常
- 方案规则列表实时更新

---

### TC038 - 批量添加规则
**测试目标：** 验证批量添加规则到方案的功能  
**前置条件：** 存在方案和多个可用规则  
**测试步骤：**
1. 在方案详情页面点击"批量添加规则"
2. 使用搜索和过滤功能筛选规则
3. 选择多个规则（使用复选框）
4. 点击"添加选中规则"
5. 验证批量添加结果

**预期结果：**
- 规则选择界面支持批量操作
- 搜索过滤功能正常
- 批量添加操作成功
- 添加进度显示正确
- 重复规则处理正确

---

### TC039 - 方案规则检索
**测试目标：** 验证方案内规则的检索功能  
**前置条件：** 方案中包含多个规则  
**测试步骤：**
1. 在方案规则列表中使用搜索功能
2. 测试按规则名称搜索
3. 测试按规则类型过滤
4. 测试按状态过滤
5. 验证搜索结果

**预期结果：**
- 方案内搜索功能正常
- 过滤条件正确生效
- 搜索结果准确
- 搜索性能良好

---

### TC040 - 方案状态管理
**测试目标：** 验证方案状态的管理功能  
**前置条件：** 存在不同状态的方案  
**测试步骤：**
1. 测试启用方案功能
2. 测试禁用方案功能
3. 验证状态变更的影响
4. 测试状态过滤功能

**预期结果：**
- 状态变更操作成功
- 状态变更影响正确
- 禁用的方案不能用于任务创建
- 状态过滤功能正常

---

### TC041 - 方案复制功能
**测试目标：** 验证方案复制功能  
**前置条件：** 存在完整配置的方案  
**测试步骤：**
1. 选择要复制的方案
2. 点击"复制方案"按钮
3. 修改新方案名称
4. 确认复制操作
5. 验证复制结果

**预期结果：**
- 复制操作成功执行
- 新方案包含原方案所有规则
- 新方案信息正确
- 规则关联关系正确

---

### TC042 - 方案删除功能
**测试目标：** 验证方案删除功能和约束检查  
**前置条件：** 存在可删除的方案  
**测试步骤：**
1. 选择未被任务使用的方案
2. 点击删除按钮
3. 确认删除操作
4. 选择被任务使用的方案
5. 尝试删除并验证约束检查

**预期结果：**
- 未使用的方案可以正常删除
- 被使用的方案删除被阻止
- 约束检查提示友好
- 删除操作记录日志

---

### TC043 - 方案导出功能
**测试目标：** 验证方案配置的导出功能  
**前置条件：** 存在完整配置的方案  
**测试步骤：**
1. 选择要导出的方案
2. 点击"导出方案"按钮
3. 选择导出格式
4. 确认导出操作
5. 验证导出文件

**预期结果：**
- 导出功能正常工作
- 导出文件格式正确
- 导出数据完整
- 包含方案和规则信息

---

### TC044 - 方案导入功能
**测试目标：** 验证方案配置的导入功能  
**前置条件：** 有有效的方案导出文件  
**测试步骤：**
1. 点击"导入方案"按钮
2. 选择导入文件
3. 验证文件格式
4. 确认导入操作
5. 验证导入结果

**预期结果：**
- 文件格式验证正确
- 导入操作成功
- 方案和规则正确创建
- 重复方案处理正确

## 3. 性能测试用例

### TC045 - 大量规则方案性能
**测试目标：** 验证包含大量规则的方案性能  
**测试数据：** 方案包含100+个规则  
**性能指标：** 
- 方案加载时间 < 5秒
- 规则列表响应时间 < 3秒

### TC046 - 批量操作性能
**测试目标：** 验证批量添加规则的性能  
**测试数据：** 一次添加50+个规则  
**性能指标：** 
- 批量添加时间 < 30秒
- 界面响应流畅

## 4. 异常测试用例

### TC047 - 并发编辑冲突
**测试目标：** 验证多用户同时编辑方案的处理  
**测试步骤：** 
1. 两个用户同时编辑同一方案
2. 验证冲突检测和处理

### TC048 - 数据一致性验证
**测试目标：** 验证方案和规则关联的数据一致性  
**测试步骤：** 
1. 删除方案中使用的规则
2. 验证数据一致性处理

## 5. 自动化测试脚本

对应的自动化测试脚本：`test_scripts/test_scheme_management.py`

## 6. 测试数据

测试数据文件：`test_data/test_schemes.json`
