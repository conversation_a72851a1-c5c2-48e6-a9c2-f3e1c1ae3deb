{% extends "base.html" %}

{% block title %}主页 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-home"></i> 主页</h4>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-tools"></i> 欢迎使用MICRA飞检数据处理工具箱
                </h5>
                <p class="card-text">
                    您好，{{ current_user.real_name or current_user.username }}！欢迎使用MICRA飞检数据处理工具箱。
                    本系统提供了完整的飞检数据处理功能，包括规则管理、数据库工具、Excel处理和数据分析等模块。
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <h5><i class="fas fa-th-large"></i> 功能模块</h5>
    </div>
</div>

<div class="row">
    <!-- 1. 自查自纠 -->
    {% if current_user.has_permission('selfcheck') %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-search-plus text-purple"></i> 自查自纠
                </h6>
                <p class="card-text">规则管理、数据上传、自查任务</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('selfcheck.index') }}" class="btn btn-purple btn-sm">
                        <i class="fas fa-arrow-right"></i> 进入自查自纠
                    </a>
                    {% if current_user.has_permission('selfcheck.rules.view') %}
                    <a href="{{ url_for('selfcheck.rules') }}" class="btn btn-outline-purple btn-sm">
                        <i class="fas fa-cogs"></i> 规则管理
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('selfcheck.schemes.view') %}
                    <a href="{{ url_for('selfcheck.schemes') }}" class="btn btn-outline-purple btn-sm">
                        <i class="fas fa-project-diagram"></i> 方案配置
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('selfcheck.uploads.view') %}
                    <a href="{{ url_for('selfcheck.uploads') }}" class="btn btn-outline-purple btn-sm">
                        <i class="fas fa-upload"></i> 数据上传
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('selfcheck.tasks.view') %}
                    <a href="{{ url_for('selfcheck.tasks') }}" class="btn btn-outline-purple btn-sm">
                        <i class="fas fa-tasks"></i> 自查任务
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 2. 规则管理 -->
    {% if current_user.has_permission('rules') %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-book text-primary"></i> 规则管理
                </h6>
                <p class="card-text">飞检规则知识库、规则SQL生成器、系统规则管理</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('rules.index') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-arrow-right"></i> 进入规则管理
                    </a>
                    {% if current_user.has_permission('rules.knowledge_base') %}
                    <a href="{{ url_for('rules.knowledge_base') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-book"></i> 规则知识库
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('rules.sql_generator') %}
                    <a href="{{ url_for('rules.sql_generator') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-code"></i> 规则SQL生成器
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('rules.system_rules') %}
                    <a href="{{ url_for('rules.system_rules') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-cogs"></i> 系统规则语句
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 3. 数据库工具 -->
    {% if current_user.has_permission('database') %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-database text-success"></i> 数据库工具
                </h6>
                <p class="card-text">SQL生成、数据库查询、批量处理、性能测试</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('database.index') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-arrow-right"></i> 进入数据库工具
                    </a>
                    {% if current_user.has_permission('database.sql_generator') %}
                    <a href="{{ url_for('database.sql_generator') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-database"></i> SQL生成器
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('database.query') %}
                    <a href="{{ url_for('database.query') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-table"></i> 数据库查询
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('database.batch_query') %}
                    <a href="{{ url_for('database.batch_query') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-tasks"></i> 批量查询
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('database.performance_test') %}
                    <a href="{{ url_for('database.performance_test') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-tachometer-alt"></i> SQL性能测试
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 4. Excel工具 -->
    {% if current_user.has_permission('excel') %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-file-excel text-warning"></i> Excel工具
                </h6>
                <p class="card-text">Excel文件拆分、内容删除、比对、转SQL</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('excel.index') }}" class="btn btn-warning btn-sm">
                        <i class="fas fa-arrow-right"></i> 进入Excel工具
                    </a>
                    {% if current_user.has_permission('excel.splitter') %}
                    <a href="{{ url_for('excel.splitter') }}" class="btn btn-outline-warning btn-sm">
                        <i class="far fa-file-excel"></i> 文件拆分
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('excel.delete') %}
                    <a href="{{ url_for('excel.delete') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-eraser"></i> 内容删除
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('excel.compare') %}
                    <a href="{{ url_for('excel.compare') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-file-excel"></i> 文件比对
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('excel.to_sql') %}
                    <a href="{{ url_for('excel.to_sql') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-file-code"></i> 转SQL
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 5. 数据处理 -->
    {% if current_user.has_permission('data') %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-chart-bar text-info"></i> 数据处理
                </h6>
                <p class="card-text">重复文件查找、数据校验、数据标准化</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('data.index') }}" class="btn btn-info btn-sm">
                        <i class="fas fa-arrow-right"></i> 进入数据处理
                    </a>
                    {% if current_user.has_permission('data.find_duplicates') %}
                    <a href="{{ url_for('data.find_duplicates') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-file-alt"></i> 查找重复文件
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('data.validator') %}
                    <a href="{{ url_for('data.validator') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-check-circle"></i> 数据校验
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('data.standardization') %}
                    <a href="{{ url_for('data.standardization') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-cogs"></i> 数据标准化
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}



    <!-- 6. 系统管理 -->
    {% if current_user.has_permission('system') %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-cog text-danger"></i> 系统管理
                </h6>
                <p class="card-text">用户管理、角色权限、审计日志</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin.index') }}" class="btn btn-danger btn-sm">
                        <i class="fas fa-arrow-right"></i> 进入系统管理
                    </a>
                    {% if current_user.has_permission('system.user.view') %}
                    <a href="{{ url_for('admin.users') }}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-users"></i> 用户管理
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('system.role.view') %}
                    <a href="{{ url_for('admin.roles') }}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-user-tag"></i> 角色管理
                    </a>
                    {% endif %}
                    {% if current_user.has_permission('system.audit.view') %}
                    <a href="{{ url_for('admin.audit_logs') }}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-history"></i> 审计日志
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle"></i> 系统信息
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>当前用户：</strong>{{ current_user.real_name or current_user.username }}</p>
                        <p><strong>用户角色：</strong>
                            {% if current_user.is_admin %}
                                <span class="badge bg-danger">超级管理员</span>
                            {% else %}
                                {% for role in current_user.roles %}
                                    <span class="badge bg-primary">{{ role.name }}</span>
                                {% endfor %}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>上次登录：</strong>
                            {% if current_user.last_login %}
                                {{ current_user.last_login.strftime('%Y-%m-%d %H:%M:%S') }}
                            {% else %}
                                首次登录
                            {% endif %}
                        </p>
                        <p><strong>所属部门：</strong>{{ current_user.department or '未设置' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
