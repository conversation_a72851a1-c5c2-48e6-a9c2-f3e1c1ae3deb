from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect
from flask_caching import Cache
from config import config

# 初始化扩展
db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()
csrf = CSRFProtect()
cache = Cache()

def create_app(config_name='development'):
    """应用工厂函数"""
    import os

    # 初始化Oracle客户端为thick模式
    try:
        import oracledb
        oracledb.init_oracle_client()
        print("Oracle客户端初始化为thick模式成功")
    except Exception as e:
        print(f"Oracle客户端初始化失败: {e}")

    # 设置静态文件路径为根目录的static文件夹
    static_folder = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static')
    template_folder = os.path.join(os.path.dirname(__file__), 'templates')

    app = Flask(__name__, static_folder=static_folder, template_folder=template_folder)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    csrf.init_app(app)
    cache.init_app(app)
    
    # 配置登录管理器
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        from app.models.user import User
        return User.query.get(int(user_id))
    
    # 注册蓝图
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')
    
    from app.rules import bp as rules_bp
    app.register_blueprint(rules_bp, url_prefix='/rules')
    
    from app.database import bp as database_bp
    app.register_blueprint(database_bp, url_prefix='/database')
    
    from app.excel import bp as excel_bp
    app.register_blueprint(excel_bp, url_prefix='/excel')
    
    from app.data import bp as data_bp
    app.register_blueprint(data_bp, url_prefix='/data')

    from app.selfcheck import bp as selfcheck_bp
    app.register_blueprint(selfcheck_bp, url_prefix='/selfcheck')

    # 全局错误处理
    @app.errorhandler(404)
    def not_found_error(error):
        from flask import render_template
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        from flask import render_template
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        from flask import render_template
        return render_template('errors/403.html'), 403
    
    return app
