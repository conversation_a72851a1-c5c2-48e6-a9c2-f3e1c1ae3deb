#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def manual_add_schemes():
    """手动添加方案管理权限"""
    app = create_app()
    
    with app.app_context():
        try:
            print('=== 手动添加方案管理权限 ===')
            
            # 直接使用SQL插入，避免ORM的复杂性
            
            # 1. 首先检查是否已存在
            result = db.session.execute(text("SELECT COUNT(*) FROM permissions WHERE code = 'selfcheck.schemes'"))
            count = result.scalar()
            
            if count > 0:
                print('方案管理权限已存在')
                return
            
            # 2. 获取下一个可用的ID
            result = db.session.execute(text("SELECT MAX(id) FROM permissions"))
            max_id = result.scalar()
            next_id = max_id + 1
            
            print(f'使用ID: {next_id}')
            
            # 3. 插入方案管理主权限
            db.session.execute(text("""
                INSERT INTO permissions (id, name, code, module, resource_type, parent_id, sort_order, is_active)
                VALUES (:id, :name, :code, :module, :resource_type, :parent_id, :sort_order, :is_active)
            """), {
                'id': next_id,
                'name': '方案管理',
                'code': 'selfcheck.schemes',
                'module': 'selfcheck',
                'resource_type': 'menu',
                'parent_id': 60,  # selfcheck的ID
                'sort_order': 1,
                'is_active': 1
            })
            
            schemes_id = next_id
            print(f'✅ 创建方案管理权限 (ID: {schemes_id})')
            
            # 4. 插入子权限
            child_permissions = [
                {'name': '查看方案', 'code': 'selfcheck.schemes.view', 'resource_type': 'page', 'sort_order': 1},
                {'name': '创建方案', 'code': 'selfcheck.schemes.create', 'resource_type': 'button', 'sort_order': 2},
                {'name': '编辑方案', 'code': 'selfcheck.schemes.edit', 'resource_type': 'button', 'sort_order': 3},
                {'name': '删除方案', 'code': 'selfcheck.schemes.delete', 'resource_type': 'button', 'sort_order': 4},
                {'name': '管理方案规则', 'code': 'selfcheck.schemes.manage_rules', 'resource_type': 'button', 'sort_order': 5},
                {'name': '查看统计', 'code': 'selfcheck.schemes.statistics', 'resource_type': 'button', 'sort_order': 6}
            ]
            
            for i, child in enumerate(child_permissions):
                child_id = next_id + i + 1
                db.session.execute(text("""
                    INSERT INTO permissions (id, name, code, module, resource_type, parent_id, sort_order, is_active)
                    VALUES (:id, :name, :code, :module, :resource_type, :parent_id, :sort_order, :is_active)
                """), {
                    'id': child_id,
                    'name': child['name'],
                    'code': child['code'],
                    'module': 'selfcheck',
                    'resource_type': child['resource_type'],
                    'parent_id': schemes_id,
                    'sort_order': child['sort_order'],
                    'is_active': 1
                })
                print(f'✅ 创建子权限: {child["name"]} (ID: {child_id})')
            
            # 5. 更新其他权限的排序
            db.session.execute(text("""
                UPDATE permissions 
                SET sort_order = 2 
                WHERE code = 'selfcheck.rules' AND parent_id = 60
            """))
            
            db.session.execute(text("""
                UPDATE permissions 
                SET sort_order = 3, code = 'selfcheck.uploads', name = '数据上传'
                WHERE code IN ('selfcheck.upload', 'selfcheck.uploads') AND parent_id = 60
            """))
            
            db.session.execute(text("""
                UPDATE permissions 
                SET sort_order = 4 
                WHERE code = 'selfcheck.tasks' AND parent_id = 60
            """))
            
            # 6. 更新upload相关的子权限代码
            db.session.execute(text("""
                UPDATE permissions 
                SET code = REPLACE(code, 'selfcheck.upload.', 'selfcheck.uploads.')
                WHERE code LIKE 'selfcheck.upload.%'
            """))
            
            db.session.commit()
            print('✅ 方案管理权限添加完成！')
            
            # 显示最终结果
            print('\n=== 最终的selfcheck权限结构 ===')
            result = db.session.execute(text("""
                SELECT id, name, code, resource_type, parent_id, sort_order
                FROM permissions 
                WHERE module = 'selfcheck'
                ORDER BY parent_id NULLS FIRST, sort_order
            """))
            
            for row in result:
                indent = '  ' if row[4] else ''  # parent_id
                parent_info = f' (父ID: {row[4]})' if row[4] else ''
                print(f'{indent}ID: {row[0]}, 名称: {row[1]}, 代码: {row[2]}, 类型: {row[3]}, 排序: {row[5]}{parent_info}')
                
        except Exception as e:
            print(f"添加失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    manual_add_schemes()
