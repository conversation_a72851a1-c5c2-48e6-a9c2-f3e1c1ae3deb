{% extends "base.html" %}

{% block title %}系统设置 - MICRA飞检数据处理工具箱{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">系统设置</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>系统布局设置
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 主题风格设置 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary">主题风格设置</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="themeStyle" id="themeLight" value="light" checked>
                                        <label class="form-check-label" for="themeLight">
                                            <div class="theme-preview light-theme">
                                                <div class="theme-header"></div>
                                                <div class="theme-sidebar"></div>
                                                <div class="theme-content"></div>
                                            </div>
                                            <span class="d-block mt-2">浅色主题</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="themeStyle" id="themeDark" value="dark">
                                        <label class="form-check-label" for="themeDark">
                                            <div class="theme-preview dark-theme">
                                                <div class="theme-header"></div>
                                                <div class="theme-sidebar"></div>
                                                <div class="theme-content"></div>
                                            </div>
                                            <span class="d-block mt-2">深色主题</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 主题颜色 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary">主题颜色</h6>
                            <div class="color-picker-container">
                                <div class="color-option" data-color="#0078D4" style="background-color: #0078D4;"></div>
                                <div class="color-option" data-color="#28a745" style="background-color: #28a745;"></div>
                                <div class="color-option" data-color="#dc3545" style="background-color: #dc3545;"></div>
                                <div class="color-option" data-color="#ffc107" style="background-color: #ffc107;"></div>
                                <div class="color-option" data-color="#6f42c1" style="background-color: #6f42c1;"></div>
                                <div class="color-option" data-color="#fd7e14" style="background-color: #fd7e14;"></div>
                                <div class="color-option" data-color="#20c997" style="background-color: #20c997;"></div>
                                <div class="color-option" data-color="#6c757d" style="background-color: #6c757d;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统布局配置 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary">系统布局配置</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="enableTopNav" checked>
                                        <label class="form-check-label" for="enableTopNav">
                                            开启 TopNav
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="enableTagsViews" checked>
                                        <label class="form-check-label" for="enableTagsViews">
                                            开启 Tags-Views
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="fixedHeader">
                                        <label class="form-check-label" for="fixedHeader">
                                            固定 Header
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="showLogo" checked>
                                        <label class="form-check-label" for="showLogo">
                                            显示 Logo
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="dynamicTitle">
                                        <label class="form-check-label" for="dynamicTitle">
                                            动态标题
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="sidebarCollapse">
                                        <label class="form-check-label" for="sidebarCollapse">
                                            侧边栏折叠
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="row">
                        <div class="col-12">
                            <button type="button" class="btn btn-primary me-2" onclick="saveSettings()">
                                <i class="fas fa-save me-1"></i>保存配置
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetSettings()">
                                <i class="fas fa-undo me-1"></i>重置配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.theme-preview {
    width: 120px;
    height: 80px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-preview:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.theme-preview.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.2);
}

.light-theme {
    background-color: #f8f9fa;
}

.light-theme .theme-header {
    height: 15px;
    background-color: #ffffff;
    border-bottom: 1px solid #dee2e6;
}

.light-theme .theme-sidebar {
    position: absolute;
    left: 0;
    top: 15px;
    width: 30px;
    height: 65px;
    background-color: #ffffff;
    border-right: 1px solid #dee2e6;
}

.light-theme .theme-content {
    position: absolute;
    left: 30px;
    top: 15px;
    right: 0;
    bottom: 0;
    background-color: #f8f9fa;
}

.dark-theme {
    background-color: #343a40;
}

.dark-theme .theme-header {
    height: 15px;
    background-color: #495057;
    border-bottom: 1px solid #6c757d;
}

.dark-theme .theme-sidebar {
    position: absolute;
    left: 0;
    top: 15px;
    width: 30px;
    height: 65px;
    background-color: #495057;
    border-right: 1px solid #6c757d;
}

.dark-theme .theme-content {
    position: absolute;
    left: 30px;
    top: 15px;
    right: 0;
    bottom: 0;
    background-color: #343a40;
}

.color-picker-container {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.active {
    border-color: #ffffff;
    box-shadow: 0 0 0 2px #000000;
}

.form-check-label {
    cursor: pointer;
}
</style>
{% endblock %}

{% block scripts %}
<script>
// 页面加载完成后初始化
$(document).ready(function() {
    loadSettings();
    initializeEventListeners();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 主题风格切换
    $('input[name="themeStyle"]').change(function() {
        const theme = $(this).val();
        applyTheme(theme);
    });

    // 颜色选择
    $('.color-option').click(function() {
        $('.color-option').removeClass('active');
        $(this).addClass('active');
        const color = $(this).data('color');
        applyPrimaryColor(color);
    });

    // 布局配置切换
    $('#enableTopNav').change(function() {
        toggleTopNav($(this).is(':checked'));
    });

    $('#enableTagsViews').change(function() {
        toggleTagsViews($(this).is(':checked'));
    });

    $('#fixedHeader').change(function() {
        toggleFixedHeader($(this).is(':checked'));
    });

    $('#showLogo').change(function() {
        toggleLogo($(this).is(':checked'));
    });

    $('#sidebarCollapse').change(function() {
        if ($(this).is(':checked')) {
            toggleSidebar();
        }
    });
}

// 加载设置
function loadSettings() {
    const settings = JSON.parse(localStorage.getItem('micra_settings') || '{}');
    
    // 应用保存的设置
    if (settings.theme) {
        $(`input[name="themeStyle"][value="${settings.theme}"]`).prop('checked', true);
        applyTheme(settings.theme);
    }
    
    if (settings.primaryColor) {
        $(`.color-option[data-color="${settings.primaryColor}"]`).addClass('active');
        applyPrimaryColor(settings.primaryColor);
    } else {
        $('.color-option[data-color="#0078D4"]').addClass('active');
    }
    
    // 布局设置
    $('#enableTopNav').prop('checked', settings.enableTopNav !== false);
    $('#enableTagsViews').prop('checked', settings.enableTagsViews !== false);
    $('#fixedHeader').prop('checked', settings.fixedHeader === true);
    $('#showLogo').prop('checked', settings.showLogo !== false);
    $('#sidebarCollapse').prop('checked', settings.sidebarCollapse === true);
}

// 保存设置
function saveSettings() {
    const settings = {
        theme: $('input[name="themeStyle"]:checked').val(),
        primaryColor: $('.color-option.active').data('color'),
        enableTopNav: $('#enableTopNav').is(':checked'),
        enableTagsViews: $('#enableTagsViews').is(':checked'),
        fixedHeader: $('#fixedHeader').is(':checked'),
        showLogo: $('#showLogo').is(':checked'),
        sidebarCollapse: $('#sidebarCollapse').is(':checked')
    };
    
    localStorage.setItem('micra_settings', JSON.stringify(settings));
    showAlert('设置已保存', 'success');
}

// 重置设置
function resetSettings() {
    if (confirm('确定要重置所有设置吗？')) {
        localStorage.removeItem('micra_settings');
        location.reload();
    }
}

// 应用主题
function applyTheme(theme) {
    if (theme === 'dark') {
        $('body').addClass('dark-theme');
    } else {
        $('body').removeClass('dark-theme');
    }
}

// 应用主色调
function applyPrimaryColor(color) {
    document.documentElement.style.setProperty('--primary-color', color);
}

// 切换顶部导航
function toggleTopNav(enabled) {
    if (enabled) {
        $('.top-nav-tabs').show();
    } else {
        $('.top-nav-tabs').hide();
    }
}

// 切换标签视图
function toggleTagsViews(enabled) {
    // 这里可以添加标签视图的显示/隐藏逻辑
    console.log('Tags Views:', enabled);
}

// 切换固定头部
function toggleFixedHeader(enabled) {
    if (enabled) {
        $('.navbar-top').addClass('fixed-top');
        $('.main-content').css('padding-top', '80px');
    } else {
        $('.navbar-top').removeClass('fixed-top');
        $('.main-content').css('padding-top', '2rem');
    }
}

// 切换Logo显示
function toggleLogo(enabled) {
    if (enabled) {
        $('.navbar-brand').show();
    } else {
        $('.navbar-brand').hide();
    }
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
