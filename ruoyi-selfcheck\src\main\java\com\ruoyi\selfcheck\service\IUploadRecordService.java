package com.ruoyi.selfcheck.service;

import java.util.List;
import com.ruoyi.selfcheck.domain.UploadRecord;
import org.springframework.web.multipart.MultipartFile;

/**
 * 数据上传记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IUploadRecordService 
{
    /**
     * 查询数据上传记录
     * 
     * @param uploadId 数据上传记录主键
     * @return 数据上传记录
     */
    public UploadRecord selectUploadRecordByUploadId(Long uploadId);

    /**
     * 查询数据上传记录列表
     * 
     * @param uploadRecord 数据上传记录
     * @return 数据上传记录集合
     */
    public List<UploadRecord> selectUploadRecordList(UploadRecord uploadRecord);

    /**
     * 新增数据上传记录
     * 
     * @param uploadRecord 数据上传记录
     * @return 结果
     */
    public int insertUploadRecord(UploadRecord uploadRecord);

    /**
     * 修改数据上传记录
     * 
     * @param uploadRecord 数据上传记录
     * @return 结果
     */
    public int updateUploadRecord(UploadRecord uploadRecord);

    /**
     * 批量删除数据上传记录
     * 
     * @param uploadIds 需要删除的数据上传记录主键集合
     * @return 结果
     */
    public int deleteUploadRecordByUploadIds(Long[] uploadIds);

    /**
     * 删除数据上传记录信息
     * 
     * @param uploadId 数据上传记录主键
     * @return 结果
     */
    public int deleteUploadRecordByUploadId(Long uploadId);

    /**
     * 上传文件并创建记录
     * 
     * @param file 上传的文件
     * @param userId 用户ID
     * @return 上传记录
     */
    public UploadRecord uploadFile(MultipartFile file, Long userId);

    /**
     * 异步校验数据
     * 
     * @param uploadId 上传记录ID
     */
    public void validateDataAsync(Long uploadId);

    /**
     * 根据用户ID查询上传记录
     * 
     * @param userId 用户ID
     * @return 上传记录列表
     */
    public List<UploadRecord> selectUploadRecordByUserId(Long userId);

    /**
     * 更新校验状态
     * 
     * @param uploadId 上传记录ID
     * @param status 状态
     * @param result 校验结果
     * @param errorMessage 错误信息
     */
    public void updateValidationStatus(Long uploadId, String status, String result, String errorMessage);
}
