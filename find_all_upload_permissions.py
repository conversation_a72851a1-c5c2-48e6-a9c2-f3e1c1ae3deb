#!/usr/bin/env python3
"""
查找所有使用错误权限代码的地方
"""

import os
import re

def find_upload_permissions():
    """查找所有使用 selfcheck.upload. 权限代码的地方"""
    
    print("=== 🔍 查找所有使用错误权限代码的地方 ===")
    
    # 要搜索的目录
    search_dirs = [
        'app/templates',
        'app/selfcheck',
        'app/main',
        'app/admin'
    ]
    
    # 要搜索的文件扩展名
    file_extensions = ['.html', '.py', '.js']
    
    # 错误的权限代码模式
    error_patterns = [
        r'selfcheck\.upload\.view',
        r'selfcheck\.upload\.create',
        r'selfcheck\.upload\.delete'
    ]
    
    found_issues = []
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        print(f"\n📁 搜索目录: {search_dir}")
        
        for root, dirs, files in os.walk(search_dir):
            for file in files:
                if any(file.endswith(ext) for ext in file_extensions):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            lines = content.split('\n')
                            
                            for line_num, line in enumerate(lines, 1):
                                for pattern in error_patterns:
                                    if re.search(pattern, line):
                                        found_issues.append({
                                            'file': file_path,
                                            'line': line_num,
                                            'content': line.strip(),
                                            'pattern': pattern
                                        })
                                        print(f"  ❌ {file_path}:{line_num}")
                                        print(f"     {line.strip()}")
                                        
                    except Exception as e:
                        print(f"  ⚠️  无法读取文件 {file_path}: {str(e)}")
    
    print(f"\n📊 搜索结果总结:")
    print(f"发现 {len(found_issues)} 个需要修复的权限代码")
    
    if found_issues:
        print(f"\n📋 详细列表:")
        for issue in found_issues:
            print(f"文件: {issue['file']}")
            print(f"行号: {issue['line']}")
            print(f"内容: {issue['content']}")
            print(f"模式: {issue['pattern']}")
            print("-" * 50)
    else:
        print("✅ 没有发现错误的权限代码！")
    
    return found_issues

def check_correct_permissions():
    """检查正确的权限代码使用情况"""
    
    print("\n=== ✅ 检查正确权限代码使用情况 ===")
    
    # 要搜索的目录
    search_dirs = [
        'app/templates',
        'app/selfcheck',
        'app/main',
        'app/admin'
    ]
    
    # 要搜索的文件扩展名
    file_extensions = ['.html', '.py', '.js']
    
    # 正确的权限代码模式
    correct_patterns = [
        r'selfcheck\.uploads\.view',
        r'selfcheck\.uploads\.create',
        r'selfcheck\.uploads\.delete'
    ]
    
    found_correct = []
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        print(f"\n📁 搜索目录: {search_dir}")
        
        for root, dirs, files in os.walk(search_dir):
            for file in files:
                if any(file.endswith(ext) for ext in file_extensions):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            lines = content.split('\n')
                            
                            for line_num, line in enumerate(lines, 1):
                                for pattern in correct_patterns:
                                    if re.search(pattern, line):
                                        found_correct.append({
                                            'file': file_path,
                                            'line': line_num,
                                            'content': line.strip(),
                                            'pattern': pattern
                                        })
                                        print(f"  ✅ {file_path}:{line_num}")
                                        print(f"     {line.strip()}")
                                        
                    except Exception as e:
                        print(f"  ⚠️  无法读取文件 {file_path}: {str(e)}")
    
    print(f"\n📊 正确权限代码使用情况:")
    print(f"发现 {len(found_correct)} 个正确的权限代码使用")
    
    return found_correct

if __name__ == '__main__':
    try:
        # 查找错误的权限代码
        error_issues = find_upload_permissions()
        
        # 查找正确的权限代码
        correct_usage = check_correct_permissions()
        
        print(f"\n🎯 最终总结:")
        print(f"❌ 需要修复的错误权限代码: {len(error_issues)} 个")
        print(f"✅ 正确使用的权限代码: {len(correct_usage)} 个")
        
        if error_issues:
            print(f"\n⚠️  仍有权限代码需要修复！")
        else:
            print(f"\n🎉 所有权限代码都已正确！")
            
    except Exception as e:
        print(f"搜索过程出错: {e}")
        import traceback
        traceback.print_exc()
