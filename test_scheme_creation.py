#!/usr/bin/env python3
"""
测试方案创建功能
"""

import requests
import json
from bs4 import BeautifulSoup

def test_scheme_creation():
    """测试方案创建功能"""
    
    # 创建会话
    session = requests.Session()
    
    print("=== 🔧 测试方案创建功能修复 ===")
    
    # 1. 登录
    print("\n1. 🔐 登录...")
    login_url = 'http://127.0.0.1:5002/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }
    
    try:
        login_page = session.get(login_url, timeout=10)
        soup = BeautifulSoup(login_page.text, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        if csrf_token:
            login_data['csrf_token'] = csrf_token['value']
        
        login_response = session.post(login_url, data=login_data, allow_redirects=True, timeout=10)
        
        if '/index' not in login_response.url:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录过程出错: {str(e)}")
        return False
    
    # 2. 获取方案页面的CSRF令牌
    print("\n2. 📋 获取方案页面...")
    try:
        schemes_page = session.get('http://127.0.0.1:5002/selfcheck/schemes', timeout=10)
        
        if schemes_page.status_code == 200:
            print("✅ 方案页面访问成功")
            
            # 提取CSRF令牌
            soup = BeautifulSoup(schemes_page.text, 'html.parser')
            csrf_meta = soup.find('meta', {'name': 'csrf-token'})
            csrf_token = csrf_meta['content'] if csrf_meta else None
            
            if csrf_token:
                print("✅ 获取CSRF令牌成功")
            else:
                print("❌ 无法获取CSRF令牌")
                return False
        else:
            print(f"❌ 方案页面访问失败: {schemes_page.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 方案页面访问出错: {str(e)}")
        return False
    
    # 3. 测试创建方案
    print("\n3. ➕ 测试创建方案...")
    try:
        create_url = 'http://127.0.0.1:5002/selfcheck/api/schemes'
        
        # 创建测试方案数据
        scheme_data = {
            'scheme_name': f'测试方案_{int(__import__("time").time())}',
            'description': '这是一个测试方案，用于验证序列问题修复'
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token
        }
        
        create_response = session.post(
            create_url,
            headers=headers,
            data=json.dumps(scheme_data),
            timeout=10
        )
        
        print(f"创建方案响应状态: {create_response.status_code}")
        
        if create_response.status_code == 200:
            try:
                response_data = create_response.json()
                print(f"创建方案响应: {response_data}")
                
                if response_data.get('success'):
                    scheme = response_data.get('scheme', {})
                    print("✅ 方案创建成功！")
                    print(f"  - 方案ID: {scheme.get('id')}")
                    print(f"  - 方案名称: {scheme.get('scheme_name')}")
                    print(f"  - 描述: {scheme.get('description')}")
                    print(f"  - 状态: {scheme.get('status')}")
                    return True
                else:
                    print(f"❌ 方案创建失败: {response_data.get('message', '未知错误')}")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {create_response.text[:200]}")
                return False
        else:
            print(f"❌ 创建方案请求失败: {create_response.status_code}")
            print(f"响应内容: {create_response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 创建方案测试出错: {str(e)}")
        return False
    
    print("\n🎉 方案创建功能测试完成！")
    
    return True

if __name__ == '__main__':
    try:
        success = test_scheme_creation()
        if success:
            print("\n✅ 方案创建功能修复验证成功！")
            print("\n📋 修复总结:")
            print("1. ✅ 修复了Oracle序列CURRVAL错误")
            print("2. ✅ 使用统一的数据库连接管理器")
            print("3. ✅ 先获取序列值再插入数据")
            print("4. ✅ 确保序列操作在同一会话中进行")
            print("\n🌟 方案创建功能现在可以正常工作了！")
        else:
            print("\n❌ 方案创建功能修复验证失败")
    except Exception as e:
        print(f"验证过程出错: {e}")
        import traceback
        traceback.print_exc()
