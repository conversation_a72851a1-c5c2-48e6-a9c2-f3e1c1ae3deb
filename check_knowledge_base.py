#!/usr/bin/env python3
"""
检查飞检规则知识库页面的具体错误
"""

import requests
import re

def check_knowledge_base():
    base_url = "http://127.0.0.1:5002"
    session = requests.Session()
    
    # 先登录
    print("🔐 正在登录...")
    login_page = session.get(f"{base_url}/auth/login")
    
    csrf_patterns = [
        r'name="csrf_token" value="([^"]+)"',
        r'<input[^>]*name="csrf_token"[^>]*value="([^"]+)"',
    ]
    
    csrf_token = None
    for pattern in csrf_patterns:
        csrf_match = re.search(pattern, login_page.text)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            break
    
    login_data = {
        'username': 'admin',
        'password': 'Admin123!',
        'remember_me': False
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    response = session.post(f"{base_url}/auth/login", data=login_data, allow_redirects=True)
    
    if response.status_code == 200 and '/auth/login' not in response.url:
        print("✅ 登录成功")
    else:
        print("❌ 登录失败")
        return
    
    # 检查飞检规则知识库页面
    print("\n🔍 检查飞检规则知识库页面...")
    try:
        response = session.get(f"{base_url}/rules/knowledge_base")
        if response.status_code == 200:
            content = response.text
            
            # 查找具体的错误信息
            error_patterns = [
                r'Error:([^<]+)',
                r'Traceback[^<]*',
                r'Exception[^<]*',
                r'TypeError[^<]*',
                r'NameError[^<]*',
                r'AttributeError[^<]*'
            ]
            
            errors_found = []
            for pattern in error_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                if matches:
                    errors_found.extend(matches)
            
            if errors_found:
                print("❌ 发现错误:")
                for error in errors_found[:3]:  # 只显示前3个错误
                    print(f"   {error.strip()}")
            else:
                print("✅ 页面内容正常")
                
            # 检查页面大小
            print(f"📊 页面大小: {len(content)} 字符")
            
            # 检查是否包含关键元素
            key_elements = [
                '规则检索',
                '规则列表',
                'rulesTable',
                'searchRules',
                'loadRules'
            ]
            
            missing_elements = []
            for element in key_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"⚠️ 缺少关键元素: {', '.join(missing_elements)}")
            else:
                print("✅ 包含所有关键元素")
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 异常: {str(e)}")

if __name__ == '__main__':
    check_knowledge_base()
