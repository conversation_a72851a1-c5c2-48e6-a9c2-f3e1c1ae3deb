from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
from sqlalchemy import Sequence
import re

from app import db

class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, Sequence('users_id_seq'), primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, index=True)
    email = db.Column(db.String(100), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    real_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    department = db.Column(db.String(100))
    is_active = db.Column(db.<PERSON>, default=True)
    is_admin = db.Column(db.<PERSON><PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)

    # 微信相关字段
    wechat_openid = db.Column(db.String(100), unique=True, index=True)
    wechat_unionid = db.Column(db.String(100), index=True)
    wechat_nickname = db.Column(db.String(100))
    wechat_avatar = db.Column(db.String(500))
    wechat_sex = db.Column(db.Integer)  # 1男性，2女性，0未知
    register_type = db.Column(db.String(20), default='normal')  # normal, wechat

    # 关系
    roles = db.relationship('Role', secondary='user_roles', back_populates='users')
    audit_logs = db.relationship('AuditLog', back_populates='user')
    
    def set_password(self, password):
        """设置密码"""
        if not self.validate_password_strength(password):
            raise ValueError("密码强度不符合要求")
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    @staticmethod
    def validate_password_strength(password):
        """验证密码强度"""
        if len(password) < 8:
            return False
        if not re.search(r'[A-Z]', password):
            return False
        if not re.search(r'[a-z]', password):
            return False
        if not re.search(r'\d', password):
            return False
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False
        return True
    
    def has_permission(self, permission_code):
        """检查用户是否有指定权限"""
        if self.is_admin:
            return True
        
        for role in self.roles:
            if role.is_active and role.has_permission(permission_code):
                return True
        return False
    
    def get_permissions(self):
        """获取用户所有权限"""
        permissions = set()
        if self.is_admin:
            # 管理员拥有所有权限
            from app.models.permission import Permission
            return set(Permission.query.filter_by(is_active=True).all())
        
        for role in self.roles:
            if role.is_active:
                permissions.update(role.permissions)
        return permissions
    
    def is_locked(self):
        """检查账户是否被锁定"""
        if self.locked_until and self.locked_until > datetime.utcnow():
            return True
        return False
    
    def lock_account(self, minutes=30):
        """锁定账户"""
        self.locked_until = datetime.utcnow() + timedelta(minutes=minutes)
        self.login_attempts = 0
        db.session.commit()
    
    def unlock_account(self):
        """解锁账户"""
        self.locked_until = None
        self.login_attempts = 0
        db.session.commit()
    
    def bind_wechat(self, openid, unionid=None, nickname=None, avatar=None, sex=None):
        """绑定微信账号"""
        self.wechat_openid = openid
        self.wechat_unionid = unionid
        self.wechat_nickname = nickname
        self.wechat_avatar = avatar
        self.wechat_sex = sex
        db.session.commit()

    def unbind_wechat(self):
        """解绑微信账号"""
        self.wechat_openid = None
        self.wechat_unionid = None
        self.wechat_nickname = None
        self.wechat_avatar = None
        self.wechat_sex = None
        db.session.commit()

    def is_wechat_bound(self):
        """检查是否已绑定微信"""
        return bool(self.wechat_openid)

    @staticmethod
    def find_by_wechat_openid(openid):
        """通过微信openid查找用户"""
        return User.query.filter_by(wechat_openid=openid).first()

    @staticmethod
    def create_from_wechat(openid, unionid=None, nickname=None, avatar=None, sex=None):
        """通过微信信息创建用户"""
        import random
        import string

        # 生成唯一用户名
        base_username = f"wx_{openid[-8:]}"
        username = base_username
        counter = 1
        while User.query.filter_by(username=username).first():
            username = f"{base_username}_{counter}"
            counter += 1

        # 生成临时邮箱
        email = f"{username}@wechat.temp"

        # 生成随机密码
        temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))

        user = User(
            username=username,
            email=email,
            real_name=nickname or username,
            wechat_openid=openid,
            wechat_unionid=unionid,
            wechat_nickname=nickname,
            wechat_avatar=avatar,
            wechat_sex=sex,
            register_type='wechat',
            is_active=True
        )
        user.set_password(temp_password)

        db.session.add(user)
        db.session.commit()
        return user

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'phone': self.phone,
            'department': self.department,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'wechat_bound': self.is_wechat_bound(),
            'wechat_nickname': self.wechat_nickname,
            'wechat_avatar': self.wechat_avatar,
            'register_type': self.register_type,
            'roles': [role.to_dict() for role in self.roles]
        }
