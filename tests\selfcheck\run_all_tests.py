#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自查自纠模块全量测试运行脚本
"""

import unittest
import sys
import os
import time
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 导入测试模块
from test_scripts.test_rules_management import TestRulesManagement
from test_scripts.test_rules_import import TestRulesImport
# from test_scripts.test_scheme_management import TestSchemeManagement
# from test_scripts.test_data_upload import TestDataUpload
# from test_scripts.test_task_management import TestTaskManagement

from utils.data_generator import TestDataGenerator


class TestSuiteRunner:
    """测试套件运行器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.results = {}
        self.total_tests = 0
        self.total_failures = 0
        self.total_errors = 0
        self.total_success = 0
        
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 80)
        print("自查自纠模块自动化测试套件")
        print("=" * 80)
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        self.start_time = time.time()
        
        # 准备测试数据
        self.prepare_test_data()
        
        # 定义测试模块
        test_modules = [
            ("规则管理模块", TestRulesManagement),
            ("规则导入模块", TestRulesImport),
            # ("方案管理模块", TestSchemeManagement),
            # ("数据上传模块", TestDataUpload),
            # ("任务管理模块", TestTaskManagement),
        ]
        
        # 运行各个测试模块
        for module_name, test_class in test_modules:
            print(f"\n{'='*60}")
            print(f"运行 {module_name} 测试")
            print(f"{'='*60}")
            
            result = self.run_test_module(test_class)
            self.results[module_name] = result
            
            # 累计统计
            self.total_tests += result['total']
            self.total_failures += result['failures']
            self.total_errors += result['errors']
            self.total_success += result['success']
        
        self.end_time = time.time()
        
        # 输出总结报告
        self.print_summary_report()
        
        # 生成HTML报告
        self.generate_html_report()
        
        return self.total_failures == 0 and self.total_errors == 0
    
    def prepare_test_data(self):
        """准备测试数据"""
        print("准备测试数据...")
        
        try:
            # 创建测试数据目录
            test_data_dir = os.path.join(os.path.dirname(__file__), 'test_data')
            os.makedirs(test_data_dir, exist_ok=True)
            
            # 生成测试数据文件
            generator = TestDataGenerator()
            files = generator.generate_test_files(test_data_dir)
            
            print(f"✅ 测试数据准备完成，生成文件:")
            for file_type, file_path in files.items():
                print(f"   {file_type}: {os.path.basename(file_path)}")
            
        except Exception as e:
            print(f"⚠️ 测试数据准备失败: {e}")
    
    def run_test_module(self, test_class):
        """运行单个测试模块"""
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromTestCase(test_class)
        
        # 使用自定义的测试运行器
        stream = TestResultStream()
        runner = unittest.TextTestRunner(
            stream=stream,
            verbosity=2,
            buffer=True
        )
        
        result = runner.run(suite)
        
        return {
            'total': result.testsRun,
            'success': result.testsRun - len(result.failures) - len(result.errors),
            'failures': len(result.failures),
            'errors': len(result.errors),
            'details': {
                'failures': [str(failure[1]) for failure in result.failures],
                'errors': [str(error[1]) for error in result.errors]
            }
        }
    
    def print_summary_report(self):
        """打印总结报告"""
        duration = self.end_time - self.start_time
        
        print("\n" + "=" * 80)
        print("测试总结报告")
        print("=" * 80)
        print(f"总测试时间: {duration:.2f} 秒")
        print(f"总测试数量: {self.total_tests}")
        print(f"成功: {self.total_success}")
        print(f"失败: {self.total_failures}")
        print(f"错误: {self.total_errors}")
        print(f"成功率: {(self.total_success/self.total_tests*100):.1f}%" if self.total_tests > 0 else "0%")
        
        print("\n各模块测试结果:")
        print("-" * 80)
        for module_name, result in self.results.items():
            status = "✅ 通过" if result['failures'] == 0 and result['errors'] == 0 else "❌ 失败"
            print(f"{module_name:<20} | 总数: {result['total']:>3} | 成功: {result['success']:>3} | 失败: {result['failures']:>3} | 错误: {result['errors']:>3} | {status}")
        
        # 显示失败和错误详情
        if self.total_failures > 0 or self.total_errors > 0:
            print("\n失败和错误详情:")
            print("-" * 80)
            for module_name, result in self.results.items():
                if result['failures'] > 0 or result['errors'] > 0:
                    print(f"\n{module_name}:")
                    for failure in result['details']['failures']:
                        print(f"  失败: {failure[:100]}...")
                    for error in result['details']['errors']:
                        print(f"  错误: {error[:100]}...")
        
        print("\n" + "=" * 80)
    
    def generate_html_report(self):
        """生成HTML测试报告"""
        try:
            reports_dir = os.path.join(os.path.dirname(__file__), 'reports')
            os.makedirs(reports_dir, exist_ok=True)
            
            html_file = os.path.join(reports_dir, f'test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html')
            
            html_content = self.generate_html_content()
            
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"📊 HTML测试报告已生成: {html_file}")
            
        except Exception as e:
            print(f"⚠️ 生成HTML报告失败: {e}")
    
    def generate_html_content(self):
        """生成HTML报告内容"""
        duration = self.end_time - self.start_time
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自查自纠模块测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .module {{ margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; }}
        .module-header {{ background-color: #e9ecef; padding: 10px; font-weight: bold; }}
        .module-content {{ padding: 15px; }}
        .success {{ color: #28a745; }}
        .failure {{ color: #dc3545; }}
        .error {{ color: #fd7e14; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f8f9fa; }}
        .status-pass {{ background-color: #d4edda; }}
        .status-fail {{ background-color: #f8d7da; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>自查自纠模块自动化测试报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>测试时长: {duration:.2f} 秒</p>
    </div>
    
    <div class="summary">
        <h2>测试概览</h2>
        <table>
            <tr>
                <th>指标</th>
                <th>数量</th>
                <th>百分比</th>
            </tr>
            <tr>
                <td>总测试数</td>
                <td>{self.total_tests}</td>
                <td>100%</td>
            </tr>
            <tr class="success">
                <td>成功</td>
                <td>{self.total_success}</td>
                <td>{(self.total_success/self.total_tests*100):.1f}%</td>
            </tr>
            <tr class="failure">
                <td>失败</td>
                <td>{self.total_failures}</td>
                <td>{(self.total_failures/self.total_tests*100):.1f}%</td>
            </tr>
            <tr class="error">
                <td>错误</td>
                <td>{self.total_errors}</td>
                <td>{(self.total_errors/self.total_tests*100):.1f}%</td>
            </tr>
        </table>
    </div>
    
    <div class="modules">
        <h2>各模块测试结果</h2>
"""
        
        for module_name, result in self.results.items():
            status_class = "status-pass" if result['failures'] == 0 and result['errors'] == 0 else "status-fail"
            status_text = "通过" if result['failures'] == 0 and result['errors'] == 0 else "失败"
            
            html += f"""
        <div class="module">
            <div class="module-header {status_class}">
                {module_name} - {status_text}
            </div>
            <div class="module-content">
                <table>
                    <tr>
                        <td>总测试数</td>
                        <td>{result['total']}</td>
                    </tr>
                    <tr>
                        <td>成功</td>
                        <td>{result['success']}</td>
                    </tr>
                    <tr>
                        <td>失败</td>
                        <td>{result['failures']}</td>
                    </tr>
                    <tr>
                        <td>错误</td>
                        <td>{result['errors']}</td>
                    </tr>
                </table>
            </div>
        </div>
"""
        
        html += """
    </div>
</body>
</html>
"""
        return html


class TestResultStream:
    """自定义测试结果输出流"""
    
    def __init__(self):
        self.data = []
    
    def write(self, text):
        self.data.append(text)
        sys.stdout.write(text)
    
    def flush(self):
        sys.stdout.flush()


def main():
    """主函数"""
    runner = TestSuiteRunner()
    success = runner.run_all_tests()
    
    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
