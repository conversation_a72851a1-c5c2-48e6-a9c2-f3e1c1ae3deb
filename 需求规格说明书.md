# MICRA自查自纠模块需求规格说明书

**文档版本：** V1.0  
**编写日期：** 2025年1月7日  
**项目名称：** MICRA工具箱 - 自查自纠模块  
**文档类型：** 需求规格说明书  

## 1. 引言

### 1.1 编写目的
本文档旨在详细描述MICRA自查自纠模块的功能需求、性能需求和约束条件，为系统设计、开发、测试和验收提供依据。

### 1.2 项目背景
MICRA工具箱是一个面向医疗保险数据管理的综合性平台。自查自纠模块作为核心功能之一，主要用于医疗机构对自身数据进行质量检查和合规性验证，帮助发现和纠正数据问题，提高数据质量和合规性。

### 1.3 产品定位
- **目标用户：** 医疗机构数据管理人员、质控人员
- **应用场景：** 医疗数据质量检查、合规性验证、数据清洗
- **商业价值：** 提高数据质量、降低合规风险、提升工作效率

### 1.4 参考资料
- 医疗保险数据标准规范
- 数据质量管理最佳实践
- 用户调研报告和需求收集文档

## 2. 产品概述

### 2.1 产品功能概述
自查自纠模块提供完整的数据质量检查解决方案，包括：
- 数据文件上传和管理
- 检查规则配置和管理
- 检查方案设计和执行
- 检查任务创建和监控
- 检查结果分析和报告

### 2.2 用户角色定义
- **系统管理员：** 拥有所有权限，负责系统配置和用户管理
- **数据管理员：** 负责数据上传、方案配置、任务执行
- **质控人员：** 负责检查规则制定、结果审核
- **普通用户：** 查看检查结果、下载报告

### 2.3 系统边界
- **包含：** 数据上传、规则管理、方案配置、任务执行、结果展示
- **不包含：** 数据源系统集成、外部系统接口、数据备份恢复

## 3. 功能需求

### 3.1 数据上传管理

#### 3.1.1 文件上传功能
**需求编号：** FR-001  
**优先级：** 高  

**功能描述：**
用户可以上传待检查的数据文件，系统支持多种文件格式，并提供上传进度显示和状态跟踪。

**具体需求：**
1. 支持文件格式：CSV、DMP、DP、BAK
2. 文件大小限制：单个文件不超过10MB
3. 支持拖拽上传和点击选择上传
4. 实时显示上传进度
5. 上传完成后显示文件基本信息

**状态流程：**
1. `pending` - 文件上传完成，待处理
2. `validating` - 数据导入数据库，验证中
3. `validated` - 校验通过，可用于检查
4. `failed` - 验证失败，需要重新上传

**验收标准：**
- 支持的文件格式能够正常上传
- 超过大小限制的文件被拒绝
- 上传进度准确显示
- 状态变化符合预期流程

#### 3.1.2 上传记录管理
**需求编号：** FR-002  
**优先级：** 中  

**功能描述：**
提供上传文件的历史记录查询、详情查看和删除功能。

**具体需求：**
1. 分页显示上传记录列表
2. 支持按文件名、类型、状态、用户筛选
3. 显示文件详细信息（大小、记录数、上传时间等）
4. 支持删除未被使用的上传记录
5. 管理员可查看所有用户的上传记录

### 3.2 规则管理

#### 3.2.1 检查规则配置
**需求编号：** FR-003  
**优先级：** 高  

**功能描述：**
提供检查规则的创建、编辑、删除和管理功能，支持SQL规则和业务规则。

**具体需求：**
1. 规则基本信息：名称、描述、类型、城市
2. SQL规则编辑器，支持语法高亮
3. 规则分类管理（门诊/住院、定量/定性）
4. 规则启用/禁用状态控制
5. 规则导入导出功能

#### 3.2.2 规则知识库
**需求编号：** FR-004  
**优先级：** 中  

**功能描述：**
维护检查规则知识库，支持规则的分类、搜索和复用。

**具体需求：**
1. 规则分类体系管理
2. 规则搜索和筛选
3. 规则使用统计和评价
4. 规则版本管理
5. 规则共享和协作

### 3.3 方案管理

#### 3.3.1 检查方案配置
**需求编号：** FR-005  
**优先级：** 高  

**功能描述：**
创建和管理检查方案，一个方案包含多个检查规则的组合。

**具体需求：**
1. 方案基本信息：名称、描述、状态
2. 方案规则管理：添加、删除、排序
3. 规则启用/禁用控制
4. 方案复制和模板功能
5. 方案授权管理

**序号显示规则：**
- 使用查询结果的行号作为序号（1, 2, 3...）
- 不依赖数据库sort_order字段
- 序号始终连续，便于用户理解

#### 3.3.2 方案权限管理
**需求编号：** FR-006  
**优先级：** 中  

**功能描述：**
控制用户对检查方案的访问权限，支持细粒度的权限控制。

**具体需求：**
1. 方案创建者权限
2. 方案使用者权限
3. 方案管理员权限
4. 权限继承和委托
5. 权限审计日志

### 3.4 任务管理

#### 3.4.1 检查任务创建
**需求编号：** FR-007  
**优先级：** 高  

**功能描述：**
基于上传的数据文件和配置的检查方案创建检查任务。

**具体需求：**
1. 任务基本信息：名称、描述
2. 数据源选择：仅显示用户有权限的数据源
3. 方案选择：仅显示授权使用的方案
4. 任务参数配置
5. 任务调度设置

#### 3.4.2 任务执行监控
**需求编号：** FR-008  
**优先级：** 高  

**功能描述：**
监控检查任务的执行状态，提供进度跟踪和错误处理。

**具体需求：**
1. 任务状态跟踪（待执行、执行中、已完成、失败）
2. 执行进度显示
3. 错误信息记录和展示
4. 任务日志查看
5. 任务取消和重试功能

### 3.5 结果管理

#### 3.5.1 检查结果展示
**需求编号：** FR-009  
**优先级：** 高  

**功能描述：**
展示检查任务的执行结果，包括统计信息和详细数据。

**具体需求：**
1. 结果概览：总体统计、问题分布
2. 规则执行结果：每个规则的执行状态和结果
3. 问题数据详情：具体的问题记录
4. 结果数据导出
5. 结果对比分析

#### 3.5.2 报告生成
**需求编号：** FR-010  
**优先级：** 中  

**功能描述：**
生成检查结果报告，支持多种格式和自定义模板。

**具体需求：**
1. 报告模板管理
2. 报告内容配置
3. 多格式导出（PDF、Excel、Word）
4. 报告预览功能
5. 报告分发和共享

## 4. 非功能需求

### 4.1 性能需求
- **响应时间：** 页面加载时间不超过3秒
- **并发用户：** 支持100个并发用户同时使用
- **数据处理：** 支持处理10万条记录的数据文件
- **文件上传：** 10MB文件上传时间不超过30秒

### 4.2 可用性需求
- **系统可用性：** 99.5%以上
- **故障恢复：** 系统故障后5分钟内恢复
- **数据备份：** 每日自动备份，支持快速恢复
- **错误处理：** 友好的错误提示和处理机制

### 4.3 安全需求
- **身份认证：** 支持用户名密码和微信登录
- **权限控制：** 基于角色的访问控制（RBAC）
- **数据加密：** 敏感数据传输和存储加密
- **审计日志：** 完整的操作日志记录

### 4.4 兼容性需求
- **浏览器支持：** Chrome 80+、Firefox 75+、Edge 80+
- **操作系统：** Windows 10+、macOS 10.15+、Linux
- **移动设备：** 支持平板电脑访问
- **数据库：** Oracle 11g+、PostgreSQL 12+

## 5. 约束条件

### 5.1 技术约束
- 基于Python Flask框架开发
- 前端使用Bootstrap 5和jQuery
- 数据库使用Oracle作为主数据库
- 部署在Linux服务器环境

### 5.2 业务约束
- 符合医疗数据安全规范
- 满足数据隐私保护要求
- 支持多租户架构
- 数据保留期限不少于5年

### 5.3 时间约束
- 核心功能开发周期：8周
- 测试和优化周期：4周
- 部署和上线周期：2周
- 总项目周期：14周

## 6. 验收标准

### 6.1 功能验收
- 所有功能需求100%实现
- 用户界面友好，操作流程顺畅
- 数据处理准确，结果可靠
- 错误处理完善，异常情况可控

### 6.2 性能验收
- 满足所有性能指标要求
- 压力测试通过
- 长时间运行稳定
- 资源使用合理

### 6.3 安全验收
- 通过安全渗透测试
- 权限控制有效
- 数据加密正确
- 审计日志完整

### 6.4 用户验收
- 用户培训完成
- 用户操作手册齐全
- 用户反馈满意度80%以上
- 关键用户签字确认

---

**文档状态：** 已审核
**批准人：** 项目经理
**批准日期：** 2025年1月7日
**版本历史：**
- V1.0 (2025-01-07): 初始版本，包含核心功能需求
