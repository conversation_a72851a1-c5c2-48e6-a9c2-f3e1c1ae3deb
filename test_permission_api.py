#!/usr/bin/env python3
"""
测试权限API返回的数据格式
"""

import requests
import json
from requests.auth import HTTPBasicAuth

def test_permission_api():
    """测试权限API"""
    
    # 创建会话
    session = requests.Session()
    
    # 先登录
    login_url = 'http://127.0.0.1:5002/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }
    
    # 获取登录页面以获取CSRF令牌
    login_page = session.get(login_url)
    print(f"登录页面状态: {login_page.status_code}")
    
    # 提取CSRF令牌
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(login_page.text, 'html.parser')
    csrf_token = soup.find('input', {'name': 'csrf_token'})
    if csrf_token:
        login_data['csrf_token'] = csrf_token['value']
        print(f"CSRF令牌: {csrf_token['value'][:20]}...")
    
    # 执行登录
    login_response = session.post(login_url, data=login_data, allow_redirects=True)
    print(f"登录响应状态: {login_response.status_code}")
    print(f"登录后重定向到: {login_response.url}")
    print(f"登录响应内容前200字符: {login_response.text[:200]}")

    # 检查是否登录成功（通过检查是否重定向到主页）
    if '/index' in login_response.url or login_response.url.endswith('/'):
        print("✅ 登录成功")
    else:
        print("❌ 登录失败，可能是用户名密码错误")
        return
    
    # 测试权限API
    permissions_url = 'http://127.0.0.1:5002/admin/api/permissions'
    permissions_response = session.get(permissions_url)
    
    print(f"\n权限API状态: {permissions_response.status_code}")
    
    print(f"响应内容长度: {len(permissions_response.text)}")
    print(f"响应头: {dict(permissions_response.headers)}")
    print(f"响应内容前500字符: {permissions_response.text[:500]}")

    if permissions_response.status_code == 200:
        if permissions_response.text.strip():
            try:
                permissions_data = permissions_response.json()
                print(f"权限数据类型: {type(permissions_data)}")
                print(f"权限数据键: {list(permissions_data.keys())}")

                if 'permissions' in permissions_data:
                    permissions = permissions_data['permissions']
                    print(f"权限数量: {len(permissions)}")

                    if permissions:
                        # 显示第一个权限的结构
                        first_permission = permissions[0]
                        print(f"\n第一个权限的结构:")
                        for key, value in first_permission.items():
                            print(f"  {key}: {value}")

                        # 统计权限模块
                        modules = {}
                        for perm in permissions:
                            module = perm.get('module', 'unknown')
                            if module not in modules:
                                modules[module] = 0
                            modules[module] += 1

                        print(f"\n权限模块统计:")
                        for module, count in modules.items():
                            print(f"  {module}: {count}个权限")

                        # 检查父子关系
                        parent_count = sum(1 for p in permissions if p.get('parent_id'))
                        root_count = sum(1 for p in permissions if not p.get('parent_id'))
                        print(f"\n权限层级统计:")
                        print(f"  根权限: {root_count}个")
                        print(f"  子权限: {parent_count}个")

                    else:
                        print("权限列表为空")
                else:
                    print("响应中没有'permissions'键")
                    print(f"响应内容: {permissions_data}")
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                print(f"响应内容: {permissions_response.text}")
        else:
            print("响应内容为空")
    else:
        print(f"权限API请求失败: {permissions_response.text[:500]}")

if __name__ == '__main__':
    try:
        test_permission_api()
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
