"""
微信授权处理模块
"""
import requests
import json
import logging
from flask import current_app, session
from config.wechat import WeChatConfig

logger = logging.getLogger(__name__)

class WeChatAuth:
    """微信授权处理类"""
    
    @staticmethod
    def get_access_token(code):
        """通过code获取access_token"""
        try:
            params = {
                'appid': WeChatConfig.WECHAT_APP_ID,
                'secret': WeChatConfig.WECHAT_APP_SECRET,
                'code': code,
                'grant_type': 'authorization_code'
            }
            
            response = requests.get(WeChatConfig.WECHAT_ACCESS_TOKEN_URL, params=params, timeout=10)
            result = response.json()
            
            if 'access_token' in result:
                return result
            else:
                logger.error(f"获取access_token失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"获取access_token异常: {str(e)}")
            return None
    
    @staticmethod
    def get_user_info(access_token, openid):
        """获取用户信息"""
        try:
            params = {
                'access_token': access_token,
                'openid': openid,
                'lang': 'zh_CN'
            }
            
            response = requests.get(WeChatConfig.WECHAT_USER_INFO_URL, params=params, timeout=10)
            result = response.json()
            
            if 'openid' in result:
                return result
            else:
                logger.error(f"获取用户信息失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"获取用户信息异常: {str(e)}")
            return None
    
    @staticmethod
    def process_wechat_login(code, state='login'):
        """处理微信登录流程"""
        try:
            # 1. 获取access_token
            token_result = WeChatAuth.get_access_token(code)
            if not token_result:
                return {'success': False, 'message': '获取微信授权失败'}
            
            access_token = token_result.get('access_token')
            openid = token_result.get('openid')
            unionid = token_result.get('unionid')
            
            if not access_token or not openid:
                return {'success': False, 'message': '微信授权信息不完整'}
            
            # 2. 获取用户信息
            user_info = WeChatAuth.get_user_info(access_token, openid)
            if not user_info:
                return {'success': False, 'message': '获取微信用户信息失败'}
            
            # 3. 处理用户信息
            wechat_data = {
                'openid': openid,
                'unionid': unionid,
                'nickname': user_info.get('nickname', ''),
                'avatar': user_info.get('headimgurl', ''),
                'sex': user_info.get('sex', 0),
                'city': user_info.get('city', ''),
                'province': user_info.get('province', ''),
                'country': user_info.get('country', '')
            }
            
            return {'success': True, 'data': wechat_data, 'state': state}
            
        except Exception as e:
            logger.error(f"处理微信登录异常: {str(e)}")
            return {'success': False, 'message': '微信登录处理失败'}
    
    @staticmethod
    def generate_state(action='login', user_id=None):
        """生成state参数"""
        import uuid
        import time
        state_id = str(uuid.uuid4())

        # 将state信息存储到session中
        session[f'wechat_state_{state_id}'] = {
            'action': action,
            'user_id': user_id,
            'timestamp': int(time.time())
        }

        return state_id
    
    @staticmethod
    def verify_state(state):
        """验证state参数"""
        import time

        state_key = f'wechat_state_{state}'
        state_data = session.get(state_key)

        if not state_data:
            return None

        # 检查是否过期（10分钟）
        if time.time() - state_data.get('timestamp', 0) > 600:
            session.pop(state_key, None)
            return None

        # 清除已使用的state
        session.pop(state_key, None)
        return state_data
