{% extends "base.html" %}

{% block title %}数据校验 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-check-circle"></i> 数据校验</h4>
{% endblock %}

{% block content %}
<!-- 文件上传区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-upload"></i> 文件上传
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="fileInput" class="form-label">选择数据文件</label>
                            <input type="file" class="form-control" id="fileInput" accept=".xlsx,.xls,.csv" onchange="uploadFile()">
                            <div class="form-text">支持 Excel (.xlsx, .xls) 和 CSV 格式文件</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mt-4">
                            <div id="uploadStatus" class="alert alert-info d-none">
                                <i class="fas fa-info-circle"></i>
                                <span id="uploadMessage">请选择文件</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件信息显示 -->
                <div id="fileInfo" class="mt-3 d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr><th width="30%">文件名:</th><td id="fileName"></td></tr>
                                <tr><th>文件大小:</th><td id="fileSize"></td></tr>
                                <tr><th>上传时间:</th><td id="uploadTime"></td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div id="filePreview">
                                <h6>文件预览:</h6>
                                <div id="columnsList" class="border rounded p-2" style="max-height: 120px; overflow-y: auto;">
                                    <!-- 列信息将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 验证规则配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 验证规则配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <!-- 必需列验证 -->
                        <div class="mb-3">
                            <label class="form-label">必需列验证</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableRequiredColumns">
                                <label class="form-check-label" for="enableRequiredColumns">
                                    启用必需列检查
                                </label>
                            </div>
                            <div id="requiredColumnsConfig" class="mt-2 d-none">
                                <textarea class="form-control" id="requiredColumns" rows="3" 
                                         placeholder="输入必需的列名，每行一个"></textarea>
                                <div class="form-text">每行输入一个必需的列名</div>
                            </div>
                        </div>
                        
                        <!-- 数据类型验证 -->
                        <div class="mb-3">
                            <label class="form-label">数据类型验证</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableDataTypes">
                                <label class="form-check-label" for="enableDataTypes">
                                    启用数据类型检查
                                </label>
                            </div>
                            <div id="dataTypesConfig" class="mt-2 d-none">
                                <div id="dataTypesContainer">
                                    <!-- 数据类型规则将动态添加 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addDataTypeRule()">
                                    <i class="fas fa-plus"></i> 添加规则
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <!-- 数值范围验证 -->
                        <div class="mb-3">
                            <label class="form-label">数值范围验证</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableValueRanges">
                                <label class="form-check-label" for="enableValueRanges">
                                    启用数值范围检查
                                </label>
                            </div>
                            <div id="valueRangesConfig" class="mt-2 d-none">
                                <div id="valueRangesContainer">
                                    <!-- 数值范围规则将动态添加 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addValueRangeRule()">
                                    <i class="fas fa-plus"></i> 添加规则
                                </button>
                            </div>
                        </div>
                        
                        <!-- 唯一性验证 -->
                        <div class="mb-3">
                            <label class="form-label">唯一性验证</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableUniqueConstraints">
                                <label class="form-check-label" for="enableUniqueConstraints">
                                    启用唯一性检查
                                </label>
                            </div>
                            <div id="uniqueConstraintsConfig" class="mt-2 d-none">
                                <textarea class="form-control" id="uniqueColumns" rows="3" 
                                         placeholder="输入需要唯一的列名，每行一个"></textarea>
                                <div class="form-text">每行输入一个需要唯一的列名</div>
                            </div>
                        </div>
                        
                        <!-- 非空验证 -->
                        <div class="mb-3">
                            <label class="form-label">非空验证</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableNullConstraints">
                                <label class="form-check-label" for="enableNullConstraints">
                                    启用非空检查
                                </label>
                            </div>
                            <div id="nullConstraintsConfig" class="mt-2 d-none">
                                <textarea class="form-control" id="notNullColumns" rows="3" 
                                         placeholder="输入不能为空的列名，每行一个"></textarea>
                                <div class="form-text">每行输入一个不能为空的列名</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="startValidation()">
                            <i class="fas fa-check"></i> 开始验证
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="resetValidationRules()">
                            <i class="fas fa-undo"></i> 重置规则
                        </button>
                        <button type="button" class="btn btn-info ms-2" onclick="loadPresetRules()">
                            <i class="fas fa-download"></i> 加载预设规则
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 验证进度区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks"></i> 验证进度
                </h6>
            </div>
            <div class="card-body">
                <div id="validationProgress" class="d-none">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>验证进度</span>
                            <span id="validationStatus">验证中...</span>
                        </div>
                        <div class="progress">
                            <div id="validationProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div id="validationInfo" class="alert alert-info">
                        <strong>当前状态：</strong><span id="currentValidationStatus">正在验证数据...</span>
                    </div>
                </div>
                
                <div id="noValidationProgress" class="text-center text-muted py-4">
                    <i class="fas fa-check-circle fa-3x mb-3"></i>
                    <p>上传文件并配置验证规则后，点击"开始验证"开始数据校验</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 验证结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list-alt"></i> 验证结果
                </h6>
                <div id="resultActions" class="d-none">
                    <button type="button" class="btn btn-success btn-sm" onclick="exportValidationReport()">
                        <i class="fas fa-download"></i> 导出报告
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="showDetailedErrors()">
                        <i class="fas fa-list"></i> 详细错误
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="validationResultsContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-clipboard-check fa-3x mb-3"></i>
                        <p>验证完成后结果将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细错误模态框 -->
<div class="modal fade" id="detailedErrorsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i> 详细错误信息
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailedErrorsContent">
                <!-- 详细错误内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportDetailedErrors()">导出错误列表</button>
            </div>
        </div>
    </div>
</div>

<!-- 预设规则模态框 -->
<div class="modal fade" id="presetRulesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download"></i> 预设验证规则
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>医疗数据验证规则</h6>
                        <div class="list-group">
                            <button type="button" class="list-group-item list-group-item-action" onclick="loadPreset('medical_basic')">
                                基础医疗数据验证
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" onclick="loadPreset('medical_fee')">
                                医疗费用数据验证
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" onclick="loadPreset('medical_drug')">
                                药品数据验证
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>通用数据验证规则</h6>
                        <div class="list-group">
                            <button type="button" class="list-group-item list-group-item-action" onclick="loadPreset('general_basic')">
                                基础数据验证
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" onclick="loadPreset('general_financial')">
                                财务数据验证
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" onclick="loadPreset('general_personal')">
                                个人信息验证
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentFilePath = '';
let currentFileName = '';
let validationResults = null;
let dataTypeRuleCount = 0;
let valueRangeRuleCount = 0;

// 页面加载完成后初始化
$(document).ready(function() {
    // 绑定复选框事件
    $('#enableRequiredColumns').change(function() {
        $('#requiredColumnsConfig').toggleClass('d-none', !this.checked);
    });

    $('#enableDataTypes').change(function() {
        $('#dataTypesConfig').toggleClass('d-none', !this.checked);
    });

    $('#enableValueRanges').change(function() {
        $('#valueRangesConfig').toggleClass('d-none', !this.checked);
    });

    $('#enableUniqueConstraints').change(function() {
        $('#uniqueConstraintsConfig').toggleClass('d-none', !this.checked);
    });

    $('#enableNullConstraints').change(function() {
        $('#nullConstraintsConfig').toggleClass('d-none', !this.checked);
    });
});

// 上传文件
function uploadFile() {
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];

    if (!file) {
        return;
    }

    // 检查文件类型
    const allowedTypes = ['.xlsx', '.xls', '.csv'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
        showUploadStatus('error', '不支持的文件格式，请选择 Excel 或 CSV 文件');
        return;
    }

    // 显示上传状态
    showUploadStatus('info', '正在上传文件...');

    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // 上传文件
    fetch('/data/api/upload', {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentFilePath = data.file_path;
            currentFileName = data.filename;

            showUploadStatus('success', '文件上传成功');
            displayFileInfo(data);
            loadFilePreview();
        } else {
            showUploadStatus('error', '上传失败: ' + data.error);
        }
    })
    .catch(error => {
        showUploadStatus('error', '网络错误: ' + error.message);
    });
}

// 显示上传状态
function showUploadStatus(type, message) {
    const statusDiv = document.getElementById('uploadStatus');
    const messageSpan = document.getElementById('uploadMessage');

    statusDiv.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
    statusDiv.classList.remove('d-none');
    messageSpan.textContent = message;
}

// 显示文件信息
function displayFileInfo(data) {
    document.getElementById('fileName').textContent = data.filename;
    document.getElementById('fileSize').textContent = formatFileSize(data.file_size);
    document.getElementById('uploadTime').textContent = new Date().toLocaleString();
    document.getElementById('fileInfo').classList.remove('d-none');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 加载文件预览
function loadFilePreview() {
    // 这里应该调用API获取文件列信息
    // 暂时使用示例数据
    const sampleColumns = ['患者姓名', '身份证号', '就诊日期', '科室名称', '费用金额', '医生姓名'];

    const columnsList = document.getElementById('columnsList');
    columnsList.innerHTML = '';

    sampleColumns.forEach(column => {
        const columnDiv = document.createElement('div');
        columnDiv.className = 'badge bg-secondary me-1 mb-1';
        columnDiv.textContent = column;
        columnsList.appendChild(columnDiv);
    });
}

// 添加数据类型规则
function addDataTypeRule() {
    const container = document.getElementById('dataTypesContainer');
    const ruleHtml = `
        <div class="row mb-2" id="dataTypeRule_${dataTypeRuleCount}">
            <div class="col-md-6">
                <input type="text" class="form-control" placeholder="列名" name="datatype_column_${dataTypeRuleCount}">
            </div>
            <div class="col-md-4">
                <select class="form-select" name="datatype_type_${dataTypeRuleCount}">
                    <option value="numeric">数值</option>
                    <option value="date">日期</option>
                    <option value="text">文本</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeDataTypeRule(${dataTypeRuleCount})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', ruleHtml);
    dataTypeRuleCount++;
}

// 删除数据类型规则
function removeDataTypeRule(index) {
    document.getElementById(`dataTypeRule_${index}`).remove();
}

// 添加数值范围规则
function addValueRangeRule() {
    const container = document.getElementById('valueRangesContainer');
    const ruleHtml = `
        <div class="row mb-2" id="valueRangeRule_${valueRangeRuleCount}">
            <div class="col-md-4">
                <input type="text" class="form-control" placeholder="列名" name="range_column_${valueRangeRuleCount}">
            </div>
            <div class="col-md-3">
                <input type="number" class="form-control" placeholder="最小值" name="range_min_${valueRangeRuleCount}">
            </div>
            <div class="col-md-3">
                <input type="number" class="form-control" placeholder="最大值" name="range_max_${valueRangeRuleCount}">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeValueRangeRule(${valueRangeRuleCount})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', ruleHtml);
    valueRangeRuleCount++;
}

// 删除数值范围规则
function removeValueRangeRule(index) {
    document.getElementById(`valueRangeRule_${index}`).remove();
}

// 开始验证
function startValidation() {
    if (!currentFilePath) {
        showAlert('请先上传文件', 'warning');
        return;
    }

    // 收集验证规则
    const validationRules = {};

    // 必需列
    if (document.getElementById('enableRequiredColumns').checked) {
        const requiredColumns = document.getElementById('requiredColumns').value
            .split('\n')
            .map(col => col.trim())
            .filter(col => col);
        if (requiredColumns.length > 0) {
            validationRules.required_columns = requiredColumns;
        }
    }

    // 数据类型
    if (document.getElementById('enableDataTypes').checked) {
        const dataTypes = {};
        for (let i = 0; i < dataTypeRuleCount; i++) {
            const columnInput = document.querySelector(`input[name="datatype_column_${i}"]`);
            const typeSelect = document.querySelector(`select[name="datatype_type_${i}"]`);

            if (columnInput && typeSelect && columnInput.value.trim()) {
                dataTypes[columnInput.value.trim()] = typeSelect.value;
            }
        }
        if (Object.keys(dataTypes).length > 0) {
            validationRules.data_types = dataTypes;
        }
    }

    // 数值范围
    if (document.getElementById('enableValueRanges').checked) {
        const valueRanges = {};
        for (let i = 0; i < valueRangeRuleCount; i++) {
            const columnInput = document.querySelector(`input[name="range_column_${i}"]`);
            const minInput = document.querySelector(`input[name="range_min_${i}"]`);
            const maxInput = document.querySelector(`input[name="range_max_${i}"]`);

            if (columnInput && columnInput.value.trim()) {
                const rangeConfig = {};
                if (minInput && minInput.value) {
                    rangeConfig.min = parseFloat(minInput.value);
                }
                if (maxInput && maxInput.value) {
                    rangeConfig.max = parseFloat(maxInput.value);
                }
                if (Object.keys(rangeConfig).length > 0) {
                    valueRanges[columnInput.value.trim()] = rangeConfig;
                }
            }
        }
        if (Object.keys(valueRanges).length > 0) {
            validationRules.value_ranges = valueRanges;
        }
    }

    // 唯一性约束
    if (document.getElementById('enableUniqueConstraints').checked) {
        const uniqueColumns = document.getElementById('uniqueColumns').value
            .split('\n')
            .map(col => col.trim())
            .filter(col => col);
        if (uniqueColumns.length > 0) {
            validationRules.unique_constraints = uniqueColumns;
        }
    }

    // 非空约束
    if (document.getElementById('enableNullConstraints').checked) {
        const notNullColumns = document.getElementById('notNullColumns').value
            .split('\n')
            .map(col => col.trim())
            .filter(col => col);
        if (notNullColumns.length > 0) {
            validationRules.null_constraints = notNullColumns;
        }
    }

    if (Object.keys(validationRules).length === 0) {
        showAlert('请至少配置一个验证规则', 'warning');
        return;
    }

    // 显示进度
    document.getElementById('validationProgress').classList.remove('d-none');
    document.getElementById('noValidationProgress').classList.add('d-none');
    document.getElementById('validationProgressBar').style.width = '0%';
    document.getElementById('validationStatus').textContent = '验证中...';

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // 发送验证请求
    fetch('/data/api/validate_data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            file_path: currentFilePath,
            validation_rules: validationRules
        })
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('validationProgress').classList.add('d-none');
        document.getElementById('noValidationProgress').classList.remove('d-none');

        if (data.success) {
            validationResults = data.validation_result;
            displayValidationResults(data.validation_result);

            const summary = data.validation_result.summary;
            if (summary.validation_passed) {
                showAlert('数据验证通过！', 'success');
            } else {
                showAlert(`数据验证失败：发现${summary.total_errors}个错误`, 'warning');
            }
        } else {
            showAlert('验证失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        document.getElementById('validationProgress').classList.add('d-none');
        document.getElementById('noValidationProgress').classList.remove('d-none');
        showAlert('网络错误: ' + error.message, 'danger');
    });
}

// 显示验证结果
function displayValidationResults(results) {
    const { file_info, validation_errors, validation_warnings, summary } = results;

    let resultsHtml = `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5>${file_info.rows}</h5>
                        <p class="mb-0">总行数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h5>${file_info.columns}</h5>
                        <p class="mb-0">总列数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-${summary.total_errors > 0 ? 'danger' : 'success'} text-white">
                    <div class="card-body text-center">
                        <h5>${summary.total_errors}</h5>
                        <p class="mb-0">错误数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h5>${summary.total_warnings}</h5>
                        <p class="mb-0">警告数</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h6>验证错误</h6>
                <div class="list-group" style="max-height: 300px; overflow-y: auto;">
    `;

    if (validation_errors.length === 0) {
        resultsHtml += '<div class="list-group-item list-group-item-success">没有发现错误</div>';
    } else {
        validation_errors.forEach(error => {
            resultsHtml += `
                <div class="list-group-item list-group-item-danger">
                    <strong>${error.type}:</strong> ${error.message}
                </div>
            `;
        });
    }

    resultsHtml += `
                </div>
            </div>
            <div class="col-md-6">
                <h6>验证警告</h6>
                <div class="list-group" style="max-height: 300px; overflow-y: auto;">
    `;

    if (validation_warnings.length === 0) {
        resultsHtml += '<div class="list-group-item list-group-item-success">没有警告</div>';
    } else {
        validation_warnings.forEach(warning => {
            resultsHtml += `
                <div class="list-group-item list-group-item-warning">
                    <strong>${warning.type}:</strong> ${warning.message}
                </div>
            `;
        });
    }

    resultsHtml += `
                </div>
            </div>
        </div>
    `;

    document.getElementById('validationResultsContainer').innerHTML = resultsHtml;
    document.getElementById('resultActions').classList.remove('d-none');
}

// 重置验证规则
function resetValidationRules() {
    // 重置所有复选框
    document.getElementById('enableRequiredColumns').checked = false;
    document.getElementById('enableDataTypes').checked = false;
    document.getElementById('enableValueRanges').checked = false;
    document.getElementById('enableUniqueConstraints').checked = false;
    document.getElementById('enableNullConstraints').checked = false;

    // 隐藏所有配置区域
    document.getElementById('requiredColumnsConfig').classList.add('d-none');
    document.getElementById('dataTypesConfig').classList.add('d-none');
    document.getElementById('valueRangesConfig').classList.add('d-none');
    document.getElementById('uniqueConstraintsConfig').classList.add('d-none');
    document.getElementById('nullConstraintsConfig').classList.add('d-none');

    // 清空输入框
    document.getElementById('requiredColumns').value = '';
    document.getElementById('uniqueColumns').value = '';
    document.getElementById('notNullColumns').value = '';

    // 清空动态规则
    document.getElementById('dataTypesContainer').innerHTML = '';
    document.getElementById('valueRangesContainer').innerHTML = '';

    dataTypeRuleCount = 0;
    valueRangeRuleCount = 0;

    showAlert('验证规则已重置', 'info');
}

// 加载预设规则
function loadPresetRules() {
    document.getElementById('presetRulesModal').querySelector('.modal').modal('show');
}

// 加载预设
function loadPreset(presetType) {
    // 这里可以根据预设类型加载不同的验证规则
    showAlert(`预设规则 "${presetType}" 加载功能开发中...`, 'info');
    $('#presetRulesModal').modal('hide');
}

// 导出验证报告
function exportValidationReport() {
    if (!validationResults) {
        showAlert('没有可导出的验证结果', 'warning');
        return;
    }

    showAlert('导出功能开发中...', 'info');
}

// 显示详细错误
function showDetailedErrors() {
    if (!validationResults) {
        showAlert('没有可显示的详细错误', 'warning');
        return;
    }

    // 生成详细错误内容
    let errorContent = '<h6>详细错误列表</h6>';
    // 这里可以添加更详细的错误信息

    document.getElementById('detailedErrorsContent').innerHTML = errorContent;
    document.getElementById('detailedErrorsModal').querySelector('.modal').modal('show');
}

// 导出详细错误
function exportDetailedErrors() {
    showAlert('详细错误导出功能开发中...', 'info');
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
