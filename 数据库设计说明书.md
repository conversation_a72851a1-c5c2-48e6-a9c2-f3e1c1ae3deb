# MICRA自查自纠模块数据库设计说明书

**文档版本：** V1.0  
**编写日期：** 2025年1月7日  
**项目名称：** MICRA工具箱 - 自查自纠模块  
**文档类型：** 数据库设计说明书  

## 1. 设计概述

### 1.1 设计目标
本文档详细描述MICRA自查自纠模块的数据库设计，包括表结构、索引、约束、存储过程等，为数据库实施和维护提供技术依据。

### 1.2 设计原则
- **数据完整性：** 通过约束确保数据的准确性和一致性
- **性能优化：** 合理的索引设计，支持高效查询
- **扩展性：** 预留扩展字段，支持功能演进
- **安全性：** 敏感数据加密，访问权限控制

### 1.3 数据库环境
- **数据库类型：** Oracle Database 11g+
- **字符集：** UTF-8 (AL32UTF8)
- **时区：** Asia/Shanghai
- **存储引擎：** InnoDB (对于支持的表)

## 2. 数据库架构

### 2.1 逻辑架构
```
MICRA数据库
├── 用户管理模块
│   ├── users (用户表)
│   ├── roles (角色表)
│   └── permissions (权限表)
├── 自查自纠模块
│   ├── selfcheck_uploads (上传记录表)
│   ├── selfcheck_schemes (方案表)
│   ├── selfcheck_scheme_rules (方案规则关联表)
│   ├── selfcheck_tasks (任务表)
│   └── selfcheck_results (结果表)
└── 规则管理模块
    ├── selfcheck_rules (规则表)
    └── rule_categories (规则分类表)
```

### 2.2 表空间设计
```sql
-- 数据表空间
CREATE TABLESPACE MICRA_DATA
DATAFILE '/oracle/data/micra_data01.dbf' SIZE 1G
AUTOEXTEND ON NEXT 100M MAXSIZE 10G;

-- 索引表空间
CREATE TABLESPACE MICRA_INDEX
DATAFILE '/oracle/data/micra_index01.dbf' SIZE 500M
AUTOEXTEND ON NEXT 50M MAXSIZE 5G;

-- 临时表空间
CREATE TEMPORARY TABLESPACE MICRA_TEMP
TEMPFILE '/oracle/data/micra_temp01.dbf' SIZE 200M
AUTOEXTEND ON NEXT 50M MAXSIZE 2G;
```

## 3. 核心表设计

### 3.1 上传记录表 (selfcheck_uploads)

#### 3.1.1 表结构
```sql
CREATE TABLE selfcheck_uploads (
    id                  NUMBER(19)      PRIMARY KEY,
    user_id             NUMBER(19)      NOT NULL,
    file_name           VARCHAR2(500)   NOT NULL,
    file_path           VARCHAR2(1000)  NOT NULL,
    file_size           NUMBER(19)      DEFAULT 0,
    file_type           VARCHAR2(50),
    upload_time         TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    status              VARCHAR2(20)    DEFAULT 'pending',
    validation_result   CLOB,
    error_message       CLOB,
    record_count        NUMBER(19)      DEFAULT 0,
    created_at          TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    updated_at          TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_uploads_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT chk_uploads_status CHECK (status IN ('pending', 'validating', 'validated', 'failed')),
    CONSTRAINT chk_uploads_file_type CHECK (file_type IN ('csv', 'dmp', 'dp', 'bak'))
) TABLESPACE MICRA_DATA;
```

#### 3.1.2 字段说明
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | NUMBER(19) | 主键ID | PK, NOT NULL |
| user_id | NUMBER(19) | 用户ID | FK, NOT NULL |
| file_name | VARCHAR2(500) | 原始文件名 | NOT NULL |
| file_path | VARCHAR2(1000) | 文件存储路径 | NOT NULL |
| file_size | NUMBER(19) | 文件大小(字节) | DEFAULT 0 |
| file_type | VARCHAR2(50) | 文件类型 | CHECK约束 |
| upload_time | TIMESTAMP | 上传时间 | DEFAULT CURRENT_TIMESTAMP |
| status | VARCHAR2(20) | 处理状态 | CHECK约束 |
| validation_result | CLOB | 验证结果详情 | - |
| error_message | CLOB | 错误信息 | - |
| record_count | NUMBER(19) | 记录数量 | DEFAULT 0 |

#### 3.1.3 索引设计
```sql
-- 主键索引（自动创建）
-- CREATE UNIQUE INDEX pk_uploads ON selfcheck_uploads(id);

-- 用户状态复合索引
CREATE INDEX idx_uploads_user_status ON selfcheck_uploads(user_id, status)
TABLESPACE MICRA_INDEX;

-- 上传时间索引
CREATE INDEX idx_uploads_time ON selfcheck_uploads(upload_time)
TABLESPACE MICRA_INDEX;

-- 文件类型索引
CREATE INDEX idx_uploads_type ON selfcheck_uploads(file_type)
TABLESPACE MICRA_INDEX;
```

### 3.2 方案表 (selfcheck_schemes)

#### 3.2.1 表结构
```sql
CREATE TABLE selfcheck_schemes (
    id              NUMBER(19)      PRIMARY KEY,
    scheme_name     VARCHAR2(200)   NOT NULL,
    description     CLOB,
    status          VARCHAR2(20)    DEFAULT 'active',
    created_by      NUMBER(19)      NOT NULL,
    created_at      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_schemes_creator FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT chk_schemes_status CHECK (status IN ('active', 'inactive', 'draft')),
    CONSTRAINT uk_schemes_name UNIQUE (scheme_name)
) TABLESPACE MICRA_DATA;
```

#### 3.2.2 字段说明
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | NUMBER(19) | 主键ID | PK, NOT NULL |
| scheme_name | VARCHAR2(200) | 方案名称 | NOT NULL, UNIQUE |
| description | CLOB | 方案描述 | - |
| status | VARCHAR2(20) | 方案状态 | CHECK约束 |
| created_by | NUMBER(19) | 创建人ID | FK, NOT NULL |
| created_at | TIMESTAMP | 创建时间 | DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | 更新时间 | DEFAULT CURRENT_TIMESTAMP |

### 3.3 方案规则关联表 (selfcheck_scheme_rules)

#### 3.3.1 表结构
```sql
CREATE TABLE selfcheck_scheme_rules (
    id              NUMBER(19)      PRIMARY KEY,
    scheme_id       NUMBER(19)      NOT NULL,
    rule_id         NUMBER(19)      NOT NULL,
    sort_order      NUMBER(10)      DEFAULT 0,
    is_enabled      NUMBER(1)       DEFAULT 1,
    created_at      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_scheme_rules_scheme FOREIGN KEY (scheme_id) REFERENCES selfcheck_schemes(id) ON DELETE CASCADE,
    CONSTRAINT fk_scheme_rules_rule FOREIGN KEY (rule_id) REFERENCES selfcheck_rules(id),
    CONSTRAINT uk_scheme_rules UNIQUE (scheme_id, rule_id),
    CONSTRAINT chk_scheme_rules_enabled CHECK (is_enabled IN (0, 1))
) TABLESPACE MICRA_DATA;
```

#### 3.3.2 索引设计
```sql
-- 方案ID索引
CREATE INDEX idx_scheme_rules_scheme ON selfcheck_scheme_rules(scheme_id)
TABLESPACE MICRA_INDEX;

-- 规则ID索引
CREATE INDEX idx_scheme_rules_rule ON selfcheck_scheme_rules(rule_id)
TABLESPACE MICRA_INDEX;

-- 排序索引
CREATE INDEX idx_scheme_rules_order ON selfcheck_scheme_rules(scheme_id, sort_order)
TABLESPACE MICRA_INDEX;
```

### 3.4 任务表 (selfcheck_tasks)

#### 3.4.1 表结构
```sql
CREATE TABLE selfcheck_tasks (
    id              NUMBER(19)      PRIMARY KEY,
    task_name       VARCHAR2(200)   NOT NULL,
    user_id         NUMBER(19)      NOT NULL,
    scheme_id       NUMBER(19)      NOT NULL,
    data_source     VARCHAR2(100)   NOT NULL,
    status          VARCHAR2(20)    DEFAULT 'pending',
    progress        NUMBER(5,2)     DEFAULT 0,
    start_time      TIMESTAMP,
    end_time        TIMESTAMP,
    error_count     NUMBER(10)      DEFAULT 0,
    warning_count   NUMBER(10)      DEFAULT 0,
    result_summary  CLOB,
    error_message   CLOB,
    created_at      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_tasks_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_tasks_scheme FOREIGN KEY (scheme_id) REFERENCES selfcheck_schemes(id),
    CONSTRAINT chk_tasks_status CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    CONSTRAINT chk_tasks_progress CHECK (progress >= 0 AND progress <= 100)
) TABLESPACE MICRA_DATA;
```

#### 3.4.2 索引设计
```sql
-- 用户任务索引
CREATE INDEX idx_tasks_user ON selfcheck_tasks(user_id, status)
TABLESPACE MICRA_INDEX;

-- 方案任务索引
CREATE INDEX idx_tasks_scheme ON selfcheck_tasks(scheme_id)
TABLESPACE MICRA_INDEX;

-- 创建时间索引
CREATE INDEX idx_tasks_created ON selfcheck_tasks(created_at)
TABLESPACE MICRA_INDEX;
```

### 3.5 规则表 (selfcheck_rules)

#### 3.5.1 表结构
```sql
CREATE TABLE selfcheck_rules (
    id              NUMBER(19)      PRIMARY KEY,
    rule_name       VARCHAR2(200)   NOT NULL,
    rule_type       VARCHAR2(50)    NOT NULL,
    sql_content     CLOB            NOT NULL,
    description     CLOB,
    city            VARCHAR2(50),
    visit_type      VARCHAR2(20),   -- 门诊/住院
    data_type       VARCHAR2(20),   -- 定量/定性
    status          VARCHAR2(20)    DEFAULT 'active',
    created_by      NUMBER(19)      NOT NULL,
    created_at      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP       DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_rules_creator FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT chk_rules_status CHECK (status IN ('active', 'inactive', 'draft')),
    CONSTRAINT chk_rules_visit_type CHECK (visit_type IN ('outpatient', 'inpatient', 'both')),
    CONSTRAINT chk_rules_data_type CHECK (data_type IN ('quantitative', 'qualitative'))
) TABLESPACE MICRA_DATA;
```

## 4. 序列设计

### 4.1 主键序列
```sql
-- 上传记录序列
CREATE SEQUENCE selfcheck_uploads_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 方案序列
CREATE SEQUENCE selfcheck_schemes_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 方案规则关联序列
CREATE SEQUENCE selfcheck_scheme_rules_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 任务序列
CREATE SEQUENCE selfcheck_tasks_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;

-- 规则序列
CREATE SEQUENCE selfcheck_rules_seq
START WITH 1
INCREMENT BY 1
NOCACHE
NOCYCLE;
```

## 5. 触发器设计

### 5.1 更新时间触发器
```sql
-- 上传记录更新时间触发器
CREATE OR REPLACE TRIGGER trg_uploads_updated_at
    BEFORE UPDATE ON selfcheck_uploads
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;

-- 方案更新时间触发器
CREATE OR REPLACE TRIGGER trg_schemes_updated_at
    BEFORE UPDATE ON selfcheck_schemes
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;

-- 任务更新时间触发器
CREATE OR REPLACE TRIGGER trg_tasks_updated_at
    BEFORE UPDATE ON selfcheck_tasks
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
```

## 6. 视图设计

### 6.1 上传记录视图
```sql
CREATE OR REPLACE VIEW v_selfcheck_uploads AS
SELECT 
    u.id,
    u.user_id,
    usr.username,
    usr.real_name,
    u.file_name,
    u.file_size,
    u.file_type,
    u.upload_time,
    u.status,
    u.record_count,
    u.validation_result,
    u.error_message
FROM selfcheck_uploads u
LEFT JOIN users usr ON u.user_id = usr.id;
```

### 6.2 方案规则视图
```sql
CREATE OR REPLACE VIEW v_scheme_rules AS
SELECT 
    sr.id,
    sr.scheme_id,
    s.scheme_name,
    sr.rule_id,
    r.rule_name,
    r.rule_type,
    r.city,
    sr.sort_order,
    sr.is_enabled,
    sr.created_at
FROM selfcheck_scheme_rules sr
JOIN selfcheck_schemes s ON sr.scheme_id = s.id
JOIN selfcheck_rules r ON sr.rule_id = r.id
WHERE s.status = 'active';
```

## 7. 存储过程设计

### 7.1 批量更新方案规则排序
```sql
CREATE OR REPLACE PROCEDURE sp_update_scheme_rules_order(
    p_scheme_id IN NUMBER,
    p_rule_orders IN VARCHAR2  -- JSON格式: [{"rule_id":1,"sort_order":1}]
) AS
    v_json_obj JSON_OBJECT_T;
    v_json_array JSON_ARRAY_T;
    v_rule_obj JSON_OBJECT_T;
    v_rule_id NUMBER;
    v_sort_order NUMBER;
BEGIN
    -- 解析JSON数组
    v_json_array := JSON_ARRAY_T.parse(p_rule_orders);
    
    -- 遍历更新
    FOR i IN 0 .. v_json_array.get_size - 1 LOOP
        v_rule_obj := JSON_OBJECT_T(v_json_array.get(i));
        v_rule_id := v_rule_obj.get_number('rule_id');
        v_sort_order := v_rule_obj.get_number('sort_order');
        
        UPDATE selfcheck_scheme_rules 
        SET sort_order = v_sort_order
        WHERE scheme_id = p_scheme_id AND rule_id = v_rule_id;
    END LOOP;
    
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END;
```

## 8. 数据初始化

### 8.1 基础数据插入
```sql
-- 插入默认方案
INSERT INTO selfcheck_schemes (id, scheme_name, description, created_by)
VALUES (selfcheck_schemes_seq.NEXTVAL, '默认检查方案', '系统默认的数据检查方案', 1);

-- 插入示例规则
INSERT INTO selfcheck_rules (id, rule_name, rule_type, sql_content, created_by)
VALUES (selfcheck_rules_seq.NEXTVAL, '重复记录检查', 'duplicate_check', 
        'SELECT * FROM (SELECT *, ROW_NUMBER() OVER (PARTITION BY patient_id ORDER BY visit_date) rn FROM medical_records) WHERE rn > 1', 1);
```

## 9. 性能优化

### 9.1 分区策略
```sql
-- 按月分区的任务表
CREATE TABLE selfcheck_tasks_partitioned (
    -- 字段定义同selfcheck_tasks
) PARTITION BY RANGE (created_at) (
    PARTITION p_202501 VALUES LESS THAN (DATE '2025-02-01'),
    PARTITION p_202502 VALUES LESS THAN (DATE '2025-03-01'),
    PARTITION p_202503 VALUES LESS THAN (DATE '2025-04-01')
);
```

### 9.2 统计信息收集
```sql
-- 收集表统计信息
BEGIN
    DBMS_STATS.GATHER_TABLE_STATS('MICRA', 'SELFCHECK_UPLOADS');
    DBMS_STATS.GATHER_TABLE_STATS('MICRA', 'SELFCHECK_SCHEMES');
    DBMS_STATS.GATHER_TABLE_STATS('MICRA', 'SELFCHECK_TASKS');
END;
```

## 10. 备份与恢复

### 10.1 备份策略
```sql
-- 逻辑备份
expdp micra/password directory=backup_dir dumpfile=selfcheck_%U.dmp 
      tables=selfcheck_uploads,selfcheck_schemes,selfcheck_tasks
      parallel=4 compression=all;

-- 物理备份
RMAN> BACKUP TABLESPACE MICRA_DATA FORMAT '/backup/micra_data_%U.bak';
```

---

**文档状态：** 已审核  
**批准人：** 数据库管理员  
**批准日期：** 2025年1月7日  
**版本历史：**
- V1.0 (2025-01-07): 初始版本，包含核心表设计和优化方案
