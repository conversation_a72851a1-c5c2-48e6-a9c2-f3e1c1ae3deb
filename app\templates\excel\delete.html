{% extends "base.html" %}

{% block title %}Excel内容删除 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-trash-alt"></i> Excel内容删除</h4>
{% endblock %}

{% block content %}
<!-- 文件上传区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-upload"></i> 文件上传
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="sourceFileInput" class="form-label">源文件（要处理的Excel文件）</label>
                            <input type="file" class="form-control" id="sourceFileInput" accept=".xlsx,.xls" onchange="uploadSourceFile()">
                            <div class="form-text">选择要删除内容的Excel文件</div>
                        </div>
                        
                        <div id="sourceFileInfo" class="mt-3 d-none">
                            <div class="alert alert-info">
                                <strong>源文件信息：</strong>
                                <div id="sourceFileName"></div>
                                <div id="sourceFileSize"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="deleteFileInput" class="form-label">删除条件文件（包含要删除内容的Excel文件）</label>
                            <input type="file" class="form-control" id="deleteFileInput" accept=".xlsx,.xls" onchange="uploadDeleteFile()">
                            <div class="form-text">选择包含要删除内容列表的Excel文件</div>
                        </div>
                        
                        <div id="deleteFileInfo" class="mt-3 d-none">
                            <div class="alert alert-warning">
                                <strong>删除条件文件信息：</strong>
                                <div id="deleteFileName"></div>
                                <div id="deleteFileSize"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 删除配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="sourceColumn" class="form-label">源文件匹配列</label>
                            <select class="form-select" id="sourceColumn">
                                <option value="">请先上传源文件</option>
                            </select>
                            <div class="form-text">选择源文件中用于匹配的列</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="deleteColumn" class="form-label">删除条件列</label>
                            <select class="form-select" id="deleteColumn">
                                <option value="">请先上传删除条件文件</option>
                            </select>
                            <div class="form-text">选择删除条件文件中包含要删除内容的列</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">匹配模式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="matchMode" id="exactMatch" value="exact" checked>
                                <label class="form-check-label" for="exactMatch">
                                    精确匹配
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="matchMode" id="containsMatch" value="contains">
                                <label class="form-check-label" for="containsMatch">
                                    包含匹配
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="matchMode" id="regexMatch" value="regex">
                                <label class="form-check-label" for="regexMatch">
                                    正则表达式匹配
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="caseSensitive">
                                <label class="form-check-label" for="caseSensitive">
                                    区分大小写
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="createBackup" checked>
                                <label class="form-check-label" for="createBackup">
                                    创建备份文件
                                </label>
                            </div>
                            <div class="form-text">建议保留此选项以防误删</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn btn-danger" onclick="startDelete()" disabled id="deleteBtn">
                            <i class="fas fa-trash-alt"></i> 开始删除
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="previewDelete()" disabled id="previewBtn">
                            <i class="fas fa-eye"></i> 预览删除
                        </button>
                        <button type="button" class="btn btn-info ms-2" onclick="resetForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览结果区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-eye"></i> 预览结果
                </h6>
            </div>
            <div class="card-body">
                <div id="previewContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p>配置删除条件后，点击"预览删除"查看将要删除的内容</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 处理进度区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks"></i> 处理进度
                </h6>
            </div>
            <div class="card-body">
                <div id="deleteProgress" class="d-none">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>删除进度</span>
                            <span id="deleteStatus">处理中...</span>
                        </div>
                        <div class="progress">
                            <div id="deleteProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div id="deleteInfo" class="alert alert-info">
                        <strong>当前状态：</strong><span id="currentDeleteStatus">正在处理...</span>
                    </div>
                </div>
                
                <div id="noDeleteProgress" class="text-center text-muted py-4">
                    <i class="fas fa-trash-alt fa-3x mb-3"></i>
                    <p>配置删除条件后，点击"开始删除"开始处理</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 处理结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list-alt"></i> 处理结果
                </h6>
                <div id="resultActions" class="d-none">
                    <button type="button" class="btn btn-success btn-sm" onclick="downloadResult()">
                        <i class="fas fa-download"></i> 下载结果
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="showDeleteLog()">
                        <i class="fas fa-list"></i> 删除日志
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="deleteResultsContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-file-excel fa-3x mb-3"></i>
                        <p>删除完成后结果将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除日志模态框 -->
<div class="modal fade" id="deleteLogModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list"></i> 删除操作日志
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="deleteLogContent">
                <!-- 删除日志内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportDeleteLog()">导出日志</button>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye"></i> 删除预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewModalContent">
                <!-- 预览内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let sourceFilePath = '';
let deleteFilePath = '';
let deleteResults = null;

// 上传源文件
function uploadSourceFile() {
    const fileInput = document.getElementById('sourceFileInput');
    const file = fileInput.files[0];
    
    if (!file) return;
    
    uploadFile(file, 'source');
}

// 上传删除条件文件
function uploadDeleteFile() {
    const fileInput = document.getElementById('deleteFileInput');
    const file = fileInput.files[0];
    
    if (!file) return;
    
    uploadFile(file, 'delete');
}

// 通用文件上传函数
function uploadFile(file, type) {
    const formData = new FormData();
    formData.append('file', file);
    
    fetch('/excel/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (type === 'source') {
                sourceFilePath = data.file_path;
                document.getElementById('sourceFileName').textContent = `文件名: ${data.filename}`;
                document.getElementById('sourceFileSize').textContent = `大小: ${formatFileSize(data.file_size)}`;
                document.getElementById('sourceFileInfo').classList.remove('d-none');
                loadFileColumns(data.file_path, 'sourceColumn');
            } else {
                deleteFilePath = data.file_path;
                document.getElementById('deleteFileName').textContent = `文件名: ${data.filename}`;
                document.getElementById('deleteFileSize').textContent = `大小: ${formatFileSize(data.file_size)}`;
                document.getElementById('deleteFileInfo').classList.remove('d-none');
                loadFileColumns(data.file_path, 'deleteColumn');
            }
            
            checkFormComplete();
        } else {
            showAlert('上传失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('网络错误: ' + error.message, 'danger');
    });
}

// 加载文件列信息
function loadFileColumns(filePath, selectId) {
    // 这里应该调用API获取文件列信息
    // 暂时使用示例数据
    const sampleColumns = ['患者姓名', '身份证号', '就诊日期', '科室名称', '费用金额', '医生姓名'];
    
    const select = document.getElementById(selectId);
    select.innerHTML = '<option value="">请选择列</option>';
    
    sampleColumns.forEach(column => {
        const option = document.createElement('option');
        option.value = column;
        option.textContent = column;
        select.appendChild(option);
    });
}

// 检查表单是否完整
function checkFormComplete() {
    const sourceColumn = document.getElementById('sourceColumn').value;
    const deleteColumn = document.getElementById('deleteColumn').value;
    
    const isComplete = sourceFilePath && deleteFilePath && sourceColumn && deleteColumn;
    
    document.getElementById('deleteBtn').disabled = !isComplete;
    document.getElementById('previewBtn').disabled = !isComplete;
}

// 预览删除
function previewDelete() {
    const config = getDeleteConfig();
    
    // 这里应该调用API进行预览
    showAlert('预览功能开发中...', 'info');
}

// 开始删除
function startDelete() {
    if (!confirm('确定要执行删除操作吗？此操作不可恢复！')) {
        return;
    }
    
    const config = getDeleteConfig();
    
    // 显示进度
    document.getElementById('deleteProgress').classList.remove('d-none');
    document.getElementById('noDeleteProgress').classList.add('d-none');
    
    // 这里应该调用API执行删除
    showAlert('删除功能开发中...', 'info');
}

// 获取删除配置
function getDeleteConfig() {
    return {
        source_file: sourceFilePath,
        delete_file: deleteFilePath,
        source_column: document.getElementById('sourceColumn').value,
        delete_column: document.getElementById('deleteColumn').value,
        match_mode: document.querySelector('input[name="matchMode"]:checked').value,
        case_sensitive: document.getElementById('caseSensitive').checked,
        create_backup: document.getElementById('createBackup').checked
    };
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 重置表单
function resetForm() {
    sourceFilePath = '';
    deleteFilePath = '';
    
    document.getElementById('sourceFileInput').value = '';
    document.getElementById('deleteFileInput').value = '';
    document.getElementById('sourceFileInfo').classList.add('d-none');
    document.getElementById('deleteFileInfo').classList.add('d-none');
    
    document.getElementById('sourceColumn').innerHTML = '<option value="">请先上传源文件</option>';
    document.getElementById('deleteColumn').innerHTML = '<option value="">请先上传删除条件文件</option>';
    
    document.getElementById('exactMatch').checked = true;
    document.getElementById('caseSensitive').checked = false;
    document.getElementById('createBackup').checked = true;
    
    checkFormComplete();
    
    showAlert('表单已重置', 'info');
}

// 下载结果
function downloadResult() {
    if (!deleteResults) {
        showAlert('没有可下载的结果', 'warning');
        return;
    }
    
    showAlert('下载功能开发中...', 'info');
}

// 显示删除日志
function showDeleteLog() {
    document.getElementById('deleteLogModal').querySelector('.modal').modal('show');
}

// 导出删除日志
function exportDeleteLog() {
    showAlert('日志导出功能开发中...', 'info');
}

// 确认删除
function confirmDelete() {
    $('#previewModal').modal('hide');
    startDelete();
}

// 使用base.html中的统一showAlert函数

// 页面加载完成后绑定事件
$(document).ready(function() {
    // 绑定列选择变化事件
    $('#sourceColumn, #deleteColumn').change(checkFormComplete);
});
</script>
{% endblock %}
