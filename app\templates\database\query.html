{% extends "base.html" %}

{% block title %}数据库查询工具 - MICRA飞检数据处理工具箱{% endblock %}

{% block styles %}
<style>
/* 查询结果表格样式优化 */
#resultTable {
    font-size: 0.9rem;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#resultTable thead th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    border: none;
    font-weight: 600;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 12px 8px;
}

#resultTable tbody td {
    padding: 10px 8px;
    border-color: #e9ecef;
    vertical-align: middle;
    transition: background-color 0.2s ease;
}

#resultTable tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 数据类型样式 */
.font-monospace {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

/* 状态徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 6px 10px;
    border-radius: 20px;
}

/* 加载指示器样式 */
#loadingIndicator {
    padding: 40px 0;
}

#loadingIndicator .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 空结果样式 */
#emptyResult {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.5rem;
    margin: 20px 0;
}

/* 数据库配置信息样式 */
#dbConfigInfo {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 8px 12px;
    border-left: 4px solid #007bff;
}

/* 结果信息样式 */
#resultInfo {
    font-size: 0.9rem;
}

/* 表格容器样式 */
.table-responsive {
    border-radius: 0.5rem;
    max-height: 600px;
    overflow-y: auto;
}

/* 滚动条样式 */
.table-responsive::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
{% endblock %}



{% block content %}
<!-- SQL编辑器区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-code"></i> SQL查询编辑器
                </h6>
                <div>
                    <button class="btn btn-success btn-sm" onclick="executeQuery()">
                        <i class="fas fa-play"></i> 执行查询
                    </button>
                    <div class="btn-group" role="group">
                        <button class="btn btn-primary btn-sm" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i> 导出Excel
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="exportToCSV()">
                            <i class="fas fa-file-csv"></i> 导出CSV
                        </button>
                    </div>
                    <button class="btn btn-info btn-sm" onclick="showTemplates()">
                        <i class="fas fa-template"></i> 模板
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 数据库配置区域 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label class="form-label">数据库类型</label>
                        <select class="form-select" id="dbType">
                            <option value="PostgreSQL">PostgreSQL</option>
                            <option value="Oracle" selected>Oracle</option>
                            <option value="MySQL">MySQL</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">数据库主机IP</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="dbHost" value="*************" placeholder="请输入数据库主机IP">
                            <button class="btn btn-outline-secondary" type="button" onclick="loadSchemas()" title="获取Schema列表">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">站点Schema</label>
                        <select class="form-select" id="dbSchema">
                            <option value="">请先获取Schema列表</option>
                        </select>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            输入主机IP后点击刷新按钮获取Schema
                        </small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <label class="form-label">SQL查询语句</label>
                        <textarea id="sqlEditor" class="form-control" rows="8" placeholder="请输入SQL查询语句...">SELECT * FROM 飞检规则知识库</textarea>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">数据库表</label>
                        <select class="form-select mb-3" id="tableSelect" onchange="selectTable()">
                            <option value="">选择表</option>
                            {% for table in tables %}
                            <option value="{{ table }}">{{ table }}</option>
                            {% endfor %}
                        </select>

                        <label class="form-label">表字段</label>
                        <div id="columnsContainer" class="border rounded p-2" style="height: 150px; overflow-y: auto;">
                            <small class="text-muted">请先选择表</small>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    支持标准SQL查询语句，仅允许SELECT操作
                                </small>
                            </div>
                            <div>
                                <span id="queryStatus" class="badge bg-secondary">就绪</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 查询结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-table text-primary"></i> 查询结果
                    </h6>
                    <div class="d-flex align-items-center gap-3">
                        <div id="resultInfo" class="text-muted small">
                            <!-- 结果统计信息 -->
                        </div>
                        <div id="queryStatusBadge">
                            <span id="queryStatus" class="badge bg-secondary">
                                <i class="fas fa-circle"></i> 就绪
                            </span>
                        </div>
                    </div>
                </div>
                <div id="dbConfigInfo" class="mt-2 d-none">
                    <small class="text-muted">
                        <i class="fas fa-server"></i>
                        <span id="dbConfigText">数据库配置信息将在查询后显示</span>
                    </small>
                </div>
            </div>
            <div class="card-body">
                <div id="loadingIndicator" class="text-center d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">查询中...</span>
                    </div>
                    <p class="mt-2">正在执行查询，请稍候...</p>
                </div>
                
                <div id="resultContainer" class="table-responsive">
                    <table id="resultTable" class="table table-striped table-hover table-bordered d-none">
                        <thead id="resultTableHead" class="table-dark sticky-top">
                            <!-- 表头将动态生成 -->
                        </thead>
                        <tbody id="resultTableBody">
                            <!-- 数据将动态生成 -->
                        </tbody>
                    </table>

                    <div id="emptyResult" class="text-center text-muted py-5">
                        <div class="d-flex flex-column align-items-center">
                            <i class="fas fa-database fa-3x mb-3 text-secondary"></i>
                            <h5 class="text-muted">暂无查询结果</h5>
                            <p class="text-muted mb-0">请执行SQL查询以查看结果</p>
                            <small class="text-muted mt-2">
                                <i class="fas fa-lightbulb"></i>
                                提示：查询结果将限制显示前10行数据
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SQL模板模态框 -->
<div class="modal fade" id="templatesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-template"></i> SQL模板
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>模板分类</h6>
                        <div class="list-group" id="templateCategories">
                            <!-- 模板分类将动态加载 -->
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h6>模板列表</h6>
                        <div id="templateList">
                            <!-- 模板列表将动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 模板变量模态框 -->
<div class="modal fade" id="templateVariablesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> 设置模板变量
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="templateVariablesForm">
                    <div id="variablesContainer">
                        <!-- 变量输入框将动态生成 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="applyTemplate()">应用模板</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentTemplate = '';
let currentTemplateVariables = {};

// 页面加载完成后初始化
$(document).ready(function() {
    // 初始化代码编辑器（如果需要的话）
    initializeEditor();

    // 监听数据库类型变化
    $('#dbType').on('change', function() {
        // 清空Schema选择
        $('#dbSchema').html('<option value="">请先获取Schema列表</option>');
        // 清空表选择
        $('#tableSelect').html('<option value="">请先选择Schema</option>');
        // 清空字段显示
        $('#columnsContainer').html('<small class="text-muted">请先选择表</small>');
    });

    // 监听主机IP变化
    $('#dbHost').on('blur', function() {
        const host = $(this).val().trim();
        if (host) {
            // 自动获取Schema列表
            loadSchemas();
        }
    });

    // 监听Schema变化
    $('#dbSchema').on('change', function() {
        const schema = $(this).val();
        if (schema) {
            // 自动获取表列表
            loadTables();
        } else {
            // 清空表选择
            $('#tableSelect').html('<option value="">请先选择Schema</option>');
            // 清空字段显示
            $('#columnsContainer').html('<small class="text-muted">请先选择表</small>');
        }
    });
});

// 初始化编辑器
function initializeEditor() {
    // 这里可以集成代码编辑器如CodeMirror或Monaco Editor
    // 暂时使用简单的textarea
}

// 获取Schema列表
function loadSchemas() {
    const dbType = $('#dbType').val();
    const dbHost = $('#dbHost').val().trim();

    if (!dbHost) {
        showAlert('请输入数据库主机IP', 'warning');
        return;
    }

    // 显示加载状态
    const schemaSelect = $('#dbSchema');
    const originalHtml = schemaSelect.html();
    schemaSelect.html('<option value="">正在获取Schema列表...</option>');
    schemaSelect.prop('disabled', true);

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/database/api/database_schemas',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            database: dbType,
            host: dbHost
        }),
        success: function(response) {
            schemaSelect.prop('disabled', false);

            if (response.success) {
                schemaSelect.html('<option value="">请选择Schema</option>');

                if (response.schemas && response.schemas.length > 0) {
                    response.schemas.forEach(schema => {
                        schemaSelect.append(`<option value="${schema}">${schema}</option>`);
                    });
                    showAlert(`成功获取到 ${response.schemas.length} 个Schema`, 'success');
                } else {
                    schemaSelect.append('<option value="">未找到Schema</option>');
                    showAlert('未找到可用的Schema', 'info');
                }
            } else {
                schemaSelect.html(originalHtml);
                showAlert('获取Schema失败: ' + response.error, 'danger');
            }
        },
        error: function() {
            schemaSelect.prop('disabled', false);
            schemaSelect.html(originalHtml);
            showAlert('网络错误，获取Schema失败', 'danger');
        }
    });
}

// 选择表
function selectTable() {
    const tableName = $('#tableSelect').val();
    if (!tableName) {
        $('#columnsContainer').html('<small class="text-muted">请先选择表</small>');
        return;
    }

    // 获取表字段信息
    loadTableColumns(tableName);
}

// 获取表字段信息
function loadTableColumns(tableName) {
    const dbType = $('#dbType').val();
    const dbHost = $('#dbHost').val().trim();
    const dbSchema = $('#dbSchema').val();

    if (!dbHost || !dbSchema || !tableName) {
        $('#columnsContainer').html('<small class="text-muted">请确保已选择数据库、Schema和表</small>');
        return;
    }

    // 显示加载状态
    $('#columnsContainer').html('<small class="text-muted">正在获取字段信息...</small>');

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/database/api/table_columns',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            database: dbType,
            host: dbHost,
            schema: dbSchema,
            table_name: tableName
        }),
        success: function(response) {
            if (response.success) {
                displayTableColumns(response.columns);
            } else {
                $('#columnsContainer').html(`<small class="text-danger">获取字段失败: ${response.error}</small>`);
            }
        },
        error: function() {
            $('#columnsContainer').html('<small class="text-danger">网络错误，获取字段失败</small>');
        }
    });
}

// 显示表字段
function displayTableColumns(columns) {
    const container = $('#columnsContainer');
    container.empty();

    if (columns.length === 0) {
        container.html('<small class="text-muted">无字段信息</small>');
        return;
    }

    // 添加标题和操作按钮
    let html = `
        <div class="mb-2 d-flex justify-content-between align-items-center">
            <small class="text-primary fw-bold">
                <i class="fas fa-columns"></i> 字段列表 (${columns.length})
            </small>
            <div>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllColumns()">
                    <i class="fas fa-check-square"></i> 全选
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="clearAllColumns()">
                    <i class="fas fa-square"></i> 清空
                </button>
                <button type="button" class="btn btn-sm btn-outline-success ms-1" onclick="insertSelectedColumns()">
                    <i class="fas fa-plus-circle"></i> 插入选中字段
                </button>
            </div>
        </div>
    `;

    // 添加字段列表
    columns.forEach(column => {
        const typeInfo = column.type +
            (column.length ? `(${column.length})` : '') +
            (column.precision && column.scale ? `(${column.precision},${column.scale})` : '');

        const tooltip = `类型: ${typeInfo} | 可空: ${column.nullable ? '是' : '否'}` +
            (column.comment ? ` | 说明: ${column.comment}` : '');

        html += `
            <div class="form-check mb-1 p-1 border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="${column.name}" id="col_${column.name}">
                        <label class="form-check-label small" for="col_${column.name}" title="${tooltip}">
                            <span class="fw-bold text-dark">${column.name}</span>
                            <span class="text-muted">(${typeInfo})</span>
                            ${column.comment ? `<br><small class="text-info">${column.comment}</small>` : ''}
                        </label>
                    </div>
                    <button class="btn btn-sm btn-outline-primary"
                            onclick="insertColumn('${column.name}')"
                            title="插入到查询">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
    });

    container.html(html);
}

// 全选字段
function selectAllColumns() {
    $('#columnsContainer input[type="checkbox"]').prop('checked', true);
}

// 清空字段选择
function clearAllColumns() {
    $('#columnsContainer input[type="checkbox"]').prop('checked', false);
}

// 插入选中的字段到查询
function insertSelectedColumns() {
    const selectedColumns = [];
    $('#columnsContainer input[type="checkbox"]:checked').each(function() {
        selectedColumns.push($(this).val());
    });

    if (selectedColumns.length === 0) {
        showAlert('请先选择要插入的字段', 'warning');
        return;
    }

    // 将字段名用逗号分隔
    const columnsText = selectedColumns.join(', ');

    const editor = $('#sqlEditor');
    const currentValue = editor.val();
    const cursorPos = editor[0].selectionStart;

    // 插入字段列表
    const newValue = currentValue.slice(0, cursorPos) + columnsText + currentValue.slice(cursorPos);
    editor.val(newValue);

    // 设置光标位置到插入文本的末尾
    const newPos = cursorPos + columnsText.length;
    editor[0].setSelectionRange(newPos, newPos);
    editor.focus();

    // 显示成功提示
    showAlert(`成功插入 ${selectedColumns.length} 个字段: ${columnsText}`, 'success');
}

// 插入单个字段到查询
function insertColumn(columnName) {
    const editor = $('#sqlEditor');
    const currentValue = editor.val();
    const cursorPos = editor[0].selectionStart;
    
    const newValue = currentValue.slice(0, cursorPos) + columnName + currentValue.slice(cursorPos);
    editor.val(newValue);
    
    // 设置光标位置
    const newPos = cursorPos + columnName.length;
    editor[0].setSelectionRange(newPos, newPos);
    editor.focus();
}

// 执行查询
function executeQuery() {
    const query = $('#sqlEditor').val().trim();
    const dbType = $('#dbType').val();
    const dbHost = $('#dbHost').val().trim();
    const dbSchema = $('#dbSchema').val();

    if (!query) {
        showAlert('请输入SQL查询语句', 'warning');
        return;
    }

    if (!dbHost) {
        showAlert('请输入数据库主机IP', 'warning');
        return;
    }

    // 显示加载状态
    $('#loadingIndicator').removeClass('d-none');
    $('#resultTable').addClass('d-none');
    $('#emptyResult').addClass('d-none');
    $('#dbConfigInfo').addClass('d-none');
    $('#queryStatus').removeClass('bg-secondary bg-success bg-danger').addClass('bg-warning').html('<i class="fas fa-spinner fa-spin"></i> 查询中');

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    // 执行查询
    $.ajax({
        url: '/database/api/execute_query',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            query: query,
            export_format: 'json',
            db_type: dbType,
            db_host: dbHost,
            db_schema: dbSchema,
            limit_rows: 10  // 限制只显示10行
        }),
        success: function(response) {
            $('#loadingIndicator').addClass('d-none');

            if (response.success) {
                displayQueryResult(response);
                $('#queryStatus').removeClass('bg-warning bg-danger').addClass('bg-success').html('<i class="fas fa-check-circle"></i> 查询成功');
            } else {
                // 显示详细的错误信息
                const errorMsg = response.error || '未知错误';
                showAlert('查询失败: ' + errorMsg, 'danger');
                $('#queryStatus').removeClass('bg-warning bg-success').addClass('bg-danger').html('<i class="fas fa-times-circle"></i> 查询失败');

                // 在结果区域显示详细错误信息
                $('#emptyResult').html(`
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> SQL执行失败</h6>
                        <p class="mb-0"><strong>错误详情：</strong></p>
                        <pre class="mt-2 mb-0" style="white-space: pre-wrap; font-size: 0.9em;">${errorMsg}</pre>
                    </div>
                `).removeClass('d-none');
                $('#dbConfigInfo').addClass('d-none');
            }
        },
        error: function(xhr, status, error) {
            $('#loadingIndicator').addClass('d-none');

            let errorMsg = '网络错误，请稍后重试';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            } else if (xhr.responseText) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.error) {
                        errorMsg = response.error;
                    }
                } catch (e) {
                    // 解析失败，使用默认错误信息
                }
            }

            showAlert('请求失败: ' + errorMsg, 'danger');
            $('#queryStatus').removeClass('bg-warning bg-success').addClass('bg-danger').html('<i class="fas fa-exclamation-triangle"></i> 请求失败');

            // 在结果区域显示详细错误信息
            $('#emptyResult').html(`
                <div class="alert alert-warning" role="alert">
                    <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> 请求处理失败</h6>
                    <p class="mb-0"><strong>错误详情：</strong></p>
                    <pre class="mt-2 mb-0" style="white-space: pre-wrap; font-size: 0.9em;">${errorMsg}</pre>
                    <hr>
                    <p class="mb-0"><small class="text-muted">状态码: ${xhr.status} | 状态: ${status}</small></p>
                </div>
            `).removeClass('d-none');
            $('#dbConfigInfo').addClass('d-none');
        }
    });
}

// 显示查询结果
function displayQueryResult(response) {
    const { data, columns, total, displayed, truncated } = response;

    if (data.length === 0) {
        $('#emptyResult').removeClass('d-none');
        $('#resultInfo').html('<span class="text-warning"><i class="fas fa-exclamation-triangle"></i> 查询结果为空</span>');
        $('#dbConfigInfo').addClass('d-none');
        return;
    }

    // 生成表头
    const thead = $('#resultTableHead');
    thead.empty();
    const headerRow = $('<tr></tr>');
    columns.forEach((column, index) => {
        headerRow.append(`
            <th class="text-nowrap" style="min-width: 120px;">
                <div class="d-flex align-items-center">
                    <i class="fas fa-columns text-primary me-1"></i>
                    <span>${column}</span>
                </div>
            </th>
        `);
    });
    thead.append(headerRow);

    // 生成表体（限制显示10行）
    const tbody = $('#resultTableBody');
    tbody.empty();
    const displayData = data.slice(0, 10); // 确保只显示前10行
    displayData.forEach((row, rowIndex) => {
        const dataRow = $('<tr></tr>');
        columns.forEach(column => {
            const value = row[column];
            let displayValue = '';
            let cellClass = '';

            if (value === null || value === undefined) {
                displayValue = '<span class="text-muted fst-italic">NULL</span>';
                cellClass = 'text-center';
            } else {
                const strValue = String(value);
                if (strValue.length > 50) {
                    displayValue = strValue.substring(0, 50) + '<span class="text-muted">...</span>';
                } else {
                    displayValue = strValue;
                }

                // 根据数据类型添加样式
                if (!isNaN(value) && value !== '') {
                    cellClass = 'text-end font-monospace';
                } else if (strValue.match(/^\d{4}-\d{2}-\d{2}/)) {
                    cellClass = 'text-center font-monospace';
                }
            }

            dataRow.append(`
                <td class="${cellClass}"
                    title="${value || 'NULL'}"
                    style="max-width: 200px; word-wrap: break-word;">
                    ${displayValue}
                </td>
            `);
        });
        tbody.append(dataRow);
    });

    // 显示结果表格
    $('#resultTable').removeClass('d-none');

    // 初始化DataTable排序功能
    initSortableTable('resultTable');

    // 更新结果信息
    let infoText = `共 <strong class="text-primary">${total || data.length}</strong> 条记录`;
    if (data.length > 10 || total > 10) {
        infoText += `，显示前 <strong class="text-success">10</strong> 条`;
    }
    if (displayed && displayed !== data.length) {
        infoText += ` <small class="text-muted">（服务器返回 ${displayed} 条）</small>`;
    }

    $('#resultInfo').html(`
        <div class="d-flex align-items-center">
            <i class="fas fa-chart-bar text-success me-1"></i>
            <span>${infoText}</span>
        </div>
    `);

    // 显示数据库配置信息
    $('#dbConfigText').html(`
        <strong>数据库:</strong> ${$('#dbType').val()} |
        <strong>主机:</strong> ${$('#dbHost').val()} |
        <strong>Schema:</strong> ${$('#dbSchema').val() || '默认'}
    `);
    $('#dbConfigInfo').removeClass('d-none');
}

// 导出到Excel
function exportToExcel() {
    const query = $('#sqlEditor').val().trim();
    const dbType = $('#dbType').val();
    const dbHost = $('#dbHost').val().trim();
    const dbSchema = $('#dbSchema').val();

    if (!query) {
        showAlert('请输入SQL查询语句', 'warning');
        return;
    }

    if (!dbHost) {
        showAlert('请输入数据库主机IP', 'warning');
        return;
    }

    $('#queryStatus').removeClass('bg-secondary bg-success bg-danger').addClass('bg-warning').html('<i class="fas fa-file-export fa-spin"></i> 导出中');

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/database/api/execute_query',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            query: query,
            export_format: 'excel',
            db_type: dbType,
            db_host: dbHost,
            db_schema: dbSchema
        }),
        success: function(response) {
            if (response.success) {
                // 下载文件
                window.location.href = response.export_url;
                showAlert('Excel文件导出成功', 'success');
                $('#queryStatus').removeClass('bg-warning bg-danger').addClass('bg-success').html('<i class="fas fa-download"></i> 导出成功');
            } else {
                const errorMsg = response.error || '未知错误';
                showAlert('Excel导出失败: ' + errorMsg, 'danger');
                $('#queryStatus').removeClass('bg-warning bg-success').addClass('bg-danger').html('<i class="fas fa-times-circle"></i> 导出失败');
            }
        },
        error: function(xhr, status, error) {
            let errorMsg = '网络错误，请稍后重试';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            }
            showAlert('Excel导出请求失败: ' + errorMsg, 'danger');
            $('#queryStatus').removeClass('bg-warning bg-success').addClass('bg-danger').html('<i class="fas fa-exclamation-triangle"></i> 请求失败');
        }
    });
}

// 导出到CSV
function exportToCSV() {
    const query = $('#sqlEditor').val().trim();
    const dbType = $('#dbType').val();
    const dbHost = $('#dbHost').val().trim();
    const dbSchema = $('#dbSchema').val();

    if (!query) {
        showAlert('请输入SQL查询语句', 'warning');
        return;
    }

    if (!dbHost) {
        showAlert('请输入数据库主机IP', 'warning');
        return;
    }

    $('#queryStatus').removeClass('bg-secondary bg-success bg-danger').addClass('bg-warning').html('<i class="fas fa-file-csv fa-spin"></i> 导出CSV中');

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/database/api/execute_query',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            query: query,
            export_format: 'csv',
            db_type: dbType,
            db_host: dbHost,
            db_schema: dbSchema
        }),
        success: function(response) {
            if (response.success) {
                // 下载文件
                window.location.href = response.export_url;
                showAlert('CSV文件导出成功', 'success');
                $('#queryStatus').removeClass('bg-warning bg-danger').addClass('bg-success').html('<i class="fas fa-download"></i> CSV导出成功');
            } else {
                const errorMsg = response.error || '未知错误';
                showAlert('CSV导出失败: ' + errorMsg, 'danger');
                $('#queryStatus').removeClass('bg-warning bg-success').addClass('bg-danger').html('<i class="fas fa-times-circle"></i> CSV导出失败');
            }
        },
        error: function(xhr, status, error) {
            let errorMsg = '网络错误，请稍后重试';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            }
            showAlert('CSV导出请求失败: ' + errorMsg, 'danger');
            $('#queryStatus').removeClass('bg-warning bg-success').addClass('bg-danger').html('<i class="fas fa-exclamation-triangle"></i> 请求失败');
        }
    });
}

// 获取表列表
function loadTables() {
    const dbType = $('#dbType').val();
    const dbHost = $('#dbHost').val().trim();
    const dbSchema = $('#dbSchema').val();

    if (!dbHost) {
        showAlert('请输入数据库主机IP', 'warning');
        return;
    }

    if (!dbSchema) {
        showAlert('请选择Schema', 'warning');
        return;
    }

    // 显示加载状态
    const tableSelect = $('#tableSelect');
    const originalHtml = tableSelect.html();
    tableSelect.html('<option value="">正在获取表列表...</option>');
    tableSelect.prop('disabled', true);

    // 清空字段显示
    $('#columnsContainer').html('<small class="text-muted">正在获取表列表...</small>');

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/database/api/database_tables',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            database: dbType,
            host: dbHost,
            schema: dbSchema
        }),
        success: function(response) {
            tableSelect.prop('disabled', false);

            if (response.success) {
                tableSelect.html('<option value="">请选择表</option>');

                if (response.tables && response.tables.length > 0) {
                    response.tables.forEach(table => {
                        const displayName = table.comment ?
                            `${table.name} (${table.comment})` :
                            table.name;
                        tableSelect.append(`<option value="${table.name}" title="${table.comment || table.name}">${displayName}</option>`);
                    });
                    showAlert(`成功获取到 ${response.tables.length} 个表`, 'success');
                    $('#columnsContainer').html('<small class="text-muted">请选择表查看字段</small>');
                } else {
                    tableSelect.append('<option value="">未找到表</option>');
                    showAlert('未找到可用的表', 'info');
                    $('#columnsContainer').html('<small class="text-muted">未找到表</small>');
                }
            } else {
                tableSelect.html(originalHtml);
                showAlert('获取表列表失败: ' + response.error, 'danger');
                $('#columnsContainer').html('<small class="text-muted">获取表列表失败</small>');
            }
        },
        error: function() {
            tableSelect.prop('disabled', false);
            tableSelect.html(originalHtml);
            showAlert('网络错误，获取表列表失败', 'danger');
            $('#columnsContainer').html('<small class="text-muted">网络错误</small>');
        }
    });
}

// 显示模板
function showTemplates() {
    $.get('/database/api/sql_templates', function(response) {
        if (response.success) {
            displayTemplates(response.templates);
            $('#templatesModal').modal('show');
        } else {
            showAlert('获取模板失败: ' + response.error, 'danger');
        }
    });
}

// 显示模板列表
function displayTemplates(templates) {
    const categories = $('#templateCategories');
    const list = $('#templateList');
    
    categories.empty();
    list.empty();
    
    // 显示分类
    Object.keys(templates).forEach((category, index) => {
        const isActive = index === 0 ? 'active' : '';
        categories.append(`
            <a href="#" class="list-group-item list-group-item-action ${isActive}" 
               onclick="showTemplateCategory('${category}')">
                ${category === 'basic' ? '基础模板' : '医疗模板'}
            </a>
        `);
    });
    
    // 显示第一个分类的模板
    if (Object.keys(templates).length > 0) {
        showTemplateCategory(Object.keys(templates)[0], templates);
    }
}

// 显示模板分类
function showTemplateCategory(category, templates = null) {
    if (!templates) {
        // 重新获取模板数据
        $.get('/database/api/sql_templates', function(response) {
            if (response.success) {
                showTemplateCategory(category, response.templates);
            }
        });
        return;
    }
    
    // 更新活动分类
    $('#templateCategories .list-group-item').removeClass('active');
    $('#templateCategories .list-group-item').each(function() {
        if ($(this).attr('onclick').includes(category)) {
            $(this).addClass('active');
        }
    });
    
    // 显示模板列表
    const list = $('#templateList');
    list.empty();
    
    if (templates[category]) {
        templates[category].forEach(template => {
            list.append(`
                <div class="card mb-2">
                    <div class="card-body">
                        <h6 class="card-title">${template.name}</h6>
                        <p class="card-text small text-muted">${template.description}</p>
                        <button class="btn btn-sm btn-primary" 
                                onclick="useTemplate('${template.template}')">
                            使用模板
                        </button>
                    </div>
                </div>
            `);
        });
    }
}

// 使用模板
function useTemplate(template) {
    currentTemplate = template;
    
    // 检查模板是否有变量
    const variables = template.match(/\{(\w+)\}/g);
    
    if (variables && variables.length > 0) {
        // 显示变量设置对话框
        showTemplateVariables(variables);
    } else {
        // 直接应用模板
        $('#sqlEditor').val(template);
        $('#templatesModal').modal('hide');
        showAlert('模板已应用', 'success');
    }
}

// 显示模板变量设置
function showTemplateVariables(variables) {
    const container = $('#variablesContainer');
    container.empty();
    
    // 去重并移除大括号
    const uniqueVars = [...new Set(variables.map(v => v.replace(/[{}]/g, '')))];
    
    uniqueVars.forEach(variable => {
        container.append(`
            <div class="mb-3">
                <label class="form-label">${variable}</label>
                <input type="text" class="form-control" name="${variable}" 
                       placeholder="请输入${variable}的值">
            </div>
        `);
    });
    
    $('#templatesModal').modal('hide');
    $('#templateVariablesModal').modal('show');
}

// 应用模板
function applyTemplate() {
    const formData = new FormData(document.getElementById('templateVariablesForm'));
    const variables = {};
    
    for (let [key, value] of formData.entries()) {
        variables[key] = value;
    }
    
    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    $.ajax({
        url: '/database/api/render_template',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({
            template: currentTemplate,
            variables: variables
        }),
        success: function(response) {
            if (response.success) {
                $('#sqlEditor').val(response.sql);
                $('#templateVariablesModal').modal('hide');
                showAlert('模板已应用', 'success');
            } else {
                showAlert('模板渲染失败: ' + response.error, 'danger');
            }
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'danger');
        }
    });
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
