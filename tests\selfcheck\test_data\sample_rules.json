[{"rule_name": "重复收费检查规则001", "rule_code": "DUPLICATE_CHARGE_001", "rule_description": "检查同一患者同一天内是否存在重复收费项目", "rule_type": "重复收费", "city": "北京", "rule_source": "飞检规则", "medical_behavior": "诊疗", "visit_type": "住院", "sql_content": "SELECT patient_id, charge_code, COUNT(*) FROM charges WHERE charge_date = TRUNC(SYSDATE) GROUP BY patient_id, charge_code HAVING COUNT(*) > 1", "template_name": "重复收费模板", "types": "A", "rule_content": "检查患者在同一天内是否存在相同收费项目的重复收费情况"}, {"rule_name": "频次上限检查规则001", "rule_code": "FREQUENCY_LIMIT_001", "rule_description": "检查药品使用频次是否超过规定上限", "rule_type": "频次上限", "city": "上海", "rule_source": "监管规则", "medical_behavior": "药品", "visit_type": "门诊", "sql_content": "SELECT patient_id, drug_code, COUNT(*) as frequency FROM prescriptions WHERE prescription_date >= SYSDATE - 30 GROUP BY patient_id, drug_code HAVING COUNT(*) > 10", "template_name": "频次检查模板", "types": "B", "rule_content": "检查患者30天内同一药品的使用频次是否超过10次"}, {"rule_name": "金额限制检查规则001", "rule_code": "AMOUNT_LIMIT_001", "rule_description": "检查单次收费金额是否超过限制", "rule_type": "金额限制", "city": "广州", "rule_source": "内控规则", "medical_behavior": "检查", "visit_type": "住院", "sql_content": "SELECT * FROM charges WHERE amount > 5000 AND charge_type = 'EXAMINATION'", "template_name": "金额限制模板", "types": "A", "rule_content": "检查检查类收费项目单次金额是否超过5000元"}, {"rule_name": "年龄限制检查规则001", "rule_code": "AGE_LIMIT_001", "rule_description": "检查特定药品使用是否符合年龄限制", "rule_type": "年龄限制", "city": "深圳", "rule_source": "系统规则", "medical_behavior": "药品", "visit_type": "门诊", "sql_content": "SELECT p.patient_id, p.age, pr.drug_code FROM patients p JOIN prescriptions pr ON p.patient_id = pr.patient_id WHERE pr.drug_code IN ('CHILD_DRUG_001') AND p.age >= 18", "template_name": "年龄限制模板", "types": "C", "rule_content": "检查儿童专用药品是否被成人患者使用"}, {"rule_name": "性别限制检查规则001", "rule_code": "GENDER_LIMIT_001", "rule_description": "检查特定治疗项目是否符合性别限制", "rule_type": "性别限制", "city": "杭州", "rule_source": "飞检规则", "medical_behavior": "治疗", "visit_type": "住院", "sql_content": "SELECT p.patient_id, p.gender, t.treatment_code FROM patients p JOIN treatments t ON p.patient_id = t.patient_id WHERE t.treatment_code IN ('GYNECOLOGY_001') AND p.gender = 'M'", "template_name": "性别限制模板", "types": "B", "rule_content": "检查妇科治疗项目是否被男性患者使用"}, {"rule_name": "住院天数检查规则001", "rule_code": "STAY_DAYS_001", "rule_description": "检查住院天数是否超过合理范围", "rule_type": "住院天数", "city": "南京", "rule_source": "监管规则", "medical_behavior": "住院", "visit_type": "住院", "sql_content": "SELECT patient_id, admission_date, discharge_date, (discharge_date - admission_date) as stay_days FROM admissions WHERE (discharge_date - admission_date) > 30", "template_name": "住院天数模板", "types": "A", "rule_content": "检查患者住院天数是否超过30天"}, {"rule_name": "组套收费检查规则001", "rule_code": "PACKAGE_CHARGE_001", "rule_description": "检查组套收费项目是否存在重复计费", "rule_type": "组套收费", "city": "武汉", "rule_source": "内控规则", "medical_behavior": "诊疗", "visit_type": "门诊", "sql_content": "SELECT patient_id, visit_id, COUNT(DISTINCT package_id) as package_count FROM package_charges WHERE charge_date = TRUNC(SYSDATE) GROUP BY patient_id, visit_id HAVING COUNT(DISTINCT package_id) > 1", "template_name": "组套收费模板", "types": "C", "rule_content": "检查患者同一次就诊是否存在多个组套收费项目"}, {"rule_name": "药品配伍禁忌检查规则001", "rule_code": "DRUG_INTERACTION_001", "rule_description": "检查药品配伍是否存在禁忌", "rule_type": "药品配伍", "city": "成都", "rule_source": "系统规则", "medical_behavior": "药品", "visit_type": "住院", "sql_content": "SELECT p1.patient_id, p1.drug_code as drug1, p2.drug_code as drug2 FROM prescriptions p1 JOIN prescriptions p2 ON p1.patient_id = p2.patient_id WHERE p1.drug_code = 'DRUG_A' AND p2.drug_code = 'DRUG_B' AND p1.prescription_date = p2.prescription_date", "template_name": "药品配伍模板", "types": "A", "rule_content": "检查患者是否同时使用存在配伍禁忌的药品"}, {"rule_name": "手术级别检查规则001", "rule_code": "SURGERY_LEVEL_001", "rule_description": "检查手术级别与医生资质是否匹配", "rule_type": "手术级别", "city": "西安", "rule_source": "飞检规则", "medical_behavior": "手术", "visit_type": "住院", "sql_content": "SELECT s.surgery_id, s.surgery_level, d.doctor_level FROM surgeries s JOIN doctors d ON s.doctor_id = d.doctor_id WHERE s.surgery_level > d.doctor_level", "template_name": "手术级别模板", "types": "B", "rule_content": "检查医生是否执行了超出其资质级别的手术"}, {"rule_name": "护理等级检查规则001", "rule_code": "NURSING_LEVEL_001", "rule_description": "检查护理等级是否与患者病情匹配", "rule_type": "护理等级", "city": "重庆", "rule_source": "监管规则", "medical_behavior": "护理", "visit_type": "住院", "sql_content": "SELECT patient_id, nursing_level, disease_severity FROM nursing_records WHERE nursing_level = 'SPECIAL' AND disease_severity = 'MILD'", "template_name": "护理等级模板", "types": "C", "rule_content": "检查轻症患者是否使用了特级护理"}]