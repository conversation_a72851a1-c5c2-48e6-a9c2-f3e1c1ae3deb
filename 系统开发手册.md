# MICRA自查自纠模块系统开发手册

**文档版本：** V1.0  
**编写日期：** 2025年1月7日  
**项目名称：** MICRA工具箱 - 自查自纠模块  
**文档类型：** 系统开发手册  

## 1. 开发环境搭建

### 1.1 开发工具要求

#### 必需工具
- **IDE：** PyCharm Professional / VS Code
- **Python：** 3.9.0+
- **Git：** 2.20+
- **数据库客户端：** DBeaver / SQL Developer
- **API测试工具：** Postman / Insomnia

#### 推荐工具
- **代码格式化：** Black, isort
- **代码检查：** Pylint, Flake8
- **类型检查：** mypy
- **文档生成：** Sphinx

### 1.2 本地开发环境

#### 1.2.1 项目克隆和设置
```bash
# 克隆项目
git clone https://github.com/your-org/micra-selfcheck.git
cd micra-selfcheck

# 创建虚拟环境
python3.9 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements-dev.txt
```

#### 1.2.2 开发配置
```python
# config/development.py
class DevelopmentConfig:
    DEBUG = True
    TESTING = False
    
    # 数据库配置（开发环境）
    ORACLE_HOST = 'localhost'
    ORACLE_PORT = '1521'
    ORACLE_SERVICE = 'xe'
    ORACLE_USER = 'micra_dev'
    ORACLE_PASSWORD = 'dev_password'
    
    # 文件上传配置
    UPLOAD_FOLDER = './uploads_dev'
    MAX_CONTENT_LENGTH = 10 * 1024 * 1024
    
    # 日志配置
    LOG_LEVEL = 'DEBUG'
    LOG_FILE = './logs/micra_dev.log'
    
    # 开发工具配置
    FLASK_DEBUG = True
    FLASK_ENV = 'development'
```

#### 1.2.3 数据库设置
```bash
# 创建开发数据库
sqlplus sys/password@localhost:1521/xe as sysdba

# 执行初始化脚本
@scripts/dev_setup.sql

# 运行数据迁移
python manage.py db upgrade
```

### 1.3 代码规范配置

#### 1.3.1 代码格式化配置
```toml
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
```

#### 1.3.2 代码检查配置
```ini
# .flake8
[flake8]
max-line-length = 88
extend-ignore = E203, E266, E501, W503
max-complexity = 10
exclude = 
    .git,
    __pycache__,
    .venv,
    migrations/
```

## 2. 项目结构说明

### 2.1 目录结构
```
micra-selfcheck/
├── app/                          # 应用主目录
│   ├── __init__.py              # 应用工厂
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py             # 基础模型
│   │   ├── user.py             # 用户模型
│   │   └── selfcheck.py        # 自查自纠模型
│   ├── selfcheck/              # 自查自纠模块
│   │   ├── __init__.py
│   │   ├── routes.py           # 路由定义
│   │   ├── services.py         # 业务逻辑
│   │   └── utils.py            # 工具函数
│   ├── auth/                   # 认证模块
│   │   ├── __init__.py
│   │   ├── routes.py
│   │   └── services.py
│   ├── api/                    # API模块
│   │   ├── __init__.py
│   │   └── v1/                 # API版本1
│   ├── templates/              # 模板文件
│   │   ├── base.html
│   │   ├── auth/
│   │   └── selfcheck/
│   ├── static/                 # 静态文件
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── utils/                  # 通用工具
│       ├── __init__.py
│       ├── database.py         # 数据库工具
│       └── helpers.py          # 辅助函数
├── config/                     # 配置文件
│   ├── __init__.py
│   ├── base.py                 # 基础配置
│   ├── development.py          # 开发配置
│   ├── production.py           # 生产配置
│   └── testing.py              # 测试配置
├── tests/                      # 测试文件
│   ├── __init__.py
│   ├── conftest.py             # 测试配置
│   ├── test_models.py          # 模型测试
│   ├── test_services.py        # 服务测试
│   └── test_routes.py          # 路由测试
├── scripts/                    # 脚本文件
│   ├── init_database.py        # 数据库初始化
│   ├── create_admin.py         # 创建管理员
│   └── migrate_data.py         # 数据迁移
├── docs/                       # 文档目录
├── requirements.txt            # 生产依赖
├── requirements-dev.txt        # 开发依赖
├── run.py                      # 应用入口
└── manage.py                   # 管理脚本
```

### 2.2 核心模块说明

#### 2.2.1 应用工厂模式
```python
# app/__init__.py
from flask import Flask
from config import config

def create_app(config_name='development'):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    from app.utils.database import db
    db.init_app(app)
    
    # 注册蓝图
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.selfcheck import bp as selfcheck_bp
    app.register_blueprint(selfcheck_bp, url_prefix='/selfcheck')
    
    return app
```

#### 2.2.2 数据模型基类
```python
# app/models/base.py
from datetime import datetime
from app.utils.database import db

class BaseModel(db.Model):
    """基础模型类"""
    __abstract__ = True
    
    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def save(self):
        """保存模型"""
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            raise e
    
    def delete(self):
        """删除模型"""
        try:
            db.session.delete(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            raise e
    
    def to_dict(self):
        """转换为字典"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
```

## 3. 开发规范

### 3.1 编码规范

#### 3.1.1 命名规范
```python
# 类名：大驼峰命名
class SelfCheckUpload:
    pass

# 函数名：小写下划线
def get_upload_by_id(upload_id):
    pass

# 变量名：小写下划线
user_id = 1
file_name = "test.csv"

# 常量：大写下划线
MAX_FILE_SIZE = 10 * 1024 * 1024
ALLOWED_EXTENSIONS = {'csv', 'dmp', 'dp', 'bak'}
```

#### 3.1.2 注释规范
```python
def process_upload_file(file_path: str, file_type: str) -> tuple[bool, str, int]:
    """
    处理上传的文件
    
    Args:
        file_path (str): 文件路径
        file_type (str): 文件类型
        
    Returns:
        tuple[bool, str, int]: (是否成功, 消息, 记录数)
        
    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 文件格式错误
        
    Example:
        >>> success, message, count = process_upload_file('/path/to/file.csv', 'csv')
        >>> print(f"处理结果: {success}, 消息: {message}, 记录数: {count}")
    """
    pass
```

#### 3.1.3 错误处理规范
```python
# 自定义异常
class SelfCheckError(Exception):
    """自查自纠模块基础异常"""
    pass

class FileProcessError(SelfCheckError):
    """文件处理异常"""
    pass

class ValidationError(SelfCheckError):
    """数据验证异常"""
    pass

# 异常处理示例
def upload_file(file):
    try:
        # 文件验证
        if not validate_file(file):
            raise ValidationError("文件格式不正确")
        
        # 文件处理
        result = process_file(file)
        return result
        
    except ValidationError as e:
        logger.warning(f"文件验证失败: {e}")
        raise
    except FileProcessError as e:
        logger.error(f"文件处理失败: {e}")
        raise
    except Exception as e:
        logger.error(f"未知错误: {e}")
        raise SelfCheckError("系统内部错误")
```

### 3.2 数据库开发规范

#### 3.2.1 模型定义规范
```python
# app/models/selfcheck.py
from app.models.base import BaseModel
from app.utils.database import db

class SelfCheckUpload(BaseModel):
    """上传记录模型"""
    __tablename__ = 'selfcheck_uploads'
    
    # 字段定义
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    file_name = db.Column(db.String(500), nullable=False)
    file_path = db.Column(db.String(1000), nullable=False)
    file_size = db.Column(db.BigInteger, default=0)
    file_type = db.Column(db.String(50))
    status = db.Column(db.String(20), default='pending')
    
    # 关系定义
    user = db.relationship('User', backref='uploads')
    
    # 索引定义
    __table_args__ = (
        db.Index('idx_uploads_user_status', 'user_id', 'status'),
        db.Index('idx_uploads_created_at', 'created_at'),
    )
    
    @classmethod
    def get_by_user(cls, user_id, status=None):
        """根据用户ID获取上传记录"""
        query = cls.query.filter_by(user_id=user_id)
        if status:
            query = query.filter_by(status=status)
        return query.all()
    
    def update_status(self, status, message=None):
        """更新状态"""
        self.status = status
        if message:
            self.validation_result = message
        self.save()
```

#### 3.2.2 查询优化规范
```python
# 使用索引优化查询
def get_uploads_by_user_and_status(user_id, status):
    """优化的查询方法"""
    return SelfCheckUpload.query.filter(
        SelfCheckUpload.user_id == user_id,
        SelfCheckUpload.status == status
    ).order_by(SelfCheckUpload.created_at.desc()).all()

# 分页查询
def get_uploads_paginated(page=1, per_page=20, **filters):
    """分页查询上传记录"""
    query = SelfCheckUpload.query
    
    # 应用过滤条件
    if filters.get('user_id'):
        query = query.filter_by(user_id=filters['user_id'])
    if filters.get('status'):
        query = query.filter_by(status=filters['status'])
    if filters.get('file_type'):
        query = query.filter_by(file_type=filters['file_type'])
    
    return query.paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
```

### 3.3 API开发规范

#### 3.3.1 RESTful API设计
```python
# app/api/v1/uploads.py
from flask import Blueprint, request, jsonify
from app.models.selfcheck import SelfCheckUpload
from app.utils.auth import login_required, permission_required

bp = Blueprint('uploads_api', __name__)

@bp.route('/uploads', methods=['GET'])
@login_required
def get_uploads():
    """获取上传记录列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 构建过滤条件
        filters = {}
        if request.args.get('status'):
            filters['status'] = request.args.get('status')
        if request.args.get('file_type'):
            filters['file_type'] = request.args.get('file_type')
        
        # 分页查询
        pagination = get_uploads_paginated(page, per_page, **filters)
        
        return jsonify({
            'success': True,
            'data': [upload.to_dict() for upload in pagination.items],
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@bp.route('/uploads', methods=['POST'])
@login_required
@permission_required('selfcheck.uploads.create')
def create_upload():
    """创建上传记录"""
    try:
        # 文件验证
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '文件名为空'
            }), 400
        
        # 处理上传
        from app.selfcheck.services import UploadService
        result = UploadService.save_upload(file, current_user.id)
        
        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500
```

#### 3.3.2 响应格式标准
```python
# 成功响应
{
    "success": true,
    "message": "操作成功",
    "data": {
        // 具体数据
    },
    "pagination": {  // 分页信息（可选）
        "page": 1,
        "pages": 10,
        "per_page": 20,
        "total": 200
    }
}

# 错误响应
{
    "success": false,
    "message": "错误描述",
    "error_code": "VALIDATION_ERROR",  // 错误代码（可选）
    "details": {  // 详细错误信息（可选）
        "field": "具体错误"
    }
}
```

## 4. 测试开发

### 4.1 单元测试

#### 4.1.1 测试配置
```python
# tests/conftest.py
import pytest
from app import create_app
from app.utils.database import db

@pytest.fixture
def app():
    """创建测试应用"""
    app = create_app('testing')
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()

@pytest.fixture
def runner(app):
    """创建CLI测试运行器"""
    return app.test_cli_runner()
```

#### 4.1.2 模型测试
```python
# tests/test_models.py
import pytest
from app.models.selfcheck import SelfCheckUpload

class TestSelfCheckUpload:
    """上传记录模型测试"""
    
    def test_create_upload(self, app):
        """测试创建上传记录"""
        with app.app_context():
            upload = SelfCheckUpload(
                user_id=1,
                file_name='test.csv',
                file_path='/path/to/test.csv',
                file_size=1024,
                file_type='csv'
            )
            
            assert upload.save() == True
            assert upload.id is not None
            assert upload.status == 'pending'
    
    def test_update_status(self, app):
        """测试状态更新"""
        with app.app_context():
            upload = SelfCheckUpload(
                user_id=1,
                file_name='test.csv',
                file_path='/path/to/test.csv'
            )
            upload.save()
            
            upload.update_status('validated', '验证通过')
            
            assert upload.status == 'validated'
            assert upload.validation_result == '验证通过'
```

#### 4.1.3 服务测试
```python
# tests/test_services.py
import pytest
from unittest.mock import Mock, patch
from app.selfcheck.services import UploadService

class TestUploadService:
    """上传服务测试"""
    
    @patch('app.selfcheck.services.UploadService._save_file_to_disk')
    def test_save_upload(self, mock_save_file, app):
        """测试文件上传"""
        with app.app_context():
            # 模拟文件对象
            mock_file = Mock()
            mock_file.filename = 'test.csv'
            mock_file.content_length = 1024
            
            # 模拟文件保存
            mock_save_file.return_value = '/path/to/test.csv'
            
            # 执行上传
            result = UploadService.save_upload(mock_file, 1)
            
            # 验证结果
            assert result['file_name'] == 'test.csv'
            assert result['status'] == 'pending'
            assert result['user_id'] == 1
```

### 4.2 集成测试

#### 4.2.1 API测试
```python
# tests/test_api.py
import pytest
import json

class TestUploadsAPI:
    """上传API测试"""
    
    def test_get_uploads(self, client, auth_headers):
        """测试获取上传列表"""
        response = client.get('/api/v1/uploads', headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'data' in data
        assert 'pagination' in data
    
    def test_create_upload(self, client, auth_headers):
        """测试文件上传"""
        data = {
            'file': (io.BytesIO(b'test data'), 'test.csv')
        }
        
        response = client.post(
            '/api/v1/uploads',
            data=data,
            headers=auth_headers,
            content_type='multipart/form-data'
        )
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] == True
        assert result['data']['file_name'] == 'test.csv'
```

## 5. 调试和性能优化

### 5.1 调试技巧

#### 5.1.1 日志调试
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def process_file(file_path):
    """文件处理示例"""
    logger.debug(f"开始处理文件: {file_path}")
    
    try:
        # 处理逻辑
        result = do_process(file_path)
        logger.info(f"文件处理成功: {file_path}, 结果: {result}")
        return result
        
    except Exception as e:
        logger.error(f"文件处理失败: {file_path}, 错误: {e}", exc_info=True)
        raise
```

#### 5.1.2 性能分析
```python
import time
import functools

def timing_decorator(func):
    """性能计时装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger.info(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper

@timing_decorator
def process_large_file(file_path):
    """处理大文件"""
    # 处理逻辑
    pass
```

### 5.2 性能优化

#### 5.2.1 数据库查询优化
```python
# 使用批量操作
def batch_update_status(upload_ids, status):
    """批量更新状态"""
    SelfCheckUpload.query.filter(
        SelfCheckUpload.id.in_(upload_ids)
    ).update(
        {'status': status},
        synchronize_session=False
    )
    db.session.commit()

# 使用懒加载
def get_uploads_with_user():
    """获取包含用户信息的上传记录"""
    return SelfCheckUpload.query.options(
        db.joinedload(SelfCheckUpload.user)
    ).all()
```

#### 5.2.2 缓存优化
```python
from functools import lru_cache
from flask_caching import Cache

cache = Cache()

@cache.memoize(timeout=300)  # 缓存5分钟
def get_user_permissions(user_id):
    """获取用户权限（缓存）"""
    # 查询逻辑
    pass

@lru_cache(maxsize=128)
def get_file_type_config(file_type):
    """获取文件类型配置（内存缓存）"""
    # 配置逻辑
    pass
```

---

**文档状态：** 已审核  
**批准人：** 技术负责人  
**批准日期：** 2025年1月7日  
**版本历史：**
- V1.0 (2025-01-07): 初始版本，包含完整开发指南
