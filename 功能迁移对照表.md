# MICRA项目功能迁移对照表

## 一、现有功能与新架构映射

### 1. 规则管理模块 (app/rules/)

| 现有功能 | 现有路由 | 新模块路径 | 新路由 | 权限代码 |
|---------|---------|-----------|--------|----------|
| 飞检规则知识库 | `/rule_knowledge_base` | `app/rules/routes.py` | `/rules/knowledge_base` | `rules.knowledge_base` |
| 规则SQL生成器 | `/rule_sql_generator` | `app/rules/routes.py` | `/rules/sql_generator` | `rules.sql_generator` |
| 系统规则语句 | `/system_rules` | `app/rules/routes.py` | `/rules/system_rules` | `rules.system_rules` |

### 2. 数据库工具模块 (app/database/)

| 现有功能 | 现有路由 | 新模块路径 | 新路由 | 权限代码 |
|---------|---------|-----------|--------|----------|
| SQL生成器 | `/sql_generator` | `app/database/routes.py` | `/database/sql_generator` | `database.sql_generator` |
| 数据库查询生成Excel | `/db_query` | `app/database/routes.py` | `/database/query` | `database.query` |
| 批量SQL查询生成Excel | `/batch_query` | `app/database/routes.py` | `/database/batch_query` | `database.batch_query` |
| SQL性能测试 | `/sql_performance` | `app/database/routes.py` | `/database/performance` | `database.performance` |

### 3. Excel工具模块 (app/excel/)

| 现有功能 | 现有路由 | 新模块路径 | 新路由 | 权限代码 |
|---------|---------|-----------|--------|----------|
| Excel文件拆分 | `/excel_splitter` | `app/excel/routes.py` | `/excel/splitter` | `excel.splitter` |
| Excel内容删除 | `/excel_delete` | `app/excel/routes.py` | `/excel/delete` | `excel.delete` |
| Excel比对工具 | `/excel_compare` | `app/excel/routes.py` | `/excel/compare` | `excel.compare` |
| Excel转SQL工具 | `/excel_to_sql` | `app/excel/routes.py` | `/excel/to_sql` | `excel.to_sql` |

### 4. 数据处理模块 (app/data/)

| 现有功能 | 现有路由 | 新模块路径 | 新路由 | 权限代码 |
|---------|---------|-----------|--------|----------|
| 查找重复文件 | `/find_duplicates` | `app/data/routes.py` | `/data/find_duplicates` | `data.find_duplicates` |
| 数据校验 | `/data_validator` | `app/data/routes.py` | `/data/validator` | `data.validator` |
| 数据标准化 | `/data_standardization` | `app/data/routes.py` | `/data/standardization` | `data.standardization` |

### 5. 系统管理模块 (app/admin/)

| 新增功能 | 新路由 | 权限代码 | 说明 |
|---------|--------|----------|------|
| 用户管理 | `/admin/users` | `system.user.view` | 用户列表、创建、编辑、删除 |
| 角色管理 | `/admin/roles` | `system.role.view` | 角色列表、创建、编辑、删除 |
| 权限管理 | `/admin/permissions` | `system.permission.view` | 权限分配和管理 |
| 审计日志 | `/admin/audit_logs` | `system.audit.view` | 操作日志查看 |

## 二、文件迁移计划

### 2.1 模板文件迁移

| 现有模板文件 | 新模板路径 | 说明 |
|-------------|-----------|------|
| `page/index.html` | `app/templates/main/index.html` | 主页模板 |
| `page/rule_knowledge_base.html` | `app/templates/rules/knowledge_base.html` | 规则知识库 |
| `page/rule_sql_generator.html` | `app/templates/rules/sql_generator.html` | 规则SQL生成器 |
| `page/system_rules.html` | `app/templates/rules/system_rules.html` | 系统规则 |
| `page/sql_query.html` | `app/templates/database/sql_generator.html` | SQL生成器 |
| `page/db_query.html` | `app/templates/database/query.html` | 数据库查询 |
| `page/batch_query.html` | `app/templates/database/batch_query.html` | 批量查询 |
| `page/sql_performance.html` | `app/templates/database/performance.html` | 性能测试 |
| `page/excel_splitter.html` | `app/templates/excel/splitter.html` | Excel拆分 |
| `page/excel_delete.html` | `app/templates/excel/delete.html` | Excel删除 |
| `page/excel_compare.html` | `app/templates/excel/compare.html` | Excel比对 |
| `page/excel_to_sql.html` | `app/templates/excel/to_sql.html` | Excel转SQL |
| `page/find_duplicates.html` | `app/templates/data/find_duplicates.html` | 查找重复文件 |
| `page/data_validator.html` | `app/templates/data/validator.html` | 数据校验 |
| `page/data_standardization.html` | `app/templates/data/standardization.html` | 数据标准化 |

### 2.2 业务逻辑迁移

| 现有函数/类 | 所在文件 | 新位置 | 说明 |
|------------|---------|--------|------|
| 规则相关函数 | `app.py` | `app/rules/services.py` | 规则业务逻辑 |
| 数据库查询函数 | `app.py` | `app/database/services.py` | 数据库操作逻辑 |
| Excel处理函数 | `app.py` | `app/excel/services.py` | Excel处理逻辑 |
| 文件处理函数 | `app.py` | `app/data/services.py` | 数据处理逻辑 |
| 数据库连接池 | `app.py` | `app/utils/database.py` | 数据库工具类 |
| 文件管理类 | `app.py` | `app/utils/file_manager.py` | 文件管理工具 |

## 三、数据库迁移计划

### 3.1 新增表结构

```sql
-- 用户认证相关表
CREATE TABLE users (...);
CREATE TABLE roles (...);
CREATE TABLE permissions (...);
CREATE TABLE user_roles (...);
CREATE TABLE role_permissions (...);
CREATE TABLE audit_logs (...);
```

### 3.2 现有表保持不变

- `飞检规则知识库` - 保持现有结构
- `规则医保编码对照` - 保持现有结构
- `rule_cities` - 保持现有结构
- 其他业务表 - 保持现有结构

## 四、配置文件迁移

### 4.1 新配置结构

```
config/
├── __init__.py
├── development.py    # 开发环境配置
├── production.py     # 生产环境配置
└── testing.py        # 测试环境配置
```

### 4.2 配置内容迁移

| 现有配置 | 新配置位置 | 说明 |
|---------|-----------|------|
| `config.ini` 数据库配置 | `config/development.py` | 数据库连接配置 |
| Flask配置 | `config/base.py` | 基础Flask配置 |
| 安全配置 | `config/security.py` | 安全相关配置 |

## 五、权限配置方案

### 5.1 预定义角色

| 角色名称 | 权限范围 | 说明 |
|---------|---------|------|
| 超级管理员 | 所有权限 | 系统最高权限 |
| 系统管理员 | 用户管理、角色管理 | 用户和权限管理 |
| 规则管理员 | 规则模块所有权限 | 规则相关功能 |
| 数据分析师 | 数据库工具、Excel工具 | 数据处理和分析 |
| 普通用户 | 基础查询权限 | 基本功能使用 |

### 5.2 权限分级

```
系统管理 (system)
├── 用户管理 (system.user)
│   ├── 查看用户 (system.user.view)
│   ├── 创建用户 (system.user.create)
│   ├── 编辑用户 (system.user.edit)
│   └── 删除用户 (system.user.delete)
├── 角色管理 (system.role)
└── 权限管理 (system.permission)

规则管理 (rules)
├── 飞检规则知识库 (rules.knowledge_base)
├── 规则SQL生成器 (rules.sql_generator)
└── 系统规则语句 (rules.system_rules)

数据库工具 (database)
├── SQL生成器 (database.sql_generator)
├── 数据库查询 (database.query)
├── 批量查询 (database.batch_query)
└── 性能测试 (database.performance)

Excel工具 (excel)
├── 文件拆分 (excel.splitter)
├── 内容删除 (excel.delete)
├── 比对工具 (excel.compare)
└── 转SQL工具 (excel.to_sql)

数据处理 (data)
├── 查找重复文件 (data.find_duplicates)
├── 数据校验 (data.validator)
└── 数据标准化 (data.standardization)
```

## 六、迁移检查清单

### 6.1 功能完整性检查

- [ ] 飞检规则知识库功能完整迁移
- [ ] 规则SQL生成器功能完整迁移
- [ ] 系统规则语句功能完整迁移
- [ ] SQL生成器功能完整迁移
- [ ] 数据库查询生成Excel功能完整迁移
- [ ] 批量SQL查询生成Excel功能完整迁移
- [ ] SQL性能测试功能完整迁移
- [ ] Excel文件拆分功能完整迁移
- [ ] Excel内容删除功能完整迁移
- [ ] Excel比对工具功能完整迁移
- [ ] Excel转SQL工具功能完整迁移
- [ ] 查找重复文件功能完整迁移
- [ ] 数据校验功能完整迁移
- [ ] 数据标准化功能完整迁移

### 6.2 权限控制检查

- [ ] 所有页面都有权限控制
- [ ] 所有API都有权限验证
- [ ] 权限配置正确生效
- [ ] 角色权限分配正确

### 6.3 安全性检查

- [ ] 用户认证功能正常
- [ ] 密码加密存储
- [ ] 会话管理安全
- [ ] CSRF保护启用
- [ ] 文件上传安全

这个对照表确保了重构过程中不会遗漏任何现有功能，并为每个功能都配置了相应的权限控制。
