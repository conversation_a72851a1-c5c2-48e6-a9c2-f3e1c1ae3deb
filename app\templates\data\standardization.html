{% extends "base.html" %}

{% block title %}数据标准化 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-magic"></i> 数据标准化</h4>
{% endblock %}

{% block content %}
<!-- 文件上传区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-upload"></i> 文件上传
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="fileInput" class="form-label">选择数据文件</label>
                            <input type="file" class="form-control" id="fileInput" accept=".xlsx,.xls,.csv" onchange="uploadFile()">
                            <div class="form-text">支持 Excel (.xlsx, .xls) 和 CSV 格式文件</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mt-4">
                            <div id="uploadStatus" class="alert alert-info d-none">
                                <i class="fas fa-info-circle"></i>
                                <span id="uploadMessage">请选择文件</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件信息显示 -->
                <div id="fileInfo" class="mt-3 d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless table-sm">
                                <tr><th width="30%">文件名:</th><td id="fileName"></td></tr>
                                <tr><th>文件大小:</th><td id="fileSize"></td></tr>
                                <tr><th>上传时间:</th><td id="uploadTime"></td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div id="filePreview">
                                <h6>文件预览:</h6>
                                <div id="columnsList" class="border rounded p-2" style="max-height: 120px; overflow-y: auto;">
                                    <!-- 列信息将动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 标准化规则配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 标准化规则配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <!-- 重复数据处理 -->
                        <div class="mb-3">
                            <label class="form-label">重复数据处理</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableRemoveDuplicates">
                                <label class="form-check-label" for="enableRemoveDuplicates">
                                    移除重复数据
                                </label>
                            </div>
                            <div id="removeDuplicatesConfig" class="mt-2 d-none">
                                <div class="mb-2">
                                    <label class="form-label">基于列（可选）</label>
                                    <textarea class="form-control" id="duplicateColumns" rows="2" 
                                             placeholder="输入列名，每行一个。留空则基于所有列"></textarea>
                                    <div class="form-text">留空则基于所有列检查重复</div>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">保留策略</label>
                                    <select class="form-select" id="duplicateKeep">
                                        <option value="first">保留第一个</option>
                                        <option value="last">保留最后一个</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 缺失值处理 -->
                        <div class="mb-3">
                            <label class="form-label">缺失值处理</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableFillMissing">
                                <label class="form-check-label" for="enableFillMissing">
                                    填充缺失值
                                </label>
                            </div>
                            <div id="fillMissingConfig" class="mt-2 d-none">
                                <div id="fillMissingContainer">
                                    <!-- 缺失值填充规则将动态添加 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addFillMissingRule()">
                                    <i class="fas fa-plus"></i> 添加规则
                                </button>
                            </div>
                        </div>
                        
                        <!-- 文本标准化 -->
                        <div class="mb-3">
                            <label class="form-label">文本标准化</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableNormalizeText">
                                <label class="form-check-label" for="enableNormalizeText">
                                    标准化文本格式
                                </label>
                            </div>
                            <div id="normalizeTextConfig" class="mt-2 d-none">
                                <div class="mb-2">
                                    <label class="form-label">应用到列</label>
                                    <textarea class="form-control" id="textColumns" rows="2" 
                                             placeholder="输入列名，每行一个"></textarea>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">操作</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="textTrim" checked>
                                        <label class="form-check-label" for="textTrim">去除前后空格</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="textLower">
                                        <label class="form-check-label" for="textLower">转换为小写</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="textUpper">
                                        <label class="form-check-label" for="textUpper">转换为大写</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="textTitle">
                                        <label class="form-check-label" for="textTitle">首字母大写</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <!-- 日期格式化 -->
                        <div class="mb-3">
                            <label class="form-label">日期格式化</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableFormatDates">
                                <label class="form-check-label" for="enableFormatDates">
                                    统一日期格式
                                </label>
                            </div>
                            <div id="formatDatesConfig" class="mt-2 d-none">
                                <div id="formatDatesContainer">
                                    <!-- 日期格式化规则将动态添加 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addDateFormatRule()">
                                    <i class="fas fa-plus"></i> 添加规则
                                </button>
                            </div>
                        </div>
                        
                        <!-- 数值标准化 -->
                        <div class="mb-3">
                            <label class="form-label">数值标准化</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableNormalizeNumbers">
                                <label class="form-check-label" for="enableNormalizeNumbers">
                                    标准化数值格式
                                </label>
                            </div>
                            <div id="normalizeNumbersConfig" class="mt-2 d-none">
                                <div id="normalizeNumbersContainer">
                                    <!-- 数值标准化规则将动态添加 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addNumberNormalizeRule()">
                                    <i class="fas fa-plus"></i> 添加规则
                                </button>
                            </div>
                        </div>
                        
                        <!-- 异常值处理 -->
                        <div class="mb-3">
                            <label class="form-label">异常值处理</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableRemoveOutliers">
                                <label class="form-check-label" for="enableRemoveOutliers">
                                    移除异常值
                                </label>
                            </div>
                            <div id="removeOutliersConfig" class="mt-2 d-none">
                                <div id="removeOutliersContainer">
                                    <!-- 异常值处理规则将动态添加 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-success" onclick="addOutlierRule()">
                                    <i class="fas fa-plus"></i> 添加规则
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="startStandardization()">
                            <i class="fas fa-magic"></i> 开始标准化
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="resetStandardizationRules()">
                            <i class="fas fa-undo"></i> 重置规则
                        </button>
                        <button type="button" class="btn btn-info ms-2" onclick="loadPresetStandardization()">
                            <i class="fas fa-download"></i> 加载预设规则
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 标准化进度区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks"></i> 标准化进度
                </h6>
            </div>
            <div class="card-body">
                <div id="standardizationProgress" class="d-none">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>标准化进度</span>
                            <span id="standardizationStatus">处理中...</span>
                        </div>
                        <div class="progress">
                            <div id="standardizationProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div id="standardizationInfo" class="alert alert-info">
                        <strong>当前状态：</strong><span id="currentStandardizationStatus">正在处理数据...</span>
                    </div>
                </div>
                
                <div id="noStandardizationProgress" class="text-center text-muted py-4">
                    <i class="fas fa-magic fa-3x mb-3"></i>
                    <p>上传文件并配置标准化规则后，点击"开始标准化"开始数据处理</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 标准化结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list-alt"></i> 标准化结果
                </h6>
                <div id="resultActions" class="d-none">
                    <button type="button" class="btn btn-success btn-sm" onclick="downloadStandardizedFile()">
                        <i class="fas fa-download"></i> 下载结果
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="showStandardizationLog()">
                        <i class="fas fa-list"></i> 处理日志
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="standardizationResultsContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-file-excel fa-3x mb-3"></i>
                        <p>标准化完成后结果将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 处理日志模态框 -->
<div class="modal fade" id="standardizationLogModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list"></i> 数据标准化处理日志
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="standardizationLogContent">
                <!-- 处理日志内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportStandardizationLog()">导出日志</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
