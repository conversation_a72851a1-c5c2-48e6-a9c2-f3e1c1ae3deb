#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.permission import Permission

def rebuild_permission_structure():
    """重新构建权限层级结构"""
    app = create_app()
    
    with app.app_context():
        try:
            print('=== 开始重新构建权限层级结构 ===')
            
            # 清理现有权限数据（保留基础数据，重新组织结构）
            print('1. 清理现有权限结构...')
            
            # 删除有问题的权限记录
            problematic_permissions = Permission.query.filter(
                Permission.sort_order.is_(None) | 
                Permission.resource_type.is_(None)
            ).all()
            
            for perm in problematic_permissions:
                print(f'删除有问题的权限: {perm.name} ({perm.code})')
                db.session.delete(perm)
            
            db.session.commit()
            
            # 定义新的权限结构
            print('2. 定义新的权限结构...')
            permission_structure = {
                # 第一级：大模块
                'selfcheck': {
                    'name': '自查自纠',
                    'code': 'selfcheck',
                    'module': 'selfcheck',
                    'resource_type': 'menu',
                    'sort_order': 1,
                    'children': {
                        # 第二级：页面
                        'schemes': {
                            'name': '方案管理',
                            'code': 'selfcheck.schemes',
                            'module': 'selfcheck',
                            'resource_type': 'menu',
                            'sort_order': 1,
                            'children': {
                                # 第三级：功能按钮
                                'view': {'name': '查看方案', 'code': 'selfcheck.schemes.view', 'resource_type': 'page', 'sort_order': 1},
                                'create': {'name': '创建方案', 'code': 'selfcheck.schemes.create', 'resource_type': 'button', 'sort_order': 2},
                                'edit': {'name': '编辑方案', 'code': 'selfcheck.schemes.edit', 'resource_type': 'button', 'sort_order': 3},
                                'delete': {'name': '删除方案', 'code': 'selfcheck.schemes.delete', 'resource_type': 'button', 'sort_order': 4},
                                'manage_rules': {'name': '管理方案规则', 'code': 'selfcheck.schemes.manage_rules', 'resource_type': 'button', 'sort_order': 5},
                                'statistics': {'name': '查看统计', 'code': 'selfcheck.schemes.statistics', 'resource_type': 'button', 'sort_order': 6}
                            }
                        },
                        'rules': {
                            'name': '规则管理',
                            'code': 'selfcheck.rules',
                            'module': 'selfcheck',
                            'resource_type': 'menu',
                            'sort_order': 2,
                            'children': {
                                'view': {'name': '查看规则', 'code': 'selfcheck.rules.view', 'resource_type': 'page', 'sort_order': 1},
                                'create': {'name': '创建规则', 'code': 'selfcheck.rules.create', 'resource_type': 'button', 'sort_order': 2},
                                'edit': {'name': '编辑规则', 'code': 'selfcheck.rules.edit', 'resource_type': 'button', 'sort_order': 3},
                                'delete': {'name': '删除规则', 'code': 'selfcheck.rules.delete', 'resource_type': 'button', 'sort_order': 4},
                                'import': {'name': '导入规则', 'code': 'selfcheck.rules.import', 'resource_type': 'button', 'sort_order': 5}
                            }
                        },
                        'uploads': {
                            'name': '数据上传',
                            'code': 'selfcheck.uploads',
                            'module': 'selfcheck',
                            'resource_type': 'menu',
                            'sort_order': 3,
                            'children': {
                                'view': {'name': '查看上传记录', 'code': 'selfcheck.uploads.view', 'resource_type': 'page', 'sort_order': 1},
                                'create': {'name': '上传文件', 'code': 'selfcheck.uploads.create', 'resource_type': 'button', 'sort_order': 2},
                                'delete': {'name': '删除上传', 'code': 'selfcheck.uploads.delete', 'resource_type': 'button', 'sort_order': 3}
                            }
                        },
                        'tasks': {
                            'name': '自查任务',
                            'code': 'selfcheck.tasks',
                            'module': 'selfcheck',
                            'resource_type': 'menu',
                            'sort_order': 4,
                            'children': {
                                'view': {'name': '查看任务', 'code': 'selfcheck.tasks.view', 'resource_type': 'page', 'sort_order': 1},
                                'create': {'name': '创建任务', 'code': 'selfcheck.tasks.create', 'resource_type': 'button', 'sort_order': 2},
                                'execute': {'name': '执行任务', 'code': 'selfcheck.tasks.execute', 'resource_type': 'button', 'sort_order': 3},
                                'result': {'name': '查看结果', 'code': 'selfcheck.tasks.result', 'resource_type': 'button', 'sort_order': 4},
                                'export': {'name': '导出结果', 'code': 'selfcheck.tasks.export', 'resource_type': 'button', 'sort_order': 5}
                            }
                        }
                    }
                },
                'rules': {
                    'name': '规则管理',
                    'code': 'rules',
                    'module': 'rules',
                    'resource_type': 'menu',
                    'sort_order': 2,
                    'children': {
                        'knowledge_base': {
                            'name': '飞检规则知识库',
                            'code': 'rules.knowledge_base',
                            'module': 'rules',
                            'resource_type': 'menu',
                            'sort_order': 1,
                            'children': {
                                'view': {'name': '查看规则', 'code': 'rules.knowledge_base.view', 'resource_type': 'page', 'sort_order': 1},
                                'create': {'name': '新增规则', 'code': 'rules.knowledge_base.create', 'resource_type': 'button', 'sort_order': 2},
                                'edit': {'name': '编辑规则', 'code': 'rules.knowledge_base.edit', 'resource_type': 'button', 'sort_order': 3},
                                'delete': {'name': '删除规则', 'code': 'rules.knowledge_base.delete', 'resource_type': 'button', 'sort_order': 4},
                                'import': {'name': '导入规则', 'code': 'rules.knowledge_base.import', 'resource_type': 'button', 'sort_order': 5},
                                'merge': {'name': '批量合并', 'code': 'rules.knowledge_base.merge', 'resource_type': 'button', 'sort_order': 6}
                            }
                        },
                        'sql_generator': {
                            'name': '规则SQL生成器',
                            'code': 'rules.sql_generator',
                            'module': 'rules',
                            'resource_type': 'menu',
                            'sort_order': 2,
                            'children': {
                                'view': {'name': '查看生成器', 'code': 'rules.sql_generator.view', 'resource_type': 'page', 'sort_order': 1},
                                'generate': {'name': '生成SQL', 'code': 'rules.sql_generator.generate', 'resource_type': 'button', 'sort_order': 2}
                            }
                        },
                        'system_rules': {
                            'name': '系统规则语句',
                            'code': 'rules.system_rules',
                            'module': 'rules',
                            'resource_type': 'menu',
                            'sort_order': 3,
                            'children': {
                                'view': {'name': '查看系统规则', 'code': 'rules.system_rules.view', 'resource_type': 'page', 'sort_order': 1}
                            }
                        }
                    }
                }
            }

            # 添加其他模块
            permission_structure.update({
                'database': {
                    'name': '数据库管理',
                    'code': 'database',
                    'module': 'database',
                    'resource_type': 'menu',
                    'sort_order': 3,
                    'children': {
                        'sql_generator': {
                            'name': 'SQL生成器',
                            'code': 'database.sql_generator',
                            'module': 'database',
                            'resource_type': 'menu',
                            'sort_order': 1,
                            'children': {
                                'view': {'name': '查看生成器', 'code': 'database.sql_generator.view', 'resource_type': 'page', 'sort_order': 1},
                                'generate': {'name': '生成SQL', 'code': 'database.sql_generator.generate', 'resource_type': 'button', 'sort_order': 2}
                            }
                        },
                        'query': {
                            'name': '数据库查询生成Excel',
                            'code': 'database.query',
                            'module': 'database',
                            'resource_type': 'menu',
                            'sort_order': 2,
                            'children': {
                                'view': {'name': '查看查询', 'code': 'database.query.view', 'resource_type': 'page', 'sort_order': 1},
                                'execute': {'name': '执行查询', 'code': 'database.query.execute', 'resource_type': 'button', 'sort_order': 2}
                            }
                        },
                        'batch_query': {
                            'name': '批量SQL查询生成Excel',
                            'code': 'database.batch_query',
                            'module': 'database',
                            'resource_type': 'menu',
                            'sort_order': 3,
                            'children': {
                                'view': {'name': '查看批量查询', 'code': 'database.batch_query.view', 'resource_type': 'page', 'sort_order': 1},
                                'execute': {'name': '执行批量查询', 'code': 'database.batch_query.execute', 'resource_type': 'button', 'sort_order': 2}
                            }
                        },
                        'performance': {
                            'name': 'SQL性能测试',
                            'code': 'database.performance',
                            'module': 'database',
                            'resource_type': 'menu',
                            'sort_order': 4,
                            'children': {
                                'view': {'name': '查看性能测试', 'code': 'database.performance.view', 'resource_type': 'page', 'sort_order': 1},
                                'test': {'name': '执行性能测试', 'code': 'database.performance.test', 'resource_type': 'button', 'sort_order': 2}
                            }
                        }
                    }
                },
                'excel': {
                    'name': 'Excel工具',
                    'code': 'excel',
                    'module': 'excel',
                    'resource_type': 'menu',
                    'sort_order': 4,
                    'children': {
                        'splitter': {
                            'name': 'Excel文件拆分',
                            'code': 'excel.splitter',
                            'module': 'excel',
                            'resource_type': 'menu',
                            'sort_order': 1,
                            'children': {
                                'view': {'name': '查看拆分工具', 'code': 'excel.splitter.view', 'resource_type': 'page', 'sort_order': 1},
                                'split': {'name': '拆分文件', 'code': 'excel.splitter.split', 'resource_type': 'button', 'sort_order': 2}
                            }
                        },
                        'delete': {
                            'name': 'Excel内容删除',
                            'code': 'excel.delete',
                            'module': 'excel',
                            'resource_type': 'menu',
                            'sort_order': 2,
                            'children': {
                                'view': {'name': '查看删除工具', 'code': 'excel.delete.view', 'resource_type': 'page', 'sort_order': 1},
                                'delete': {'name': '删除内容', 'code': 'excel.delete.delete', 'resource_type': 'button', 'sort_order': 2}
                            }
                        },
                        'compare': {
                            'name': 'Excel比对工具',
                            'code': 'excel.compare',
                            'module': 'excel',
                            'resource_type': 'menu',
                            'sort_order': 3,
                            'children': {
                                'view': {'name': '查看比对工具', 'code': 'excel.compare.view', 'resource_type': 'page', 'sort_order': 1},
                                'compare': {'name': '比对文件', 'code': 'excel.compare.compare', 'resource_type': 'button', 'sort_order': 2}
                            }
                        },
                        'to_sql': {
                            'name': 'Excel转SQL工具',
                            'code': 'excel.to_sql',
                            'module': 'excel',
                            'resource_type': 'menu',
                            'sort_order': 4,
                            'children': {
                                'view': {'name': '查看转换工具', 'code': 'excel.to_sql.view', 'resource_type': 'page', 'sort_order': 1},
                                'convert': {'name': '转换为SQL', 'code': 'excel.to_sql.convert', 'resource_type': 'button', 'sort_order': 2}
                            }
                        }
                    }
                },
                'data': {
                    'name': '数据处理',
                    'code': 'data',
                    'module': 'data',
                    'resource_type': 'menu',
                    'sort_order': 5,
                    'children': {
                        'find_duplicates': {
                            'name': '查找重复文件',
                            'code': 'data.find_duplicates',
                            'module': 'data',
                            'resource_type': 'menu',
                            'sort_order': 1,
                            'children': {
                                'view': {'name': '查看重复文件工具', 'code': 'data.find_duplicates.view', 'resource_type': 'page', 'sort_order': 1},
                                'find': {'name': '查找重复文件', 'code': 'data.find_duplicates.find', 'resource_type': 'button', 'sort_order': 2}
                            }
                        },
                        'validator': {
                            'name': '数据校验',
                            'code': 'data.validator',
                            'module': 'data',
                            'resource_type': 'menu',
                            'sort_order': 2,
                            'children': {
                                'view': {'name': '查看数据校验', 'code': 'data.validator.view', 'resource_type': 'page', 'sort_order': 1},
                                'validate': {'name': '执行校验', 'code': 'data.validator.validate', 'resource_type': 'button', 'sort_order': 2}
                            }
                        },
                        'standardization': {
                            'name': '数据标准化',
                            'code': 'data.standardization',
                            'module': 'data',
                            'resource_type': 'menu',
                            'sort_order': 3,
                            'children': {
                                'view': {'name': '查看数据标准化', 'code': 'data.standardization.view', 'resource_type': 'page', 'sort_order': 1},
                                'standardize': {'name': '执行标准化', 'code': 'data.standardization.standardize', 'resource_type': 'button', 'sort_order': 2}
                            }
                        }
                    }
                },
                'system': {
                    'name': '系统管理',
                    'code': 'system',
                    'module': 'system',
                    'resource_type': 'menu',
                    'sort_order': 6,
                    'children': {
                        'user': {
                            'name': '用户管理',
                            'code': 'system.user',
                            'module': 'system',
                            'resource_type': 'menu',
                            'sort_order': 1,
                            'children': {
                                'view': {'name': '查看用户', 'code': 'system.user.view', 'resource_type': 'page', 'sort_order': 1},
                                'create': {'name': '创建用户', 'code': 'system.user.create', 'resource_type': 'button', 'sort_order': 2},
                                'edit': {'name': '编辑用户', 'code': 'system.user.edit', 'resource_type': 'button', 'sort_order': 3},
                                'delete': {'name': '删除用户', 'code': 'system.user.delete', 'resource_type': 'button', 'sort_order': 4},
                                'reset_password': {'name': '重置密码', 'code': 'system.user.reset_password', 'resource_type': 'button', 'sort_order': 5}
                            }
                        },
                        'role': {
                            'name': '角色管理',
                            'code': 'system.role',
                            'module': 'system',
                            'resource_type': 'menu',
                            'sort_order': 2,
                            'children': {
                                'view': {'name': '查看角色', 'code': 'system.role.view', 'resource_type': 'page', 'sort_order': 1},
                                'create': {'name': '创建角色', 'code': 'system.role.create', 'resource_type': 'button', 'sort_order': 2},
                                'edit': {'name': '编辑角色', 'code': 'system.role.edit', 'resource_type': 'button', 'sort_order': 3},
                                'delete': {'name': '删除角色', 'code': 'system.role.delete', 'resource_type': 'button', 'sort_order': 4}
                            }
                        },
                        'permission': {
                            'name': '权限管理',
                            'code': 'system.permission',
                            'module': 'system',
                            'resource_type': 'menu',
                            'sort_order': 3,
                            'children': {
                                'view': {'name': '查看权限', 'code': 'system.permission.view', 'resource_type': 'page', 'sort_order': 1}
                            }
                        },
                        'audit': {
                            'name': '审计日志',
                            'code': 'system.audit',
                            'module': 'system',
                            'resource_type': 'menu',
                            'sort_order': 4,
                            'children': {
                                'view': {'name': '查看审计日志', 'code': 'system.audit.view', 'resource_type': 'page', 'sort_order': 1}
                            }
                        }
                    }
                }
            })

            print('3. 创建新的权限结构...')
            _create_permissions_recursive(permission_structure, None)

            db.session.commit()
            print('✅ 权限结构重建完成！')

        except Exception as e:
            print(f"重建失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

def _create_permissions_recursive(structure, parent_id):
        """递归创建权限"""
        for key, config in structure.items():
            # 检查权限是否已存在
            existing = Permission.query.filter_by(code=config['code']).first()
            if existing:
                # 更新现有权限
                existing.name = config['name']
                existing.module = config['module']
                existing.resource_type = config['resource_type']
                existing.sort_order = config['sort_order']
                existing.parent_id = parent_id
                existing.is_active = True
                permission = existing
                print(f'更新权限: {permission.name} ({permission.code})')
            else:
                # 创建新权限
                permission = Permission(
                    name=config['name'],
                    code=config['code'],
                    module=config['module'],
                    resource_type=config['resource_type'],
                    sort_order=config['sort_order'],
                    parent_id=parent_id,
                    is_active=True
                )
                db.session.add(permission)
                db.session.flush()  # 获取ID
                print(f'创建权限: {permission.name} ({permission.code})')
            
            # 递归创建子权限
            if 'children' in config:
                _create_permissions_recursive(config['children'], permission.id)

if __name__ == '__main__':
    rebuild_permission_structure()
