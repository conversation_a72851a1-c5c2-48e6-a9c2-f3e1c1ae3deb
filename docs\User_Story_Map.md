# 用户故事地图 (User Story Map)

## 1. 用户故事地图概述

本用户故事地图旨在通过可视化方式，展现医保智能审核系统的用户活动、任务分解以及功能优先级，帮助团队理解用户旅程，并将其与产品路线图和版本规划关联起来。

## 2. 用户活动流 (横向)

以下是医保智能审核系统的主要用户活动流，代表用户在使用系统时会经历的宏观阶段：

1.  **系统登录与初始化**：用户进入系统并进行身份验证。
2.  **数据准备与上传**：用户准备医保相关数据并上传至系统。
3.  **自查任务配置**：用户根据需求配置医保合规性自查任务。
4.  **执行与监控自查**：系统执行自查任务，用户监控任务状态。
5.  **查看与分析报告**：用户查看自查结果报告，分析违规数据。
6.  **违规处理与反馈**：用户对发现的违规进行处理，并可能向系统反馈。
7.  **规则库管理**：用户管理和维护医保合规性规则。
8.  **系统管理与维护**：管理员对系统进行配置和日常维护。

## 3. 用户任务分解 (纵向)

针对每个用户活动，我们进一步分解为具体的任务和用户故事。用户故事采用“作为 [用户角色]，我想要 [完成某事]，以便 [获得价值]”的格式。

### 3.1 系统登录与初始化

-   **任务**：用户登录
    -   作为 **系统用户**，我想要 **通过用户名和密码登录系统**，以便 **访问我的工作台**。
    -   作为 **系统用户**，我想要 **在忘记密码时重置密码**，以便 **重新获得系统访问权限**。
-   **任务**：首次使用引导
    -   作为 **新用户**，我想要 **获得系统功能简介和操作指引**，以便 **快速上手使用系统**。

### 3.2 数据准备与上传

-   **任务**：上传医保数据
    -   作为 **医保管理员**，我想要 **上传HIS/LIS/PACS导出的医保原始数据文件**，以便 **系统进行合规性自查**。
    -   作为 **医保管理员**，我想要 **查看数据上传进度和结果**，以便 **确认数据是否成功导入**。
-   **任务**：管理已上传数据
    -   作为 **医保管理员**，我想要 **查看和管理已上传的数据批次**，以便 **了解数据状态和进行历史追溯**。

### 3.3 自查任务配置

-   **任务**：创建自查任务
    -   作为 **医保管理员**，我想要 **选择要自查的数据批次**，以便 **指定自查范围**。
    -   作为 **医保管理员**，我想要 **选择要应用的规则集**，以便 **根据不同场景进行合规性检查**。
    -   作为 **医保管理员**，我想要 **设置任务名称和描述**，以便 **清晰标识任务目的**。
-   **任务**：编辑/复制自查任务
    -   作为 **医保管理员**，我想要 **编辑或复制现有自查任务**，以便 **快速创建类似任务或进行调整**。

### 3.4 执行与监控自查

-   **任务**：启动自查任务
    -   作为 **医保管理员**，我想要 **手动启动已配置的自查任务**，以便 **立即开始合规性检查**。
    -   作为 **医保管理员**，我想要 **设置定时任务**，以便 **系统自动执行周期性自查**。
-   **任务**：监控任务状态
    -   作为 **医保管理员**，我想要 **实时查看自查任务的执行状态和进度**，以便 **了解任务是否正常运行**。
    -   作为 **医保管理员**，我想要 **接收任务完成或异常通知**，以便 **及时处理问题**。

### 3.5 查看与分析报告

-   **任务**：查看自查报告
    -   作为 **医保管理员**，我想要 **查看违规总览报告**，以便 **快速了解整体合规情况**。
    -   作为 **医保管理员**，我想要 **查看违规明细列表**，以便 **定位具体的违规记录**。
-   **任务**：分析违规数据
    -   作为 **医保管理员**，我想要 **根据违规类型、科室、医生等维度筛选和排序违规数据**，以便 **进行多角度分析**。
    -   作为 **医保管理员**，我想要 **钻取到原始数据详情**，以便 **核实违规细节**。
    -   作为 **医保管理员**，我想要 **导出违规报告和明细数据**，以便 **进行线下处理或汇报**。

### 3.6 违规处理与反馈

-   **任务**：标记处理违规
    -   作为 **医保管理员**，我想要 **对已发现的违规记录进行标记（如“已处理”、“待复核”）**，以便 **跟踪处理进度**。
    -   作为 **医保管理员**，我想要 **添加违规处理备注**，以便 **记录处理过程和结果**。
-   **任务**：系统反馈
    -   作为 **医保管理员**，我想要 **对系统功能或规则提出反馈建议**，以便 **帮助系统持续改进**。

### 3.7 规则库管理

-   **任务**：查看和启用/禁用规则
    -   作为 **医保管理员**，我想要 **查看系统内置的医保合规性规则**，以便 **了解规则内容**。
    -   作为 **医保管理员**，我想要 **启用或禁用特定规则**，以便 **灵活调整自查范围**。
-   **任务**：自定义规则 (v2.0)
    -   作为 **医保管理员**，我想要 **创建和编辑自定义规则**，以便 **满足医院特有的合规性要求**。
    -   作为 **医保管理员**，我想要 **测试自定义规则的有效性**，以便 **确保规则准确无误**。

### 3.8 系统管理与维护

-   **任务**：用户管理
    -   作为 **系统管理员**，我想要 **添加、编辑和删除系统用户**，以便 **管理系统访问权限**。
    -   作为 **系统管理员**，我想要 **分配和修改用户角色**，以便 **控制用户操作范围**。
-   **任务**：系统配置
    -   作为 **系统管理员**，我想要 **配置系统参数（如数据保留策略、通知设置）**，以便 **满足医院管理需求**。
-   **任务**：日志审计
    -   作为 **系统管理员**，我想要 **查看系统操作日志和异常日志**，以便 **进行问题排查和安全审计**。

## 4. 故事优先级与版本映射

以下表格将用户故事与产品路线图中的版本进行映射，并标注其优先级 (P0/P1/P2)。

| 用户活动 | 用户故事 | 优先级 | 版本 |
|---|---|---|---|
| 系统登录与初始化 | 作为系统用户，我想要通过用户名和密码登录系统，以便访问我的工作台。 | P0 | v1.0 |
| | 作为系统用户，我想要在忘记密码时重置密码，以便重新获得系统访问权限。 | P1 | v1.0 |
| 数据准备与上传 | 作为医保管理员，我想要上传HIS/LIS/PACS导出的医保原始数据文件，以便系统进行合规性自查。 | P0 | v1.0 |
| | 作为医保管理员，我想要查看数据上传进度和结果，以便确认数据是否成功导入。 | P0 | v1.0 |
| 自查任务配置 | 作为医保管理员，我想要选择要自查的数据批次，以便指定自查范围。 | P0 | v1.0 |
| | 作为医保管理员，我想要选择要应用的规则集，以便根据不同场景进行合规性检查。 | P0 | v1.0 |
| | 作为医保管理员，我想要设置任务名称和描述，以便清晰标识任务目的。 | P0 | v1.0 |
| 执行与监控自查 | 作为医保管理员，我想要手动启动已配置的自查任务，以便立即开始合规性检查。 | P0 | v1.0 |
| | 作为医保管理员，我想要实时查看自查任务的执行状态和进度，以便了解任务是否正常运行。 | P0 | v1.0 |
| 查看与分析报告 | 作为医保管理员，我想要查看违规总览报告，以便快速了解整体合规情况。 | P0 | v1.0 |
| | 作为医保管理员，我想要查看违规明细列表，以便定位具体的违规记录。 | P0 | v1.0 |
| | 作为医保管理员，我想要根据违规类型、科室、医生等维度筛选和排序违规数据，以便进行多角度分析。 | P1 | v1.0 |
| | 作为医保管理员，我想要导出违规报告和明细数据，以便进行线下处理或汇报。 | P1 | v1.0 |
| 违规处理与反馈 | 作为医保管理员，我想要对已发现的违规记录进行标记（如“已处理”、“待复核”），以便跟踪处理进度。 | P1 | v2.0 |
| 规则库管理 | 作为医保管理员，我想要查看系统内置的医保合规性规则，以便了解规则内容。 | P0 | v1.0 |
| | 作为医保管理员，我想要启用或禁用特定规则，以便灵活调整自查范围。 | P0 | v1.0 |
| | 作为医保管理员，我想要创建和编辑自定义规则，以便满足医院特有的合规性要求。 | P1 | v2.0 |
| 系统管理与维护 | 作为系统管理员，我想要添加、编辑和删除系统用户，以便管理系统访问权限。 | P0 | v1.0 |
| | 作为系统管理员，我想要分配和修改用户角色，以便控制用户操作范围。 | P0 | v1.0 |
| | 作为系统管理员，我想要查看系统操作日志和异常日志，以便进行问题排查和安全审计。 | P1 | v2.0 |