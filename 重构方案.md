# MICRA飞检数据处理工具箱 - 重构和功能增强方案

## 一、项目现状分析

### 1.1 现有架构问题
- **单体文件过大**: app.py 5900+行，难以维护
- **无用户认证**: 完全开放访问，存在安全隐患
- **权限管理缺失**: 无角色控制和功能权限管理
- **代码耦合严重**: 业务逻辑、数据访问、路由混合
- **配置管理混乱**: 硬编码配置，缺乏环境区分
- **前端技术落后**: 纯HTML/CSS/JS，无现代化框架

### 1.2 功能模块现状
**规则管理模块:**
- 飞检规则知识库管理
- 规则SQL生成器
- 系统规则语句

**数据库工具模块:**
- SQL生成器（多模板支持）
- 数据库查询生成Excel
- 批量SQL查询生成Excel
- SQL性能测试

**Excel处理模块:**
- Excel文件拆分
- Excel内容删除
- Excel比对工具
- Excel转SQL工具

**数据处理模块:**
- 查找重复文件
- 数据校验
- 数据标准化

## 二、重构目标

### 2.1 架构目标
- **模块化设计**: 按功能域拆分模块
- **用户认证系统**: 完整的登录注册体系
- **细粒度权限控制**: 精确到页面按钮级别
- **代码可维护性**: 清晰的分层架构
- **安全性提升**: 全面的安全防护
- **现代化前端**: 提升用户体验

### 2.2 功能目标
- **用户管理**: 用户创建、编辑、禁用
- **角色管理**: 灵活的角色定义
- **权限管理**: 菜单、页面、按钮级权限
- **审计日志**: 操作记录和追踪
- **系统配置**: 动态配置管理

## 三、新架构设计

### 3.1 目录结构设计
```
MICRA_登录版/
├── app/                          # 应用核心
│   ├── __init__.py              # 应用工厂
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py             # 用户模型
│   │   ├── role.py             # 角色模型
│   │   ├── permission.py       # 权限模型
│   │   ├── rule.py             # 规则模型
│   │   └── audit.py            # 审计日志模型
│   ├── auth/                    # 认证模块
│   │   ├── __init__.py
│   │   ├── routes.py           # 认证路由
│   │   ├── forms.py            # 认证表单
│   │   └── decorators.py       # 权限装饰器
│   ├── main/                    # 主要功能模块
│   │   ├── __init__.py
│   │   └── routes.py           # 主页路由
│   ├── admin/                   # 管理模块
│   │   ├── __init__.py
│   │   ├── routes.py           # 管理路由
│   │   └── forms.py            # 管理表单
│   ├── rules/                   # 规则管理模块
│   │   ├── __init__.py
│   │   ├── routes.py           # 规则知识库、SQL生成器、系统规则
│   │   ├── services.py
│   │   └── forms.py
│   ├── database/                # 数据库工具模块
│   │   ├── __init__.py
│   │   ├── routes.py           # SQL生成器、查询工具、批量查询、性能测试
│   │   ├── services.py
│   │   └── forms.py
│   ├── excel/                   # Excel工具模块
│   │   ├── __init__.py
│   │   ├── routes.py           # 拆分、删除、比对、转SQL
│   │   ├── services.py
│   │   └── forms.py
│   ├── data/                    # 数据处理模块
│   │   ├── __init__.py
│   │   ├── routes.py           # 重复文件、数据校验、标准化
│   │   ├── services.py
│   │   └── forms.py
│   ├── utils/                   # 工具类
│   │   ├── __init__.py
│   │   ├── database.py         # 数据库连接池
│   │   ├── security.py         # 安全工具
│   │   ├── file_manager.py     # 文件管理
│   │   └── validators.py       # 验证器
│   └── templates/               # 模板文件
│       ├── base.html           # 基础模板
│       ├── auth/               # 认证模板
│       ├── admin/              # 管理模板
│       ├── rules/              # 规则模板
│       ├── database/           # 数据库模板
│       ├── excel/              # Excel模板
│       └── data/               # 数据处理模板
├── static/                      # 静态资源
│   ├── css/
│   ├── js/
│   └── img/
├── migrations/                  # 数据库迁移
├── config/                      # 配置文件
│   ├── __init__.py
│   ├── development.py
│   ├── production.py
│   └── testing.py
├── tests/                       # 测试文件
├── requirements.txt             # 依赖包
├── run.py                      # 启动文件
└── README.md                   # 项目说明
```

### 3.2 数据库设计

#### 3.2.1 用户认证相关表
```sql
-- 用户表
CREATE TABLE users (
    id NUMBER PRIMARY KEY,
    username VARCHAR2(50) UNIQUE NOT NULL,
    email VARCHAR2(100) UNIQUE NOT NULL,
    password_hash VARCHAR2(255) NOT NULL,
    real_name VARCHAR2(100),
    phone VARCHAR2(20),
    department VARCHAR2(100),
    is_active NUMBER(1) DEFAULT 1,
    is_admin NUMBER(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- 角色表
CREATE TABLE roles (
    id NUMBER PRIMARY KEY,
    name VARCHAR2(50) UNIQUE NOT NULL,
    description VARCHAR2(200),
    is_active NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE permissions (
    id NUMBER PRIMARY KEY,
    name VARCHAR2(100) UNIQUE NOT NULL,
    code VARCHAR2(100) UNIQUE NOT NULL,
    description VARCHAR2(200),
    module VARCHAR2(50),
    resource_type VARCHAR2(20), -- menu, page, button, api
    parent_id NUMBER,
    sort_order NUMBER DEFAULT 0,
    is_active NUMBER(1) DEFAULT 1
);

-- 用户角色关联表
CREATE TABLE user_roles (
    user_id NUMBER,
    role_id NUMBER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE role_permissions (
    role_id NUMBER,
    permission_id NUMBER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id)
);

-- 审计日志表
CREATE TABLE audit_logs (
    id NUMBER PRIMARY KEY,
    user_id NUMBER,
    action VARCHAR2(50),
    resource VARCHAR2(100),
    resource_id VARCHAR2(50),
    details CLOB,
    ip_address VARCHAR2(45),
    user_agent VARCHAR2(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.2.2 权限资源定义
```sql
-- 插入基础权限数据
INSERT INTO permissions (id, name, code, description, module, resource_type, parent_id, sort_order) VALUES
-- 系统管理
(1, '系统管理', 'system', '系统管理模块', 'system', 'menu', NULL, 1),
(2, '用户管理', 'system.user', '用户管理', 'system', 'menu', 1, 1),
(3, '角色管理', 'system.role', '角色管理', 'system', 'menu', 1, 2),
(4, '权限管理', 'system.permission', '权限管理', 'system', 'menu', 1, 3),

-- 用户管理权限
(10, '查看用户', 'system.user.view', '查看用户列表', 'system', 'page', 2, 1),
(11, '创建用户', 'system.user.create', '创建新用户', 'system', 'button', 2, 2),
(12, '编辑用户', 'system.user.edit', '编辑用户信息', 'system', 'button', 2, 3),
(13, '删除用户', 'system.user.delete', '删除用户', 'system', 'button', 2, 4),
(14, '重置密码', 'system.user.reset_password', '重置用户密码', 'system', 'button', 2, 5),

-- 规则管理
(20, '规则管理', 'rules', '规则管理模块', 'rules', 'menu', NULL, 2),
(21, '飞检规则知识库', 'rules.knowledge_base', '飞检规则知识库', 'rules', 'menu', 20, 1),
(22, '规则SQL生成器', 'rules.sql_generator', '规则SQL生成器', 'rules', 'menu', 20, 2),
(23, '系统规则语句', 'rules.system_rules', '系统规则语句', 'rules', 'menu', 20, 3),

-- 数据库工具
(30, '数据库工具', 'database', '数据库工具模块', 'database', 'menu', NULL, 3),
(31, 'SQL生成器', 'database.sql_generator', 'SQL生成器', 'database', 'menu', 30, 1),
(32, '数据库查询生成Excel', 'database.query', '数据库查询生成Excel', 'database', 'menu', 30, 2),
(33, '批量SQL查询生成Excel', 'database.batch_query', '批量SQL查询生成Excel', 'database', 'menu', 30, 3),
(34, 'SQL性能测试', 'database.performance', 'SQL性能测试', 'database', 'menu', 30, 4),

-- Excel工具
(40, 'Excel工具', 'excel', 'Excel工具模块', 'excel', 'menu', NULL, 4),
(41, 'Excel文件拆分', 'excel.splitter', 'Excel文件拆分', 'excel', 'menu', 40, 1),
(42, 'Excel内容删除', 'excel.delete', 'Excel内容删除', 'excel', 'menu', 40, 2),
(43, 'Excel比对工具', 'excel.compare', 'Excel比对工具', 'excel', 'menu', 40, 3),
(44, 'Excel转SQL工具', 'excel.to_sql', 'Excel转SQL工具', 'excel', 'menu', 40, 4),

-- 数据处理工具
(50, '数据处理', 'data', '数据处理模块', 'data', 'menu', NULL, 5),
(51, '查找重复文件', 'data.find_duplicates', '查找重复文件', 'data', 'menu', 50, 1),
(52, '数据校验', 'data.validator', '数据校验', 'data', 'menu', 50, 2),
(53, '数据标准化', 'data.standardization', '数据标准化', 'data', 'menu', 50, 3);
```

### 3.3 核心功能设计

#### 3.3.1 用户认证系统
- **登录功能**: 用户名/邮箱 + 密码登录
- **注册功能**: 管理员创建用户账号
- **密码管理**: 密码强度验证、重置功能
- **会话管理**: 安全的会话控制和超时处理
- **记住登录**: 可选的自动登录功能

#### 3.3.2 权限控制系统
- **RBAC模型**: 基于角色的访问控制
- **多级权限**: 菜单 -> 页面 -> 按钮 -> API
- **动态权限**: 运行时权限检查
- **权限继承**: 角色权限继承机制
- **权限缓存**: Redis缓存提升性能

#### 3.3.3 用户管理功能
- **用户CRUD**: 创建、查看、编辑、删除用户
- **批量操作**: 批量导入、导出用户
- **用户状态**: 启用/禁用用户账号
- **密码策略**: 密码复杂度、过期策略
- **登录限制**: 登录失败锁定机制

#### 3.3.4 角色权限管理
- **角色定义**: 灵活的角色创建和配置
- **权限分配**: 可视化权限分配界面
- **权限模板**: 预定义权限模板
- **权限审计**: 权限变更记录和审计

## 四、技术选型

### 4.1 后端技术栈
- **Web框架**: Flask + Flask-Login + Flask-WTF
- **数据库ORM**: SQLAlchemy
- **认证授权**: Flask-Login + Flask-Principal
- **表单验证**: WTForms
- **密码加密**: Werkzeug + bcrypt
- **缓存**: Redis
- **任务队列**: Celery (可选)

### 4.2 前端技术栈
- **基础框架**: Bootstrap 5
- **JavaScript**: jQuery + 原生JS
- **图标**: Font Awesome
- **表格组件**: DataTables
- **图表组件**: Chart.js (可选)
- **文件上传**: Dropzone.js

### 4.3 安全措施
- **CSRF保护**: Flask-WTF CSRF令牌
- **XSS防护**: 模板自动转义
- **SQL注入防护**: SQLAlchemy参数化查询
- **文件上传安全**: 文件类型和大小限制
- **会话安全**: 安全的会话配置
- **密码安全**: 强密码策略和加密存储

## 五、实施计划

### 5.1 第一阶段：基础架构重构 (1-2周)
1. **项目结构重组**
   - 创建新的目录结构
   - 拆分app.py为多个模块
   - 配置管理重构

2. **数据库设计实现**
   - 创建用户认证相关表
   - 数据迁移脚本
   - 基础数据初始化

3. **基础框架搭建**
   - Flask应用工厂模式
   - 蓝图模块化
   - 配置管理系统

### 5.2 第二阶段：认证授权系统 (2-3周)
1. **用户认证功能**
   - 登录/注册页面
   - 密码加密和验证
   - 会话管理

2. **权限控制系统**
   - RBAC权限模型实现
   - 权限装饰器
   - 动态菜单生成

3. **用户管理界面**
   - 用户列表和CRUD操作
   - 角色分配界面
   - 权限管理界面

### 5.3 第三阶段：业务功能迁移 (3-4周)
1. **规则管理模块**
   - 飞检规则知识库功能迁移
   - 规则SQL生成器功能迁移
   - 系统规则语句功能迁移
   - 权限控制集成
   - 界面优化

2. **数据库工具模块**
   - SQL生成器功能迁移
   - 数据库查询生成Excel功能迁移
   - 批量SQL查询生成Excel功能迁移
   - SQL性能测试功能迁移
   - 权限控制集成

3. **Excel工具模块**
   - Excel文件拆分功能迁移
   - Excel内容删除功能迁移
   - Excel比对工具功能迁移
   - Excel转SQL工具功能迁移
   - 文件上传安全加固
   - 权限控制集成

4. **数据处理模块**
   - 查找重复文件功能迁移
   - 数据校验功能迁移
   - 数据标准化功能迁移
   - 权限控制集成

### 5.4 第四阶段：优化和测试 (1-2周)
1. **性能优化**
   - 数据库查询优化
   - 缓存策略实施
   - 前端资源优化

2. **安全加固**
   - 安全测试和修复
   - 日志和监控
   - 备份策略

3. **测试和部署**
   - 单元测试
   - 集成测试
   - 部署文档

## 六、风险评估和应对

### 6.1 技术风险
- **数据迁移风险**: 制定详细的数据备份和迁移计划
- **兼容性风险**: 充分测试现有功能的兼容性
- **性能风险**: 进行性能测试和优化

### 6.2 业务风险
- **功能中断风险**: 分阶段上线，保持业务连续性
- **用户接受度风险**: 提供培训和文档支持
- **数据安全风险**: 实施全面的安全措施

### 6.3 应对策略
- **渐进式迁移**: 逐步迁移功能，降低风险
- **并行运行**: 新旧系统并行运行一段时间
- **回滚机制**: 准备快速回滚方案
- **监控告警**: 实施全面的监控和告警机制

## 七、预期收益

### 7.1 技术收益
- **代码可维护性**: 模块化设计，易于维护和扩展
- **系统安全性**: 全面的安全防护机制
- **开发效率**: 清晰的架构提升开发效率
- **系统稳定性**: 更好的错误处理和监控

### 7.2 业务收益
- **用户体验**: 现代化界面和更好的交互体验
- **权限管控**: 精细化权限控制，提升安全性
- **操作审计**: 完整的操作记录和审计功能
- **系统扩展**: 为未来功能扩展奠定基础

这个重构方案将把您的项目从一个单体应用转变为一个现代化、安全、可维护的企业级应用系统。

## 八、SelfCheck在线自查自纠模块

### 8.1 模块概述
SelfCheck模块是从ruoyi-selfcheck项目移植而来的在线自查自纠功能，集成到MICRA工具箱中，提供完整的数据自查解决方案。

### 8.2 核心功能
- **规则管理**: 自查规则的CRUD操作，支持从rule_sql_history表导入历史规则
- **数据上传**: 支持CSV、DMP、DP、BAK格式文件上传，文件大小限制10MB
- **任务执行**: 基于规则对上传数据进行自动检查
- **结果管理**: 检查结果的查看、分析和导出

### 8.3 数据库设计
```sql
-- 自查规则表
CREATE TABLE selfcheck_rules (
    id NUMBER PRIMARY KEY,
    rule_name VARCHAR2(200) NOT NULL,
    rule_code VARCHAR2(100) UNIQUE NOT NULL,
    rule_description CLOB,
    rule_type VARCHAR2(50) NOT NULL,
    rule_content CLOB,
    rule_version VARCHAR2(20) DEFAULT '1.0',
    status VARCHAR2(10) DEFAULT 'active',
    sort_order NUMBER DEFAULT 0,
    created_by NUMBER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by NUMBER,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- 从rule_sql_history导入的字段
    sql_content CLOB,
    city VARCHAR2(10),
    rule_source VARCHAR2(200),
    medical_behavior VARCHAR2(150),
    template_name VARCHAR2(30),
    visit_type VARCHAR2(10),
    types VARCHAR2(10),
    original_rule_id NUMBER
);

-- 数据上传表
CREATE TABLE selfcheck_uploads (
    id NUMBER PRIMARY KEY,
    user_id NUMBER NOT NULL,
    file_name VARCHAR2(500) NOT NULL,
    file_path VARCHAR2(1000) NOT NULL,
    file_size NUMBER,
    file_type VARCHAR2(50),
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR2(20) DEFAULT 'pending',
    validation_result CLOB,
    error_message CLOB,
    record_count NUMBER DEFAULT 0,
    CONSTRAINT fk_selfcheck_uploads_user FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 自查任务表
CREATE TABLE selfcheck_tasks (
    id NUMBER PRIMARY KEY,
    task_name VARCHAR2(200) NOT NULL,
    user_id NUMBER NOT NULL,
    upload_id NUMBER NOT NULL,
    rule_id NUMBER NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR2(20) DEFAULT 'pending',
    progress NUMBER DEFAULT 0,
    result_summary CLOB,
    result_detail CLOB,
    error_message CLOB,
    error_count NUMBER DEFAULT 0,
    warning_count NUMBER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_selfcheck_tasks_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_selfcheck_tasks_upload FOREIGN KEY (upload_id) REFERENCES selfcheck_uploads(id),
    CONSTRAINT fk_selfcheck_tasks_rule FOREIGN KEY (rule_id) REFERENCES selfcheck_rules(id)
);
```

### 8.4 权限设计
```sql
-- 自查自纠模块权限
INSERT INTO permissions (id, name, code, description, module, resource_type, parent_id, sort_order) VALUES
-- 主菜单
(60, '自查自纠', 'selfcheck', '在线自查自纠模块', 'selfcheck', 'menu', NULL, 6),

-- 规则管理
(61, '规则管理', 'selfcheck.rules', '自查规则管理', 'selfcheck', 'menu', 60, 1),
(611, '查看规则', 'selfcheck.rules.view', '查看自查规则', 'selfcheck', 'page', 61, 1),
(612, '创建规则', 'selfcheck.rules.create', '创建自查规则', 'selfcheck', 'button', 61, 2),
(613, '编辑规则', 'selfcheck.rules.edit', '编辑自查规则', 'selfcheck', 'button', 61, 3),
(614, '删除规则', 'selfcheck.rules.delete', '删除自查规则', 'selfcheck', 'button', 61, 4),
(615, '导入规则', 'selfcheck.rules.import', '从历史规则导入', 'selfcheck', 'button', 61, 5),

-- 数据上传
(62, '数据上传', 'selfcheck.upload', '数据文件上传', 'selfcheck', 'menu', 60, 2),
(621, '查看上传记录', 'selfcheck.upload.view', '查看上传记录', 'selfcheck', 'page', 62, 1),
(622, '上传文件', 'selfcheck.upload.create', '上传数据文件', 'selfcheck', 'button', 62, 2),
(623, '删除上传', 'selfcheck.upload.delete', '删除上传记录', 'selfcheck', 'button', 62, 3),

-- 自查任务
(63, '自查任务', 'selfcheck.tasks', '自查任务管理', 'selfcheck', 'menu', 60, 3),
(631, '查看任务', 'selfcheck.tasks.view', '查看自查任务', 'selfcheck', 'page', 63, 1),
(632, '创建任务', 'selfcheck.tasks.create', '创建自查任务', 'selfcheck', 'button', 63, 2),
(633, '执行任务', 'selfcheck.tasks.execute', '执行自查任务', 'selfcheck', 'button', 63, 3),
(634, '查看结果', 'selfcheck.tasks.result', '查看任务结果', 'selfcheck', 'button', 63, 4),
(635, '导出结果', 'selfcheck.tasks.export', '导出任务结果', 'selfcheck', 'button', 63, 5);
```

### 8.5 技术特性
- **模块化设计**: 独立的selfcheck模块，易于维护和扩展
- **规则引擎**: 灵活的规则配置和执行机制
- **文件处理**: 支持多种格式的数据文件上传和处理
- **任务调度**: 异步任务执行和进度监控
- **权限集成**: 完整的权限控制体系

### 8.6 实施状态
✅ **已完成**:
- 数据库表结构设计
- 数据模型定义
- 服务层实现
- 路由控制器
- 前端页面模板
- 权限集成
- 导航菜单集成

🔄 **进行中**:
- 规则导入功能完善
- 文件上传API实现
- 任务执行引擎开发

📋 **待完成**:
- 规则引擎核心逻辑
- 任务调度系统
- 结果分析和导出
- 性能优化和测试
```
