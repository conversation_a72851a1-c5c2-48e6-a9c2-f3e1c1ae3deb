#!/usr/bin/env python3
"""
规则管理模块功能测试脚本
测试具体的业务功能
"""

import requests
import json
import time
from datetime import datetime

class FunctionalRulesTest:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def test_function(self, name, test_func):
        """执行功能测试"""
        print(f"🧪 功能测试: {name}")
        start_time = time.time()
        
        try:
            result = test_func()
            duration = time.time() - start_time
            
            if result['success']:
                print(f"✅ {name} - 通过 ({duration:.2f}s)")
                if 'details' in result:
                    print(f"   详情: {result['details']}")
                self.test_results.append({
                    'name': name,
                    'status': 'PASS',
                    'duration': duration,
                    'details': result.get('details', ''),
                    'error': None
                })
            else:
                print(f"❌ {name} - 失败 ({duration:.2f}s)")
                print(f"   原因: {result.get('error', '未知错误')}")
                self.test_results.append({
                    'name': name,
                    'status': 'FAIL',
                    'duration': duration,
                    'details': result.get('details', ''),
                    'error': result.get('error', '未知错误')
                })
        except Exception as e:
            duration = time.time() - start_time
            print(f"❌ {name} - 异常 ({duration:.2f}s): {str(e)}")
            self.test_results.append({
                'name': name,
                'status': 'ERROR',
                'duration': duration,
                'details': '',
                'error': str(e)
            })
    
    def test_rules_api_data_structure(self):
        """测试规则API数据结构"""
        try:
            response = self.session.get(f"{self.base_url}/rules/api/rules?per_page=5")
            
            if response.status_code != 200:
                return {'success': False, 'error': f'API返回状态码: {response.status_code}'}
            
            data = response.json()
            
            if not data.get('success'):
                return {'success': False, 'error': 'API返回success=false'}
            
            rules = data.get('rules', [])
            if not rules:
                return {'success': True, 'details': '规则列表为空，但API结构正确'}
            
            # 检查数据结构
            first_rule = rules[0]
            required_fields = ['ID', '规则名称']
            missing_fields = [field for field in required_fields if field not in first_rule]
            
            if missing_fields:
                return {'success': False, 'error': f'缺少必要字段: {missing_fields}'}
            
            return {
                'success': True, 
                'details': f'获取到{len(rules)}条规则，数据结构完整'
            }
            
        except Exception as e:
            return {'success': False, 'error': f'请求异常: {str(e)}'}
    
    def test_rules_search_functionality(self):
        """测试规则搜索功能"""
        try:
            # 测试无参数搜索
            response = self.session.get(f"{self.base_url}/rules/api/search")
            
            if response.status_code != 200:
                return {'success': False, 'error': f'搜索API状态码: {response.status_code}'}
            
            data = response.json()
            if not data.get('success'):
                return {'success': False, 'error': '搜索API返回success=false'}
            
            # 测试带参数搜索
            search_params = {
                'city': '北京',
                'rule_type': '频次上限'
            }
            
            response = self.session.get(f"{self.base_url}/rules/api/search", params=search_params)
            
            if response.status_code != 200:
                return {'success': False, 'error': f'参数搜索状态码: {response.status_code}'}
            
            data = response.json()
            if not data.get('success'):
                return {'success': False, 'error': '参数搜索返回success=false'}
            
            return {
                'success': True,
                'details': f'搜索功能正常，支持无参数和带参数搜索'
            }
            
        except Exception as e:
            return {'success': False, 'error': f'搜索测试异常: {str(e)}'}
    
    def test_rules_pagination(self):
        """测试规则分页功能"""
        try:
            # 测试第一页
            response = self.session.get(f"{self.base_url}/rules/api/rules?page=1&per_page=10")
            
            if response.status_code != 200:
                return {'success': False, 'error': f'分页API状态码: {response.status_code}'}
            
            data = response.json()
            if not data.get('success'):
                return {'success': False, 'error': '分页API返回success=false'}
            
            # 检查分页信息
            pagination_fields = ['total', 'page', 'per_page', 'pages']
            missing_pagination = [field for field in pagination_fields if field not in data]
            
            if missing_pagination:
                return {'success': False, 'error': f'缺少分页字段: {missing_pagination}'}
            
            total = data.get('total', 0)
            page = data.get('page', 1)
            per_page = data.get('per_page', 10)
            pages = data.get('pages', 1)
            
            return {
                'success': True,
                'details': f'分页功能正常 - 总计:{total}条, 第{page}页, 每页{per_page}条, 共{pages}页'
            }
            
        except Exception as e:
            return {'success': False, 'error': f'分页测试异常: {str(e)}'}
    
    def test_database_query_functionality(self):
        """测试数据库查询功能"""
        try:
            # 测试数据库查询页面
            response = self.session.get(f"{self.base_url}/database/query")
            
            if response.status_code != 200:
                return {'success': False, 'error': f'数据库查询页面状态码: {response.status_code}'}
            
            # 检查页面是否包含必要元素
            page_content = response.text
            required_elements = ['SQL查询', '执行查询', 'textarea']
            missing_elements = [elem for elem in required_elements if elem not in page_content]
            
            if missing_elements:
                return {'success': False, 'error': f'页面缺少元素: {missing_elements}'}
            
            return {
                'success': True,
                'details': '数据库查询页面正常，包含必要的查询元素'
            }
            
        except Exception as e:
            return {'success': False, 'error': f'数据库查询测试异常: {str(e)}'}
    
    def test_static_resources_loading(self):
        """测试静态资源加载"""
        try:
            # 测试关键静态资源
            resources = [
                ('/static/css/bootstrap.min.css', 'Bootstrap CSS'),
                ('/static/js/bootstrap.bundle.min.js', 'Bootstrap JS'),
                ('/static/css/app.css', '应用样式')
            ]
            
            loaded_resources = []
            failed_resources = []
            
            for path, name in resources:
                response = self.session.get(f"{self.base_url}{path}")
                if response.status_code == 200:
                    loaded_resources.append(name)
                else:
                    failed_resources.append(f"{name}({response.status_code})")
            
            if loaded_resources:
                details = f"成功加载: {', '.join(loaded_resources)}"
                if failed_resources:
                    details += f"; 失败: {', '.join(failed_resources)}"
                return {'success': True, 'details': details}
            else:
                return {'success': False, 'error': f'所有资源加载失败: {failed_resources}'}
            
        except Exception as e:
            return {'success': False, 'error': f'静态资源测试异常: {str(e)}'}
    
    def test_error_handling(self):
        """测试错误处理"""
        try:
            # 测试无效的规则ID
            response = self.session.get(f"{self.base_url}/rules/api/rules/99999")
            
            # 应该返回错误但不是500
            if response.status_code == 500:
                return {'success': False, 'error': '无效ID返回500错误，错误处理不当'}
            
            # 测试无效的API路径
            response = self.session.get(f"{self.base_url}/rules/api/invalid_endpoint")
            
            if response.status_code == 500:
                return {'success': False, 'error': '无效路径返回500错误，错误处理不当'}
            
            return {
                'success': True,
                'details': '错误处理正常，无效请求不会导致500错误'
            }
            
        except Exception as e:
            return {'success': False, 'error': f'错误处理测试异常: {str(e)}'}
    
    def test_performance_metrics(self):
        """测试性能指标"""
        try:
            # 测试规则列表加载性能
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/rules/api/rules?per_page=50")
            load_time = time.time() - start_time
            
            if response.status_code != 200:
                return {'success': False, 'error': f'性能测试API状态码: {response.status_code}'}
            
            # 性能标准：2秒内完成
            if load_time > 2.0:
                return {
                    'success': False, 
                    'error': f'加载时间过长: {load_time:.2f}s (标准: <2s)'
                }
            
            return {
                'success': True,
                'details': f'性能良好，50条记录加载时间: {load_time:.2f}s'
            }
            
        except Exception as e:
            return {'success': False, 'error': f'性能测试异常: {str(e)}'}
    
    def run_functional_tests(self):
        """运行功能测试"""
        print("🚀 开始规则管理模块功能测试")
        print("=" * 60)
        
        # 定义测试用例
        test_cases = [
            ("规则API数据结构测试", self.test_rules_api_data_structure),
            ("规则搜索功能测试", self.test_rules_search_functionality),
            ("规则分页功能测试", self.test_rules_pagination),
            ("数据库查询功能测试", self.test_database_query_functionality),
            ("静态资源加载测试", self.test_static_resources_loading),
            ("错误处理测试", self.test_error_handling),
            ("性能指标测试", self.test_performance_metrics),
        ]
        
        # 执行测试
        for test_name, test_func in test_cases:
            self.test_function(test_name, test_func)
            time.sleep(0.5)  # 避免请求过快
        
        # 生成报告
        self.generate_functional_report()
    
    def generate_functional_report(self):
        """生成功能测试报告"""
        print("\n" + "=" * 60)
        print("📋 功能测试报告")
        print("=" * 60)
        
        total = len(self.test_results)
        passed = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed = len([r for r in self.test_results if r['status'] == 'FAIL'])
        errors = len([r for r in self.test_results if r['status'] == 'ERROR'])
        
        print(f"总功能测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        print(f"错误: {errors}")
        if total > 0:
            print(f"通过率: {(passed/total*100):.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASS' else "❌"
            print(f"{status_icon} {result['name']} - {result['status']} ({result['duration']:.2f}s)")
            if result['details']:
                print(f"   详情: {result['details']}")
            if result['error']:
                print(f"   错误: {result['error']}")
        
        # 保存报告
        report_file = f"functional_rules_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'test_type': 'functional',
                'summary': {
                    'total': total,
                    'passed': passed,
                    'failed': failed,
                    'errors': errors,
                    'pass_rate': passed/total*100 if total > 0 else 0
                },
                'results': self.test_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 功能测试报告已保存到: {report_file}")

if __name__ == '__main__':
    tester = FunctionalRulesTest()
    tester.run_functional_tests()
