#!/usr/bin/env python3
"""
测试导入规则改进功能的脚本
"""

import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_concurrent_processing():
    """测试并发处理逻辑"""
    print("=== 测试并发处理逻辑 ===")
    
    # 模拟处理函数
    def process_item(item_id):
        print(f"开始处理项目 {item_id} (线程: {threading.current_thread().ident})")
        time.sleep(1)  # 模拟处理时间
        if item_id % 3 == 0:  # 模拟部分失败
            raise Exception(f"项目 {item_id} 处理失败")
        print(f"项目 {item_id} 处理成功")
        return True
    
    items = list(range(1, 11))  # 10个项目
    success_count = 0
    errors = []
    
    # 使用线程池并发处理
    max_workers = min(5, len(items))
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_item = {
            executor.submit(process_item, item_id): item_id 
            for item_id in items
        }
        
        for future in future_to_item:
            item_id = future_to_item[future]
            try:
                result = future.result()
                if result:
                    success_count += 1
            except Exception as exc:
                errors.append(f"项目 {item_id}: {str(exc)}")
    
    print(f"\n处理结果:")
    print(f"总计: {len(items)}")
    print(f"成功: {success_count}")
    print(f"失败: {len(errors)}")
    print(f"错误列表: {errors}")
    
    return success_count, errors

def test_save_verification():
    """测试保存验证逻辑"""
    print("\n=== 测试保存验证逻辑 ===")
    
    # 模拟数据库操作
    class MockRule:
        def __init__(self, rule_id, rule_code):
            self.id = None
            self.rule_id = rule_id
            self.rule_code = rule_code
            self.rule_name = f"测试规则_{rule_id}"
            
        def save_with_verification(self):
            """模拟带验证的保存"""
            print(f"保存规则: {self.rule_code}")
            
            # 模拟检查重复
            if self.rule_code.endswith("_duplicate"):
                print(f"规则编码已存在: {self.rule_code}")
                return False
            
            # 模拟保存操作
            time.sleep(0.1)  # 模拟数据库操作时间
            
            # 模拟部分失败
            if self.rule_id % 4 == 0:
                print(f"保存失败: {self.rule_code}")
                return False
            
            # 模拟验证保存结果
            self.id = self.rule_id * 100  # 模拟生成的ID
            print(f"保存成功: {self.rule_code}, ID: {self.id}")
            return True
    
    # 测试不同情况
    test_cases = [
        (1, "IMPORT_1_123456"),
        (2, "IMPORT_2_123457"),
        (3, "IMPORT_3_duplicate"),  # 重复编码
        (4, "IMPORT_4_123458"),     # 保存失败
        (5, "IMPORT_5_123459"),
    ]
    
    success_count = 0
    for rule_id, rule_code in test_cases:
        rule = MockRule(rule_id, rule_code)
        if rule.save_with_verification():
            success_count += 1
    
    print(f"\n验证结果:")
    print(f"总计: {len(test_cases)}")
    print(f"成功: {success_count}")
    print(f"失败: {len(test_cases) - success_count}")
    
    return success_count

def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 测试性能对比 ===")
    
    def sequential_processing(items):
        """顺序处理"""
        start_time = time.time()
        for item in items:
            time.sleep(0.1)  # 模拟处理时间
        end_time = time.time()
        return end_time - start_time
    
    def concurrent_processing(items):
        """并发处理"""
        start_time = time.time()
        max_workers = min(5, len(items))
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(lambda x: time.sleep(0.1), item) for item in items]
            for future in futures:
                future.result()
        end_time = time.time()
        return end_time - start_time
    
    items = list(range(20))  # 20个项目
    
    seq_time = sequential_processing(items)
    conc_time = concurrent_processing(items)
    
    print(f"处理 {len(items)} 个项目:")
    print(f"顺序处理耗时: {seq_time:.2f} 秒")
    print(f"并发处理耗时: {conc_time:.2f} 秒")
    print(f"性能提升: {seq_time/conc_time:.2f}x")
    
    return seq_time, conc_time

def test_table_structure():
    """测试表结构"""
    print("\n=== 测试 rule_sql_history 表结构 ===")

    try:
        from app.selfcheck.database import execute_query

        # 查询表的字段信息
        desc_query = """
        SELECT column_name, data_type, nullable
        FROM user_tab_columns
        WHERE table_name = 'RULE_SQL_HISTORY'
        ORDER BY column_id
        """

        columns = execute_query(desc_query)
        if columns:
            print("表字段信息:")
            for col in columns:
                print(f"  - {col['column_name']} ({col['data_type']})")

        # 查询一条记录看字段
        sample_query = "SELECT * FROM rule_sql_history WHERE ROWNUM <= 1"
        result = execute_query(sample_query)
        if result:
            print("\n实际字段列表:")
            for key in result[0].keys():
                print(f"  - {key}")

        # 检查关键字段
        key_fields = ['COMPARE_ID', 'VISIT_TYPE', 'RULE_ID']
        existing_fields = list(result[0].keys()) if result else []

        print("\n关键字段检查:")
        for field in key_fields:
            status = "存在" if field.lower() in [f.lower() for f in existing_fields] else "不存在"
            print(f"  - {field}: {status}")

        # 测试COMPARE_ID+VISIT_TYPE的唯一性
        if result:
            print("\n=== 测试COMPARE_ID+VISIT_TYPE唯一性 ===")
            unique_check_query = """
            SELECT compare_id, visit_type, COUNT(*) as cnt
            FROM rule_sql_history
            WHERE compare_id IS NOT NULL AND visit_type IS NOT NULL
            GROUP BY compare_id, visit_type
            HAVING COUNT(*) > 1
            ORDER BY cnt DESC
            """

            duplicates = execute_query(unique_check_query)
            if duplicates:
                print(f"发现 {len(duplicates)} 组重复的 COMPARE_ID + VISIT_TYPE:")
                for dup in duplicates[:5]:  # 只显示前5个
                    print(f"  COMPARE_ID: {dup['compare_id']}, VISIT_TYPE: {dup['visit_type']}, 重复次数: {dup['cnt']}")
            else:
                print("COMPARE_ID + VISIT_TYPE 组合是唯一的")

            # 测试有效记录数量
            valid_count_query = """
            SELECT COUNT(*) as total
            FROM rule_sql_history
            WHERE compare_id IS NOT NULL AND visit_type IS NOT NULL
            """
            valid_result = execute_query(valid_count_query)
            if valid_result:
                print(f"有效记录数量（有COMPARE_ID和VISIT_TYPE）: {valid_result[0]['total']}")

        return existing_fields

    except Exception as e:
        print(f"查询失败: {e}")
        return []

if __name__ == "__main__":
    print("开始测试导入规则改进功能...\n")

    # 测试表结构
    fields = test_table_structure()

    # 测试并发处理
    success_count, errors = test_concurrent_processing()

    # 测试保存验证
    verified_count = test_save_verification()

    # 测试性能对比
    seq_time, conc_time = test_performance_comparison()

    print("\n" + "="*50)
    print("测试总结:")
    print(f"1. 表结构检查: 发现 {len(fields)} 个字段")
    print(f"2. 并发处理测试: 成功 {success_count}/10, 错误 {len(errors)} 个")
    print(f"3. 保存验证测试: 成功 {verified_count}/5")
    print(f"4. 性能提升: {seq_time/conc_time:.2f}x")
    print("="*50)
