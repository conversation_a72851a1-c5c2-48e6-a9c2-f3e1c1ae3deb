{% extends "base.html" %}

{% block title %}SQL生成器 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-magic"></i> SQL生成器</h4>
{% endblock %}

{% block content %}
<!-- 模板选择区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-template"></i> SQL模板选择
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">模板分类</label>
                        <select class="form-select" id="templateCategory" onchange="loadTemplatesByCategory()">
                            <option value="basic">基础查询</option>
                            <option value="medical">医疗查询</option>
                            <option value="statistical">统计分析</option>
                            <option value="custom">自定义模板</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">选择模板</label>
                        <select class="form-select" id="templateSelect" onchange="loadTemplate()">
                            <option value="">请选择模板</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="button" class="btn btn-info" onclick="previewTemplate()">
                            <i class="fas fa-eye"></i> 预览模板
                        </button>
                        <button type="button" class="btn btn-success ms-2" onclick="useTemplate()">
                            <i class="fas fa-check"></i> 使用模板
                        </button>
                    </div>
                </div>
                
                <!-- 模板描述 -->
                <div class="mt-3">
                    <div id="templateDescription" class="alert alert-info d-none">
                        <strong>模板说明：</strong>
                        <span id="templateDescText"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 参数配置区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs"></i> 参数配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">数据库表</label>
                            <select class="form-select" id="tableSelect" onchange="loadTableColumns()">
                                <option value="">选择表</option>
                                {% for table in tables %}
                                <option value="{{ table }}">{{ table }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">查询字段</label>
                            <div id="columnsContainer" class="border rounded p-2" style="height: 120px; overflow-y: auto;">
                                <small class="text-muted">请先选择表</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">时间范围</label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="date" class="form-control" id="startDate" value="2024-01-01">
                                </div>
                                <div class="col-6">
                                    <input type="date" class="form-control" id="endDate" value="2024-12-31">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">条件参数</label>
                            <div id="parametersContainer">
                                <!-- 动态参数输入框 -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="generateSQL()">
                            <i class="fas fa-magic"></i> 生成SQL
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="resetParameters()">
                            <i class="fas fa-undo"></i> 重置参数
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SQL编辑器区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-code"></i> 生成的SQL语句
                </h6>
                <div>
                    <button type="button" class="btn btn-success btn-sm" onclick="executeSQL()">
                        <i class="fas fa-play"></i> 执行SQL
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> 导出Excel
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="copySQL()">
                        <i class="fas fa-copy"></i> 复制SQL
                    </button>
                </div>
            </div>
            <div class="card-body">
                <textarea id="sqlEditor" class="form-control" rows="12" placeholder="生成的SQL语句将显示在这里..."></textarea>
                
                <div class="mt-3">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="formatSQL" checked>
                                <label class="form-check-label" for="formatSQL">
                                    格式化SQL
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="addComments" checked>
                                <label class="form-check-label" for="addComments">
                                    添加注释
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="addLimits">
                                <label class="form-check-label" for="addLimits">
                                    添加行数限制
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 执行结果区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-table"></i> 执行结果
                </h6>
            </div>
            <div class="card-body">
                <div id="sqlResultContainer">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-play-circle fa-3x mb-3"></i>
                        <p>生成SQL后点击"执行SQL"查看结果</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模板预览模态框 -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye"></i> 模板预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6 id="previewTemplateName"></h6>
                    <p id="previewTemplateDesc" class="text-muted"></p>
                </div>
                <pre id="previewTemplateContent" class="bg-light p-3 rounded"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="useTemplateFromPreview()">使用此模板</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentTemplate = null;
let templateVariables = [];

// 页面加载完成后初始化
$(document).ready(function() {
    loadTemplatesByCategory();
});

// 根据分类加载模板
function loadTemplatesByCategory() {
    const category = $('#templateCategory').val();
    
    // 获取模板列表
    $.get('/database/api/sql_templates', function(response) {
        if (response.success) {
            displayTemplates(response.templates, category);
        } else {
            showAlert('获取模板失败: ' + response.error, 'danger');
        }
    });
}

// 显示模板列表
function displayTemplates(templates, category) {
    const select = $('#templateSelect');
    select.empty().append('<option value="">请选择模板</option>');
    
    // 根据分类筛选模板
    const categoryTemplates = templates[category] || [];
    
    categoryTemplates.forEach(template => {
        select.append(`<option value="${template.name}">${template.name}</option>`);
    });
}

// 加载模板
function loadTemplate() {
    const templateName = $('#templateSelect').val();
    const category = $('#templateCategory').val();
    
    if (!templateName) {
        $('#templateDescription').addClass('d-none');
        return;
    }
    
    // 获取模板内容
    $.get('/database/api/sql_templates', function(response) {
        if (response.success) {
            const categoryTemplates = response.templates[category] || [];
            const template = categoryTemplates.find(t => t.name === templateName);
            
            if (template) {
                currentTemplate = template;
                displayTemplateInfo(template);
                extractTemplateVariables(template.template);
            }
        }
    });
}

// 显示模板信息
function displayTemplateInfo(template) {
    $('#templateDescText').text(template.description);
    $('#templateDescription').removeClass('d-none');
}

// 提取模板变量
function extractTemplateVariables(templateContent) {
    const variables = templateContent.match(/\{(\w+)\}/g) || [];
    templateVariables = [...new Set(variables.map(v => v.replace(/[{}]/g, '')))];
    
    displayParameterInputs();
}

// 显示参数输入框
function displayParameterInputs() {
    const container = $('#parametersContainer');
    container.empty();
    
    templateVariables.forEach(variable => {
        const inputGroup = $(`
            <div class="mb-2">
                <label class="form-label">${variable}</label>
                <input type="text" class="form-control" id="param_${variable}" 
                       placeholder="请输入${variable}的值">
            </div>
        `);
        container.append(inputGroup);
    });
}

// 预览模板
function previewTemplate() {
    if (!currentTemplate) {
        showAlert('请先选择一个模板', 'warning');
        return;
    }
    
    $('#previewTemplateName').text(currentTemplate.name);
    $('#previewTemplateDesc').text(currentTemplate.description);
    $('#previewTemplateContent').text(currentTemplate.template);
    $('#templatePreviewModal').modal('show');
}

// 使用模板
function useTemplate() {
    if (!currentTemplate) {
        showAlert('请先选择一个模板', 'warning');
        return;
    }
    
    $('#sqlEditor').val(currentTemplate.template);
    showAlert('模板已加载到编辑器', 'success');
}

// 从预览使用模板
function useTemplateFromPreview() {
    useTemplate();
    $('#templatePreviewModal').modal('hide');
}

// 加载表字段
function loadTableColumns() {
    const tableName = $('#tableSelect').val();
    if (!tableName) {
        $('#columnsContainer').html('<small class="text-muted">请先选择表</small>');
        return;
    }
    
    $.get(`/database/api/table_columns/${tableName}`, function(response) {
        if (response.success) {
            displayTableColumns(response.columns);
        } else {
            $('#columnsContainer').html('<small class="text-danger">获取字段失败</small>');
        }
    });
}

// 显示表字段
function displayTableColumns(columns) {
    const container = $('#columnsContainer');
    container.empty();
    
    if (columns.length === 0) {
        container.html('<small class="text-muted">无字段信息</small>');
        return;
    }
    
    columns.forEach(column => {
        const columnDiv = $(`
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="col_${column.COLUMN_NAME}" 
                       value="${column.COLUMN_NAME}">
                <label class="form-check-label" for="col_${column.COLUMN_NAME}">
                    ${column.COLUMN_NAME} <small class="text-muted">(${column.DATA_TYPE})</small>
                </label>
            </div>
        `);
        container.append(columnDiv);
    });
}

// 生成SQL
function generateSQL() {
    let sql = $('#sqlEditor').val();
    
    if (!sql) {
        showAlert('请先选择模板或输入SQL语句', 'warning');
        return;
    }
    
    // 替换基本参数
    const tableName = $('#tableSelect').val();
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();
    
    // 获取选中的字段
    const selectedColumns = [];
    $('#columnsContainer input:checked').each(function() {
        selectedColumns.push($(this).val());
    });
    
    // 替换模板变量
    sql = sql.replace(/\{table_name\}/g, tableName || '表名');
    sql = sql.replace(/\{start_date\}/g, startDate);
    sql = sql.replace(/\{end_date\}/g, endDate);
    sql = sql.replace(/\{columns\}/g, selectedColumns.length > 0 ? selectedColumns.join(', ') : '*');
    
    // 替换自定义参数
    templateVariables.forEach(variable => {
        const value = $(`#param_${variable}`).val() || '';
        const regex = new RegExp(`\\{${variable}\\}`, 'g');
        sql = sql.replace(regex, value);
    });
    
    // 格式化SQL
    if ($('#formatSQL').is(':checked')) {
        sql = formatSQL(sql);
    }
    
    // 添加注释
    if ($('#addComments').is(':checked')) {
        const comment = `-- SQL生成器自动生成\n-- 生成时间: ${new Date().toLocaleString()}\n-- 表名: ${tableName}\n\n`;
        sql = comment + sql;
    }
    
    // 添加行数限制
    if ($('#addLimits').is(':checked')) {
        if (!sql.toLowerCase().includes('rownum') && !sql.toLowerCase().includes('limit')) {
            sql = sql.replace(/ORDER BY[^;]*$/i, '$& \nAND ROWNUM <= 1000');
            if (!sql.toLowerCase().includes('order by')) {
                sql += '\nAND ROWNUM <= 1000';
            }
        }
    }
    
    $('#sqlEditor').val(sql);
    showAlert('SQL生成成功', 'success');
}

// 格式化SQL
function formatSQL(sql) {
    return sql
        .replace(/\bSELECT\b/gi, 'SELECT')
        .replace(/\bFROM\b/gi, '\nFROM')
        .replace(/\bWHERE\b/gi, '\nWHERE')
        .replace(/\bAND\b/gi, '\n  AND')
        .replace(/\bOR\b/gi, '\n  OR')
        .replace(/\bORDER BY\b/gi, '\nORDER BY')
        .replace(/\bGROUP BY\b/gi, '\nGROUP BY')
        .replace(/\bHAVING\b/gi, '\nHAVING')
        .replace(/\bJOIN\b/gi, '\nJOIN')
        .replace(/\bLEFT JOIN\b/gi, '\nLEFT JOIN')
        .replace(/\bRIGHT JOIN\b/gi, '\nRIGHT JOIN')
        .replace(/\bINNER JOIN\b/gi, '\nINNER JOIN');
}

// 执行SQL
function executeSQL() {
    const sql = $('#sqlEditor').val().trim();
    if (!sql) {
        showAlert('请输入SQL语句', 'warning');
        return;
    }
    
    $('#sqlResultContainer').html(`
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">执行中...</span>
            </div>
            <p class="mt-2">正在执行SQL查询...</p>
        </div>
    `);
    
    $.ajax({
        url: '/database/api/execute_query',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            query: sql,
            export_format: 'json'
        }),
        success: function(response) {
            if (response.success) {
                displaySQLResult(response);
            } else {
                $('#sqlResultContainer').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        执行失败: ${response.error}
                    </div>
                `);
            }
        },
        error: function() {
            $('#sqlResultContainer').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    网络错误，请稍后重试
                </div>
            `);
        }
    });
}

// 显示SQL执行结果
function displaySQLResult(response) {
    const { data, columns, total, displayed, truncated } = response;
    
    if (data.length === 0) {
        $('#sqlResultContainer').html(`
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                查询结果为空
            </div>
        `);
        return;
    }
    
    // 生成表格
    let tableHtml = `
        <div class="mb-3">
            <span class="badge bg-primary">共 ${total} 条记录</span>
            ${truncated ? `<span class="badge bg-warning">显示前 ${displayed} 条</span>` : ''}
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
    `;
    
    columns.forEach(column => {
        tableHtml += `<th>${column}</th>`;
    });
    
    tableHtml += `
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.forEach(row => {
        tableHtml += '<tr>';
        columns.forEach(column => {
            const value = row[column] || '';
            tableHtml += `<td>${value}</td>`;
        });
        tableHtml += '</tr>';
    });
    
    tableHtml += `
                </tbody>
            </table>
        </div>
    `;
    
    $('#sqlResultContainer').html(tableHtml);
}

// 导出到Excel
function exportToExcel() {
    const sql = $('#sqlEditor').val().trim();
    if (!sql) {
        showAlert('请输入SQL语句', 'warning');
        return;
    }
    
    $.ajax({
        url: '/database/api/execute_query',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            query: sql,
            export_format: 'excel'
        }),
        success: function(response) {
            if (response.success) {
                window.location.href = response.export_url;
                showAlert('Excel文件导出成功', 'success');
            } else {
                showAlert('导出失败: ' + response.error, 'danger');
            }
        },
        error: function() {
            showAlert('网络错误，请稍后重试', 'danger');
        }
    });
}

// 复制SQL
function copySQL() {
    const sql = $('#sqlEditor').val();
    if (!sql) {
        showAlert('没有可复制的SQL语句', 'warning');
        return;
    }
    
    navigator.clipboard.writeText(sql).then(function() {
        showAlert('SQL已复制到剪贴板', 'success');
    }, function() {
        showAlert('复制失败，请手动复制', 'danger');
    });
}

// 重置参数
function resetParameters() {
    $('#templateSelect').val('');
    $('#tableSelect').val('');
    $('#startDate').val('2024-01-01');
    $('#endDate').val('2024-12-31');
    $('#sqlEditor').val('');
    $('#parametersContainer').empty();
    $('#columnsContainer').html('<small class="text-muted">请先选择表</small>');
    $('#templateDescription').addClass('d-none');
    
    currentTemplate = null;
    templateVariables = [];
    
    showAlert('参数已重置', 'info');
}

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
