#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理工具
用于查看、搜索和管理MICRA应用的日志文件
"""

import os
import sys
import glob
import argparse
from datetime import datetime, timedelta
import re


class LogManager:
    """日志管理器"""
    
    def __init__(self, logs_dir='logs'):
        self.logs_dir = logs_dir
        if not os.path.exists(self.logs_dir):
            print(f"日志目录不存在: {self.logs_dir}")
            sys.exit(1)
    
    def list_log_files(self):
        """列出所有日志文件"""
        log_files = glob.glob(os.path.join(self.logs_dir, 'micra_*.log*'))
        log_files.sort(reverse=True)  # 按时间倒序
        
        print("可用的日志文件:")
        print("-" * 60)
        for log_file in log_files:
            file_name = os.path.basename(log_file)
            file_size = os.path.getsize(log_file)
            file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
            
            size_str = self._format_file_size(file_size)
            print(f"{file_name:<25} {size_str:>10} {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return log_files
    
    def _format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes}B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes/1024:.1f}KB"
        else:
            return f"{size_bytes/(1024*1024):.1f}MB"
    
    def view_log(self, date=None, lines=50, level=None, search=None, follow=False):
        """查看日志"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        log_file = os.path.join(self.logs_dir, f'micra_{date}.log')
        
        if not os.path.exists(log_file):
            print(f"日志文件不存在: {log_file}")
            available_files = self.list_log_files()
            if available_files:
                print("\n请选择一个可用的日期:")
                for file_path in available_files[:5]:  # 显示最近5个文件
                    file_name = os.path.basename(file_path)
                    if 'micra_' in file_name:
                        date_part = file_name.replace('micra_', '').replace('.log', '').split('.')[0]
                        print(f"  {date_part}")
            return
        
        print(f"查看日志文件: {log_file}")
        print("=" * 80)
        
        if follow:
            self._follow_log(log_file, level, search)
        else:
            self._view_log_static(log_file, lines, level, search)
    
    def _view_log_static(self, log_file, lines, level, search):
        """查看静态日志"""
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_lines = f.readlines()
            
            # 过滤日志级别
            if level:
                log_lines = [line for line in log_lines if f' {level.upper()} ' in line]
            
            # 搜索关键词
            if search:
                log_lines = [line for line in log_lines if search.lower() in line.lower()]
            
            # 显示最后N行
            if lines > 0:
                log_lines = log_lines[-lines:]
            
            for line in log_lines:
                print(line.rstrip())
                
        except Exception as e:
            print(f"读取日志文件失败: {e}")
    
    def _follow_log(self, log_file, level, search):
        """实时跟踪日志"""
        print("实时跟踪日志 (按Ctrl+C退出)...")
        print("-" * 80)
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                # 移动到文件末尾
                f.seek(0, 2)
                
                while True:
                    line = f.readline()
                    if line:
                        # 过滤条件
                        if level and f' {level.upper()} ' not in line:
                            continue
                        if search and search.lower() not in line.lower():
                            continue
                        
                        print(line.rstrip())
                    else:
                        import time
                        time.sleep(0.1)
                        
        except KeyboardInterrupt:
            print("\n停止跟踪日志")
        except Exception as e:
            print(f"跟踪日志失败: {e}")
    
    def search_logs(self, keyword, date_range=7, level=None):
        """搜索日志"""
        print(f"搜索关键词: {keyword}")
        if level:
            print(f"日志级别: {level.upper()}")
        print(f"搜索范围: 最近{date_range}天")
        print("=" * 80)
        
        # 获取日期范围内的日志文件
        end_date = datetime.now()
        start_date = end_date - timedelta(days=date_range)
        
        found_count = 0
        
        for i in range(date_range + 1):
            current_date = start_date + timedelta(days=i)
            date_str = current_date.strftime('%Y-%m-%d')
            log_file = os.path.join(self.logs_dir, f'micra_{date_str}.log')
            
            if os.path.exists(log_file):
                file_found_count = self._search_in_file(log_file, keyword, level)
                found_count += file_found_count
        
        print(f"\n搜索完成，共找到 {found_count} 条匹配记录")
    
    def _search_in_file(self, log_file, keyword, level):
        """在单个文件中搜索"""
        found_count = 0
        file_name = os.path.basename(log_file)
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    # 过滤日志级别
                    if level and f' {level.upper()} ' not in line:
                        continue
                    
                    # 搜索关键词
                    if keyword.lower() in line.lower():
                        if found_count == 0:
                            print(f"\n--- {file_name} ---")
                        
                        # 高亮显示关键词
                        highlighted_line = re.sub(
                            f'({re.escape(keyword)})', 
                            r'\033[93m\1\033[0m', 
                            line.rstrip(), 
                            flags=re.IGNORECASE
                        )
                        print(f"{line_num:>6}: {highlighted_line}")
                        found_count += 1
                        
        except Exception as e:
            print(f"搜索文件 {file_name} 失败: {e}")
        
        return found_count
    
    def clean_old_logs(self, days=30):
        """清理旧日志文件"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        log_files = glob.glob(os.path.join(self.logs_dir, 'micra_*.log*'))
        deleted_count = 0
        deleted_size = 0
        
        print(f"清理 {days} 天前的日志文件...")
        print("-" * 60)
        
        for log_file in log_files:
            file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
            if file_time < cutoff_date:
                file_size = os.path.getsize(log_file)
                file_name = os.path.basename(log_file)
                
                try:
                    os.remove(log_file)
                    print(f"删除: {file_name} ({self._format_file_size(file_size)})")
                    deleted_count += 1
                    deleted_size += file_size
                except Exception as e:
                    print(f"删除失败 {file_name}: {e}")
        
        if deleted_count > 0:
            print(f"\n清理完成: 删除了 {deleted_count} 个文件，释放空间 {self._format_file_size(deleted_size)}")
        else:
            print("没有需要清理的文件")
    
    def get_log_stats(self, date=None):
        """获取日志统计信息"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        log_file = os.path.join(self.logs_dir, f'micra_{date}.log')
        
        if not os.path.exists(log_file):
            print(f"日志文件不存在: {log_file}")
            return
        
        stats = {
            'DEBUG': 0,
            'INFO': 0,
            'WARNING': 0,
            'ERROR': 0,
            'CRITICAL': 0,
            'total_lines': 0
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    stats['total_lines'] += 1
                    for level in stats.keys():
                        if level != 'total_lines' and f' {level} ' in line:
                            stats[level] += 1
                            break
            
            print(f"日志统计 ({date}):")
            print("-" * 40)
            print(f"总行数:    {stats['total_lines']:>8}")
            print(f"DEBUG:     {stats['DEBUG']:>8}")
            print(f"INFO:      {stats['INFO']:>8}")
            print(f"WARNING:   {stats['WARNING']:>8}")
            print(f"ERROR:     {stats['ERROR']:>8}")
            print(f"CRITICAL:  {stats['CRITICAL']:>8}")
            
        except Exception as e:
            print(f"统计日志失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MICRA日志管理工具')
    parser.add_argument('--logs-dir', default='logs', help='日志目录路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 列出日志文件
    subparsers.add_parser('list', help='列出所有日志文件')
    
    # 查看日志
    view_parser = subparsers.add_parser('view', help='查看日志')
    view_parser.add_argument('--date', help='日期 (YYYY-MM-DD)')
    view_parser.add_argument('--lines', type=int, default=50, help='显示行数')
    view_parser.add_argument('--level', choices=['debug', 'info', 'warning', 'error', 'critical'], help='日志级别')
    view_parser.add_argument('--search', help='搜索关键词')
    view_parser.add_argument('--follow', '-f', action='store_true', help='实时跟踪')
    
    # 搜索日志
    search_parser = subparsers.add_parser('search', help='搜索日志')
    search_parser.add_argument('keyword', help='搜索关键词')
    search_parser.add_argument('--days', type=int, default=7, help='搜索天数')
    search_parser.add_argument('--level', choices=['debug', 'info', 'warning', 'error', 'critical'], help='日志级别')
    
    # 清理日志
    clean_parser = subparsers.add_parser('clean', help='清理旧日志')
    clean_parser.add_argument('--days', type=int, default=30, help='保留天数')
    
    # 统计信息
    stats_parser = subparsers.add_parser('stats', help='日志统计')
    stats_parser.add_argument('--date', help='日期 (YYYY-MM-DD)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    log_manager = LogManager(args.logs_dir)
    
    if args.command == 'list':
        log_manager.list_log_files()
    elif args.command == 'view':
        log_manager.view_log(args.date, args.lines, args.level, args.search, args.follow)
    elif args.command == 'search':
        log_manager.search_logs(args.keyword, args.days, args.level)
    elif args.command == 'clean':
        log_manager.clean_old_logs(args.days)
    elif args.command == 'stats':
        log_manager.get_log_stats(args.date)


if __name__ == '__main__':
    main()
