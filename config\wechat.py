"""
微信配置文件
"""
import os

class WeChatConfig:
    """微信配置类"""
    
    # 微信公众号配置
    WECHAT_APP_ID = os.environ.get('WECHAT_APP_ID', 'your_app_id')
    WECHAT_APP_SECRET = os.environ.get('WECHAT_APP_SECRET', 'your_app_secret')
    
    # 微信授权回调地址
    WECHAT_REDIRECT_URI = os.environ.get('WECHAT_REDIRECT_URI', 'http://localhost:5002/auth/wechat/callback')
    
    # 微信API地址
    WECHAT_OAUTH_URL = 'https://open.weixin.qq.com/connect/oauth2/authorize'
    WECHAT_ACCESS_TOKEN_URL = 'https://api.weixin.qq.com/sns/oauth2/access_token'
    WECHAT_USER_INFO_URL = 'https://api.weixin.qq.com/sns/userinfo'
    
    # 微信授权scope
    WECHAT_SCOPE = 'snsapi_userinfo'  # 获取用户基本信息
    
    @classmethod
    def get_oauth_url(cls, state=None):
        """获取微信OAuth授权URL"""
        import urllib.parse
        
        params = {
            'appid': cls.WECHAT_APP_ID,
            'redirect_uri': cls.WECHAT_REDIRECT_URI,
            'response_type': 'code',
            'scope': cls.WECHAT_SCOPE,
            'state': state or 'login'
        }
        
        query_string = urllib.parse.urlencode(params)
        return f"{cls.WECHAT_OAUTH_URL}?{query_string}#wechat_redirect"
    
    @classmethod
    def is_configured(cls):
        """检查微信配置是否完整"""
        return (cls.WECHAT_APP_ID != 'your_app_id' and 
                cls.WECHAT_APP_SECRET != 'your_app_secret' and
                cls.WECHAT_APP_ID and cls.WECHAT_APP_SECRET)
