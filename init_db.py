#!/usr/bin/env python
"""初始化数据库脚本"""

import os
from app import create_app, db
from app.models.user import User
from app.models.role import Role
from app.models.permission import Permission
from app.models.audit import AuditLog

def init_database():
    """初始化数据库"""
    app = create_app('development')

    with app.app_context():
        try:
            # 删除所有表（如果存在）
            print("正在删除现有表...")
            db.drop_all()
            print("现有表删除成功")

            # 使用SQLAlchemy创建表
            print("正在创建数据库表...")
            db.create_all()
            print("数据库表创建成功")

            # 创建基础权限数据
            create_permissions()
            print("权限数据创建成功")

            # 创建默认角色
            create_roles()
            print("角色数据创建成功")

            # 创建管理员用户
            create_admin_user()
            print("管理员用户创建成功")

        except Exception as e:
            print(f"数据库初始化失败: {e}")
            import traceback
            traceback.print_exc()

def create_oracle_tables():
    """执行Oracle建表脚本"""
    import os
    sql_file = os.path.join(os.path.dirname(__file__), 'create_oracle_tables.sql')

    if os.path.exists(sql_file):
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # 分割SQL语句（以分号和换行符分割）
        sql_statements = []
        current_statement = ""

        for line in sql_content.split('\n'):
            line = line.strip()
            if line and not line.startswith('--'):
                current_statement += line + "\n"
                if line.endswith(';') or line == '/':
                    if current_statement.strip():
                        sql_statements.append(current_statement.strip())
                    current_statement = ""

        # 执行SQL语句
        for sql in sql_statements:
            if sql and sql != '/':
                try:
                    db.session.execute(db.text(sql))
                    db.session.commit()
                except Exception as e:
                    print(f"执行SQL失败: {sql[:50]}... 错误: {e}")
                    db.session.rollback()
    else:
        print("Oracle建表脚本不存在，使用SQLAlchemy创建表")
        db.create_all()

def create_permissions():
    """创建基础权限数据"""
    permissions_data = [
        # 系统管理
        {'id': 1, 'name': '系统管理', 'code': 'system', 'description': '系统管理模块', 'module': 'system', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 1},
        {'id': 2, 'name': '用户管理', 'code': 'system.user', 'description': '用户管理', 'module': 'system', 'resource_type': 'menu', 'parent_id': 1, 'sort_order': 1},
        {'id': 3, 'name': '角色管理', 'code': 'system.role', 'description': '角色管理', 'module': 'system', 'resource_type': 'menu', 'parent_id': 1, 'sort_order': 2},
        {'id': 4, 'name': '权限管理', 'code': 'system.permission', 'description': '权限管理', 'module': 'system', 'resource_type': 'menu', 'parent_id': 1, 'sort_order': 3},
        {'id': 5, 'name': '审计日志', 'code': 'system.audit', 'description': '审计日志', 'module': 'system', 'resource_type': 'menu', 'parent_id': 1, 'sort_order': 4},
        
        # 用户管理权限
        {'id': 10, 'name': '查看用户', 'code': 'system.user.view', 'description': '查看用户列表', 'module': 'system', 'resource_type': 'page', 'parent_id': 2, 'sort_order': 1},
        {'id': 11, 'name': '创建用户', 'code': 'system.user.create', 'description': '创建新用户', 'module': 'system', 'resource_type': 'button', 'parent_id': 2, 'sort_order': 2},
        {'id': 12, 'name': '编辑用户', 'code': 'system.user.edit', 'description': '编辑用户信息', 'module': 'system', 'resource_type': 'button', 'parent_id': 2, 'sort_order': 3},
        {'id': 13, 'name': '删除用户', 'code': 'system.user.delete', 'description': '删除用户', 'module': 'system', 'resource_type': 'button', 'parent_id': 2, 'sort_order': 4},
        {'id': 14, 'name': '重置密码', 'code': 'system.user.reset_password', 'description': '重置用户密码', 'module': 'system', 'resource_type': 'button', 'parent_id': 2, 'sort_order': 5},
        
        # 角色管理权限
        {'id': 15, 'name': '查看角色', 'code': 'system.role.view', 'description': '查看角色列表', 'module': 'system', 'resource_type': 'page', 'parent_id': 3, 'sort_order': 1},
        {'id': 16, 'name': '查看权限', 'code': 'system.permission.view', 'description': '查看权限列表', 'module': 'system', 'resource_type': 'page', 'parent_id': 4, 'sort_order': 1},
        {'id': 17, 'name': '查看审计日志', 'code': 'system.audit.view', 'description': '查看审计日志', 'module': 'system', 'resource_type': 'page', 'parent_id': 5, 'sort_order': 1},
        
        # 规则管理
        {'id': 20, 'name': '规则管理', 'code': 'rules', 'description': '规则管理模块', 'module': 'rules', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 2},
        {'id': 21, 'name': '飞检规则知识库', 'code': 'rules.knowledge_base', 'description': '飞检规则知识库', 'module': 'rules', 'resource_type': 'menu', 'parent_id': 20, 'sort_order': 1},
        {'id': 22, 'name': '规则SQL生成器', 'code': 'rules.sql_generator', 'description': '规则SQL生成器', 'module': 'rules', 'resource_type': 'menu', 'parent_id': 20, 'sort_order': 2},
        {'id': 23, 'name': '系统规则语句', 'code': 'rules.system_rules', 'description': '系统规则语句', 'module': 'rules', 'resource_type': 'menu', 'parent_id': 20, 'sort_order': 3},
        
        # 数据库工具
        {'id': 30, 'name': '数据库工具', 'code': 'database', 'description': '数据库工具模块', 'module': 'database', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 3},
        {'id': 31, 'name': 'SQL生成器', 'code': 'database.sql_generator', 'description': 'SQL生成器', 'module': 'database', 'resource_type': 'menu', 'parent_id': 30, 'sort_order': 1},
        {'id': 32, 'name': '数据库查询生成Excel', 'code': 'database.query', 'description': '数据库查询生成Excel', 'module': 'database', 'resource_type': 'menu', 'parent_id': 30, 'sort_order': 2},
        {'id': 33, 'name': '批量SQL查询生成Excel', 'code': 'database.batch_query', 'description': '批量SQL查询生成Excel', 'module': 'database', 'resource_type': 'menu', 'parent_id': 30, 'sort_order': 3},
        {'id': 34, 'name': 'SQL性能测试', 'code': 'database.performance', 'description': 'SQL性能测试', 'module': 'database', 'resource_type': 'menu', 'parent_id': 30, 'sort_order': 4},
        
        # Excel工具
        {'id': 40, 'name': 'Excel工具', 'code': 'excel', 'description': 'Excel工具模块', 'module': 'excel', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 4},
        {'id': 41, 'name': 'Excel文件拆分', 'code': 'excel.splitter', 'description': 'Excel文件拆分', 'module': 'excel', 'resource_type': 'menu', 'parent_id': 40, 'sort_order': 1},
        {'id': 42, 'name': 'Excel内容删除', 'code': 'excel.delete', 'description': 'Excel内容删除', 'module': 'excel', 'resource_type': 'menu', 'parent_id': 40, 'sort_order': 2},
        {'id': 43, 'name': 'Excel比对工具', 'code': 'excel.compare', 'description': 'Excel比对工具', 'module': 'excel', 'resource_type': 'menu', 'parent_id': 40, 'sort_order': 3},
        {'id': 44, 'name': 'Excel转SQL工具', 'code': 'excel.to_sql', 'description': 'Excel转SQL工具', 'module': 'excel', 'resource_type': 'menu', 'parent_id': 40, 'sort_order': 4},
        
        # 数据处理工具
        {'id': 50, 'name': '数据处理', 'code': 'data', 'description': '数据处理模块', 'module': 'data', 'resource_type': 'menu', 'parent_id': None, 'sort_order': 5},
        {'id': 51, 'name': '查找重复文件', 'code': 'data.find_duplicates', 'description': '查找重复文件', 'module': 'data', 'resource_type': 'menu', 'parent_id': 50, 'sort_order': 1},
        {'id': 52, 'name': '数据校验', 'code': 'data.validator', 'description': '数据校验', 'module': 'data', 'resource_type': 'menu', 'parent_id': 50, 'sort_order': 2},
        {'id': 53, 'name': '数据标准化', 'code': 'data.standardization', 'description': '数据标准化', 'module': 'data', 'resource_type': 'menu', 'parent_id': 50, 'sort_order': 3},
    ]
    
    for perm_data in permissions_data:
        permission = Permission.query.filter_by(code=perm_data['code']).first()
        if not permission:
            permission = Permission(**perm_data)
            db.session.add(permission)
    
    db.session.commit()

def create_roles():
    """创建默认角色"""
    roles_data = [
        {'name': '超级管理员', 'description': '系统最高权限'},
        {'name': '系统管理员', 'description': '用户和权限管理'},
        {'name': '规则管理员', 'description': '规则相关功能'},
        {'name': '数据分析师', 'description': '数据处理和分析'},
        {'name': '普通用户', 'description': '基本功能使用'},
    ]
    
    for role_data in roles_data:
        role = Role.query.filter_by(name=role_data['name']).first()
        if not role:
            role = Role(**role_data)
            db.session.add(role)
    
    db.session.commit()

def create_admin_user():
    """创建管理员用户"""
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            real_name='系统管理员',
            is_admin=True
        )
        admin.set_password('Admin123!')
        db.session.add(admin)
        db.session.commit()
        print('管理员用户已创建: admin / Admin123!')

if __name__ == '__main__':
    init_database()
