"""Excel工具服务类"""

import os
import pandas as pd
import numpy as np
import tempfile
import zipfile
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from werkzeug.datastructures import FileStorage
from app.utils.file_manager import create_temp_file, create_temp_dir, sanitize_filename
from app.models.audit import AuditLog

class ExcelSplitService:
    """Excel文件拆分服务类"""
    
    @staticmethod
    def split_excel_by_column(file_path: str, column_name: str, output_dir: str = None) -> Tuple[List[Dict], str]:
        """按指定列拆分Excel文件"""
        try:
            if not output_dir:
                output_dir = create_temp_dir()
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            if column_name not in df.columns:
                raise ValueError(f"列 '{column_name}' 不存在于Excel文件中")
            
            # 获取唯一值
            unique_values = df[column_name].dropna().unique()
            
            results = []
            for value in unique_values:
                # 筛选数据
                subset = df[df[column_name] == value]
                
                # 生成文件名
                safe_value = str(value).replace('/', '_').replace('\\', '_')
                filename = f"{column_name}_{safe_value}.xlsx"
                filename = sanitize_filename(filename)
                
                output_file = os.path.join(output_dir, filename)
                
                # 保存文件
                subset.to_excel(output_file, index=False)
                
                results.append({
                    'value': str(value),
                    'filename': filename,
                    'file_path': output_file,
                    'rows': len(subset),
                    'status': 'success'
                })
                
                logging.info(f"拆分文件已保存: {filename}, 行数: {len(subset)}")
            
            return results, output_dir
            
        except Exception as e:
            logging.error(f"Excel拆分失败: {str(e)}")
            raise

    @staticmethod
    def split_excel_by_rows(file_path: str, rows_per_file: int, output_dir: str = None) -> Tuple[List[Dict], str]:
        """按行数拆分Excel文件"""
        try:
            if not output_dir:
                output_dir = create_temp_dir()
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            total_rows = len(df)
            
            if rows_per_file <= 0:
                raise ValueError("每个文件的行数必须大于0")
            
            results = []
            file_count = 0
            
            for start_row in range(0, total_rows, rows_per_file):
                end_row = min(start_row + rows_per_file, total_rows)
                subset = df.iloc[start_row:end_row]
                
                file_count += 1
                filename = f"split_part_{file_count:03d}.xlsx"
                output_file = os.path.join(output_dir, filename)
                
                # 保存文件
                subset.to_excel(output_file, index=False)
                
                results.append({
                    'part': file_count,
                    'filename': filename,
                    'file_path': output_file,
                    'rows': len(subset),
                    'start_row': start_row + 1,
                    'end_row': end_row,
                    'status': 'success'
                })
                
                logging.info(f"拆分文件已保存: {filename}, 行数: {len(subset)}")
            
            return results, output_dir
            
        except Exception as e:
            logging.error(f"Excel按行数拆分失败: {str(e)}")
            raise

class ExcelCompareService:
    """Excel文件比对服务类"""
    
    @staticmethod
    def compare_excel_files(file1_path: str, file2_path: str, key_columns: List[str] = None) -> Dict[str, Any]:
        """比对两个Excel文件"""
        try:
            # 读取Excel文件
            df1 = pd.read_excel(file1_path)
            df2 = pd.read_excel(file2_path)
            
            # 基本信息
            result = {
                'file1_info': {
                    'rows': len(df1),
                    'columns': len(df1.columns),
                    'column_names': df1.columns.tolist()
                },
                'file2_info': {
                    'rows': len(df2),
                    'columns': len(df2.columns),
                    'column_names': df2.columns.tolist()
                },
                'comparison': {}
            }
            
            # 列名比对
            common_columns = set(df1.columns) & set(df2.columns)
            only_in_file1 = set(df1.columns) - set(df2.columns)
            only_in_file2 = set(df2.columns) - set(df1.columns)
            
            result['comparison']['columns'] = {
                'common': list(common_columns),
                'only_in_file1': list(only_in_file1),
                'only_in_file2': list(only_in_file2)
            }
            
            # 如果指定了关键列，进行数据比对
            if key_columns and all(col in common_columns for col in key_columns):
                data_comparison = ExcelCompareService._compare_data(df1, df2, key_columns)
                result['comparison']['data'] = data_comparison
            
            return result
            
        except Exception as e:
            logging.error(f"Excel文件比对失败: {str(e)}")
            raise

    @staticmethod
    def _compare_data(df1: pd.DataFrame, df2: pd.DataFrame, key_columns: List[str]) -> Dict[str, Any]:
        """比对数据内容"""
        try:
            # 创建关键字段组合
            df1['_key'] = df1[key_columns].astype(str).agg('|'.join, axis=1)
            df2['_key'] = df2[key_columns].astype(str).agg('|'.join, axis=1)
            
            keys1 = set(df1['_key'])
            keys2 = set(df2['_key'])
            
            # 找出差异
            common_keys = keys1 & keys2
            only_in_file1 = keys1 - keys2
            only_in_file2 = keys2 - keys1
            
            result = {
                'total_keys_file1': len(keys1),
                'total_keys_file2': len(keys2),
                'common_keys': len(common_keys),
                'only_in_file1': len(only_in_file1),
                'only_in_file2': len(only_in_file2),
                'only_in_file1_sample': list(only_in_file1)[:10],  # 前10个示例
                'only_in_file2_sample': list(only_in_file2)[:10]   # 前10个示例
            }
            
            # 清理临时列
            df1.drop('_key', axis=1, inplace=True)
            df2.drop('_key', axis=1, inplace=True)
            
            return result
            
        except Exception as e:
            logging.error(f"数据比对失败: {str(e)}")
            raise

class ExcelMergeService:
    """Excel文件合并服务类"""
    
    @staticmethod
    def merge_excel_files(file_paths: List[str], output_path: str = None, merge_type: str = 'vertical') -> str:
        """合并多个Excel文件"""
        try:
            if not output_path:
                output_path = create_temp_file(suffix='.xlsx')
            
            dataframes = []
            
            # 读取所有Excel文件
            for file_path in file_paths:
                df = pd.read_excel(file_path)
                # 添加来源文件信息
                df['_source_file'] = os.path.basename(file_path)
                dataframes.append(df)
            
            if merge_type == 'vertical':
                # 垂直合并（追加行）
                merged_df = pd.concat(dataframes, ignore_index=True, sort=False)
            elif merge_type == 'horizontal':
                # 水平合并（追加列）
                merged_df = pd.concat(dataframes, axis=1)
            else:
                raise ValueError(f"不支持的合并类型: {merge_type}")
            
            # 保存合并后的文件
            merged_df.to_excel(output_path, index=False)
            
            logging.info(f"Excel文件合并完成: {output_path}, 总行数: {len(merged_df)}")
            
            return output_path
            
        except Exception as e:
            logging.error(f"Excel文件合并失败: {str(e)}")
            raise

class ExcelConvertService:
    """Excel格式转换服务类"""
    
    @staticmethod
    def excel_to_csv(file_path: str, output_dir: str = None, encoding: str = 'utf-8') -> List[str]:
        """Excel转CSV"""
        try:
            if not output_dir:
                output_dir = create_temp_dir()
            
            # 读取Excel文件（可能有多个sheet）
            excel_file = pd.ExcelFile(file_path)
            output_files = []
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                # 生成CSV文件名
                base_name = os.path.splitext(os.path.basename(file_path))[0]
                csv_filename = f"{base_name}_{sheet_name}.csv"
                csv_filename = sanitize_filename(csv_filename)
                csv_path = os.path.join(output_dir, csv_filename)
                
                # 保存CSV文件
                df.to_csv(csv_path, index=False, encoding=encoding)
                output_files.append(csv_path)
                
                logging.info(f"CSV文件已保存: {csv_filename}")
            
            return output_files
            
        except Exception as e:
            logging.error(f"Excel转CSV失败: {str(e)}")
            raise

    @staticmethod
    def csv_to_excel(csv_paths: List[str], output_path: str = None) -> str:
        """CSV转Excel"""
        try:
            if not output_path:
                output_path = create_temp_file(suffix='.xlsx')
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                for csv_path in csv_paths:
                    # 读取CSV文件
                    df = pd.read_csv(csv_path)
                    
                    # 生成sheet名称
                    sheet_name = os.path.splitext(os.path.basename(csv_path))[0]
                    sheet_name = sheet_name[:31]  # Excel sheet名称限制
                    
                    # 写入Excel
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    logging.info(f"CSV已转换为Excel sheet: {sheet_name}")
            
            return output_path
            
        except Exception as e:
            logging.error(f"CSV转Excel失败: {str(e)}")
            raise

class ExcelValidationService:
    """Excel数据验证服务类"""
    
    @staticmethod
    def validate_excel_structure(file_path: str, required_columns: List[str] = None) -> Dict[str, Any]:
        """验证Excel文件结构"""
        try:
            df = pd.read_excel(file_path)
            
            result = {
                'file_info': {
                    'rows': len(df),
                    'columns': len(df.columns),
                    'column_names': df.columns.tolist(),
                    'file_size': os.path.getsize(file_path)
                },
                'validation': {
                    'structure_valid': True,
                    'missing_columns': [],
                    'extra_columns': [],
                    'issues': []
                }
            }
            
            # 检查必需列
            if required_columns:
                missing_columns = set(required_columns) - set(df.columns)
                extra_columns = set(df.columns) - set(required_columns)
                
                result['validation']['missing_columns'] = list(missing_columns)
                result['validation']['extra_columns'] = list(extra_columns)
                
                if missing_columns:
                    result['validation']['structure_valid'] = False
                    result['validation']['issues'].append(f"缺少必需列: {', '.join(missing_columns)}")
            
            # 检查空值
            null_counts = df.isnull().sum()
            if null_counts.any():
                null_info = null_counts[null_counts > 0].to_dict()
                result['validation']['null_values'] = null_info
                result['validation']['issues'].append(f"存在空值的列: {', '.join(null_info.keys())}")
            
            # 检查重复行
            duplicate_count = df.duplicated().sum()
            if duplicate_count > 0:
                result['validation']['duplicate_rows'] = duplicate_count
                result['validation']['issues'].append(f"存在 {duplicate_count} 行重复数据")
            
            return result
            
        except Exception as e:
            logging.error(f"Excel结构验证失败: {str(e)}")
            raise

    @staticmethod
    def clean_excel_data(file_path: str, output_path: str = None, 
                        remove_duplicates: bool = True, 
                        fill_na: bool = True, 
                        na_value: str = '') -> str:
        """清理Excel数据"""
        try:
            if not output_path:
                output_path = create_temp_file(suffix='.xlsx')
            
            df = pd.read_excel(file_path)
            original_rows = len(df)
            
            # 移除重复行
            if remove_duplicates:
                df = df.drop_duplicates()
                logging.info(f"移除重复行: {original_rows - len(df)} 行")
            
            # 填充空值
            if fill_na:
                df = df.fillna(na_value)
                logging.info("空值已填充")
            
            # 清理字符串列的前后空格
            string_columns = df.select_dtypes(include=['object']).columns
            df[string_columns] = df[string_columns].apply(lambda x: x.str.strip() if x.dtype == "object" else x)
            
            # 保存清理后的文件
            df.to_excel(output_path, index=False)
            
            logging.info(f"数据清理完成: {output_path}, 最终行数: {len(df)}")
            
            return output_path
            
        except Exception as e:
            logging.error(f"Excel数据清理失败: {str(e)}")
            raise
