#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试审计日志功能
"""

import requests
import json
from datetime import datetime

def test_audit_logs():
    """测试审计日志功能"""
    base_url = "http://localhost:5002"
    
    print("=== 审计日志功能测试 ===")
    
    # 创建session
    session = requests.Session()
    
    try:
        # 1. 测试应用连接
        print("1. 测试应用连接...")
        response = session.get(f"{base_url}/", timeout=5)
        print(f"   应用状态: {response.status_code}")
        
        if response.status_code != 200:
            print("   应用未正常运行，请检查")
            return
        
        # 2. 测试登录页面
        print("2. 获取登录页面...")
        login_page = session.get(f"{base_url}/auth/login", timeout=5)
        print(f"   登录页面状态: {login_page.status_code}")
        
        # 3. 执行登录
        print("3. 执行管理员登录...")
        login_data = {
            'username': 'admin',
            'password': 'Admin123!'
        }
        
        login_response = session.post(
            f"{base_url}/auth/login",
            data=login_data,
            allow_redirects=False,
            timeout=5
        )
        print(f"   登录响应状态: {login_response.status_code}")
        
        if login_response.status_code in [302, 200]:
            print("   ✅ 登录成功")
        else:
            print("   ❌ 登录失败")
            return
        
        # 4. 访问审计日志页面
        print("4. 访问审计日志页面...")
        audit_page = session.get(f"{base_url}/admin/audit_logs", timeout=5)
        print(f"   审计日志页面状态: {audit_page.status_code}")
        
        if audit_page.status_code == 200:
            print("   ✅ 审计日志页面访问成功")
        else:
            print("   ❌ 审计日志页面访问失败")
            return
        
        # 5. 测试审计日志API
        print("5. 测试审计日志API...")
        api_response = session.get(f"{base_url}/admin/api/audit_logs", timeout=5)
        print(f"   API响应状态: {api_response.status_code}")
        
        if api_response.status_code == 200:
            try:
                data = api_response.json()
                print("   ✅ 审计日志API正常")
                print(f"   总日志数: {data.get('total', 0)}")
                print(f"   当前页日志数: {len(data.get('logs', []))}")
                
                # 显示最近的几条日志
                logs = data.get('logs', [])
                if logs:
                    print("   最近的审计日志:")
                    for i, log in enumerate(logs[:3]):
                        print(f"     {i+1}. {log.get('timestamp', 'N/A')} - {log.get('action', 'N/A')} - {log.get('resource', 'N/A')}")
                else:
                    print("   暂无审计日志数据")
                    
            except json.JSONDecodeError:
                print("   ❌ API返回数据格式错误")
        else:
            print("   ❌ 审计日志API访问失败")
        
        # 6. 测试统计信息API
        print("6. 测试统计信息API...")
        stats_response = session.get(f"{base_url}/admin/api/audit_logs/statistics", timeout=5)
        print(f"   统计API响应状态: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            try:
                stats = stats_response.json()
                print("   ✅ 统计信息API正常")
                print(f"   总日志数: {stats.get('total_logs', 0)}")
                print(f"   今日日志: {stats.get('today_logs', 0)}")
                print(f"   活跃用户: {stats.get('active_users', 0)}")
                print(f"   成功率: {stats.get('success_rate', 0)}%")
            except json.JSONDecodeError:
                print("   ❌ 统计API返回数据格式错误")
        else:
            print("   ❌ 统计信息API访问失败")
        
        # 7. 测试单个日志详情API
        print("7. 测试日志详情API...")
        # 先获取一个日志ID
        if api_response.status_code == 200:
            try:
                data = api_response.json()
                logs = data.get('logs', [])
                if logs:
                    log_id = logs[0].get('id')
                    if log_id:
                        detail_response = session.get(f"{base_url}/admin/api/audit_logs/{log_id}", timeout=5)
                        print(f"   日志详情API状态: {detail_response.status_code}")
                        
                        if detail_response.status_code == 200:
                            detail = detail_response.json()
                            print("   ✅ 日志详情API正常")
                            print(f"   日志详情: {detail.get('action', 'N/A')} - {detail.get('details', 'N/A')}")
                        else:
                            print("   ❌ 日志详情API访问失败")
                    else:
                        print("   跳过日志详情测试（无有效日志ID）")
                else:
                    print("   跳过日志详情测试（无日志数据）")
            except:
                print("   跳过日志详情测试（数据解析失败）")
        
        print("\n=== 测试完成 ===")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
    finally:
        session.close()


def create_test_audit_logs():
    """创建测试审计日志数据"""
    print("\n=== 创建测试审计日志数据 ===")
    
    try:
        from app import create_app, db
        from app.models.audit import AuditLog
        from app.models.user import User
        from datetime import datetime, timedelta
        
        app = create_app()
        with app.app_context():
            # 检查现有日志数量
            count = AuditLog.query.count()
            print(f"当前审计日志数量: {count}")
            
            # 获取admin用户
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ 未找到admin用户")
                return
            
            print(f"找到admin用户: {admin_user.username}")
            
            # 创建测试日志
            test_logs = [
                {
                    'action': 'login',
                    'resource': 'auth',
                    'details': '管理员登录系统',
                    'ip_address': '127.0.0.1'
                },
                {
                    'action': 'access',
                    'resource': 'admin_index',
                    'details': '访问系统管理首页',
                    'ip_address': '127.0.0.1'
                },
                {
                    'action': 'access',
                    'resource': 'audit_logs',
                    'details': '访问审计日志页面',
                    'ip_address': '127.0.0.1'
                },
                {
                    'action': 'create_user',
                    'resource': 'user',
                    'resource_id': '2',
                    'details': '创建新用户: test_user',
                    'ip_address': '*************'
                },
                {
                    'action': 'update_user',
                    'resource': 'user',
                    'resource_id': '2',
                    'details': '更新用户信息: test_user',
                    'ip_address': '*************'
                },
                {
                    'action': 'upload',
                    'resource': 'excel_file',
                    'resource_id': '1',
                    'details': '上传Excel文件: data.xlsx',
                    'ip_address': '*************'
                },
                {
                    'action': 'download',
                    'resource': 'data_file',
                    'resource_id': '3',
                    'details': '下载数据文件: report.csv',
                    'ip_address': '*************'
                }
            ]
            
            created_count = 0
            for i, log_data in enumerate(test_logs):
                # 创建时间稍有差异
                created_at = datetime.now() - timedelta(minutes=len(test_logs)-i)
                
                log = AuditLog(
                    user_id=admin_user.id,
                    action=log_data['action'],
                    resource=log_data['resource'],
                    resource_id=log_data.get('resource_id'),
                    details=log_data['details'],
                    ip_address=log_data.get('ip_address', '127.0.0.1'),
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    created_at=created_at
                )
                db.session.add(log)
                created_count += 1
            
            db.session.commit()
            print(f"✅ 成功创建 {created_count} 条测试审计日志")
            
            # 验证创建结果
            new_count = AuditLog.query.count()
            print(f"更新后审计日志总数: {new_count}")
            
            # 显示最新的几条日志
            recent_logs = AuditLog.query.order_by(AuditLog.created_at.desc()).limit(3).all()
            print("最新的审计日志:")
            for log in recent_logs:
                print(f"  {log.created_at.strftime('%Y-%m-%d %H:%M:%S')} - {log.action} - {log.resource} - {log.details}")
                
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 先创建测试数据
    create_test_audit_logs()
    
    # 然后测试功能
    test_audit_logs()
