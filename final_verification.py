#!/usr/bin/env python3
"""
最终验证新的任务创建功能
"""

import requests
import json
from bs4 import BeautifulSoup

def final_verification():
    """最终验证"""
    
    # 创建会话
    session = requests.Session()
    
    print("=== 🎯 最终验证新的任务创建功能 ===")
    
    # 1. 登录
    print("\n1. 🔐 登录验证...")
    login_url = 'http://127.0.0.1:5002/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }
    
    try:
        login_page = session.get(login_url, timeout=10)
        soup = BeautifulSoup(login_page.text, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        if csrf_token:
            login_data['csrf_token'] = csrf_token['value']
        
        login_response = session.post(login_url, data=login_data, allow_redirects=True, timeout=10)
        
        if '/index' not in login_response.url:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录过程出错: {str(e)}")
        return False
    
    # 2. 验证数据源API
    print("\n2. 📊 数据源API验证...")
    try:
        data_sources_url = 'http://127.0.0.1:5002/selfcheck/api/data-sources'
        data_sources_response = session.get(data_sources_url, timeout=10)
        
        if data_sources_response.status_code == 200:
            data_sources_data = data_sources_response.json()
            if data_sources_data.get('success'):
                data_sources = data_sources_data['data_sources']
                print(f"✅ 成功获取 {len(data_sources)} 个数据源")
                
                # 验证数据源结构
                if data_sources:
                    ds = data_sources[0]
                    required_fields = ['id', 'name', 'type', 'description']
                    missing_fields = [field for field in required_fields if field not in ds]
                    if missing_fields:
                        print(f"❌ 数据源缺少字段: {missing_fields}")
                        return False
                    else:
                        print("✅ 数据源数据结构完整")
                        
                        # 显示数据源信息
                        print("📋 可用数据源:")
                        for ds in data_sources:
                            print(f"  - {ds['name']} (ID: {ds['id']}, 类型: {ds['type']})")
                
                first_data_source_id = data_sources[0]['id'] if data_sources else None
            else:
                print(f"❌ 数据源API返回错误: {data_sources_data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 数据源API失败: {data_sources_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 数据源API测试出错: {str(e)}")
        return False
    
    # 3. 验证授权方案API
    print("\n3. 📋 授权方案API验证...")
    try:
        schemes_url = 'http://127.0.0.1:5002/selfcheck/api/authorized-schemes'
        schemes_response = session.get(schemes_url, timeout=10)
        
        if schemes_response.status_code == 200:
            schemes_data = schemes_response.json()
            if schemes_data.get('success'):
                schemes = schemes_data['schemes']
                print(f"✅ 成功获取 {len(schemes)} 个授权方案")
                
                # 验证方案结构
                if schemes:
                    scheme = schemes[0]
                    required_fields = ['id', 'scheme_name', 'rule_count']
                    missing_fields = [field for field in required_fields if field not in scheme]
                    if missing_fields:
                        print(f"❌ 方案缺少字段: {missing_fields}")
                        return False
                    else:
                        print("✅ 方案数据结构完整")
                        
                        # 显示方案信息
                        print("📋 可用方案:")
                        for scheme in schemes:
                            rule_count = scheme.get('rule_count', 0)
                            print(f"  - {scheme['scheme_name']} (ID: {scheme['id']}, {rule_count}个规则)")
                
                first_scheme_id = schemes[0]['id'] if schemes else None
            else:
                print(f"❌ 授权方案API返回错误: {schemes_data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 授权方案API失败: {schemes_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 授权方案API测试出错: {str(e)}")
        return False
    
    # 4. 验证任务创建API
    print("\n4. 🚀 任务创建API验证...")
    try:
        if not first_data_source_id or not first_scheme_id:
            print("❌ 缺少必要的数据源或方案信息")
            return False
        
        # 获取CSRF令牌
        tasks_page = session.get('http://127.0.0.1:5002/selfcheck/tasks', timeout=10)
        soup = BeautifulSoup(tasks_page.text, 'html.parser')
        csrf_token = soup.find('meta', {'name': 'csrf-token'})
        csrf_token_value = csrf_token['content'] if csrf_token else None
        
        if not csrf_token_value:
            print("❌ 无法获取CSRF令牌")
            return False
        
        create_task_data = {
            'task_name': '最终验证任务',
            'data_source': first_data_source_id,
            'scheme_id': first_scheme_id
        }
        
        create_task_url = 'http://127.0.0.1:5002/selfcheck/api/tasks'
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token_value
        }
        
        create_response = session.post(
            create_task_url,
            headers=headers,
            data=json.dumps(create_task_data),
            timeout=10
        )
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            if create_result.get('success'):
                print("✅ 任务创建成功!")
                task = create_result['task']
                
                # 验证返回的任务数据
                required_task_fields = ['id', 'task_name', 'data_source', 'scheme_id', 'status']
                missing_task_fields = [field for field in required_task_fields if field not in task]
                if missing_task_fields:
                    print(f"❌ 任务数据缺少字段: {missing_task_fields}")
                    return False
                else:
                    print("✅ 任务数据结构完整")
                    print(f"📋 创建的任务信息:")
                    print(f"  - 任务ID: {task['id']}")
                    print(f"  - 任务名称: {task['task_name']}")
                    print(f"  - 数据源: {task['data_source']} ({task.get('data_source_name', '')})")
                    print(f"  - 方案: {task['scheme_id']} ({task.get('scheme_name', '')})")
                    print(f"  - 状态: {task['status']}")
                    
                created_task_id = task['id']
            else:
                print(f"❌ 任务创建失败: {create_result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 任务创建API失败: {create_response.status_code}")
            print(f"响应内容: {create_response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 创建任务API测试出错: {str(e)}")
        return False
    
    # 5. 验证任务列表API
    print("\n5. 📝 任务列表API验证...")
    try:
        tasks_list_url = 'http://127.0.0.1:5002/selfcheck/api/tasks?page=1&per_page=10'
        tasks_list_response = session.get(tasks_list_url, timeout=10)
        
        if tasks_list_response.status_code == 200:
            tasks_list_data = tasks_list_response.json()
            if tasks_list_data.get('success'):
                tasks = tasks_list_data['tasks']
                print(f"✅ 成功获取 {len(tasks)} 个任务")
                
                # 验证是否包含刚创建的任务
                created_task_found = False
                for task in tasks:
                    if task.get('id') == created_task_id:
                        created_task_found = True
                        print(f"✅ 找到刚创建的任务: {task['task_name']}")
                        break
                
                if not created_task_found:
                    print("❌ 未找到刚创建的任务")
                    return False
                
                # 验证任务数据结构
                if tasks:
                    task = tasks[0]
                    required_fields = ['id', 'task_name', 'data_source', 'scheme_name', 'status']
                    missing_fields = [field for field in required_fields if field not in task]
                    if missing_fields:
                        print(f"❌ 任务列表数据缺少字段: {missing_fields}")
                        return False
                    else:
                        print("✅ 任务列表数据结构完整")
                        
            else:
                print(f"❌ 任务列表API返回错误: {tasks_list_data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 任务列表API失败: {tasks_list_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 任务列表API测试出错: {str(e)}")
        return False
    
    # 6. 验证任务页面
    print("\n6. 🌐 任务页面验证...")
    try:
        tasks_page_url = 'http://127.0.0.1:5002/selfcheck/tasks'
        tasks_page_response = session.get(tasks_page_url, timeout=10)
        
        if tasks_page_response.status_code == 200:
            print("✅ 任务页面访问成功")
            
            # 检查页面是否包含新的字段
            page_content = tasks_page_response.text
            checks = [
                ('dataSourceSelect', '数据源选择器'),
                ('schemeSelect', '方案选择器'),
                ('loadDataSources', '加载数据源函数'),
                ('loadAuthorizedSchemes', '加载授权方案函数'),
                ('数据源', '数据源列标题'),
                ('检查方案', '检查方案列标题')
            ]
            
            all_checks_passed = True
            for check_item, description in checks:
                if check_item in page_content:
                    print(f"✅ 页面包含{description}")
                else:
                    print(f"❌ 页面缺少{description}")
                    all_checks_passed = False
            
            if not all_checks_passed:
                return False
                
        else:
            print(f"❌ 任务页面访问失败: {tasks_page_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 任务页面测试出错: {str(e)}")
        return False
    
    print("\n🎉 所有验证通过！")
    print("\n📊 验证总结:")
    print("✅ 用户登录正常")
    print("✅ 数据源API正常工作")
    print("✅ 授权方案API正常工作")
    print("✅ 任务创建API正常工作")
    print("✅ 任务列表API正常工作")
    print("✅ 任务页面功能完整")
    print("\n🌟 新的任务创建功能已完全实现并正常工作！")
    
    return True

if __name__ == '__main__':
    try:
        success = final_verification()
        if success:
            print("\n🎊 恭喜！所有功能验证成功！")
        else:
            print("\n❌ 验证失败，请检查相关问题")
    except Exception as e:
        print(f"验证过程出错: {e}")
        import traceback
        traceback.print_exc()
