{% extends "base.html" %}

{% block title %}医保基金负面清单智能转换工具 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-magic"></i> 医保基金负面清单智能转换工具</h4>
{% endblock %}

{% block content %}

<!-- 文件导入区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-upload"></i> 文件导入与解析
                </h6>
            </div>
                <div class="card-body">
                    <!-- 文件上传区域 -->
                    <div class="upload-area border-2 border-dashed border-primary rounded p-4 text-center mb-4"
                         id="uploadArea"
                         ondrop="handleDrop(event)"
                         ondragover="handleDragOver(event)"
                         ondragleave="handleDragLeave(event)">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h5>上传负面清单文件</h5>
                            <p class="text-muted mb-3">
                                拖放文件到此处，或点击选择文件<br>
                                支持 .xlsx, .xls, .pdf, .doc, .docx 等格式
                            </p>
                            <input type="file" id="fileInput" class="d-none"
                                   accept=".xlsx,.xls,.pdf,.doc,.docx"
                                   onchange="handleFileSelect(event)">
                            <button type="button" class="btn btn-primary" onclick="$('#fileInput').click()">
                                <i class="fas fa-folder-open"></i> 选择文件
                            </button>
                        </div>
                    </div>

                    <!-- 上传进度 -->
                    <div id="uploadProgress" class="mb-4" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span id="uploadFileName">文件名</span>
                            <span id="uploadStatus">准备上传...</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- 解析控制 -->
                    <div id="parseControls" class="text-center" style="display: none;">
                        <button type="button" class="btn btn-success btn-lg" id="parseBtn" onclick="startParsing()">
                            <i class="fas fa-cogs"></i> 开始解析所有文件
                        </button>
                        <p class="text-muted mt-2">预计解析时间：约 150 条规则 5 秒</p>
                    </div>

                    <!-- 解析结果概览 -->
                    <div id="parseResults" class="mt-4" style="display: none;">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> 解析完成概览</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>识别规则数：</strong>
                                    <span id="totalRules" class="badge bg-primary">0</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>高置信度：</strong>
                                    <span id="highConfidence" class="badge bg-success">0</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>待校对：</strong>
                                    <span id="pendingReview" class="badge bg-warning">0</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>解析时间：</strong>
                                    <span id="parseTime" class="badge bg-info">0秒</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- 上传历史 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history"></i> 上传历史 (共 <span id="totalCount">0</span> 条)
                </h6>
                <div>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="loadUploadHistory()">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
            </div>
                <div class="card-body">
                    <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="25%">文件名</th>
                                <th width="10%">文件类型</th>
                                <th width="10%">文件大小</th>
                                <th width="10%">状态</th>
                                <th width="10%">规则数量</th>
                                <th width="15%">上传时间</th>
                                <th width="20%">操作</th>
                            </tr>
                        </thead>
                        <tbody id="uploadHistoryBody">
                            <!-- 动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="上传历史分页">
                    <ul class="pagination justify-content-center" id="uploadPagination">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 规则预览与校对模态框 -->
<div class="modal fade" id="rulesModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list-alt"></i> 规则预览与校对
                    <span id="modalFileName" class="text-muted"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 统计信息 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4 id="statsTotal">0</h4>
                                <small>总规则数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4 id="statsApproved">0</h4>
                                <small>已确认</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4 id="statsPending">0</h4>
                                <small>待校对</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h4 id="statsRejected">0</h4>
                                <small>已拒绝</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 筛选和搜索 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-select" id="filterStatus" onchange="filterRules()">
                            <option value="">全部状态</option>
                            <option value="pending_review">待校对</option>
                            <option value="reviewed">已校对</option>
                            <option value="approved">已确认</option>
                            <option value="rejected">已拒绝</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterType" onchange="filterRules()">
                            <option value="">全部类型</option>
                            <option value="禁止性规则">禁止性规则</option>
                            <option value="限制性规则">限制性规则</option>
                            <option value="频次性规则">频次性规则</option>
                            <option value="欺诈性规则">欺诈性规则</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterCategory" onchange="filterRules()">
                            <option value="">全部类别</option>
                            <option value="药品">药品</option>
                            <option value="诊疗">诊疗</option>
                            <option value="检查">检查</option>
                            <option value="耗材">耗材</option>
                            <option value="费用">费用</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="searchRule" placeholder="搜索规则..." onkeyup="searchRules()">
                    </div>
                </div>

                <!-- 规则列表 -->
                <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                    <table class="table table-sm table-hover">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th width="25%">规则名称</th>
                                <th width="15%">类型</th>
                                <th width="10%">类别</th>
                                <th width="8%">严重程度</th>
                                <th width="8%">置信度</th>
                                <th width="10%">状态</th>
                                <th width="19%">操作</th>
                            </tr>
                        </thead>
                        <tbody id="rulesTableBody">
                            <!-- 动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 批量操作 -->
                <div class="mt-3">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success btn-sm" onclick="batchApprove()">
                            <i class="fas fa-check"></i> 批量确认
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="batchReview()">
                            <i class="fas fa-edit"></i> 批量校对
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="batchReject()">
                            <i class="fas fa-times"></i> 批量拒绝
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="batchDelete()">
                            <i class="fas fa-trash"></i> 批量删除
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportRules()">
                    <i class="fas fa-download"></i> 导出Excel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 规则编辑模态框 -->
<div class="modal fade" id="editRuleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑规则</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editRuleForm">
                    <input type="hidden" id="editRuleId">
                    <div class="mb-3">
                        <label class="form-label">规则名称</label>
                        <input type="text" class="form-control" id="editRuleName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">规则描述</label>
                        <textarea class="form-control" id="editRuleDescription" rows="4" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">规则类型</label>
                                <select class="form-select" id="editRuleType" required>
                                    <option value="">请选择</option>
                                    <option value="禁止性规则">禁止性规则</option>
                                    <option value="限制性规则">限制性规则</option>
                                    <option value="频次性规则">频次性规则</option>
                                    <option value="欺诈性规则">欺诈性规则</option>
                                    <option value="其他规则">其他规则</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">医疗类别</label>
                                <select class="form-select" id="editMedicalCategory" required>
                                    <option value="">请选择</option>
                                    <option value="药品">药品</option>
                                    <option value="诊疗">诊疗</option>
                                    <option value="检查">检查</option>
                                    <option value="耗材">耗材</option>
                                    <option value="费用">费用</option>
                                    <option value="住院">住院</option>
                                    <option value="门诊">门诊</option>
                                    <option value="通用">通用</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">严重程度</label>
                                <select class="form-select" id="editSeverity" required>
                                    <option value="">请选择</option>
                                    <option value="高">高</option>
                                    <option value="中">中</option>
                                    <option value="低">低</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRule()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局变量
let currentUploadId = null;
let currentRules = [];
let selectedRules = [];

$(document).ready(function() {
    loadUploadHistory();
});

// 文件拖拽处理
function handleDragOver(event) {
    event.preventDefault();
    $('#uploadArea').addClass('border-success bg-light');
}

function handleDragLeave(event) {
    event.preventDefault();
    $('#uploadArea').removeClass('border-success bg-light');
}

function handleDrop(event) {
    event.preventDefault();
    $('#uploadArea').removeClass('border-success bg-light');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        handleFiles(files);
    }
}

// 文件选择处理
function handleFileSelect(event) {
    const files = event.target.files;
    if (files.length > 0) {
        handleFiles(files);
    }
}

// 处理文件
function handleFiles(files) {
    const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                         'application/vnd.ms-excel',
                         'application/pdf',
                         'application/msword',
                         'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    const validFiles = Array.from(files).filter(file => {
        const extension = file.name.split('.').pop().toLowerCase();
        return ['xlsx', 'xls', 'pdf', 'doc', 'docx'].includes(extension);
    });

    if (validFiles.length === 0) {
        showAlert('请选择支持的文件格式（Excel、PDF、Word）', 'warning');
        return;
    }

    // 上传第一个文件
    uploadFile(validFiles[0]);
}

// 上传文件
function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    const csrfToken = $('meta[name=csrf-token]').attr('content');

    // 显示上传进度
    $('#uploadProgress').show();
    $('#uploadFileName').text(file.name);
    $('#uploadStatus').text('准备上传...');
    $('#progressBar').css('width', '0%');

    $.ajax({
        url: '/rules/api/converter/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRFToken': csrfToken
        },
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    $('#progressBar').css('width', percentComplete + '%');
                    $('#uploadStatus').text('上传中...');
                }
            }, false);
            return xhr;
        }
    })
    .done(function(response) {
        $('#progressBar').css('width', '100%');
        if (response.success) {
            $('#uploadStatus').text('上传成功');
            showAlert(response.message, 'success');

            // 显示解析按钮
            $('#parseControls').show();
            currentUploadId = response.upload_id;

            // 刷新上传历史
            loadUploadHistory();

            // 3秒后隐藏进度条
            setTimeout(() => {
                $('#uploadProgress').hide();
            }, 3000);
        } else {
            $('#uploadStatus').text('上传失败');
            showAlert('上传失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        $('#uploadStatus').text('上传失败');
        showAlert('文件上传失败', 'danger');
    });
}

// 开始解析
function startParsing() {
    if (!currentUploadId) {
        showAlert('请先上传文件', 'warning');
        return;
    }

    $('#parseBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 解析中...');

    const startTime = Date.now();

    $.ajax({
        url: `/rules/api/converter/parse/${currentUploadId}`,
        type: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        }
    })
    .done(function(response) {
        const endTime = Date.now();
        const parseTime = Math.round((endTime - startTime) / 1000);

        if (response.success) {
            // 显示解析结果
            $('#totalRules').text(response.rules_count);
            $('#highConfidence').text(response.rules.filter(r => r.confidence > 0.8).length);
            $('#pendingReview').text(response.rules_count);
            $('#parseTime').text(parseTime + '秒');
            $('#parseResults').show();

            showAlert(response.message, 'success');

            // 刷新上传历史
            loadUploadHistory();

            // 自动打开规则预览
            setTimeout(() => {
                viewRules(currentUploadId);
            }, 1000);
        } else {
            showAlert('解析失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('文件解析失败', 'danger');
    })
    .always(function() {
        $('#parseBtn').prop('disabled', false).html('<i class="fas fa-cogs"></i> 开始解析所有文件');
    });
}

// 加载上传历史
function loadUploadHistory(page = 1) {
    $.get(`/rules/api/converter/uploads?page=${page}&per_page=10`)
        .done(function(response) {
            if (response.success) {
                renderUploadHistory(response.uploads);
                renderUploadPagination(response.pagination);
            } else {
                showAlert('加载上传历史失败: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载上传历史失败', 'danger');
        });
}

// 渲染上传历史
function renderUploadHistory(uploads) {
    const tbody = $('#uploadHistoryBody');
    tbody.empty();

    if (uploads.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted">暂无上传记录</td>
            </tr>
        `);
        return;
    }

    uploads.forEach(upload => {
        const statusBadge = getStatusBadge(upload.status);
        const fileSize = formatFileSize(upload.file_size);
        const uploadTime = formatDateTime(upload.upload_time);

        tbody.append(`
            <tr>
                <td>
                    <i class="fas fa-file-${getFileIcon(upload.file_type)}"></i>
                    ${upload.original_filename}
                </td>
                <td><span class="badge bg-secondary">${upload.file_type.toUpperCase()}</span></td>
                <td>${fileSize}</td>
                <td>${statusBadge}</td>
                <td>
                    ${upload.rules_count > 0 ?
                        `<span class="badge bg-info">${upload.rules_count}</span>` :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>${uploadTime}</td>
                <td>
                    ${upload.status === 'parsed' ?
                        `<button type="button" class="btn btn-outline-primary btn-sm" onclick="viewRules(${upload.id})" title="查看规则">
                            <i class="fas fa-list"></i>
                        </button>` : ''
                    }
                    ${upload.status === 'uploaded' ?
                        `<button type="button" class="btn btn-outline-success btn-sm" onclick="parseUpload(${upload.id})" title="解析">
                            <i class="fas fa-cogs"></i>
                        </button>` : ''
                    }
                    ${upload.rules_count > 0 ?
                        `<button type="button" class="btn btn-outline-info btn-sm ms-1" onclick="exportUploadRules(${upload.id})" title="导出">
                            <i class="fas fa-download"></i>
                        </button>` : ''
                    }
                    <button type="button" class="btn btn-outline-danger btn-sm ms-1" onclick="deleteUpload(${upload.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `);
    });
}

// 获取状态徽章
function getStatusBadge(status) {
    const statusMap = {
        'uploaded': '<span class="badge bg-secondary">已上传</span>',
        'parsing': '<span class="badge bg-primary">解析中</span>',
        'parsed': '<span class="badge bg-success">已解析</span>',
        'failed': '<span class="badge bg-danger">失败</span>'
    };
    return statusMap[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取文件图标
function getFileIcon(fileType) {
    const iconMap = {
        'xlsx': 'excel',
        'xls': 'excel',
        'pdf': 'pdf',
        'doc': 'word',
        'docx': 'word'
    };
    return iconMap[fileType] || 'alt';
}

// 解析上传文件
function parseUpload(uploadId) {
    currentUploadId = uploadId;
    startParsing();
}

// 查看规则
function viewRules(uploadId) {
    currentUploadId = uploadId;

    // 获取上传信息
    $.get(`/rules/api/converter/uploads/${uploadId}`)
        .done(function(response) {
            if (response.success) {
                $('#modalFileName').text(`- ${response.upload.original_filename}`);
            }
        });

    // 加载规则
    loadRules(uploadId);
    $('#rulesModal').modal('show');
}

// 加载规则
function loadRules(uploadId, page = 1) {
    $.get(`/rules/api/converter/rules/${uploadId}?page=${page}&per_page=50`)
        .done(function(response) {
            if (response.success) {
                currentRules = response.rules;
                renderRules(response.rules);
                updateRulesStats(response.rules);
            } else {
                showAlert('加载规则失败: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载规则失败', 'danger');
        });
}

// 渲染规则列表
function renderRules(rules) {
    const tbody = $('#rulesTableBody');
    tbody.empty();

    if (rules.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted">暂无规则数据</td>
            </tr>
        `);
        return;
    }

    rules.forEach(rule => {
        const confidenceBadge = getConfidenceBadge(rule.confidence);
        const statusBadge = getRuleStatusBadge(rule.status);
        const severityBadge = getSeverityBadge(rule.severity);

        tbody.append(`
            <tr>
                <td>
                    <input type="checkbox" class="rule-checkbox" value="${rule.id}" onchange="updateSelectedRules()">
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 200px;" title="${rule.rule_description}">
                        ${rule.rule_name}
                    </div>
                </td>
                <td><span class="badge bg-info">${rule.rule_type}</span></td>
                <td><span class="badge bg-secondary">${rule.medical_category}</span></td>
                <td>${severityBadge}</td>
                <td>${confidenceBadge}</td>
                <td>${statusBadge}</td>
                <td>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editRule(${rule.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm ms-1" onclick="approveRule(${rule.id})" title="确认">
                        <i class="fas fa-check"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm ms-1" onclick="rejectRule(${rule.id})" title="拒绝">
                        <i class="fas fa-times"></i>
                    </button>
                </td>
            </tr>
        `);
    });
}

// 获取置信度徽章
function getConfidenceBadge(confidence) {
    const percent = Math.round(confidence * 100);
    if (percent >= 80) {
        return `<span class="badge bg-success">${percent}%</span>`;
    } else if (percent >= 60) {
        return `<span class="badge bg-warning">${percent}%</span>`;
    } else {
        return `<span class="badge bg-danger">${percent}%</span>`;
    }
}

// 获取规则状态徽章
function getRuleStatusBadge(status) {
    const statusMap = {
        'pending_review': '<span class="badge bg-warning">待校对</span>',
        'reviewed': '<span class="badge bg-info">已校对</span>',
        'approved': '<span class="badge bg-success">已确认</span>',
        'rejected': '<span class="badge bg-danger">已拒绝</span>'
    };
    return statusMap[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取严重程度徽章
function getSeverityBadge(severity) {
    const severityMap = {
        '高': '<span class="badge bg-danger">高</span>',
        '中': '<span class="badge bg-warning">中</span>',
        '低': '<span class="badge bg-success">低</span>'
    };
    return severityMap[severity] || '<span class="badge bg-secondary">-</span>';
}

// 更新规则统计
function updateRulesStats(rules) {
    const total = rules.length;
    const approved = rules.filter(r => r.status === 'approved').length;
    const pending = rules.filter(r => r.status === 'pending_review').length;
    const rejected = rules.filter(r => r.status === 'rejected').length;

    $('#statsTotal').text(total);
    $('#statsApproved').text(approved);
    $('#statsPending').text(pending);
    $('#statsRejected').text(rejected);
}

// 工具函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function showAlert(message, type = 'info') {
    // 这里可以使用Bootstrap的Alert组件或其他通知组件
    const alertClass = `alert-${type}`;
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 添加到页面顶部
    $('body').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 3000);
}
</script>
{% endblock %}