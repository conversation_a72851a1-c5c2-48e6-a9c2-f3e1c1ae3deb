{% extends "base.html" %}

{% block title %}医保基金负面清单智能转换工具 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-magic"></i> 医保基金负面清单智能转换工具</h4>
{% endblock %}

{% block content %}

<!-- 文件导入区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-upload"></i> 文件导入与解析
                </h6>
            </div>
                <div class="card-body">
                    <!-- 文件上传区域 -->
                    <div class="upload-area border-2 border-dashed border-primary rounded p-4 text-center mb-4"
                         id="uploadArea"
                         ondrop="handleDrop(event)"
                         ondragover="handleDragOver(event)"
                         ondragleave="handleDragLeave(event)">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h5>上传负面清单文件</h5>
                            <p class="text-muted mb-3">
                                拖放文件到此处，或点击选择文件<br>
                                支持 .xlsx, .xls, .pdf, .doc, .docx 等格式
                            </p>
                            <input type="file" id="fileInput" class="d-none"
                                   accept=".xlsx,.xls,.pdf,.doc,.docx"
                                   onchange="handleFileSelect(event)">
                            <button type="button" class="btn btn-primary" onclick="$('#fileInput').click()">
                                <i class="fas fa-folder-open"></i> 选择文件
                            </button>
                        </div>
                    </div>

                    <!-- 上传进度 -->
                    <div id="uploadProgress" class="mb-4" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span id="uploadFileName">文件名</span>
                            <span id="uploadStatus">准备上传...</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- 解析控制 -->
                    <div id="parseControls" class="text-center" style="display: none;">
                        <button type="button" class="btn btn-success btn-lg" id="parseBtn" onclick="startParsing()">
                            <i class="fas fa-cogs"></i> 开始解析所有文件
                        </button>
                        <p class="text-muted mt-2">预计解析时间：约 150 条规则 5 秒</p>
                    </div>

                    <!-- 解析结果概览 -->
                    <div id="parseResults" class="mt-4" style="display: none;">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> 解析完成概览</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>识别规则数：</strong>
                                    <span id="totalRules" class="badge bg-primary">0</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>高置信度：</strong>
                                    <span id="highConfidence" class="badge bg-success">0</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>待校对：</strong>
                                    <span id="pendingReview" class="badge bg-warning">0</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>解析时间：</strong>
                                    <span id="parseTime" class="badge bg-info">0秒</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- 上传历史 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history"></i> 上传历史 (共 <span id="totalCount">0</span> 条)
                </h6>
                <div>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="loadUploadHistory()">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
            </div>
                <div class="card-body">
                    <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="20%">文件名</th>
                                <th width="8%">文件类型</th>
                                <th width="8%">文件大小</th>
                                <th width="10%">状态</th>
                                <th width="6%">规则数量</th>
                                <th width="13%">上传时间</th>
                                <th width="35%">操作步骤</th>
                            </tr>
                        </thead>
                        <tbody id="uploadHistoryBody">
                            <!-- 动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="上传历史分页">
                    <ul class="pagination justify-content-center" id="uploadPagination">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 规则预览与校对模态框 -->
<div class="modal fade" id="rulesModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list-alt"></i> 规则预览与校对
                    <span id="modalFileName" class="text-muted"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 统计信息 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4 id="statsTotal">0</h4>
                                <small>总规则数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4 id="statsApproved">0</h4>
                                <small>已确认</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4 id="statsPending">0</h4>
                                <small>待校对</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h4 id="statsRejected">0</h4>
                                <small>已拒绝</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 筛选和搜索 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-select" id="filterStatus" onchange="filterRules()">
                            <option value="">全部状态</option>
                            <option value="pending_review">待校对</option>
                            <option value="reviewed">已校对</option>
                            <option value="approved">已确认</option>
                            <option value="rejected">已拒绝</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterType" onchange="filterRules()">
                            <option value="">全部类型</option>
                            <option value="禁止性规则">禁止性规则</option>
                            <option value="限制性规则">限制性规则</option>
                            <option value="频次性规则">频次性规则</option>
                            <option value="欺诈性规则">欺诈性规则</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterCategory" onchange="filterRules()">
                            <option value="">全部类别</option>
                            <option value="药品">药品</option>
                            <option value="诊疗">诊疗</option>
                            <option value="检查">检查</option>
                            <option value="耗材">耗材</option>
                            <option value="费用">费用</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="searchRule" placeholder="搜索规则..." onkeyup="searchRules()">
                    </div>
                </div>

                <!-- 标准化规则列表 -->
                <div class="table-responsive" style="max-height: 600px; overflow-y: auto;">
                    <table class="table table-sm table-hover">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th width="3%">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th width="8%">规则来源</th>
                                <th width="4%">城市</th>
                                <th width="3%">序号</th>
                                <th width="6%">涉及科室</th>
                                <th width="6%">行为认定</th>
                                <th width="15%">规则名称</th>
                                <th width="15%">规则内涵</th>
                                <th width="8%">医保名称1</th>
                                <th width="8%">医保名称2</th>
                                <th width="4%">类型</th>
                                <th width="4%">时间类型</th>
                                <th width="4%">置信度</th>
                                <th width="4%">状态</th>
                                <th width="8%">操作</th>
                            </tr>
                        </thead>
                        <tbody id="rulesTableBody">
                            <!-- 动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 批量操作 -->
                <div class="mt-3">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-info btn-sm" onclick="startFieldConfirmationForCurrentUpload()">
                            <i class="fas fa-clipboard-check"></i> 字段确认
                        </button>
                        <button type="button" class="btn btn-success btn-sm" onclick="batchApprove()">
                            <i class="fas fa-check"></i> 批量确认
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="batchReview()">
                            <i class="fas fa-edit"></i> 批量校对
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="batchReject()">
                            <i class="fas fa-times"></i> 批量拒绝
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="batchDelete()">
                            <i class="fas fa-trash"></i> 批量删除
                        </button>
                    </div>
                    <div class="btn-group ms-2" role="group">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="batchConfirmRules(currentUploadId)">
                            <i class="fas fa-magic"></i> AI批量确认
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportRules()">
                    <i class="fas fa-download"></i> 导出Excel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 原始表格数据查看模态框 -->
<div class="modal fade" id="tableDataModal" tabindex="-1" aria-labelledby="tableDataModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tableDataModalLabel">
                    <i class="fas fa-table"></i> 原始表格数据 <span id="modalTableFileName"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 数据统计 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">总行数</h6>
                                <h4 class="text-primary" id="totalRowsCount">-</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">表头列数</h6>
                                <h4 class="text-info" id="headerColumnsCount">-</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">文件状态</h6>
                                <h4 class="text-success" id="fileStatus">-</h4>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 表格数据展示 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-eye"></i> 表格内容预览
                            <small class="text-muted">（以下是解析出的原始数据，请确认是否正确）</small>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="tableDataContent" style="max-height: 500px; overflow-y: auto; font-family: monospace; white-space: pre-wrap;">
                            <!-- 这里将显示Markdown格式的表格内容 -->
                        </div>
                    </div>
                </div>

                <!-- 用户交互区域 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-cog"></i> 转换设置
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">数据起始行（跳过表头）</label>
                                <input type="number" class="form-control" id="dataStartRow" value="2" min="1">
                                <small class="text-muted">指定从第几行开始作为数据行进行转换</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">转换模式</label>
                                <select class="form-select" id="conversionMode">
                                    <option value="intelligent">智能识别模式</option>
                                    <option value="structured">结构化模式</option>
                                </select>
                                <small class="text-muted">选择适合的转换模式</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> 关闭
                </button>
                <button type="button" class="btn btn-primary" onclick="proceedToConversion()">
                    <i class="fas fa-arrow-right"></i> 确认数据，开始转换
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 规则编辑模态框 -->
<div class="modal fade" id="editRuleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑规则</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editRuleForm">
                    <input type="hidden" id="editRuleId">
                    <div class="mb-3">
                        <label class="form-label">规则名称</label>
                        <input type="text" class="form-control" id="editRuleName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">规则描述</label>
                        <textarea class="form-control" id="editRuleDescription" rows="4" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">规则类型</label>
                                <select class="form-select" id="editRuleType" required>
                                    <option value="">请选择</option>
                                    <option value="禁止性规则">禁止性规则</option>
                                    <option value="限制性规则">限制性规则</option>
                                    <option value="频次性规则">频次性规则</option>
                                    <option value="欺诈性规则">欺诈性规则</option>
                                    <option value="其他规则">其他规则</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">医疗类别</label>
                                <select class="form-select" id="editMedicalCategory" required>
                                    <option value="">请选择</option>
                                    <option value="药品">药品</option>
                                    <option value="诊疗">诊疗</option>
                                    <option value="检查">检查</option>
                                    <option value="耗材">耗材</option>
                                    <option value="费用">费用</option>
                                    <option value="住院">住院</option>
                                    <option value="门诊">门诊</option>
                                    <option value="通用">通用</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">严重程度</label>
                                <select class="form-select" id="editSeverity" required>
                                    <option value="">请选择</option>
                                    <option value="高">高</option>
                                    <option value="中">中</option>
                                    <option value="低">低</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRule()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局变量
let currentUploadId = null;
let currentRules = [];
let selectedRules = [];

$(document).ready(function() {
    loadUploadHistory();
});

// 文件拖拽处理
function handleDragOver(event) {
    event.preventDefault();
    $('#uploadArea').addClass('border-success bg-light');
}

function handleDragLeave(event) {
    event.preventDefault();
    $('#uploadArea').removeClass('border-success bg-light');
}

function handleDrop(event) {
    event.preventDefault();
    $('#uploadArea').removeClass('border-success bg-light');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        handleFiles(files);
    }
}

// 文件选择处理
function handleFileSelect(event) {
    const files = event.target.files;
    if (files.length > 0) {
        handleFiles(files);
    }
}

// 处理文件
function handleFiles(files) {
    const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                         'application/vnd.ms-excel',
                         'application/pdf',
                         'application/msword',
                         'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    const validFiles = Array.from(files).filter(file => {
        const extension = file.name.split('.').pop().toLowerCase();
        return ['xlsx', 'xls', 'pdf', 'doc', 'docx'].includes(extension);
    });

    if (validFiles.length === 0) {
        showAlert('请选择支持的文件格式（Excel、PDF、Word）', 'warning');
        return;
    }

    // 上传第一个文件
    uploadFile(validFiles[0]);
}

// 上传文件
function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    const csrfToken = $('meta[name=csrf-token]').attr('content');

    // 显示上传进度
    $('#uploadProgress').show();
    $('#uploadFileName').text(file.name);
    $('#uploadStatus').text('准备上传...');
    $('#progressBar').css('width', '0%');

    $.ajax({
        url: '/rules/api/converter/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRFToken': csrfToken
        },
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    $('#progressBar').css('width', percentComplete + '%');
                    $('#uploadStatus').text('上传中...');
                }
            }, false);
            return xhr;
        }
    })
    .done(function(response) {
        $('#progressBar').css('width', '100%');
        if (response.success) {
            $('#uploadStatus').text('上传成功');
            showAlert(response.message, 'success');

            // 显示解析按钮
            $('#parseControls').show();
            currentUploadId = response.upload_id;

            // 刷新上传历史
            loadUploadHistory();

            // 3秒后隐藏进度条
            setTimeout(() => {
                $('#uploadProgress').hide();
            }, 3000);
        } else {
            $('#uploadStatus').text('上传失败');
            showAlert('上传失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        $('#uploadStatus').text('上传失败');
        showAlert('文件上传失败', 'danger');
    });
}

// 开始解析
function startParsing() {
    if (!currentUploadId) {
        showAlert('请先上传文件', 'warning');
        return;
    }

    $('#parseBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 解析中...');

    const startTime = Date.now();

    $.ajax({
        url: `/rules/api/converter/parse/${currentUploadId}`,
        type: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        }
    })
    .done(function(response) {
        const endTime = Date.now();
        const parseTime = Math.round((endTime - startTime) / 1000);

        if (response.success) {
            // 显示解析结果
            $('#totalRules').text(response.rules_count);
            $('#highConfidence').text(response.rules.filter(r => r.confidence > 0.8).length);
            $('#pendingReview').text(response.rules_count);
            $('#parseTime').text(parseTime + '秒');
            $('#parseResults').show();

            showAlert(response.message, 'success');

            // 刷新上传历史
            loadUploadHistory();

            // 自动打开规则预览
            setTimeout(() => {
                viewRules(currentUploadId);
            }, 1000);
        } else {
            showAlert('解析失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('文件解析失败', 'danger');
    })
    .always(function() {
        $('#parseBtn').prop('disabled', false).html('<i class="fas fa-cogs"></i> 开始解析所有文件');
    });
}

// 加载上传历史
function loadUploadHistory(page = 1) {
    $.get(`/rules/api/converter/uploads?page=${page}&per_page=10`)
        .done(function(response) {
            if (response.success) {
                renderUploadHistory(response.uploads);
                renderUploadPagination(response.pagination);
            } else {
                showAlert('加载上传历史失败: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载上传历史失败', 'danger');
        });
}

// 渲染上传历史
function renderUploadHistory(uploads) {
    const tbody = $('#uploadHistoryBody');
    tbody.empty();

    if (uploads.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted">暂无上传记录</td>
            </tr>
        `);
        return;
    }

    uploads.forEach(upload => {
        const statusBadge = getStatusBadge(upload.status);
        const fileSize = formatFileSize(upload.file_size);
        const uploadTime = formatDateTime(upload.upload_time);

        tbody.append(`
            <tr>
                <td>
                    <i class="fas fa-file-${getFileIcon(upload.file_type)}"></i>
                    ${upload.original_filename}
                </td>
                <td><span class="badge bg-secondary">${upload.file_type.toUpperCase()}</span></td>
                <td>${fileSize}</td>
                <td>${statusBadge}</td>
                <td>
                    ${upload.rules_count > 0 ?
                        `<span class="badge bg-info">${upload.rules_count}</span>` :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>${uploadTime}</td>
                <td>
                    <div class="btn-group-vertical" role="group" style="width: 100%;">
                        <!-- 步骤1: 解析表格 -->
                        ${upload.status === 'uploaded' ?
                            `<button type="button" class="btn btn-outline-success btn-sm mb-1" onclick="parseTableData(${upload.id})" title="步骤1: 解析表格数据">
                                <i class="fas fa-table"></i> 1.解析表格
                            </button>` : ''
                        }

                        <!-- 步骤2: 查看原始数据 -->
                        ${upload.status === 'table_parsed' ?
                            `<button type="button" class="btn btn-outline-primary btn-sm mb-1" onclick="viewTableData(${upload.id})" title="步骤2: 查看原始表格数据">
                                <i class="fas fa-eye"></i> 2.查看数据
                            </button>` : ''
                        }

                        <!-- 步骤3: 智能转换 -->
                        ${upload.status === 'table_parsed' ?
                            `<button type="button" class="btn btn-outline-warning btn-sm mb-1" onclick="startIntelligentConversion(${upload.id})" title="步骤3: 启动智能转换">
                                <i class="fas fa-robot"></i> 3.智能转换
                            </button>` : ''
                        }

                        <!-- 步骤4: 查看转换结果 -->
                        ${upload.status === 'converted' && upload.rules_count > 0 ?
                            `<button type="button" class="btn btn-outline-info btn-sm mb-1" onclick="viewConversionResults(${upload.id})" title="步骤4: 查看转换结果">
                                <i class="fas fa-list"></i> 4.查看结果
                            </button>` : ''
                        }

                        <!-- 步骤5: 确认修正 -->
                        ${upload.status === 'converted' && upload.rules_count > 0 ?
                            `<button type="button" class="btn btn-outline-warning btn-sm mb-1" onclick="startConfirmation(${upload.id})" title="步骤5: 确认修正">
                                <i class="fas fa-clipboard-check"></i> 5.确认修正
                            </button>` : ''
                        }

                        <!-- 步骤6: 导出数据 -->
                        ${upload.status === 'confirmed' && upload.rules_count > 0 ?
                            `<button type="button" class="btn btn-outline-success btn-sm mb-1" onclick="exportFinalData(${upload.id})" title="步骤6: 导出最终数据">
                                <i class="fas fa-download"></i> 6.导出
                            </button>` : ''
                        }

                        <!-- 删除操作 -->
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteUpload(${upload.id})" title="删除记录">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </td>
            </tr>
        `);
    });
}

// 获取状态徽章
function getStatusBadge(status) {
    const statusMap = {
        'uploaded': '<span class="badge bg-secondary">已上传</span>',
        'parsing': '<span class="badge bg-primary">解析中</span>',
        'table_parsed': '<span class="badge bg-info">表格已解析</span>',
        'converting': '<span class="badge bg-warning">转换中</span>',
        'converted': '<span class="badge bg-success">已转换</span>',
        'confirmed': '<span class="badge bg-success">已确认</span>',
        'parsed': '<span class="badge bg-success">已解析</span>',
        'failed': '<span class="badge bg-danger">失败</span>'
    };
    return statusMap[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取文件图标
function getFileIcon(fileType) {
    const iconMap = {
        'xlsx': 'excel',
        'xls': 'excel',
        'pdf': 'pdf',
        'doc': 'word',
        'docx': 'word'
    };
    return iconMap[fileType] || 'alt';
}

// ==================== 新的业务流程函数 ====================

// 步骤1: 解析表格数据
function parseTableData(uploadId) {
    if (!confirm('步骤1: 解析表格数据\n\n将解析文件内容，提取表格结构和数据\n\n确定要执行此步骤吗？')) {
        return;
    }

    currentUploadId = uploadId;

    // 显示加载状态
    const button = event.target;
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 解析中...';

    $.ajax({
        url: `/rules/api/converter/parse/${uploadId}`,
        type: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            loadUploadHistory(); // 刷新列表

            // 如果有下一步提示，自动提示用户
            if (response.next_step === 'view_table') {
                setTimeout(() => {
                    showAlert('表格解析完成！请点击"2.查看数据"按钮查看解析结果', 'info');
                }, 1000);
            }
        } else {
            showAlert('解析失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('表格解析失败', 'danger');
    })
    .always(function() {
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// 步骤2: 查看原始表格数据
function viewTableData(uploadId) {
    currentUploadId = uploadId;

    // 获取表格数据
    $.get(`/rules/api/converter/table/${uploadId}`)
        .done(function(response) {
            if (response.success) {
                const tableData = response.table_data;

                // 更新模态框内容
                $('#modalTableFileName').text(`- ${tableData.filename}`);
                $('#totalRowsCount').text(tableData.rows.length);
                $('#headerColumnsCount').text(tableData.headers.length);
                $('#fileStatus').text(tableData.status);

                // 显示Markdown格式的表格内容
                $('#tableDataContent').text(response.markdown);

                // 显示模态框
                $('#tableDataModal').modal('show');
            } else {
                showAlert('获取表格数据失败: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('获取表格数据失败', 'danger');
        });
}

// 步骤3: 启动智能转换
function startIntelligentConversion(uploadId) {
    if (!confirm('步骤3: 启动智能转换\n\n将逐行推送给AI大模型，转换为19列标准格式\n\n确定要开始转换吗？')) {
        return;
    }

    currentUploadId = uploadId;

    // 这里将实现逐行AI转换功能
    showAlert('智能转换功能开发中...', 'info');
}

// 步骤4: 查看转换结果
function viewConversionResults(uploadId) {
    currentUploadId = uploadId;

    // 这里将显示转换后的19列标准格式数据
    showAlert('查看转换结果功能开发中...', 'info');
}

// 步骤5: 确认修正
function startConfirmation(uploadId) {
    currentUploadId = uploadId;

    // 这里将实现确认修正功能
    showAlert('确认修正功能开发中...', 'info');
}

// 步骤6: 导出最终数据
function exportFinalData(uploadId) {
    if (!confirm('步骤6: 导出最终数据\n\n将导出确认后的19列标准格式Excel文件\n\n确定要导出吗？')) {
        return;
    }

    // 直接下载文件
    window.location.href = `/rules/api/converter/export/${uploadId}`;
    showAlert('文件导出中，请稍候...', 'info');
}

// 从表格数据模态框确认并开始转换
function proceedToConversion() {
    if (!currentUploadId) {
        showAlert('请先选择文件', 'warning');
        return;
    }

    const startRow = parseInt($('#dataStartRow').val()) || 2;
    const mode = $('#conversionMode').val();

    if (!confirm(`确认转换设置：\n\n数据起始行: 第${startRow}行\n转换模式: ${mode === 'intelligent' ? '智能识别模式' : '结构化模式'}\n\n确定要开始转换吗？`)) {
        return;
    }

    // 关闭模态框
    $('#tableDataModal').modal('hide');

    // 启动转换
    setTimeout(() => {
        startIntelligentConversion(currentUploadId);
    }, 500);
}

// 解析上传文件（保留原有功能）
function parseUpload(uploadId) {
    currentUploadId = uploadId;
    startParsing();
}

// 查看规则
function viewRules(uploadId) {
    currentUploadId = uploadId;

    // 获取上传信息
    $.get(`/rules/api/converter/uploads/${uploadId}`)
        .done(function(response) {
            if (response.success) {
                $('#modalFileName').text(`- ${response.upload.original_filename}`);
            }
        });

    // 加载规则
    loadRules(uploadId);
    $('#rulesModal').modal('show');
}

// 加载规则
function loadRules(uploadId, page = 1) {
    $.get(`/rules/api/converter/rules/${uploadId}?page=${page}&per_page=50`)
        .done(function(response) {
            if (response.success) {
                currentRules = response.rules;
                renderRules(response.rules);
                updateRulesStats(response.rules);
            } else {
                showAlert('加载规则失败: ' + response.error, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载规则失败', 'danger');
        });
}

// 渲染标准化规则列表（19列格式）
function renderRules(rules) {
    const tbody = $('#rulesTableBody');
    tbody.empty();

    if (rules.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="15" class="text-center text-muted">暂无规则数据</td>
            </tr>
        `);
        return;
    }

    rules.forEach(rule => {
        const confidenceBadge = getConfidenceBadge(rule.confidence);
        const statusBadge = getRuleStatusBadge(rule.status);

        tbody.append(`
            <tr>
                <td>
                    <input type="checkbox" class="rule-checkbox" value="${rule.id}" onchange="updateSelectedRules()">
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 100px;" title="${rule.rule_source || ''}">
                        ${rule.rule_source || '-'}
                    </div>
                </td>
                <td>${rule.city || '-'}</td>
                <td>${rule.sequence_number || '-'}</td>
                <td>
                    <div class="text-truncate" style="max-width: 80px;" title="${rule.department || ''}">
                        ${rule.department || '-'}
                    </div>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 80px;" title="${rule.violation_type || ''}">
                        ${rule.violation_type || '-'}
                    </div>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 150px;" title="${rule.rule_name || ''}">
                        ${rule.rule_name || '-'}
                    </div>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 150px;" title="${rule.rule_content || ''}">
                        ${rule.rule_content || '-'}
                    </div>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 100px;" title="${rule.medical_name1 || ''}">
                        ${rule.medical_name1 || '-'}
                    </div>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 100px;" title="${rule.medical_name2 || ''}">
                        ${rule.medical_name2 || '-'}
                    </div>
                </td>
                <td>${rule.type || '-'}</td>
                <td>${rule.time_type || '-'}</td>
                <td>${confidenceBadge}</td>
                <td>${statusBadge}</td>
                <td>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editRule(${rule.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm ms-1" onclick="approveRule(${rule.id})" title="确认">
                        <i class="fas fa-check"></i>
                    </button>
                </td>
            </tr>
        `);
    });
}

// 获取置信度徽章
function getConfidenceBadge(confidence) {
    const percent = Math.round(confidence * 100);
    if (percent >= 80) {
        return `<span class="badge bg-success">${percent}%</span>`;
    } else if (percent >= 60) {
        return `<span class="badge bg-warning">${percent}%</span>`;
    } else {
        return `<span class="badge bg-danger">${percent}%</span>`;
    }
}

// 获取规则状态徽章
function getRuleStatusBadge(status) {
    const statusMap = {
        'pending_review': '<span class="badge bg-warning">待校对</span>',
        'reviewed': '<span class="badge bg-info">已校对</span>',
        'approved': '<span class="badge bg-success">已确认</span>',
        'rejected': '<span class="badge bg-danger">已拒绝</span>'
    };
    return statusMap[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取严重程度徽章
function getSeverityBadge(severity) {
    const severityMap = {
        '高': '<span class="badge bg-danger">高</span>',
        '中': '<span class="badge bg-warning">中</span>',
        '低': '<span class="badge bg-success">低</span>'
    };
    return severityMap[severity] || '<span class="badge bg-secondary">-</span>';
}

// 更新规则统计
function updateRulesStats(rules) {
    const total = rules.length;
    const approved = rules.filter(r => r.status === 'approved').length;
    const pending = rules.filter(r => r.status === 'pending_review').length;
    const rejected = rules.filter(r => r.status === 'rejected').length;

    $('#statsTotal').text(total);
    $('#statsApproved').text(approved);
    $('#statsPending').text(pending);
    $('#statsRejected').text(rejected);
}

// 工具函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function showAlert(message, type = 'info') {
    // 这里可以使用Bootstrap的Alert组件或其他通知组件
    const alertClass = `alert-${type}`;
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 添加到页面顶部
    $('body').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        $('.alert').alert('close');
    }, 3000);
}

// 启动当前上传文件的字段确认
function startFieldConfirmationForCurrentUpload() {
    if (!currentUploadId) {
        showAlert('请先选择一个上传文件', 'warning');
        return;
    }

    if (currentRules.length === 0) {
        showAlert('当前文件没有解析出规则', 'warning');
        return;
    }

    // 启动字段确认流程
    startFieldConfirmation(currentUploadId, currentRules);
}

// 删除上传记录
function deleteUpload(uploadId) {
    if (!confirm('确定要删除这个上传记录吗？删除后将无法恢复。')) {
        return;
    }

    $.ajax({
        url: `/rules/api/converter/uploads/${uploadId}`,
        type: 'DELETE',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert('删除成功', 'success');
            loadUploadHistory(); // 刷新列表
        } else {
            showAlert('删除失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('删除失败', 'danger');
    });
}

// 导出规则
function exportUploadRules(uploadId) {
    // 直接下载文件
    window.location.href = `/rules/api/converter/export/${uploadId}`;
}

// 批量操作相关函数
function updateSelectedRules() {
    selectedRules = [];
    $('.rule-checkbox:checked').each(function() {
        selectedRules.push(parseInt($(this).val()));
    });
}

function toggleSelectAll() {
    const selectAll = $('#selectAll').prop('checked');
    $('.rule-checkbox').prop('checked', selectAll);
    updateSelectedRules();
}

function batchApprove() {
    if (selectedRules.length === 0) {
        showAlert('请先选择要确认的规则', 'warning');
        return;
    }

    if (!confirm(`确定要批量确认 ${selectedRules.length} 条规则吗？`)) {
        return;
    }

    batchUpdateRules('approve');
}

function batchReview() {
    if (selectedRules.length === 0) {
        showAlert('请先选择要校对的规则', 'warning');
        return;
    }

    batchUpdateRules('review');
}

function batchReject() {
    if (selectedRules.length === 0) {
        showAlert('请先选择要拒绝的规则', 'warning');
        return;
    }

    if (!confirm(`确定要批量拒绝 ${selectedRules.length} 条规则吗？`)) {
        return;
    }

    batchUpdateRules('reject');
}

function batchDelete() {
    if (selectedRules.length === 0) {
        showAlert('请先选择要删除的规则', 'warning');
        return;
    }

    if (!confirm(`确定要批量删除 ${selectedRules.length} 条规则吗？删除后无法恢复。`)) {
        return;
    }

    batchUpdateRules('delete');
}

function batchUpdateRules(action) {
    $.ajax({
        url: '/rules/api/converter/rules/batch',
        type: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            rule_ids: selectedRules,
            action: action
        })
    })
    .done(function(response) {
        if (response.success) {
            showAlert(response.message, 'success');
            loadRules(currentUploadId); // 刷新规则列表
            selectedRules = [];
            $('#selectAll').prop('checked', false);
        } else {
            showAlert('批量操作失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('批量操作失败', 'danger');
    });
}

// 单个规则操作
function editRule(ruleId) {
    // 这里可以实现编辑规则的功能
    showAlert('编辑功能开发中', 'info');
}

function approveRule(ruleId) {
    $.ajax({
        url: `/rules/api/converter/rules/${ruleId}/approve`,
        type: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert('规则确认成功', 'success');
            loadRules(currentUploadId); // 刷新规则列表
        } else {
            showAlert('确认失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('确认失败', 'danger');
    });
}

function rejectRule(ruleId) {
    if (!confirm('确定要拒绝这条规则吗？')) {
        return;
    }

    $.ajax({
        url: `/rules/api/converter/rules/${ruleId}/reject`,
        type: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            showAlert('规则拒绝成功', 'success');
            loadRules(currentUploadId); // 刷新规则列表
        } else {
            showAlert('拒绝失败: ' + response.error, 'danger');
        }
    })
    .fail(function() {
        showAlert('拒绝失败', 'danger');
    });
}

// 导出规则（从模态框）
function exportRules() {
    if (!currentUploadId) {
        showAlert('请先选择一个上传文件', 'warning');
        return;
    }

    exportUploadRules(currentUploadId);
}

// ==================== 步骤化操作函数 ====================

// 步骤3: 为特定上传文件启动字段确认
function startFieldConfirmationForUpload(uploadId) {
    // 先获取规则列表
    $.get(`/rules/api/converter/rules/${uploadId}?page=1&per_page=100`)
        .done(function(response) {
            if (response.success && response.rules.length > 0) {
                // 启动字段确认流程
                startFieldConfirmation(uploadId, response.rules);
            } else {
                showAlert('该文件没有解析出规则或规则加载失败', 'warning');
            }
        })
        .fail(function() {
            showAlert('加载规则失败', 'danger');
        });
}

// 步骤4: 为特定上传文件进行批量校对
function batchReviewForUpload(uploadId) {
    if (!confirm('确定要将该文件的所有规则标记为"已校对"状态吗？')) {
        return;
    }

    // 获取该上传文件的所有规则ID
    $.get(`/rules/api/converter/rules/${uploadId}?page=1&per_page=1000`)
        .done(function(response) {
            if (response.success && response.rules.length > 0) {
                const ruleIds = response.rules.map(rule => rule.id);

                // 批量更新为校对状态
                $.ajax({
                    url: '/rules/api/converter/rules/batch',
                    type: 'POST',
                    headers: {
                        'X-CSRFToken': $('meta[name=csrf-token]').attr('content'),
                        'Content-Type': 'application/json'
                    },
                    data: JSON.stringify({
                        rule_ids: ruleIds,
                        action: 'review'
                    })
                })
                .done(function(batchResponse) {
                    if (batchResponse.success) {
                        showAlert(`批量校对完成，共处理 ${ruleIds.length} 条规则`, 'success');
                        loadUploadHistory(); // 刷新上传历史
                    } else {
                        showAlert('批量校对失败: ' + batchResponse.error, 'danger');
                    }
                })
                .fail(function() {
                    showAlert('批量校对失败', 'danger');
                });
            } else {
                showAlert('该文件没有规则可以校对', 'warning');
            }
        })
        .fail(function() {
            showAlert('获取规则列表失败', 'danger');
        });
}

// 步骤确认对话框
function showStepConfirmation(step, uploadId, callback) {
    const stepNames = {
        1: '解析文件',
        2: '查看规则',
        3: '字段确认',
        4: '批量校对',
        5: '导出数据'
    };

    const stepDescriptions = {
        1: '将使用AI智能解析文件内容，提取医保违规规则',
        2: '查看AI解析出的规则列表，检查提取效果',
        3: '对低置信度的规则进行人工确认和校正',
        4: '将所有规则标记为已校对状态',
        5: '导出标准化的Excel文件'
    };

    const message = `步骤 ${step}: ${stepNames[step]}\n\n${stepDescriptions[step]}\n\n确定要执行此步骤吗？`;

    if (confirm(message)) {
        callback();
    }
}

// 重写原有函数，添加步骤确认
function parseUpload(uploadId) {
    showStepConfirmation(1, uploadId, function() {
        currentUploadId = uploadId;
        startParsing();
    });
}

function viewRules(uploadId) {
    showStepConfirmation(2, uploadId, function() {
        currentUploadId = uploadId;

        // 获取上传信息
        $.get(`/rules/api/converter/uploads/${uploadId}`)
            .done(function(response) {
                if (response.success) {
                    $('#modalFileName').text(`- ${response.upload.original_filename}`);
                }
            });

        // 加载规则
        loadRules(uploadId);
        $('#rulesModal').modal('show');
    });
}

function exportUploadRules(uploadId) {
    showStepConfirmation(5, uploadId, function() {
        // 直接下载文件
        window.location.href = `/rules/api/converter/export/${uploadId}`;
        showAlert('文件导出中，请稍候...', 'info');
    });
}
</script>

<!-- 引入字段确认功能 -->
<script src="{{ url_for('static', filename='js/field-confirmation.js') }}"></script>

<!-- 字段确认模态框 -->
{% include 'rules/field_confirmation_modal.html' %}

{% endblock %}