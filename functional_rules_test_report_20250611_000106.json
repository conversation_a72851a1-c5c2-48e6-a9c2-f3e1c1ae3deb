{"timestamp": "2025-06-11T00:01:06.525543", "test_type": "functional", "summary": {"total": 7, "passed": 3, "failed": 4, "errors": 0, "pass_rate": 42.857142857142854}, "results": [{"name": "规则API数据结构测试", "status": "FAIL", "duration": 0.009200334548950195, "details": "", "error": "请求异常: Expecting value: line 1 column 1 (char 0)"}, {"name": "规则搜索功能测试", "status": "FAIL", "duration": 0.01432180404663086, "details": "", "error": "搜索测试异常: Expecting value: line 1 column 1 (char 0)"}, {"name": "规则分页功能测试", "status": "FAIL", "duration": 0.00901031494140625, "details": "", "error": "分页测试异常: Expecting value: line 1 column 1 (char 0)"}, {"name": "数据库查询功能测试", "status": "FAIL", "duration": 0.005242824554443359, "details": "", "error": "页面缺少元素: ['SQL查询', '执行查询', 'textarea']"}, {"name": "静态资源加载测试", "status": "PASS", "duration": 0.019221067428588867, "details": "成功加载: Bootstrap CSS, Bootstrap JS; 失败: 应用样式(404)", "error": null}, {"name": "错误处理测试", "status": "PASS", "duration": 0.010967493057250977, "details": "错误处理正常，无效请求不会导致500错误", "error": null}, {"name": "性能指标测试", "status": "PASS", "duration": 0.012659549713134766, "details": "性能良好，50条记录加载时间: 0.01s", "error": null}]}