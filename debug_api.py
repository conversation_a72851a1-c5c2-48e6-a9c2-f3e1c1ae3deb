#!/usr/bin/env python3
"""
调试API返回内容
"""

import requests

def debug_api():
    base_url = "http://127.0.0.1:5002"
    
    # 测试规则API
    print("🔍 调试规则API...")
    try:
        response = requests.get(f"{base_url}/rules/api/rules")
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        print(f"响应长度: {len(response.text)}")
        print("响应内容前500字符:")
        print("-" * 50)
        print(response.text[:500])
        print("-" * 50)
        
        if response.headers.get('Content-Type', '').startswith('application/json'):
            try:
                data = response.json()
                print("JSON解析成功:")
                print(f"Keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            except Exception as e:
                print(f"JSON解析失败: {e}")
        
    except Exception as e:
        print(f"请求失败: {e}")
    
    print("\n" + "="*60)
    
    # 测试搜索API
    print("🔍 调试搜索API...")
    try:
        response = requests.get(f"{base_url}/rules/api/search")
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        print(f"响应长度: {len(response.text)}")
        print("响应内容前500字符:")
        print("-" * 50)
        print(response.text[:500])
        print("-" * 50)
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == '__main__':
    debug_api()
