{% extends "base.html" %}

{% block title %}规则导入 - 自查自纠{% endblock %}

{% block extra_css %}
<style>
/* 排序相关样式 */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.sort-icon {
    margin-left: 0.5rem;
    opacity: 0.6;
    font-size: 0.8rem;
}

.sortable.sort-asc .sort-icon {
    opacity: 1;
}

.sortable.sort-desc .sort-icon {
    opacity: 1;
}

.sortable.sort-asc .sort-icon:before {
    content: "\f0de"; /* fa-sort-up */
}

.sortable.sort-desc .sort-icon:before {
    content: "\f0dd"; /* fa-sort-down */
}
</style>
{% endblock %}

{% block page_title %}
<i class="fas fa-download me-2"></i>规则导入
{% endblock %}

{% block content %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<div class="container-fluid">
    <!-- 操作工具栏 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <!-- 第一行搜索条件 -->
                            <div class="row g-2 mb-2">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" id="searchRuleName" placeholder="规则名称">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="searchCity">
                                        <option value="">所有城市</option>
                                        <!-- 城市选项通过JavaScript动态加载 -->
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="searchRuleSource">
                                        <option value="">所有规则来源</option>
                                        <!-- 规则来源选项通过JavaScript动态加载 -->
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="searchImported">
                                        <option value="">全部</option>
                                        <option value="false">未导入</option>
                                        <option value="true">已导入</option>
                                    </select>
                                </div>
                            </div>
                            <!-- 第二行搜索条件 -->
                            <div class="row g-2">
                                <div class="col-md-3">
                                    <select class="form-select" id="searchRuleType">
                                        <option value="">所有规则类型</option>
                                        <!-- 规则类型选项通过JavaScript动态加载 -->
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="searchVisitType">
                                        <option value="">所有用途</option>
                                        <!-- 用途选项通过JavaScript动态加载 -->
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-primary" onclick="searchRules()">
                                        <i class="fas fa-search me-1"></i>搜索
                                    </button>
                                </div>
                                <div class="col-md-1">
                                    <button type="button" class="btn btn-secondary" onclick="resetSearch()">
                                        <i class="fas fa-undo me-1"></i>重置
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <button type="button" class="btn btn-success" onclick="importSelected()" id="importBtn" disabled>
                                <i class="fas fa-download me-1"></i>导入选中 (<span id="selectedCount">0</span>)
                            </button>
                            <a href="{{ url_for('selfcheck.rules') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-arrow-left me-1"></i>返回规则管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 规则列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <!-- 表格上方的分页控制区域 -->
                    <div class="row mb-3 align-items-center">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0 small">每页显示:</label>
                                <select class="form-select form-select-sm" id="rulesPerPage" style="width: auto;" onchange="changeRulesPerPage()">
                                    <option value="10">10条</option>
                                    <option value="20" selected>20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <nav aria-label="规则分页">
                                <ul class="pagination pagination-sm justify-content-center mb-0" id="rulesPagination">
                                    <!-- 分页按钮将通过JavaScript生成 -->
                                </ul>
                            </nav>
                        </div>
                        <div class="col-md-3">
                            <div id="rulesPaginationInfo" class="text-end small text-muted"></div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="rulesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th width="100">对照ID</th>
                                    <th class="sortable" data-sort="rule_name">
                                        规则名称 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="rule_type">
                                        规则类型 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="city">
                                        城市 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="medical_behavior">
                                        医疗行为 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="rule_source">
                                        规则来源 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th width="100">用途</th>
                                    <th class="sortable" data-sort="create_time">
                                        创建时间 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="is_imported">
                                        导入状态 <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="rulesTableBody">
                                <!-- 数据通过AJAX加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 表格下方的分页控制区域 -->
                    <div class="row mt-3 align-items-center">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0 small">每页显示:</label>
                                <select class="form-select form-select-sm" id="rulesPerPageBottom" style="width: auto;" onchange="changeRulesPerPageFromBottom()">
                                    <option value="10">10条</option>
                                    <option value="20" selected>20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <nav aria-label="规则分页">
                                <ul class="pagination pagination-sm justify-content-center mb-0" id="rulesPaginationBottom">
                                    <!-- 分页按钮将通过JavaScript生成 -->
                                </ul>
                    </nav>
                        </div>
                        <div class="col-md-3">
                            <div id="rulesPaginationInfoBottom" class="text-end small text-muted"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导入进度模态框 -->
<div class="modal fade" id="importProgressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download me-2"></i>规则导入进度
                </h5>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>导入进度</span>
                        <span id="progressText">0 / 0</span>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar"
                             id="importProgressBar"
                             style="width: 0%"
                             aria-valuenow="0"
                             aria-valuemin="0"
                             aria-valuemax="100">
                            0%
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title text-success">
                                    <i class="fas fa-check-circle"></i> 成功
                                </h5>
                                <h3 class="text-success" id="successCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h5 class="card-title text-danger">
                                    <i class="fas fa-times-circle"></i> 失败
                                </h5>
                                <h3 class="text-danger" id="errorCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="card-title text-info">
                                    <i class="fas fa-clock"></i> 耗时
                                </h5>
                                <h3 class="text-info" id="elapsedTime">0s</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <h6>实时日志</h6>
                    <div id="importLog" class="border rounded p-2" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; font-family: monospace; font-size: 12px;">
                        <!-- 日志内容 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="closeProgressModal" disabled>关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 规则详情模态框 -->
<div class="modal fade" id="ruleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">规则详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="ruleDetailContent">
                <!-- 详情内容通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let totalPages = 1;
let currentPerPage = 20;
let currentSortBy = 'create_time';
let currentSortOrder = 'desc';
let selectedRules = new Set();
let currentRulesData = []; // 存储当前页面的规则数据

// 页面加载完成后初始化
$(document).ready(function() {
    loadSearchOptions();
    loadRules(1);

    // 初始化排序事件
    initSortEvents();

    // 初始化进度模态框关闭事件
    $('#closeProgressModal').click(function() {
        $('#importProgressModal').modal('hide');
    });
});

// 加载所有搜索选项
function loadSearchOptions() {
    // 加载规则来源选项
    loadRuleSourceOptions();
    // 加载城市选项
    loadCityOptions();
    // 加载规则类型选项
    loadRuleTypeOptions();
    // 加载用途选项
    loadVisitTypeOptions();
}

// 加载规则来源选项
function loadRuleSourceOptions() {
    $.get('/selfcheck/api/rules/rule-sources')
        .done(function(response) {
            if (response.success) {
                const select = $('#searchRuleSource');
                select.empty().append('<option value="">所有规则来源</option>');

                response.rule_sources.forEach(source => {
                    if (source) {
                        select.append(`<option value="${source}">${source}</option>`);
                    }
                });
            }
        })
        .fail(function() {
            console.log('加载规则来源选项失败');
        });
}

// 加载城市选项
function loadCityOptions() {
    $.get('/selfcheck/api/rules/cities')
        .done(function(response) {
            if (response.success) {
                const select = $('#searchCity');
                select.empty().append('<option value="">所有城市</option>');

                response.cities.forEach(city => {
                    if (city) {
                        select.append(`<option value="${city}">${city}</option>`);
                    }
                });
            }
        })
        .fail(function() {
            console.log('加载城市选项失败');
        });
}

// 加载规则类型选项
function loadRuleTypeOptions() {
    $.get('/selfcheck/api/rules/rule-types')
        .done(function(response) {
            if (response.success) {
                const select = $('#searchRuleType');
                select.empty().append('<option value="">所有规则类型</option>');

                response.rule_types.forEach(type => {
                    if (type) {
                        select.append(`<option value="${type}">${type}</option>`);
                    }
                });
            }
        })
        .fail(function() {
            console.log('加载规则类型选项失败');
        });
}

// 加载用途选项
function loadVisitTypeOptions() {
    $.get('/selfcheck/api/rules/visit-types')
        .done(function(response) {
            if (response.success) {
                const select = $('#searchVisitType');
                select.empty().append('<option value="">所有用途</option>');

                response.visit_types.forEach(type => {
                    if (type) {
                        select.append(`<option value="${type}">${type}</option>`);
                    }
                });
            }
        })
        .fail(function() {
            console.log('加载用途选项失败');
        });
}

// 加载规则列表
function loadRules(page = 1) {
    currentPage = page;
    
    const params = new URLSearchParams({
        page: page,
        per_page: currentPerPage,
        sort_by: currentSortBy,
        sort_order: currentSortOrder,
        rule_name: $('#searchRuleName').val(),
        city: $('#searchCity').val(),
        rule_type: $('#searchRuleType').val(),
        rule_source: $('#searchRuleSource').val(),
        visit_type: $('#searchVisitType').val(),
        is_imported: $('#searchImported').val()
    });
    
    // 移除空参数
    for (let [key, value] of [...params]) {
        if (!value) params.delete(key);
    }
    
    $.get(`/selfcheck/api/rules/importable?${params.toString()}`)
        .done(function(response) {
            if (response.success) {
                currentRulesData = response.rules; // 保存当前页面的规则数据
                renderRulesTable(response.rules);
                renderRulesPagination(response.page, response.pages, response.total, response.per_page);
                updateSortIcons();
            } else {
                showAlert('加载规则列表失败: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('加载规则列表失败', 'danger');
        });
}

// 渲染规则表格
function renderRulesTable(rules) {
    const tbody = $('#rulesTableBody');
    tbody.empty();
    
    if (rules.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="11" class="text-center text-muted">暂无数据</td>
            </tr>
        `);
        return;
    }
    
    rules.forEach(rule => {
        const isImported = rule.is_imported === 'Y';
        const importedBadge = isImported 
            ? '<span class="badge bg-success">已导入</span>'
            : '<span class="badge bg-warning">未导入</span>';
            
        const checkbox = isImported 
            ? '<input type="checkbox" disabled>'
            : `<input type="checkbox" value="${rule.rule_id}" onchange="toggleRuleSelection(${rule.rule_id})">`;
            
        const actions = `
            <button class="btn btn-sm btn-outline-primary" onclick="viewRule(${rule.rule_id})" title="查看">
                <i class="fas fa-eye"></i>
            </button>
        `;
        
        // 显示唯一标识信息
        const uniqueInfo = rule.unique_key ?
            `<small class="text-muted d-block">${rule.unique_key}</small>` : '';

        tbody.append(`
            <tr class="${isImported ? 'table-secondary' : ''}">
                <td>${checkbox}</td>
                <td>
                    <strong>${rule.compare_id || '-'}</strong>
                    ${uniqueInfo}
                </td>
                <td>${rule.rule_name}</td>
                <td>${rule.rule_type || '-'}</td>
                <td>${rule.city || '-'}</td>
                <td>${rule.medical_behavior || '-'}</td>
                <td>${rule.rule_source || '-'}</td>
                <td>${rule.visit_type || '-'}</td>
                <td>${formatDateTime(rule.create_time)}</td>
                <td>${importedBadge}</td>
                <td>${actions}</td>
            </tr>
        `);
    });
    
    updateSelectedCount();
}

// 渲染规则分页
function renderRulesPagination(page, pages, total, perPage) {
    const paginationInfo = `共 ${total} 条记录，第 ${page} 页，共 ${pages} 页`;

    // 更新上方和下方的分页信息
    $('#rulesPaginationInfo').text(paginationInfo);
    $('#rulesPaginationInfoBottom').text(paginationInfo);

    // 同步每页显示数量选择器
    $('#rulesPerPage').val(perPage);
    $('#rulesPerPageBottom').val(perPage);

    // 生成分页按钮HTML
    function generatePaginationHTML() {
        if (pages <= 1) return '';

        let html = '';
    
    // 上一页
        html += `
            <li class="page-item ${page <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadRules(${page - 1})">上一页</a>
            </li>
        `;
    
    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(pages, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="loadRules(${i})">${i}</a>
                </li>
            `;
    }
    
    // 下一页
        html += `
            <li class="page-item ${page >= pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadRules(${page + 1})">下一页</a>
            </li>
        `;

        return html;
    }

    // 更新上方和下方的分页按钮
    const paginationHTML = generatePaginationHTML();
    $('#rulesPagination').html(paginationHTML);
    $('#rulesPaginationBottom').html(paginationHTML);
}

// 改变每页显示数量（上方控制）
function changeRulesPerPage() {
    const perPage = $('#rulesPerPage').val();
    $('#rulesPerPageBottom').val(perPage); // 同步下方选择器
    currentPerPage = parseInt(perPage);
    loadRules(1); // 重置到第一页
}

// 改变每页显示数量（下方控制）
function changeRulesPerPageFromBottom() {
    const perPage = $('#rulesPerPageBottom').val();
    $('#rulesPerPage').val(perPage); // 同步上方选择器
    currentPerPage = parseInt(perPage);
    loadRules(1); // 重置到第一页
}

// 搜索规则
function searchRules() {
    selectedRules.clear();
    $('#selectAll').prop('checked', false);
    loadRules(1);
}

// 重置搜索
function resetSearch() {
    $('#searchRuleName').val('');
    $('#searchCity').val('');
    $('#searchRuleType').val('');
    $('#searchRuleSource').val('');
    $('#searchVisitType').val('');
    $('#searchImported').val('');
    selectedRules.clear();
    $('#selectAll').prop('checked', false);
    loadRules(1);
}

// 全选/取消全选
function toggleSelectAll() {
    const isChecked = $('#selectAll').prop('checked');
    console.log('toggleSelectAll called, isChecked:', isChecked);

    $('input[type="checkbox"][value]').each(function() {
        if (!$(this).prop('disabled')) {
            $(this).prop('checked', isChecked);
            const ruleId = parseInt($(this).val());
            console.log('Processing rule:', ruleId, 'isChecked:', isChecked);
            if (isChecked) {
                selectedRules.add(ruleId);
            } else {
                selectedRules.delete(ruleId);
            }
        }
    });

    console.log('After toggleSelectAll, selectedRules:', Array.from(selectedRules));
    updateSelectedCount();
}

// 切换规则选择状态
function toggleRuleSelection(ruleId) {
    console.log('toggleRuleSelection called with ruleId:', ruleId);
    const checkbox = $(`input[value="${ruleId}"]`);
    console.log('Checkbox found:', checkbox.length, 'checked:', checkbox.prop('checked'));

    if (checkbox.prop('checked')) {
        selectedRules.add(ruleId);
        console.log('Added rule to selection:', ruleId);
    } else {
        selectedRules.delete(ruleId);
        console.log('Removed rule from selection:', ruleId);
    }

    console.log('Current selectedRules:', Array.from(selectedRules));
    updateSelectedCount();

    // 更新全选状态
    const totalCheckboxes = $('input[type="checkbox"][value]:not(:disabled)').length;
    const checkedCheckboxes = $('input[type="checkbox"][value]:checked').length;
    $('#selectAll').prop('checked', totalCheckboxes > 0 && checkedCheckboxes === totalCheckboxes);
}

// 更新选中数量
function updateSelectedCount() {
    const count = selectedRules.size;
    console.log('updateSelectedCount called, count:', count);
    $('#selectedCount').text(count);

    if (count === 0) {
        $('#importBtn').prop('disabled', true).html('<i class="fas fa-download me-1"></i>导入选中 (<span id="selectedCount">0</span>)');
    } else {
        $('#importBtn').prop('disabled', false).html(`<i class="fas fa-download me-1"></i>导入选中 (<span id="selectedCount">${count}</span>)`);
    }
}

// 导入选中的规则
function importSelected() {
    console.log('importSelected called, selectedRules:', selectedRules);

    if (selectedRules.size === 0) {
        showAlert('请选择要导入的规则', 'warning');
        return;
    }

    if (!confirm(`确定要导入选中的 ${selectedRules.size} 个规则吗？\n\n注意：导入过程将使用并发处理以提高速度。`)) {
        return;
    }

    const ruleIds = Array.from(selectedRules);
    console.log('Sending rule_ids:', ruleIds);

    // 从当前页面数据中提取选中规则的 compare_id 和 visit_type
    const compareIds = [];
    const visitTypes = [];

    ruleIds.forEach(ruleId => {
        const ruleData = currentRulesData.find(rule => rule.rule_id === ruleId);
        if (ruleData && ruleData.compare_id && ruleData.visit_type) {
            compareIds.push(ruleData.compare_id);
            visitTypes.push(ruleData.visit_type);
        }
    });

    console.log('Compare IDs:', compareIds);
    console.log('Visit Types:', visitTypes);

    if (compareIds.length === 0 || visitTypes.length === 0) {
        showAlert('无法获取选中规则的详细信息，请刷新页面后重试', 'danger');
        return;
    }

    // 显示进度模态框
    showImportProgress(compareIds.length);

    // 禁用导入按钮，防止重复点击
    $('#importBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>正在并发导入...');

    // 获取CSRF令牌
    const csrfToken = $('meta[name=csrf-token]').attr('content');

    const startTime = Date.now();
    let progressTimer = null;

    // 启动进度更新定时器
    progressTimer = setInterval(() => {
        const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
        $('#elapsedTime').text(elapsed + 's');
    }, 100);

    $.ajax({
        url: '/selfcheck/api/rules/import-from-history',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': csrfToken
        },
        data: JSON.stringify({ compare_ids: compareIds, visit_types: visitTypes }),
        timeout: 120000 // 增加到120秒超时
    })
    .done(function(response) {
        console.log('Import response:', response);
        clearInterval(progressTimer);

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);

        if (response.success) {
            const successCount = response.imported_count || 0;
            const errorCount = response.errors ? response.errors.length : 0;
            const totalCount = ruleIds.length;

            // 更新进度显示
            updateImportProgress(successCount, errorCount, totalCount, true);
            addImportLog(`✅ 导入完成！耗时 ${duration} 秒`, 'success');
            addImportLog(`📊 总计: ${totalCount}, 成功: ${successCount}, 失败: ${errorCount}`, 'info');

            selectedRules.clear();
            $('#selectAll').prop('checked', false);
            loadRules(currentPage);

            // 显示详细错误信息
            if (response.errors && response.errors.length > 0) {
                response.errors.forEach((error, index) => {
                    addImportLog(`❌ 错误 ${index + 1}: ${error}`, 'error');
                });
            }

            // 启用关闭按钮
            $('#closeProgressModal').prop('disabled', false);

            if (errorCount === 0) {
                showAlert(`✅ 全部导入成功！耗时 ${duration} 秒`, 'success');
            } else {
                showAlert(`⚠️ 部分导入成功，请查看详细信息`, 'warning');
            }
        } else {
            clearInterval(progressTimer);
            addImportLog(`❌ 导入失败：${response.message}`, 'error');
            $('#closeProgressModal').prop('disabled', false);
            showAlert(`❌ 导入失败：${response.message}`, 'danger');
        }
    })
    .fail(function(xhr, status, error) {
        console.error('Import failed:', xhr, status, error);
        clearInterval(progressTimer);

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);

        let errorMessage = `导入失败（耗时 ${duration} 秒）`;
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage += `：${xhr.responseJSON.message}`;
        } else if (status === 'timeout') {
            errorMessage += '：请求超时，可能是因为规则数量较多，请尝试分批导入';
        } else if (xhr.status === 403) {
            errorMessage += '：权限不足，无法导入规则';
        } else if (xhr.status === 500) {
            errorMessage += '：服务器内部错误，请检查日志';
        } else {
            errorMessage += `：${error}`;
        }

        addImportLog(`❌ ${errorMessage}`, 'error');
        $('#closeProgressModal').prop('disabled', false);
        showAlert(`❌ ${errorMessage}`, 'danger');
    })
    .always(function() {
        clearInterval(progressTimer);
        // 恢复导入按钮
        updateSelectedCount();
    });
}

// 显示导入进度模态框
function showImportProgress(totalCount) {
    // 重置进度显示
    $('#importProgressBar').css('width', '0%').attr('aria-valuenow', 0).text('0%');
    $('#progressText').text(`0 / ${totalCount}`);
    $('#successCount').text('0');
    $('#errorCount').text('0');
    $('#elapsedTime').text('0s');
    $('#importLog').empty();
    $('#closeProgressModal').prop('disabled', true);

    // 添加初始日志
    addImportLog(`🚀 开始导入 ${totalCount} 个规则...`, 'info');
    addImportLog(`⚡ 使用并发处理提高导入速度`, 'info');

    // 显示模态框
    $('#importProgressModal').modal('show');
}

// 更新导入进度
function updateImportProgress(successCount, errorCount, totalCount, isComplete = false) {
    const processedCount = successCount + errorCount;
    const percentage = totalCount > 0 ? Math.round((processedCount / totalCount) * 100) : 0;

    // 更新进度条
    $('#importProgressBar').css('width', percentage + '%').attr('aria-valuenow', percentage).text(percentage + '%');
    $('#progressText').text(`${processedCount} / ${totalCount}`);
    $('#successCount').text(successCount);
    $('#errorCount').text(errorCount);

    // 如果完成，更新进度条样式
    if (isComplete) {
        if (errorCount === 0) {
            $('#importProgressBar').removeClass('progress-bar-striped progress-bar-animated')
                                   .addClass('bg-success');
        } else {
            $('#importProgressBar').removeClass('progress-bar-striped progress-bar-animated')
                                   .addClass('bg-warning');
        }
    }
}

// 添加导入日志
function addImportLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    let iconClass = 'fas fa-info-circle';
    let textClass = 'text-info';

    switch(type) {
        case 'success':
            iconClass = 'fas fa-check-circle';
            textClass = 'text-success';
            break;
        case 'error':
            iconClass = 'fas fa-times-circle';
            textClass = 'text-danger';
            break;
        case 'warning':
            iconClass = 'fas fa-exclamation-triangle';
            textClass = 'text-warning';
            break;
    }

    const logEntry = `<div class="${textClass}"><i class="${iconClass}"></i> [${timestamp}] ${message}</div>`;
    $('#importLog').append(logEntry);

    // 自动滚动到底部
    const logContainer = $('#importLog')[0];
    logContainer.scrollTop = logContainer.scrollHeight;
}

// 查看规则详情
function viewRule(ruleId) {
    // 这里需要调用历史规则的详情接口
    // 暂时显示基本信息
    const detailHtml = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            规则ID: ${ruleId}
            <br>
            详细信息请在导入后查看。
        </div>
    `;
    $('#ruleDetailContent').html(detailHtml);
    $('#ruleDetailModal').modal('show');
}

// 使用base.html中的统一showAlert函数

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 初始化排序事件
function initSortEvents() {
    $('.sortable').click(function() {
        const sortBy = $(this).data('sort');

        if (currentSortBy === sortBy) {
            // 如果点击的是当前排序字段，切换排序方向
            currentSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果点击的是新字段，设置为降序
            currentSortBy = sortBy;
            currentSortOrder = 'desc';
        }

        // 重新加载数据
        loadRules(1);
    });
}

// 更新排序图标
function updateSortIcons() {
    // 重置所有排序图标
    $('.sortable').removeClass('sort-asc sort-desc');
    $('.sort-icon').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');

    // 设置当前排序字段的图标
    const currentSortHeader = $(`.sortable[data-sort="${currentSortBy}"]`);
    if (currentSortHeader.length > 0) {
        currentSortHeader.addClass(`sort-${currentSortOrder}`);
        const icon = currentSortHeader.find('.sort-icon');
        icon.removeClass('fa-sort');
        if (currentSortOrder === 'asc') {
            icon.addClass('fa-sort-up');
        } else {
            icon.addClass('fa-sort-down');
        }
    }
}
</script>
{% endblock %}
