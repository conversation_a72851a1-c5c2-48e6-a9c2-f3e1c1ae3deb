"""数据库工具服务类"""

import os
import re
import logging
import pandas as pd
import tempfile
from datetime import datetime
from flask import current_app
from app.utils.database import db_manager, execute_rules_query, handle_db_error, execute_query_in_chunks
from app.utils.file_manager import create_temp_file, sanitize_filename
from app.models.audit import AuditLog

# 添加日志记录器
logger = logging.getLogger(__name__)

class DatabaseQueryService:
    """数据库查询服务类"""

    @staticmethod
    def execute_query_with_config(query, db_type, db_host, db_schema, limit_rows=10, for_export=False):
        """使用指定数据库配置执行查询"""
        try:
            import oracledb
            import pandas as pd

            # 为了安全起见，禁止执行危险的SQL操作（使用单词边界匹配）
            import re
            sql_lower = query.strip().lower()
            dangerous_keywords = ['update', 'delete', 'insert', 'drop', 'create', 'alter', 'truncate', 'grant', 'revoke']

            for keyword in dangerous_keywords:
                # 使用正则表达式检查单词边界，避免误判
                pattern = r'\b' + keyword + r'\b'
                if re.search(pattern, sql_lower):
                    return {'success': False, 'error': f'出于安全考虑，不允许执行包含 {keyword.upper()} 的语句'}

            if db_type.lower() == 'oracle':
                # 使用Oracle数据库
                try:
                    dsn = f"{db_host}:1521/orcl"
                    with oracledb.connect(
                        user="datachange",
                        password="drgs2019",
                        dsn=dsn
                    ) as conn:
                        with conn.cursor() as cursor:
                            # 如果指定了schema，切换到该schema
                            if db_schema:
                                cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {db_schema}")

                            # 处理SQL语句
                            limited_sql = query.strip()
                            if limited_sql.endswith(';'):
                                limited_sql = limited_sql[:-1]  # 移除末尾分号

                            # 只有在非导出模式且没有ROWNUM限制时才添加行数限制
                            if not for_export and 'SELECT' in limited_sql.upper() and 'ROWNUM' not in limited_sql.upper():
                                limited_sql = f"SELECT * FROM ({limited_sql}) WHERE ROWNUM <= {limit_rows}"

                            cursor.execute(limited_sql)

                            # 获取查询结果
                            columns = [col[0] for col in cursor.description] if cursor.description else []
                            rows = cursor.fetchall()

                            # 转换为字典格式
                            data = []
                            for row in rows:
                                row_dict = {}
                                for i, col in enumerate(columns):
                                    value = row[i]
                                    # 处理特殊数据类型
                                    if value is None:
                                        row_dict[col] = None
                                    elif hasattr(value, 'read'):  # CLOB/BLOB
                                        row_dict[col] = str(value.read()) if value else None
                                    else:
                                        row_dict[col] = str(value)
                                data.append(row_dict)

                            return {
                                'success': True,
                                'data': data,
                                'columns': columns,
                                'total': len(data),
                                'displayed': len(data),
                                'truncated': not for_export and len(data) >= limit_rows
                            }

                except Exception as e:
                    error_msg = f'Oracle数据库执行失败: {str(e)}'
                    logging.error(f"Oracle查询执行错误 - 主机: {db_host}, Schema: {db_schema}, SQL: {query[:100]}..., 错误: {str(e)}")
                    return {'success': False, 'error': error_msg}

            elif db_type.lower() == 'postgresql':
                # 使用PostgreSQL数据库
                try:
                    try:
                        # 尝试使用psycopg2
                        import psycopg2
                        conn = psycopg2.connect(
                            host=db_host,
                            port=5432,
                            database="databasetools",
                            user="postgres",
                            password="P@ssw0rd"
                        )
                        with conn.cursor() as cursor:
                            # 如果指定了schema，设置search_path
                            if db_schema:
                                cursor.execute(f'SET search_path TO "{db_schema}", public')

                            # 处理SQL语句
                            limited_sql = query.strip()
                            if not limited_sql.upper().endswith(';'):
                                limited_sql += ';'

                            # 只有在非导出模式且没有LIMIT限制时才添加行数限制
                            if not for_export and 'SELECT' in limited_sql.upper() and 'LIMIT' not in limited_sql.upper():
                                limited_sql = limited_sql.replace(';', f' LIMIT {limit_rows};')

                            cursor.execute(limited_sql)

                            # 获取查询结果
                            columns = [col[0] for col in cursor.description] if cursor.description else []
                            rows = cursor.fetchall()

                            # 转换为字典格式
                            data = []
                            for row in rows:
                                row_dict = {}
                                for i, col in enumerate(columns):
                                    row_dict[col] = str(row[i]) if row[i] is not None else None
                                data.append(row_dict)

                            conn.close()

                            return {
                                'success': True,
                                'data': data,
                                'columns': columns,
                                'total': len(data),
                                'displayed': len(data),
                                'truncated': not for_export and len(data) >= limit_rows
                            }
                    except ImportError:
                        # 如果psycopg2不可用，尝试使用psycopg3
                        import psycopg
                        with psycopg.connect(
                            host=db_host,
                            port=5432,
                            dbname="databasetools",
                            user="postgres",
                            password="P@ssw0rd"
                        ) as conn:
                            with conn.cursor() as cursor:
                                # 如果指定了schema，设置search_path
                                if db_schema:
                                    cursor.execute(f'SET search_path TO "{db_schema}", public')

                                # 处理SQL语句
                                limited_sql = query.strip()
                                if not limited_sql.upper().endswith(';'):
                                    limited_sql += ';'

                                # 只有在非导出模式且没有LIMIT限制时才添加行数限制
                                if not for_export and 'SELECT' in limited_sql.upper() and 'LIMIT' not in limited_sql.upper():
                                    limited_sql = limited_sql.replace(';', f' LIMIT {limit_rows};')

                                cursor.execute(limited_sql)

                                # 获取查询结果
                                columns = [col[0] for col in cursor.description] if cursor.description else []
                                rows = cursor.fetchall()

                                # 转换为字典格式
                                data = []
                                for row in rows:
                                    row_dict = {}
                                    for i, col in enumerate(columns):
                                        row_dict[col] = str(row[i]) if row[i] is not None else None
                                    data.append(row_dict)

                                return {
                                    'success': True,
                                    'data': data,
                                    'columns': columns,
                                    'total': len(data),
                                    'displayed': len(data),
                                    'truncated': not for_export and len(data) >= limit_rows
                                }

                except Exception as e:
                    error_msg = f'PostgreSQL数据库执行失败: {str(e)}'
                    logging.error(f"PostgreSQL查询执行错误 - 主机: {db_host}, Schema: {db_schema}, SQL: {query[:100]}..., 错误: {str(e)}")
                    return {'success': False, 'error': error_msg}

            else:
                return {'success': False, 'error': f'不支持的数据库类型: {db_type}'}

        except Exception as e:
            error_msg = f'数据库查询执行失败: {str(e)}'
            logging.error(f"数据库查询执行异常 - 数据库类型: {db_type}, 主机: {db_host}, Schema: {db_schema}, SQL: {query[:100]}..., 错误: {str(e)}")
            return {'success': False, 'error': error_msg}

    @staticmethod
    def execute_query_to_excel_with_config(query, db_type, db_host, db_schema, filename_prefix="query_result"):
        """使用指定数据库配置执行查询并导出到Excel"""
        try:
            # 执行查询获取全量数据（for_export=True表示导出模式，不限制行数）
            result = DatabaseQueryService.execute_query_with_config(
                query, db_type, db_host, db_schema, limit_rows=10, for_export=True
            )

            if not result['success']:
                return None, result['error']

            if not result['data']:
                return None, "查询结果为空"

            # 转换为DataFrame
            df = pd.DataFrame(result['data'])

            # 生成文件名和路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{filename_prefix}_{timestamp}.xlsx"
            filename = sanitize_filename(filename)
            file_path = create_temp_file(suffix='.xlsx')

            # 导出到Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='查询结果', index=False)

            return file_path, filename

        except Exception as e:
            logger.error(f"导出Excel失败: {str(e)}")
            return None, f"导出失败: {str(e)}"

    @staticmethod
    def execute_query_to_csv_with_config(query, db_type, db_host, db_schema, filename_prefix="query_result"):
        """使用指定数据库配置执行查询并导出到CSV"""
        try:
            # 执行查询获取全量数据（for_export=True表示导出模式，不限制行数）
            result = DatabaseQueryService.execute_query_with_config(
                query, db_type, db_host, db_schema, limit_rows=10, for_export=True
            )

            if not result['success']:
                return None, result['error']

            if not result['data']:
                return None, "查询结果为空"

            # 转换为DataFrame
            df = pd.DataFrame(result['data'])

            # 生成文件名和路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{filename_prefix}_{timestamp}.csv"
            filename = sanitize_filename(filename)
            file_path = create_temp_file(suffix='.csv')

            # 导出到CSV（使用UTF-8编码，支持中文）
            df.to_csv(file_path, index=False, encoding='utf-8-sig')

            return file_path, filename

        except Exception as e:
            logger.error(f"导出CSV失败: {str(e)}")
            return None, f"导出失败: {str(e)}"

    @staticmethod
    def execute_query_to_excel(query, filename_prefix="query_result", chunk_size=50000):
        """执行查询并导出到Excel"""
        try:
            with db_manager.get_connection() as conn:
                # 执行查询
                df = execute_rules_query(conn, query)
                
                if df.empty:
                    return None, "查询结果为空"
                
                # 生成文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{filename_prefix}_{timestamp}.xlsx"
                filename = sanitize_filename(filename)
                
                # 创建临时文件
                temp_file = create_temp_file(suffix='.xlsx')
                
                # 如果数据量大，分批写入
                if len(df) > chunk_size:
                    with pd.ExcelWriter(temp_file, engine='openpyxl') as writer:
                        for i in range(0, len(df), chunk_size):
                            chunk = df.iloc[i:i+chunk_size]
                            sheet_name = f'Sheet{i//chunk_size + 1}'
                            chunk.to_excel(writer, sheet_name=sheet_name, index=False)
                else:
                    df.to_excel(temp_file, index=False)
                
                return temp_file, filename
                
        except Exception as e:
            logging.error(f"执行查询导出Excel失败: {str(e)}")
            raise

    @staticmethod
    def batch_query_to_excel(queries, output_dir=None):
        """批量执行查询并导出到Excel"""
        try:
            if not output_dir:
                output_dir = tempfile.mkdtemp()
            
            results = []
            
            with db_manager.get_connection() as conn:
                for i, query_info in enumerate(queries):
                    query = query_info.get('query', '')
                    name = query_info.get('name', f'Query_{i+1}')
                    
                    if not query.strip():
                        continue
                    
                    try:
                        # 执行查询
                        df = execute_rules_query(conn, query)
                        
                        if not df.empty:
                            # 生成文件名
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            filename = f"{name}_{timestamp}.xlsx"
                            filename = sanitize_filename(filename)
                            
                            file_path = os.path.join(output_dir, filename)
                            df.to_excel(file_path, index=False)
                            
                            results.append({
                                'name': name,
                                'filename': filename,
                                'file_path': file_path,
                                'rows': len(df),
                                'status': 'success'
                            })
                        else:
                            results.append({
                                'name': name,
                                'filename': '',
                                'file_path': '',
                                'rows': 0,
                                'status': 'empty'
                            })
                            
                    except Exception as e:
                        logging.error(f"批量查询 {name} 执行失败: {str(e)}")
                        results.append({
                            'name': name,
                            'filename': '',
                            'file_path': '',
                            'rows': 0,
                            'status': 'error',
                            'error': str(e)
                        })
            
            return results, output_dir
            
        except Exception as e:
            logging.error(f"批量查询执行失败: {str(e)}")
            raise

    @staticmethod
    def test_query_performance(query, iterations=3):
        """测试查询性能"""
        try:
            results = []
            
            with db_manager.get_connection() as conn:
                for i in range(iterations):
                    start_time = datetime.now()
                    
                    # 执行查询
                    df = execute_rules_query(conn, query)
                    
                    end_time = datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    
                    results.append({
                        'iteration': i + 1,
                        'execution_time': execution_time,
                        'rows_returned': len(df) if not df.empty else 0,
                        'start_time': start_time.isoformat(),
                        'end_time': end_time.isoformat()
                    })
            
            # 计算统计信息
            execution_times = [r['execution_time'] for r in results]
            stats = {
                'min_time': min(execution_times),
                'max_time': max(execution_times),
                'avg_time': sum(execution_times) / len(execution_times),
                'total_iterations': iterations,
                'results': results
            }
            
            return stats
            
        except Exception as e:
            logging.error(f"性能测试失败: {str(e)}")
            raise

class SQLGeneratorService:
    """SQL生成器服务类"""
    
    @staticmethod
    def get_sql_templates():
        """获取SQL模板列表"""
        try:
            templates = {
                'basic': [
                    {
                        'name': '基础查询模板',
                        'description': '基础的SELECT查询模板',
                        'template': '''SELECT {columns}
FROM {table_name}
WHERE {conditions}
ORDER BY {order_by}'''
                    },
                    {
                        'name': '分组统计模板',
                        'description': '带GROUP BY的统计查询模板',
                        'template': '''SELECT {group_columns}, 
       COUNT(*) as 记录数,
       SUM({sum_column}) as 总计,
       AVG({avg_column}) as 平均值
FROM {table_name}
WHERE {conditions}
GROUP BY {group_columns}
ORDER BY {order_by}'''
                    }
                ],
                'medical': [
                    {
                        'name': '医保费用查询',
                        'description': '医保费用相关查询模板',
                        'template': '''SELECT 就诊流水号, 患者姓名, 科室名称, 医生姓名,
       费用总额, 医保支付金额, 个人支付金额,
       就诊日期, 出院日期
FROM {table_name}
WHERE 就诊日期 BETWEEN '{start_date}' AND '{end_date}'
  AND 科室名称 LIKE '%{department}%'
ORDER BY 就诊日期 DESC'''
                    },
                    {
                        'name': '药品使用统计',
                        'description': '药品使用情况统计模板',
                        'template': '''SELECT 药品名称, 规格, 
       COUNT(*) as 使用次数,
       SUM(数量) as 总数量,
       SUM(金额) as 总金额
FROM {table_name}
WHERE 开药日期 BETWEEN '{start_date}' AND '{end_date}'
  AND 药品名称 LIKE '%{drug_name}%'
GROUP BY 药品名称, 规格
ORDER BY 总金额 DESC'''
                    }
                ]
            }
            
            return templates
            
        except Exception as e:
            logging.error(f"获取SQL模板失败: {str(e)}")
            return {}

    @staticmethod
    def render_template(template, variables):
        """渲染SQL模板"""
        try:
            result = template
            
            # 替换变量
            for key, value in variables.items():
                placeholder = '{' + key + '}'
                result = result.replace(placeholder, str(value))
            
            return result
            
        except Exception as e:
            logging.error(f"渲染SQL模板失败: {str(e)}")
            return template

    @staticmethod
    def validate_sql(sql):
        """验证SQL语句"""
        try:
            import re

            # 基本的SQL验证
            sql_lower = sql.strip().lower()

            # 检查危险关键字（使用单词边界匹配，避免误判）
            dangerous_keywords = ['drop', 'delete', 'update', 'insert', 'alter', 'create', 'truncate']
            for keyword in dangerous_keywords:
                # 使用正则表达式检查单词边界，避免误判（如 "selected" 包含 "select"）
                pattern = r'\b' + keyword + r'\b'
                if re.search(pattern, sql_lower):
                    return False, f"不允许使用 {keyword.upper()} 语句"

            # 移除严格的SELECT开头检查，允许WITH、EXPLAIN等语句
            # 只要不包含危险操作即可执行

            return True, "SQL语句验证通过"

        except Exception as e:
            return False, f"SQL验证失败: {str(e)}"

class DatabaseConnectionService:
    """数据库连接服务类"""
    
    @staticmethod
    def test_connection(db_config):
        """测试数据库连接"""
        try:
            db_type = db_config.get('type', 'oracle')
            
            if db_type == 'oracle':
                import oracledb
                conn = oracledb.connect(
                    user=db_config['username'],
                    password=db_config['password'],
                    dsn=db_config['dsn']
                )
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1 FROM DUAL")
                    result = cursor.fetchone()
                conn.close()
                return True, "Oracle连接成功"
                
            elif db_type == 'postgresql':
                import psycopg2
                conn = psycopg2.connect(
                    host=db_config['host'],
                    port=db_config['port'],
                    database=db_config['database'],
                    user=db_config['username'],
                    password=db_config['password']
                )
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                conn.close()
                return True, "PostgreSQL连接成功"
                
            elif db_type == 'mysql':
                import pymysql
                conn = pymysql.connect(
                    host=db_config['host'],
                    port=db_config['port'],
                    database=db_config['database'],
                    user=db_config['username'],
                    password=db_config['password']
                )
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                conn.close()
                return True, "MySQL连接成功"
                
            elif db_type == 'sqlserver':
                import pyodbc
                conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={db_config['host']},{db_config['port']};DATABASE={db_config['database']};UID={db_config['username']};PWD={db_config['password']}"
                conn = pyodbc.connect(conn_str)
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                conn.close()
                return True, "SQL Server连接成功"
            
            else:
                return False, f"不支持的数据库类型: {db_type}"
                
        except Exception as e:
            return False, f"数据库连接失败: {str(e)}"

    @staticmethod
    def get_table_list(db_config=None):
        """获取数据库表列表"""
        try:
            if db_config:
                # 使用指定配置连接
                pass  # 这里可以实现自定义连接
            else:
                # 使用默认连接
                with db_manager.get_connection() as conn:
                    query = """
                    SELECT table_name 
                    FROM user_tables 
                    ORDER BY table_name
                    """
                    df = execute_rules_query(conn, query)
                    return df['TABLE_NAME'].tolist() if not df.empty else []
                    
        except Exception as e:
            logging.error(f"获取表列表失败: {str(e)}")
            return []

    @staticmethod
    def get_table_columns(table_name, db_config=None):
        """获取表字段列表"""
        try:
            if db_config:
                # 使用指定配置连接
                pass  # 这里可以实现自定义连接
            else:
                # 使用默认连接
                with db_manager.get_connection() as conn:
                    query = """
                    SELECT column_name, data_type, data_length, nullable
                    FROM user_tab_columns 
                    WHERE table_name = :table_name
                    ORDER BY column_id
                    """
                    df = execute_rules_query(conn, query, {'table_name': table_name.upper()})
                    return df.to_dict('records') if not df.empty else []
                    
        except Exception as e:
            logging.error(f"获取表字段失败: {str(e)}")
            return []
