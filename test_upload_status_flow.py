#!/usr/bin/env python3
"""
测试文件上传状态流程
"""

import requests
import time
import json
from bs4 import BeautifulSoup

def test_upload_status_flow():
    """测试文件上传状态流程"""
    
    # 创建会话
    session = requests.Session()
    
    print("=== 🔧 测试文件上传状态流程 ===")
    
    # 1. 登录
    print("\n1. 🔐 登录...")
    login_url = 'http://127.0.0.1:5002/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }
    
    try:
        login_page = session.get(login_url, timeout=10)
        soup = BeautifulSoup(login_page.text, 'html.parser')
        csrf_token = soup.find('input', {'name': 'csrf_token'})
        if csrf_token:
            login_data['csrf_token'] = csrf_token['value']
        
        login_response = session.post(login_url, data=login_data, allow_redirects=True, timeout=10)
        
        if '/index' not in login_response.url:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
    except Exception as e:
        print(f"❌ 登录过程出错: {str(e)}")
        return False
    
    # 2. 创建测试文件
    print("\n2. 📄 创建测试文件...")
    try:
        import tempfile
        import os
        
        # 创建一个简单的CSV测试文件
        test_data = """姓名,年龄,城市
张三,25,北京
李四,30,上海
王五,28,广州
赵六,35,深圳
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            f.write(test_data)
            test_file_path = f.name
        
        print(f"✅ 测试文件创建成功: {test_file_path}")
        
    except Exception as e:
        print(f"❌ 创建测试文件失败: {str(e)}")
        return False
    
    # 3. 上传文件并监控状态变化
    print("\n3. 📤 上传文件并监控状态变化...")
    try:
        # 获取CSRF令牌
        uploads_page = session.get('http://127.0.0.1:5002/selfcheck/uploads', timeout=10)
        soup = BeautifulSoup(uploads_page.text, 'html.parser')
        csrf_token = soup.find('meta', {'name': 'csrf-token'})['content']
        
        # 上传文件
        upload_url = 'http://127.0.0.1:5002/selfcheck/api/uploads'
        
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test_data.csv', f, 'text/csv')}
            headers = {'X-CSRFToken': csrf_token}
            
            upload_response = session.post(upload_url, files=files, headers=headers, timeout=30)
            
            if upload_response.status_code == 200:
                upload_result = upload_response.json()
                if upload_result.get('success'):
                    upload_id = upload_result['upload']['id']
                    print(f"✅ 文件上传成功，上传ID: {upload_id}")
                    
                    # 立即检查初始状态
                    initial_status = get_upload_status(session, upload_id)
                    print(f"  📊 初始状态: {initial_status}")
                    
                    if initial_status != 'pending':
                        print(f"❌ 初始状态错误！期望: pending, 实际: {initial_status}")
                        return False
                    else:
                        print("✅ 初始状态正确: pending (待处理)")
                    
                    # 监控状态变化
                    print("\n  🔍 监控状态变化...")
                    status_history = [initial_status]
                    
                    for i in range(20):  # 最多监控20次，每次间隔1秒
                        time.sleep(1)
                        current_status = get_upload_status(session, upload_id)
                        
                        if current_status != status_history[-1]:
                            status_history.append(current_status)
                            print(f"  📈 状态变化: {status_history[-2]} → {current_status}")
                            
                            # 检查状态流程是否正确
                            if len(status_history) == 2 and current_status != 'validating':
                                print(f"❌ 状态流程错误！期望第二个状态: validating, 实际: {current_status}")
                                return False
                            
                            if current_status in ['validated', 'failed']:
                                print(f"✅ 最终状态: {current_status}")
                                break
                    
                    # 验证完整的状态流程
                    print(f"\n  📋 完整状态流程: {' → '.join(status_history)}")
                    
                    expected_flow = ['pending', 'validating']
                    if len(status_history) >= 2:
                        if status_history[:2] == expected_flow:
                            print("✅ 状态流程正确")
                            
                            final_status = status_history[-1]
                            if final_status == 'validated':
                                print("✅ 数据验证通过")
                            elif final_status == 'failed':
                                print("⚠️  数据验证失败")
                            else:
                                print(f"ℹ️  当前状态: {final_status}")
                        else:
                            print(f"❌ 状态流程错误！期望: {expected_flow}, 实际: {status_history[:2]}")
                            return False
                    else:
                        print("⚠️  状态变化不完整，可能处理时间较长")
                    
                    # 获取详细信息
                    upload_detail = get_upload_detail(session, upload_id)
                    if upload_detail:
                        print(f"\n  📄 上传详情:")
                        print(f"    - 文件名: {upload_detail.get('file_name', 'N/A')}")
                        print(f"    - 文件大小: {upload_detail.get('file_size', 'N/A')} 字节")
                        print(f"    - 记录数: {upload_detail.get('record_count', 'N/A')}")
                        print(f"    - 验证结果: {upload_detail.get('validation_result', 'N/A')}")
                        if upload_detail.get('error_message'):
                            print(f"    - 错误信息: {upload_detail.get('error_message')}")
                    
                else:
                    print(f"❌ 文件上传失败: {upload_result.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 文件上传请求失败: {upload_response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ 文件上传测试出错: {str(e)}")
        return False
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file_path)
            print(f"🗑️  清理测试文件: {test_file_path}")
        except:
            pass
    
    print("\n🎉 文件上传状态流程测试完成！")
    
    return True

def get_upload_status(session, upload_id):
    """获取上传记录状态"""
    try:
        detail_url = f'http://127.0.0.1:5002/selfcheck/api/uploads/{upload_id}'
        response = session.get(detail_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                return result['upload']['status']
        
        return 'unknown'
    except:
        return 'error'

def get_upload_detail(session, upload_id):
    """获取上传记录详情"""
    try:
        detail_url = f'http://127.0.0.1:5002/selfcheck/api/uploads/{upload_id}'
        response = session.get(detail_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                return result['upload']
        
        return None
    except:
        return None

if __name__ == '__main__':
    try:
        success = test_upload_status_flow()
        if success:
            print("\n✅ 文件上传状态流程测试成功！")
            print("\n📋 状态流程说明:")
            print("1. ✅ pending (待处理) - 文件上传后的初始状态")
            print("2. ✅ validating (验证中) - 数据导入数据库后开始验证")
            print("3. ✅ validated (已验证) - 校验通过")
            print("4. ✅ failed (失败) - 验证不通过")
            print("\n🌟 状态流程现在完全符合用户要求！")
        else:
            print("\n❌ 文件上传状态流程测试失败")
    except Exception as e:
        print(f"测试过程出错: {e}")
        import traceback
        traceback.print_exc()
