# 项目计划书 (Project Plan)

## 1. 文档信息

### 1.1 版本历史

| 版本 | 日期       | 作者 | 变更说明 |
|------|------------|------|----------|
| 0.1  | YYYY-MM-DD | AI助手 | 初稿创建 |

### 1.2 文档目的

本文档旨在明确 MICRA 自查自纠系统项目的目标、范围、计划、资源、风险和沟通策略，作为项目团队执行、监控和收尾工作的核心依据。

### 1.3 相关文档引用

*   `产品设计说明书.md`
*   `功能迁移对照表.md`
*   `实施人员操作手册.md`
*   `实施手册.md`
*   `技术实现指南.md`
*   `需求规格说明书.md` (如果存在)
*   `Roadmap.md` (如果存在)
*   `User_Story_Map.md` (如果存在)

## 2. 项目概述

### 2.1 项目名称与背景

*   **项目名称:** MICRA 医疗机构自查自纠系统
*   **项目背景:** 本项目旨在开发一套在线的医疗机构自查自纠系统。产品需求已趋于稳定，其核心初衷是为小型诊所和医院提供一定程度的免费在线自查服务，帮助它们提升合规性。对于数据量更大、需求更复杂的医院，系统将提供更专业的线上自查功能以及线下专家咨询服务。项目致力于通过用户注册信息和持续优质服务，与客户建立长期的合作关系，并逐步拓展医疗行业市场。

### 2.2 项目目标与范围
- **项目目标**：开发一个稳定、高效、易用的在线医院自查自纠系统（MICRA工具箱 - 自查自纠模块），帮助医疗机构对自身数据进行质量检查和合规性验证，发现和纠正数据问题，提高数据质量和合规性，降低合规风险，提升工作效率，并支持医保飞检。
- **项目范围**：
    - **核心功能**：
        - 用户认证系统 (用户名/密码登录、微信登录、权限控制)
        - 数据文件上传和管理 (多种格式支持、状态跟踪)
        - 检查规则配置和管理 (规则知识库、SQL规则编辑、规则分类)
        - 检查方案设计和执行 (方案创建、规则组合、权限控制)
        - 检查任务创建和监控 (任务管理、执行监控、进度跟踪)
        - 检查结果分析和报告 (结果展示、报告生成、数据导出)
        - 系统管理功能 (用户管理、权限控制、审计日志)
    - **技术栈**：Python Flask, Oracle, Pandas, Bootstrap (基于已有信息初步设定，待进一步确认)。
    - **用户群体**：医疗机构数据管理人员、质控人员、系统管理员、普通用户。
    - **主要交付物**：可部署的Web应用系统（MICRA自查自纠模块）、用户手册、技术文档、需求规格说明书。
- **不在范围** (根据需求规格说明书V2.0):
    - 外部数据源直接集成。
    - 第三方系统接口。
    - 自动化数据备份恢复。

### 2.3 项目交付物
- **可部署的Web应用系统**：MICRA自查自纠模块，包含需求规格说明书中定义的所有核心功能。
- **用户手册**：详细说明系统各项功能的使用方法，面向不同用户角色。
- **技术文档**：包括系统架构设计、数据库设计、API接口文档、部署运维手册等。
- **需求规格说明书** (`需求规格说明书.md` V2.0)。
- **项目计划书** (本文档)。
- **项目状态报告** (定期更新)。
- **项目风险登记册** (定期更新)。
- **会议纪要** (按需生成)。

### 2.4 项目约束条件

*   **时间:** (待明确，例如：X 个月内完成第一阶段上线)
*   **成本:** (待明确，例如：预算上限 Y 元)
*   **资源:**
    *   现有开发团队 (角色和人数待明确)
    *   现有技术栈 (Flask, Oracle, Pandas, Bootstrap 等)
*   **技术:**
    *   遵循现有项目的技术架构和编码规范。
    *   确保数据安全和隐私保护，特别是涉及敏感医疗数据时。
*   **质量:**
    *   系统稳定可靠，核心功能无重大缺陷。
    *   用户体验良好，操作便捷。

2.5 项目干系人列表: (根据需求规格说明书V2.0初步定义，待进一步确认和细化)
    - **产品负责人 (Product Owner)**: [待明确 - 通常是提出需求和定义产品方向的人，可能是协调者或更高级别的产品经理]
        - 职责: 定义产品愿景和需求优先级，对产品最终价值负责，确保产品满足市场和用户需求。
        - 期望: 系统功能完善、用户体验好、按时高质量交付、满足业务目标。
        - 沟通偏好: 定期产品会议、需求评审会、产品演示、关键决策沟通。
    - **项目经理 (Project Manager)**: [AI 助手担任此角色]
        - 职责: 负责项目规划、组织、执行、监控和收尾，确保项目按时、按预算、按质量交付。
        - 期望: 项目顺利推进，风险得到有效管理，团队高效协作，干系人满意。
        - 沟通偏好: 项目计划、状态报告、风险登记册、项目例会、即时沟通。
    - **开发团队 (Development Team)**: [待明确具体的开发工程师及其职责分配]
        - 职责: 负责系统的详细设计、编码实现、单元测试、代码维护和缺陷修复。
        - 期望: 清晰的需求和设计文档，稳定的开发环境，合理的技术选型和任务分配，有效的技术支持。
        - 沟通偏好: 每日站会、技术评审会、代码审查、迭代计划会、即时技术交流。
    - **测试团队/质控人员 (QA Team/QC Staff)**: [待明确具体的测试工程师和质控人员]
        - 职责: 负责制定测试计划和测试用例，执行系统测试、集成测试、回归测试，报告和跟踪缺陷，参与需求评审和验收测试，质控人员还负责检查规则制定、方案设计、结果审核。
        - 期望: 详细的需求和设计文档，稳定的测试环境，明确的验收标准，及时的缺陷修复。
        - 沟通偏好: 测试计划、测试报告、缺陷报告、测试评审会、质控标准研讨。
    - **系统管理员 (System Administrator)**: [需求规格书中定义的用户角色，可能是客户方的IT人员或内部运维人员]
        - 职责: 拥有系统所有权限，负责系统配置、用户管理、权限分配、日常运维和监控。
        - 期望: 系统稳定可靠、易于管理和维护，有完善的监控和日志功能。
        - 沟通偏好: 运维手册、系统配置指南、告警通知、定期运维会议。
    - **数据管理员 (Data Administrator)**: [需求规格书中定义的用户角色，通常是医疗机构的数据管理人员]
        - 职责: 负责数据上传、方案配置、任务执行、结果查看和初步分析。
        - 期望: 系统操作便捷、数据处理高效准确、结果清晰易懂。
        - 沟通偏好: 用户手册、操作培训、问题反馈渠道。
    - **普通用户 (General User)**: [需求规格书中定义的用户角色，可能是医疗机构的其他相关人员]
        - 职责: 查看检查结果、下载报告、进行基础数据操作。
        - 期望: 系统界面友好、信息获取方便快捷。
        - 沟通偏好: 用户手册、系统通知。
    - **协调者/用户代表 (Coordinator/User Representative)**: [通常是与AI助手对接的用户]
        - 职责: 代表最终用户或业务方提供需求、反馈信息，参与项目决策和验收。
        - 期望: AI助手能够准确理解需求并高效完成项目管理工作，项目成果符合预期。
        - 沟通偏好: 即时消息、邮件、定期会议。

### 2.6 项目成功标准

*   在预定时间内完成核心功能的开发和上线。
*   系统运行稳定，关键性能指标达标 (如响应时间、并发用户数)。
*   用户注册数达到预期目标 (例如：上线后 X 个月内达到 Y 注册用户)。
*   付费用户转化率达到预期 (例如：Z% 的免费用户转化为付费用户)。
*   用户满意度达到一定水平 (通过用户调研或反馈收集)。
*   项目成本控制在预算范围内。
*   主要风险得到有效控制。

## 3. 项目组织结构

### 3.1 项目团队结构与角色职责

(待与用户确认团队成员后补充，可使用 Mermaid 图表)

```mermaid
graph TD
    A[项目发起人/Sponsor] --> B{项目经理 (AI助手)};
    B --> C[产品经理/协调者 (用户)];
    B --> D[开发团队];
    B --> E[测试团队];
    B --> F[设计团队];
    B --> G[运维团队];
    C --> D;
    C --> E;
    C --> F;
```

*   **项目经理 (AI助手):** 负责整体项目规划、执行、监控和收尾，协调资源，管理风险和变更。
*   **产品经理/协调者 (用户):** 负责需求定义、产品规划、用户体验，并作为业务方代表与开发团队沟通。
*   **开发团队:** 负责系统架构设计、功能开发、单元测试和代码维护。
*   **测试团队:** 负责制定测试策略、编写测试用例、执行测试并跟踪缺陷。
*   **设计团队:** (如需要) 负责用户界面 (UI) 和用户体验 (UX) 设计。
*   **运维团队:** (如需要) 负责系统部署、服务器维护、性能监控和故障排除。

### 3.2 项目治理结构与决策机制

*   **项目指导委员会 (PSC) (如设立):** 由项目发起人、关键业务方代表组成，负责重大决策、审批关键变更、解决高级别冲突。
*   **项目核心团队会议:** 定期召开 (如每周)，由项目经理主持，产品经理、开发负责人、测试负责人等核心成员参与，回顾进展、讨论问题、制定计划。
*   **变更控制委员会 (CCB) (如设立):** 负责评审和批准所有对项目范围、进度、成本产生影响的变更请求。
*   **决策流程:**
    *   一般性问题：由项目经理或相关职能负责人决策。
    *   重要问题：提交项目核心团队会议讨论决策。
    *   重大问题/变更：提交项目指导委员会或 CCB 决策。

## 4. 工作分解结构 (WBS)

### 4.1 WBS 层级分解 (基于需求规格说明书V2.0进行初步分解)

1.  **项目管理**
    1.1. 项目启动与规划 (已部分完成)
    1.2. 需求确认与细化 (进行中)
    1.3. 项目监控与控制
    1.4. 风险管理
    1.5. 沟通管理
    1.6. 项目收尾
2.  **系统设计**
    2.1. 系统架构设计 (复核与确认)
    2.2. 数据库设计 (复核与确认)
    2.3. UI/UX 设计 (复核与确认，或根据需求微调)
    2.4. 接口设计 (内部及外部，如需要)
3.  **功能模块开发 (对应需求规格说明书 FR-XXX)**
    3.1. 用户认证系统 (FR-001, FR-002)
        3.1.1. 用户登录功能开发与测试
        3.1.2. 权限控制系统开发与测试
    3.2. 数据上传管理 (FR-003, FR-004)
        3.2.1. 文件上传功能开发与测试
        3.2.2. 上传记录管理功能开发与测试
    3.3. 规则管理 (FR-005, FR-006)
        3.3.1. 检查规则配置功能开发与测试
        3.3.2. 规则知识库功能开发与测试
    3.4. 方案管理 (FR-007, FR-008)
        3.4.1. 检查方案配置功能开发与测试
        3.4.2. 方案权限管理功能开发与测试
    3.5. 任务管理 (参照需求规格说明书后续章节，如FR-009等)
        3.5.1. 检查任务创建与执行功能开发与测试
        3.5.2. 任务监控与管理功能开发与测试
    3.6. 结果管理 (参照需求规格说明书后续章节，如FR-011等)
        3.6.1. 检查结果展示与分析功能开发与测试
        3.6.2. 结果报告生成与导出功能开发与测试
    3.7. 系统管理 (参照需求规格说明书后续章节，如FR-013等)
        3.7.1. 用户管理模块开发与测试
        3.7.2. 系统配置模块开发与测试
        3.7.3. 审计日志模块开发与测试
4.  **测试与质量保证**
    4.1. 测试计划制定与评审
    4.2. 测试用例设计与评审
    4.3. 测试环境搭建与维护
    4.4. 单元测试 (开发团队执行)
    4.5. 集成测试
    4.6. 系统测试
    4.7. 性能测试 (根据需求确定范围和指标)
    4.8. 安全测试 (根据需求确定范围和标准)
    4.9. 用户验收测试 (UAT) 支持
5.  **部署与上线**
    5.1. 生产环境准备与配置
    5.2. 系统部署与验证
    5.3. 数据迁移 (如需要)
    5.4. 上线切换与监控
6.  **文档与培训**
    6.1. 用户手册编写与评审
    6.2. 技术文档完善 (架构、设计、部署、运维)
    6.3. 用户培训材料准备与实施 (如需要)

### 4.2 任务包描述与负责人

(待项目团队明确后，针对各任务包细化描述、分配负责人、估算工时)

## 5. 项目进度计划

### 5.1 项目关键里程碑定义

| 里程碑名称             | 计划完成日期 | 交付物/成果                      |
|------------------------|--------------|----------------------------------|
| 项目启动会召开         | (待定)       | 项目启动会纪要，项目目标明确       |
| 需求规格说明书确认     | (待定)       | 确认的需求规格说明书             |
| 系统设计方案评审通过   | (待定)       | 确认的系统设计方案               |
| 核心功能开发完成       | (待定)       | 核心模块代码，内部测试通过         |
| Alpha 版本发布         | (待定)       | 可供内部测试的 Alpha 版本          |
| Beta 版本发布          | (待定)       | 可供小范围用户测试的 Beta 版本     |
| 用户验收测试 (UAT) 完成 | (待定)       | UAT 测试报告，用户确认             |
| 系统正式上线           | (待定)       | 系统上线，对外提供服务             |
| 项目总结报告提交       | (待定)       | 项目总结报告                     |

### 5.2 任务依赖关系分析

(可使用 Mermaid 甘特图或流程图描述关键路径，待 WBS 和任务估算后补充)

```mermaid
gantt
    dateFormat  YYYY-MM-DD
    title MICRA 项目进度计划 (示例)
    excludes weekends

    section 项目规划与设计
    需求分析与确认     :a1, 2024-07-01, 10d
    系统架构设计       :a2, after a1, 7d
    数据库设计         :a3, after a1, 5d
    UI/UX 设计         :a4, after a2, 7d

    section 系统开发 (迭代一)
    核心框架搭建       :b1, after a2, 5d
    用户管理模块       :b2, after b1, 10d
    权限管理模块       :b3, after b1, 10d

    section 测试
    测试计划与用例     :c1, after a1, 5d
    迭代一集成测试     :c2, after b3, 7d

    section 上线
    系统上线           :d1, after c2, 3d
```

### 5.3 详细任务进度安排与时间估算

(待 WBS 细化后，结合资源情况进行估算和排期)

### 5.4 进度基线建立

在详细进度计划制定并获得批准后，将建立项目进度基线，作为后续项目跟踪和控制的依据。

## 6. 资源计划

### 6.1 人力资源需求与分配

| 角色         | 数量 | 技能要求                                 | 投入时间 (人/天) |
|--------------|------|------------------------------------------|------------------|
| 项目经理     | 1    | 项目管理经验，熟悉敏捷/瀑布方法论        | (待定)           |
| 产品经理     | 1    | 需求分析，产品设计，用户沟通             | (待定)           |
| Python 开发工程师 | (待定) | Flask, SQLAlchemy, Pandas, API 开发等    | (待定)           |
| 前端开发工程师 | (待定) | HTML, CSS, JavaScript, JQuery, Bootstrap | (待定)           |
| 数据库管理员 | (待定) | Oracle, SQL, 数据库优化与维护          | (待定)           |
| 测试工程师   | (待定) | 测试理论，自动化测试，缺陷管理           | (待定)           |
| UI/UX 设计师 | (待定) | UI 设计，UX 研究，原型制作             | (待定)           |

### 6.2 物力资源需求

*   开发服务器与环境
*   测试服务器与环境
*   生产服务器与环境 (云服务或自建)
*   必要的开发工具和软件许可证
*   版本控制系统 (如 Git)
*   项目管理与协作工具

### 6.3 预算概述与成本估算

(待详细规划后进行估算，主要包括人力成本、软硬件采购成本、云服务费用等)

## 7. 风险管理计划

### 7.1 风险识别与登记

(详细内容将在 `docs/Project_Risk_Register.md` 中维护，此处列举部分初始识别的风险)

*   **需求风险:** 需求不明确或频繁变更，导致范围蔓延。
*   **技术风险:** 新技术引入困难，现有技术栈无法满足性能要求，数据迁移复杂。
*   **资源风险:** 核心人员流失，资源不足或技能不匹配。
*   **进度风险:** 任务估算不准确，关键任务延期。
*   **成本风险:** 项目超出预算。
*   **外部风险:** 市场竞争加剧，政策法规变化 (如医疗数据隐私保护)。
*   **安全风险:** 系统遭受攻击，数据泄露。
*   **用户接受度风险:** 产品不符合用户使用习惯，推广困难。

### 7.2 风险评估与优先级排序

将对识别的风险进行概率和影响评估，确定风险优先级。

### 7.3 风险应对策略

针对不同风险制定相应的应对策略：规避、减轻、转移、接受。

### 7.4 风险责任人与监控计划

为每个重要风险指定责任人，并定期监控风险状态和应对措施的有效性。

## 8. 质量管理计划

### 8.1 项目质量标准与度量指标

*   **代码质量:** 代码规范符合 PEP 8，低复杂度，高可读性，单元测试覆盖率达到 X%。
*   **功能完整性:** 所有核心功能按需求实现。
*   **系统性能:** 关键操作响应时间小于 X 秒，并发用户数支持 Y 个。
*   **系统稳定性:** 平均无故障运行时间 (MTBF) 大于 Z 小时。
*   **用户满意度:** 用户调研评分高于 N 分 (满分5分)。
*   **缺陷密度:** 上线后每千行代码缺陷数低于 M 个。

### 8.2 质量保证活动

*   **需求评审:** 确保需求的清晰性、完整性和可测试性。
*   **设计评审:** 确保架构和设计的合理性、可扩展性。
*   **代码评审 (Code Review):** 提高代码质量，发现潜在缺陷。
*   **测试策略制定:** 覆盖单元测试、集成测试、系统测试、性能测试、安全测试、用户验收测试。
*   **自动化测试:** 对核心功能和回归场景实现自动化测试。
*   **版本控制与配置管理:** 严格管理代码版本和配置项。
*   **文档规范:** 确保项目文档的完整性和一致性。

### 8.3 质量控制活动

*   **测试执行与缺陷跟踪:** 按照测试计划执行测试，记录并跟踪缺陷修复过程。
*   **质量审计:** 定期检查项目活动和产出物是否符合质量标准。
*   **过程改进:** 根据质量审计和回顾结果，持续改进开发和管理过程。

## 9. 沟通管理计划

### 9.1 项目干系人沟通矩阵

| 干系人           | 需要的信息                               | 沟通频率 | 沟通方式             | 负责人     |
|------------------|------------------------------------------|----------|----------------------|------------|
| 项目发起人       | 项目总体进展、关键风险、重大决策           | 每月/按需 | 项目状态报告、会议   | 项目经理   |
| 产品经理/协调者  | 项目详细进展、问题与风险、需求澄清         | 每日/每周 | 站会、周会、即时沟通 | 项目经理   |
| 开发团队         | 任务分配、技术问题、需求变更             | 每日     | 站会、代码评审、文档 | 项目经理/开发负责人 |
| 测试团队         | 测试计划、测试进度、缺陷报告             | 每日/每周 | 测试会议、缺陷系统   | 项目经理/测试负责人 |
| 最终用户         | 产品功能介绍、使用反馈、问题支持         | 按需     | 用户手册、反馈渠道   | 产品经理   |

### 9.2 定期会议安排

*   **每日站会 (Daily Stand-up):** 15分钟，团队成员同步进展、计划和障碍。
*   **项目周会:** 1小时，回顾上周工作，计划下周任务，讨论关键问题和风险。
*   **需求评审会:** 按需召开，评审和确认需求。
*   **设计评审会:** 按需召开，评审技术方案和设计文档。
*   **测试评审会:** 按需召开，评审测试计划和测试结果。
*   **项目回顾会 (Retrospective):** 每个迭代/阶段结束时召开，总结经验教训，改进工作方式。
*   **项目指导委员会会议 (如适用):** 每月/每季度召开，汇报项目状态，进行关键决策。

### 9.3 信息报告机制

*   **项目状态报告:** 每周/每双周发布，包含项目进展、风险、问题、变更等内容。
*   **问题报告:** 及时记录和跟踪项目中出现的问题。
*   **风险报告:** 定期更新风险登记册，并向相关干系人通报高优先级风险。
*   **会议纪要:** 所有重要会议均需有会议纪要，并分发给相关人员。

## 10. 变更管理计划

### 10.1 变更控制流程

1.  **变更请求提交:** 干系人通过指定的渠道提交变更请求 (CR)，说明变更内容、原因和期望。
2.  **变更请求记录:** 项目经理记录变更请求，并进行初步评估。
3.  **变更影响分析:** 相关团队 (开发、测试、产品) 分析变更对项目范围、进度、成本、质量、资源和风险的影响。
4.  **变更评审与批准:**
    *   小型变更：由项目经理和产品经理共同审批。
    *   重大变更：提交变更控制委员会 (CCB) 或项目指导委员会评审和批准。
5.  **变更实施:** 批准的变更纳入项目计划，并分配资源执行。
6.  **变更验证:** 变更完成后进行测试和验证，确保符合预期。
7.  **变更沟通:** 将变更结果通知所有相关干系人，并更新项目文档。

### 10.2 变更对范围、进度、成本的影响评估机制

*   **范围影响:** 评估变更是否会导致新的功能增加或现有功能修改，是否超出原定范围。
*   **进度影响:** 评估变更所需的工作量，对现有任务和项目里程碑的影响。
*   **成本影响:** 评估变更所需的人力、物力资源成本，以及对项目总预算的影响。
*   **质量影响:** 评估变更对系统稳定性、性能、安全性的潜在影响。
*   **风险影响:** 评估变更是否会引入新的风险或改变现有风险的级别。

## 11. 采购管理计划 (如适用)

### 11.1 需要外部采购的服务或产品

*   (例如：第三方 API 服务、特定的商业软件、外部咨询服务等，待明确)

### 11.2 采购流程与管理方式

*   **需求识别与定义:** 明确采购需求和规格。
*   **供应商选择:** 进行市场调研，评估和选择合适的供应商。
*   **合同谈判与签订:** 与选定的供应商进行合同谈判并签订采购合同。
*   **采购执行与监控:** 跟踪采购订单的执行情况，确保按时按质交付。
*   **验收与付款:** 对交付的产品或服务进行验收，并按合同约定支付款项。

## 12. 项目收尾计划

### 12.1 项目验收流程与标准

*   **验收标准:** 基于需求规格说明书和项目目标，明确各项功能的验收标准。
*   **用户验收测试 (UAT):** 邀请最终用户或业务代表参与测试，并签署验收报告。
*   **最终验收:** 项目发起人或项目指导委员会对项目整体成果进行最终验收。

### 12.2 项目总结与经验教训回顾

*   召开项目总结会，回顾项目过程中的成功经验和遇到的问题。
*   分析问题产生的原因，总结经验教训，为未来项目提供借鉴。
*   编写项目总结报告。

### 12.3 项目文档归档与交付

*   整理并归档所有项目相关的文档，包括需求文档、设计文档、代码、测试报告、用户手册、会议纪要等。
*   将最终交付物正式移交给运维团队或用户方。
*   释放项目资源，解散项目团队 (或转入下一阶段)。