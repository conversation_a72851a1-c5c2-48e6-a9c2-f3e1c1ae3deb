<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.selfcheck.mapper.UploadRecordMapper">
    
    <resultMap type="UploadRecord" id="UploadRecordResult">
        <result property="uploadId"    column="upload_id"    />
        <result property="userId"    column="user_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileType"    column="file_type"    />
        <result property="uploadTime"    column="upload_time"    />
        <result property="status"    column="status"    />
        <result property="validationResult"    column="validation_result"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="userName"    column="user_name"    />
    </resultMap>

    <sql id="selectUploadRecordVo">
        select ur.upload_id, ur.user_id, ur.file_name, ur.file_path, ur.file_size, ur.file_type, ur.upload_time, ur.status, ur.validation_result, ur.error_message, ur.create_by, ur.create_time, ur.update_by, ur.update_time, ur.remark,
               (select u.user_name from sys_user u where u.user_id = ur.user_id) as user_name
        from biz_upload_record ur
    </sql>

    <select id="selectUploadRecordList" parameterType="UploadRecord" resultMap="UploadRecordResult">
        <include refid="selectUploadRecordVo"/>
        <where>
            <if test="userId != null "> and ur.user_id = #{userId}</if>
            <if test="fileName != null  and fileName != ''"> and ur.file_name like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''"> and ur.file_path = #{filePath}</if>
            <if test="fileType != null  and fileType != ''"> and ur.file_type = #{fileType}</if>
            <if test="uploadTime != null "> and ur.upload_time = #{uploadTime}</if>
            <if test="status != null  and status != ''"> and ur.status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(ur.upload_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(ur.upload_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by ur.upload_time desc
    </select>


    
    <select id="selectUploadRecordByUploadId" parameterType="Long" resultMap="UploadRecordResult">
        <include refid="selectUploadRecordVo"/>
        where ur.upload_id = #{uploadId}
    </select>

    <select id="selectUploadRecordByUserId" parameterType="Long" resultMap="UploadRecordResult">
        <include refid="selectUploadRecordVo"/>
        where ur.user_id = #{userId}
        order by ur.upload_time desc
    </select>

    <select id="selectUploadRecordByStatus" parameterType="String" resultMap="UploadRecordResult">
        <include refid="selectUploadRecordVo"/>
        where ur.status = #{status}
        order by ur.upload_time desc
    </select>
        
    <insert id="insertUploadRecord" parameterType="UploadRecord" useGeneratedKeys="true" keyProperty="uploadId">
        insert into biz_upload_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="filePath != null and filePath != ''">file_path,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileType != null">file_type,</if>
            <if test="uploadTime != null">upload_time,</if>
            <if test="status != null">status,</if>
            <if test="validationResult != null">validation_result,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="filePath != null and filePath != ''">#{filePath},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
            <if test="status != null">#{status},</if>
            <if test="validationResult != null">#{validationResult},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateUploadRecord" parameterType="UploadRecord">
        update biz_upload_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="filePath != null and filePath != ''">file_path = #{filePath},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="validationResult != null">validation_result = #{validationResult},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where upload_id = #{uploadId}
    </update>

    <delete id="deleteUploadRecordByUploadId" parameterType="Long">
        delete from biz_upload_record where upload_id = #{uploadId}
    </delete>

    <delete id="deleteUploadRecordByUploadIds" parameterType="String">
        delete from biz_upload_record where upload_id in 
        <foreach item="uploadId" collection="array" open="(" separator="," close=")">
            #{uploadId}
        </foreach>
    </delete>

</mapper>
