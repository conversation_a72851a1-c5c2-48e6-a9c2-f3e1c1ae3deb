package com.ruoyi.selfcheck.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 自查规则对象 biz_rule
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class SelfCheckRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private Long ruleId;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String ruleName;

    /** 规则编码 */
    @Excel(name = "规则编码")
    private String ruleCode;

    /** 规则描述 */
    @Excel(name = "规则描述")
    private String ruleDescription;

    /** 规则类型 */
    @Excel(name = "规则类型")
    private String ruleType;

    /** 规则内容JSON */
    private String ruleContent;

    /** 规则版本 */
    @Excel(name = "规则版本")
    private String ruleVersion;

    /** 状态(0启用 1禁用) */
    @Excel(name = "状态", readConverterExp = "0=启用,1=禁用")
    private String status;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }
    public void setRuleName(String ruleName) 
    {
        this.ruleName = ruleName;
    }

    public String getRuleName() 
    {
        return ruleName;
    }
    public void setRuleCode(String ruleCode) 
    {
        this.ruleCode = ruleCode;
    }

    public String getRuleCode() 
    {
        return ruleCode;
    }
    public void setRuleDescription(String ruleDescription) 
    {
        this.ruleDescription = ruleDescription;
    }

    public String getRuleDescription() 
    {
        return ruleDescription;
    }
    public void setRuleType(String ruleType) 
    {
        this.ruleType = ruleType;
    }

    public String getRuleType() 
    {
        return ruleType;
    }
    public void setRuleContent(String ruleContent) 
    {
        this.ruleContent = ruleContent;
    }

    public String getRuleContent() 
    {
        return ruleContent;
    }
    public void setRuleVersion(String ruleVersion) 
    {
        this.ruleVersion = ruleVersion;
    }

    public String getRuleVersion() 
    {
        return ruleVersion;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return "SelfCheckRule{" +
                "ruleId=" + ruleId +
                ", ruleName='" + ruleName + '\'' +
                ", ruleCode='" + ruleCode + '\'' +
                ", ruleDescription='" + ruleDescription + '\'' +
                ", ruleType='" + ruleType + '\'' +
                ", ruleContent='" + ruleContent + '\'' +
                ", ruleVersion='" + ruleVersion + '\'' +
                ", status='" + status + '\'' +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
