"""
自查自纠模块数据模型 - 不使用SQLAlchemy，使用纯Python类
"""
from datetime import datetime
from typing import Dict, Any, Optional, List
from app.selfcheck.database import execute_query, execute_update


class BaseModel:
    """基础模型类"""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result


class SelfCheckRule(BaseModel):
    """自查规则模型"""
    
    TABLE_NAME = 'selfcheck_rules'
    
    def __init__(self, **kwargs):
        # 设置默认值
        self.id = kwargs.get('id')
        self.rule_name = kwargs.get('rule_name', '')
        self.rule_code = kwargs.get('rule_code', '')
        self.rule_description = kwargs.get('rule_description', '')
        self.rule_type = kwargs.get('rule_type', '')
        self.rule_content = kwargs.get('rule_content', '')
        self.rule_version = kwargs.get('rule_version', '1.0')
        self.status = kwargs.get('status', 'active')
        self.sort_order = kwargs.get('sort_order', 0)
        self.created_by = kwargs.get('created_by')
        self.created_at = kwargs.get('created_at')
        self.updated_by = kwargs.get('updated_by')
        self.updated_at = kwargs.get('updated_at')
        
        # 从rule_sql_history导入的字段
        self.sql_content = kwargs.get('sql_content', '')
        self.city = kwargs.get('city', '')
        self.rule_source = kwargs.get('rule_source', '')
        self.medical_behavior = kwargs.get('medical_behavior', '')
        self.template_name = kwargs.get('template_name', '')
        self.visit_type = kwargs.get('visit_type', '')
        self.types = kwargs.get('types', '')
        self.appl_scope = kwargs.get('appl_scope', '')
        self.selected_rule_type = kwargs.get('selected_rule_type', '')
        self.sql_type = kwargs.get('sql_type', '')
        self.original_rule_id = kwargs.get('original_rule_id')
    
    @classmethod
    def get_all(cls, page: int = 1, per_page: int = 20, sort_by: str = 'created_at', sort_order: str = 'desc', **filters) -> Dict[str, Any]:
        """获取所有规则（分页）"""
        # 构建查询条件
        where_conditions = []
        params = []

        if filters.get('rule_name'):
            where_conditions.append("rule_name LIKE ?")
            params.append(f"%{filters['rule_name']}%")

        if filters.get('rule_type'):
            where_conditions.append("rule_type = ?")
            params.append(filters['rule_type'])

        if filters.get('status'):
            where_conditions.append("status = ?")
            params.append(filters['status'])

        if filters.get('city'):
            where_conditions.append("city = ?")
            params.append(filters['city'])

        if filters.get('visit_type'):
            where_conditions.append("visit_type = ?")
            params.append(filters['visit_type'])

        if filters.get('rule_source'):
            where_conditions.append("rule_source = ?")
            params.append(filters['rule_source'])

        if filters.get('types'):
            where_conditions.append("types = ?")
            params.append(filters['types'])

        # 默认只显示未删除的规则，除非明确指定要显示已删除的
        if filters.get('include_deleted') != 'true':
            where_conditions.append("status != 'deleted'")

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 构建排序子句
        # 映射前端字段名到数据库字段名
        sort_field_mapping = {
            'rule_name': 'rule_name',
            'rule_code': 'rule_code',
            'rule_type': 'rule_type',
            'city': 'city',
            'visit_type': 'visit_type',
            'rule_source': 'rule_source',
            'types': 'types',
            'status': 'status',
            'created_at': 'created_at'
        }

        # 验证排序字段
        if sort_by not in sort_field_mapping:
            sort_by = 'created_at'

        # 验证排序方向
        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'desc'

        db_sort_field = sort_field_mapping[sort_by]
        order_clause = f"{db_sort_field} {sort_order.upper()}"

        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM {cls.TABLE_NAME} WHERE {where_clause}"
        count_result = execute_query(count_query, params)
        total = count_result[0]['total'] if count_result else 0

        # 获取分页数据
        offset = (page - 1) * per_page
        end_row = offset + per_page

        # 使用Oracle 11g兼容的分页查询
        query = f"""
        SELECT * FROM (
            SELECT a.*, ROWNUM rn FROM (
                SELECT * FROM {cls.TABLE_NAME}
                WHERE {where_clause}
                ORDER BY {order_clause}
            ) a WHERE ROWNUM <= {end_row}
        )
        WHERE rn > {offset}
        """

        rows = execute_query(query, params)
        rules = [cls(**row) for row in rows]

        return {
            'rules': [rule.to_dict() for rule in rules],
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page if total > 0 else 0
        }
    
    @classmethod
    def get_by_id(cls, rule_id: int) -> Optional['SelfCheckRule']:
        """根据ID获取规则"""
        query = f"SELECT * FROM {cls.TABLE_NAME} WHERE id = ?"
        rows = execute_query(query, [rule_id])
        return cls(**rows[0]) if rows else None
    
    def save(self) -> bool:
        """保存规则"""
        try:
            if self.id:
                # 更新 - 需要特殊处理CLOB字段
                if hasattr(self, 'sql_content') and self.sql_content:
                    # 如果有sql_content，使用Oracle CLOB处理方法
                    return self._update_with_clob()
                else:
                    # 普通更新
                    query = f"""
                    UPDATE {self.TABLE_NAME} SET
                        rule_name = ?, rule_code = ?, rule_description = ?,
                        rule_type = ?, rule_content = ?, rule_version = ?,
                        status = ?, sort_order = ?, updated_by = ?,
                        updated_at = CURRENT_TIMESTAMP,
                        city = ?, rule_source = ?, medical_behavior = ?,
                        template_name = ?, visit_type = ?, types = ?
                    WHERE id = ?
                    """
                    params = [
                        self.rule_name, self.rule_code, self.rule_description,
                        self.rule_type, self.rule_content, self.rule_version,
                        self.status, self.sort_order, self.updated_by,
                        self.city, self.rule_source, self.medical_behavior,
                        self.template_name, self.visit_type, self.types, self.id
                    ]
            else:
                # 插入
                query = f"""
                INSERT INTO {self.TABLE_NAME} (
                    id, rule_name, rule_code, rule_description, rule_type,
                    rule_content, rule_version, status, sort_order,
                    created_by, created_at, updated_by, updated_at,
                    sql_content, city, rule_source, medical_behavior,
                    template_name, visit_type, types, original_rule_id
                ) VALUES (
                    selfcheck_rules_seq.NEXTVAL, ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, CURRENT_TIMESTAMP, ?, CURRENT_TIMESTAMP,
                    ?, ?, ?, ?, ?, ?, ?, ?
                )
                """
                params = [
                    self.rule_name, self.rule_code, self.rule_description,
                    self.rule_type, self.rule_content, self.rule_version,
                    self.status, self.sort_order, self.created_by, self.updated_by,
                    self.sql_content, self.city, self.rule_source,
                    self.medical_behavior, self.template_name, self.visit_type,
                    self.types, self.original_rule_id
                ]

            execute_update(query, params)
            return True
        except Exception as e:
            print(f"保存规则失败: {str(e)}")
            return False

    def save_with_verification(self) -> bool:
        """保存规则并验证是否真正保存成功（使用批量导入连接池）"""
        try:
            from app.selfcheck.database import get_batch_import_db_manager, batch_import_execute_query

            # 检查规则编码是否已存在
            check_query = "SELECT COUNT(*) as count FROM selfcheck_rules WHERE rule_code = :rule_code"
            check_result = batch_import_execute_query(check_query, {'rule_code': self.rule_code})
            if check_result and check_result[0]['count'] > 0:
                print(f"规则编码已存在: {self.rule_code}")
                return False

            if self.id:
                # 更新逻辑
                if hasattr(self, 'sql_content') and self.sql_content:
                    success = self._update_with_clob()
                else:
                    query = f"""
                    UPDATE {self.TABLE_NAME} SET
                        rule_name = :rule_name, rule_code = :rule_code, rule_description = :rule_description,
                        rule_type = :rule_type, rule_content = :rule_content,appl_scope = :appl_scope,
                        selected_rule_type = :selected_rule_type,sql_type = :sql_type, rule_version = :rule_version,
                        status = :status, sort_order = :sort_order, updated_by = :updated_by,
                        updated_at = CURRENT_TIMESTAMP,
                        city = :city, rule_source = :rule_source, medical_behavior = :medical_behavior,
                        template_name = :template_name, visit_type = :visit_type, types = :types
                    WHERE id = :id
                    """
                    params = {
                        'rule_name': self.rule_name, 'rule_code': self.rule_code,
                        'rule_description': self.rule_description, 'rule_type': self.rule_type,
                        'rule_content': self.rule_content, 'appl_scope': self.appl_scope,
                        'selected_rule_type': self.selected_rule_type, 'sql_type': self.sql_type,
                        'rule_version': self.rule_version,
                        'status': self.status, 'sort_order': self.sort_order,
                        'updated_by': self.updated_by, 'city': self.city,
                        'rule_source': self.rule_source, 'medical_behavior': self.medical_behavior,
                        'template_name': self.template_name, 'visit_type': self.visit_type,
                        'types': self.types, 'id': self.id
                    }

                    from app.selfcheck.database import batch_import_execute_update, batch_import_execute_query
                    affected_rows = batch_import_execute_update(query, params)
                    success = affected_rows > 0

                if success:
                    # 验证更新是否成功
                    verify_query = "SELECT COUNT(*) as count FROM selfcheck_rules WHERE id = :id"
                    verify_result = batch_import_execute_query(verify_query, {'id': self.id})
                    return verify_result and verify_result[0]['count'] > 0
                return False
            else:
                # 插入逻辑 - 使用批量导入连接池确保数据一致性
                from app.selfcheck.database import batch_import_execute_query, batch_import_execute_update

                # 先获取序列值
                seq_query = "SELECT selfcheck_rules_seq.NEXTVAL as new_id FROM dual"
                seq_result = batch_import_execute_query(seq_query)
                if not seq_result:
                    print("获取序列值失败")
                    return False

                new_id = seq_result[0]['new_id']
                self.id = new_id

                # 插入记录
                query = f"""
                INSERT INTO {self.TABLE_NAME} (
                    id, rule_name, rule_code, rule_description, rule_type,
                    rule_content, rule_version, status, sort_order,
                    created_by, created_at, updated_by, updated_at,
                    sql_content, city, rule_source, medical_behavior,
                    template_name, visit_type, types, original_rule_id,appl_scope,selected_rule_type,sql_type
                ) VALUES (
                    :id, :rule_name, :rule_code, :rule_description, :rule_type,
                    :rule_content, :rule_version, :status, :sort_order,
                    :created_by, CURRENT_TIMESTAMP, :updated_by, CURRENT_TIMESTAMP,
                    :sql_content, :city, :rule_source, :medical_behavior,
                    :template_name, :visit_type, :types, :original_rule_id,:appl_scope,:selected_rule_type,:sql_type
                )
                """
                params = {
                    'id': new_id,
                    'rule_name': self.rule_name, 'rule_code': self.rule_code,
                    'rule_description': self.rule_description, 'rule_type': self.rule_type,
                    'rule_content': self.rule_content, 'rule_version': self.rule_version,
                    'status': self.status, 'sort_order': self.sort_order,
                    'created_by': self.created_by, 'updated_by': self.updated_by,
                    'sql_content': self.sql_content, 'city': self.city,
                    'rule_source': self.rule_source, 'medical_behavior': self.medical_behavior,
                    'template_name': self.template_name, 'visit_type': self.visit_type,
                    'types': self.types, 'original_rule_id': self.original_rule_id,
                    'appl_scope': self.appl_scope, 'selected_rule_type': self.selected_rule_type,
                    'sql_type': self.sql_type
                }

                affected_rows = batch_import_execute_update(query, params)

                if affected_rows > 0:
                    # 验证插入是否成功
                    verify_query = "SELECT COUNT(*) as count FROM selfcheck_rules WHERE id = :id"
                    verify_result = batch_import_execute_query(verify_query, {'id': new_id})
                    return verify_result and verify_result[0]['count'] > 0
                else:
                    print(f"插入规则失败，影响行数: {affected_rows}")
                    return False

        except Exception as e:
            print(f"保存规则失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def delete(self) -> bool:
        """软删除规则（逻辑删除）"""
        if not self.id:
            return False

        try:
            # 使用软删除，将状态改为 'deleted'
            query = f"""
            UPDATE {self.TABLE_NAME}
            SET status = 'deleted', updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            execute_update(query, [self.id])
            self.status = 'deleted'
            return True
        except Exception as e:
            print(f"删除规则失败: {str(e)}")
            return False

    def hard_delete(self) -> bool:
        """物理删除规则（仅在确认没有外键约束时使用）"""
        if not self.id:
            return False

        try:
            query = f"DELETE FROM {self.TABLE_NAME} WHERE id = ?"
            execute_update(query, [self.id])
            return True
        except Exception as e:
            print(f"物理删除规则失败: {str(e)}")
            return False

    def restore(self) -> bool:
        """恢复已删除的规则"""
        if not self.id:
            return False

        try:
            # 将状态从 'deleted' 改回 'active'
            query = f"""
            UPDATE {self.TABLE_NAME}
            SET status = 'active', updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            execute_update(query, [self.id])
            self.status = 'active'
            return True
        except Exception as e:
            print(f"恢复规则失败: {str(e)}")
            return False

    def _update_with_clob(self) -> bool:
        """使用Oracle CLOB处理方法更新规则"""
        try:
            from app.selfcheck.database import get_db_manager

            # 先更新非CLOB字段
            query = f"""
            UPDATE {self.TABLE_NAME} SET
                rule_name = :rule_name, rule_code = :rule_code, rule_description = :rule_description,
                rule_type = :rule_type, rule_content = :rule_content,appl_scope = :appl_scope,
                selected_rule_type = :selected_rule_type,sql_type = :sql_type, rule_version = :rule_version,
                status = :status, sort_order = :sort_order, updated_by = :updated_by,
                updated_at = CURRENT_TIMESTAMP,
                city = :city, rule_source = :rule_source, medical_behavior = :medical_behavior,
                template_name = :template_name, visit_type = :visit_type, types = :types
            WHERE id = :id
            """

            params = {
                'rule_name': self.rule_name,
                'rule_code': self.rule_code,
                'rule_description': self.rule_description,
                'rule_type': self.rule_type,
                'rule_content': self.rule_content,
                'appl_scope': self.appl_scope,
                'selected_rule_type': self.selected_rule_type,
                'sql_type': self.sql_type,
                'rule_version': self.rule_version,
                'status': self.status,
                'sort_order': self.sort_order,
                'updated_by': self.updated_by,
                'city': self.city,
                'rule_source': self.rule_source,
                'medical_behavior': self.medical_behavior,
                'template_name': self.template_name,
                'visit_type': self.visit_type,
                'types': self.types,
                'id': self.id
            }

            db_manager = get_db_manager()
            db_manager.execute_update(query, params)

            # 然后使用Oracle CLOB方法更新sql_content
            if self.sql_content:
                self._update_clob_field('sql_content', self.sql_content)

            return True
        except Exception as e:
            print(f"使用CLOB方法更新规则失败: {str(e)}")
            return False

    def _update_clob_field(self, field_name: str, content: str):
        """更新CLOB字段"""
        try:
            from app.selfcheck.database import get_db_manager

            # 分块处理SQL内容
            chunk_size = 4000
            sql_chunks = [content[i:i+chunk_size] for i in range(0, len(content), chunk_size)]

            db_manager = get_db_manager()

            # 先设置为空CLOB并获取ROWID
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    row_id_var = cursor.var(str)
                    cursor.execute(f"""
                        UPDATE {self.TABLE_NAME}
                        SET {field_name} = EMPTY_CLOB()
                        WHERE id = :id
                        RETURNING ROWID INTO :row_id
                    """, {'id': self.id, 'row_id': row_id_var})

                    row_id = row_id_var.getvalue()[0]

                    # 分块更新CLOB内容
                    for i, chunk in enumerate(sql_chunks):
                        if i == 0:
                            # 第一个块使用普通UPDATE
                            cursor.execute(f"""
                                UPDATE {self.TABLE_NAME}
                                SET {field_name} = :chunk
                                WHERE ROWID = :row_id
                            """, {'chunk': chunk, 'row_id': row_id})
                        else:
                            # 后续块使用DBMS_LOB.WRITEAPPEND
                            cursor.execute(f"""
                                DECLARE
                                    l_clob CLOB;
                                BEGIN
                                    SELECT {field_name} INTO l_clob
                                    FROM {self.TABLE_NAME}
                                    WHERE ROWID = :row_id
                                    FOR UPDATE;

                                    DBMS_LOB.WRITEAPPEND(l_clob, :chunk_len, :chunk);
                                END;
                            """, {
                                'row_id': row_id,
                                'chunk_len': len(chunk),
                                'chunk': chunk
                            })

                    conn.commit()
        except Exception as e:
            print(f"更新CLOB字段失败: {str(e)}")
            raise


class SelfCheckUpload(BaseModel):
    """数据上传记录模型"""
    
    TABLE_NAME = 'selfcheck_uploads'
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.user_id = kwargs.get('user_id')
        self.file_name = kwargs.get('file_name', '')
        self.file_path = kwargs.get('file_path', '')
        self.file_size = kwargs.get('file_size', 0)
        self.file_type = kwargs.get('file_type', '')
        self.upload_time = kwargs.get('upload_time')
        self.status = kwargs.get('status', 'pending')
        self.validation_result = kwargs.get('validation_result', '')
        self.error_message = kwargs.get('error_message', '')
        self.record_count = kwargs.get('record_count', 0)
    
    @classmethod
    def get_all(cls, page: int = 1, per_page: int = 20, user_id: Optional[int] = None, **filters) -> Dict[str, Any]:
        """获取所有上传记录（分页）"""
        where_conditions = []
        params = []
        
        if user_id:
            where_conditions.append("user_id = ?")
            params.append(user_id)
        
        if filters.get('file_name'):
            where_conditions.append("file_name LIKE ?")
            params.append(f"%{filters['file_name']}%")
        
        if filters.get('file_type'):
            where_conditions.append("file_type = ?")
            params.append(filters['file_type'])
        
        if filters.get('status'):
            where_conditions.append("status = ?")
            params.append(filters['status'])
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM {cls.TABLE_NAME} WHERE {where_clause}"
        count_result = execute_query(count_query, params)
        total = count_result[0]['total'] if count_result else 0
        
        # 获取分页数据
        offset = (page - 1) * per_page
        end_row = offset + per_page
        
        # 使用Oracle 11g兼容的分页查询，包含用户信息
        query = f"""
        SELECT * FROM (
            SELECT a.*, ROWNUM rn FROM (
                SELECT u.id, u.user_id, u.file_name, u.file_path, u.file_size,
                       u.file_type, u.upload_time, u.status, u.validation_result,
                       u.error_message, u.record_count,
                       usr.username, usr.real_name
                FROM {cls.TABLE_NAME} u
                LEFT JOIN users usr ON u.user_id = usr.id
                WHERE {where_clause}
                ORDER BY u.upload_time DESC
            ) a WHERE ROWNUM <= {end_row}
        )
        WHERE rn > {offset}
        """

        rows = execute_query(query, params)
        uploads = []
        for row in rows:
            upload = cls(**{k: v for k, v in row.items() if k not in ['username', 'real_name']})
            upload_dict = upload.to_dict()
            upload_dict['username'] = row.get('username', '')
            upload_dict['real_name'] = row.get('real_name', '')
            uploads.append(upload_dict)
        
        return {
            'uploads': uploads,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page if total > 0 else 0
        }
    
    @classmethod
    def get_by_id(cls, upload_id: int) -> Optional['SelfCheckUpload']:
        """根据ID获取上传记录"""
        query = f"SELECT * FROM {cls.TABLE_NAME} WHERE id = ?"
        rows = execute_query(query, [upload_id])
        return cls(**rows[0]) if rows else None
    
    def save(self) -> bool:
        """保存上传记录"""
        try:
            if self.id:
                # 更新
                query = f"""
                UPDATE {self.TABLE_NAME} SET
                    file_name = ?, file_path = ?, file_size = ?,
                    file_type = ?, status = ?, validation_result = ?,
                    error_message = ?, record_count = ?
                WHERE id = ?
                """
                params = [
                    self.file_name, self.file_path, self.file_size,
                    self.file_type, self.status, self.validation_result,
                    self.error_message, self.record_count, self.id
                ]
                execute_update(query, params)
            else:
                # 插入 - 先获取序列值
                seq_query = "SELECT selfcheck_uploads_seq.NEXTVAL as new_id FROM dual"
                seq_result = execute_query(seq_query)
                if seq_result:
                    new_id = seq_result[0]['new_id']
                    self.id = new_id

                    query = f"""
                    INSERT INTO {self.TABLE_NAME} (
                        id, user_id, file_name, file_path, file_size,
                        file_type, upload_time, status, validation_result,
                        error_message, record_count
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?,
                        CURRENT_TIMESTAMP, ?, ?, ?, ?
                    )
                    """
                    params = [
                        self.id, self.user_id, self.file_name, self.file_path,
                        self.file_size, self.file_type, self.status,
                        self.validation_result, self.error_message, self.record_count
                    ]
                    execute_update(query, params)
                else:
                    return False

            return True
        except Exception as e:
            print(f"保存上传记录失败: {str(e)}")
            return False
    
    def delete(self) -> bool:
        """删除上传记录"""
        if not self.id:
            return False
        
        try:
            query = f"DELETE FROM {self.TABLE_NAME} WHERE id = ?"
            execute_update(query, [self.id])
            return True
        except Exception as e:
            print(f"删除上传记录失败: {str(e)}")
            return False


class SelfCheckScheme:
    """自查方案模型"""

    def __init__(self, scheme_id=None, scheme_name=None, description=None,
                 status='active', created_by=None, created_at=None, updated_at=None):
        self.id = scheme_id
        self.scheme_name = scheme_name
        self.description = description
        self.status = status
        self.created_by = created_by
        self.created_at = created_at
        self.updated_at = updated_at

    def save(self):
        """保存方案到数据库"""
        try:
            from datetime import datetime

            if self.id:
                # 更新现有方案
                update_query = """
                UPDATE selfcheck_schemes
                SET scheme_name = :scheme_name, description = :description,
                    status = :status, updated_at = :updated_at
                WHERE id = :id
                """
                params = {
                    'id': self.id,
                    'scheme_name': self.scheme_name,
                    'description': self.description,
                    'status': self.status,
                    'updated_at': datetime.now()
                }
                execute_update(update_query, params)
            else:
                # 创建新方案 - 使用数据库管理器确保在同一连接中操作
                from app.selfcheck.database import get_db_manager

                db_manager = get_db_manager()

                # 先获取下一个序列值
                seq_query = "SELECT selfcheck_schemes_seq.NEXTVAL as new_id FROM dual"
                seq_result = db_manager.execute_query(seq_query)
                if not seq_result:
                    raise Exception("获取序列值失败")

                new_id = seq_result[0]['new_id']
                self.id = new_id

                # 插入新方案
                insert_query = """
                INSERT INTO selfcheck_schemes (id, scheme_name, description, status, created_by)
                VALUES (:id, :scheme_name, :description, :status, :created_by)
                """
                params = {
                    'id': new_id,
                    'scheme_name': self.scheme_name,
                    'description': self.description,
                    'status': self.status,
                    'created_by': self.created_by
                }
                db_manager.execute_update(insert_query, params)

            return self.to_dict()
        except Exception as e:
            logger.error(f"保存方案失败: {str(e)}")
            raise

    def delete(self):
        """删除方案"""
        try:
            if not self.id:
                raise ValueError("方案ID不能为空")

            # 删除方案（关联的规则会因为外键约束自动删除）
            delete_query = "DELETE FROM selfcheck_schemes WHERE id = :id"
            execute_update(delete_query, {'id': self.id})

        except Exception as e:
            logger.error(f"删除方案失败: {str(e)}")
            raise

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'scheme_name': self.scheme_name,
            'description': self.description,
            'status': self.status,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_by_id(cls, scheme_id):
        """根据ID获取方案"""
        try:
            query = "SELECT * FROM selfcheck_schemes WHERE id = :id"
            result = execute_query(query, {'id': scheme_id})

            if result:
                row = result[0]
                return cls(
                    scheme_id=row['id'],
                    scheme_name=row['scheme_name'],
                    description=row['description'],
                    status=row['status'],
                    created_by=row['created_by'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at']
                )
            return None
        except Exception as e:
            logger.error(f"获取方案失败: {str(e)}")
            raise
