#!/usr/bin/env python3
"""
微信配置检查工具
用于检查微信登录功能的配置状态
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def check_wechat_config():
    """检查微信配置"""
    print("=" * 50)
    print("微信登录配置检查工具")
    print("=" * 50)
    
    # 检查环境变量
    app_id = os.environ.get('WECHAT_APP_ID', 'your_app_id')
    app_secret = os.environ.get('WECHAT_APP_SECRET', 'your_app_secret')
    redirect_uri = os.environ.get('WECHAT_REDIRECT_URI', 'http://localhost:5002/auth/wechat/callback')
    
    print(f"1. 微信AppID: {app_id}")
    print(f"2. 微信AppSecret: {'*' * len(app_secret) if app_secret != 'your_app_secret' else app_secret}")
    print(f"3. 回调地址: {redirect_uri}")
    print()
    
    # 检查配置状态
    is_configured = (app_id != 'your_app_id' and 
                    app_secret != 'your_app_secret' and
                    app_id and app_secret)
    
    if is_configured:
        print("✅ 微信配置已完成")
        print()
        print("下一步操作：")
        print("1. 确保在微信公众平台配置了正确的授权回调域名")
        print("2. 重启应用：python run.py")
        print("3. 访问登录页面测试微信登录功能")
    else:
        print("❌ 微信配置未完成")
        print()
        print("需要完成的配置：")
        if app_id == 'your_app_id':
            print("- 设置 WECHAT_APP_ID 环境变量")
        if app_secret == 'your_app_secret':
            print("- 设置 WECHAT_APP_SECRET 环境变量")
        print()
        print("配置步骤：")
        print("1. 在微信公众平台获取AppID和AppSecret")
        print("2. 修改 .env 文件中的微信配置")
        print("3. 重新运行此检查工具")
    
    print()
    print("详细配置指南请查看：微信登录配置指南.md")
    print("=" * 50)

def generate_oauth_url():
    """生成微信授权URL（用于测试）"""
    app_id = os.environ.get('WECHAT_APP_ID', 'your_app_id')
    redirect_uri = os.environ.get('WECHAT_REDIRECT_URI', 'http://localhost:5002/auth/wechat/callback')
    
    if app_id == 'your_app_id':
        print("请先配置WECHAT_APP_ID")
        return
    
    import urllib.parse
    
    params = {
        'appid': app_id,
        'redirect_uri': redirect_uri,
        'response_type': 'code',
        'scope': 'snsapi_userinfo',
        'state': 'test'
    }
    
    query_string = urllib.parse.urlencode(params)
    oauth_url = f"https://open.weixin.qq.com/connect/oauth2/authorize?{query_string}#wechat_redirect"
    
    print("测试用微信授权URL：")
    print(oauth_url)
    print()
    print("注意：此URL需要在微信环境中访问才能正常工作")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--url':
        generate_oauth_url()
    else:
        check_wechat_config()
