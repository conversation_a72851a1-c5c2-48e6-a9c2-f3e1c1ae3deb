<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限树测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h2>权限树测试</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>权限数据</h4>
                <pre id="permissionData" style="max-height: 400px; overflow-y: auto; font-size: 12px;"></pre>
            </div>
            <div class="col-md-6">
                <h4>权限树</h4>
                <div id="permissionTree" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;"></div>
            </div>
        </div>
        <div class="mt-3">
            <button class="btn btn-primary" onclick="loadPermissions()">加载权限数据</button>
            <button class="btn btn-success" onclick="buildTree()">构建权限树</button>
        </div>
    </div>

    <script>
        let allPermissions = [];

        // 模拟权限数据（从API测试结果中提取的样本）
        const samplePermissions = [
            {
                "code": "selfcheck",
                "description": "自查自纠模块",
                "id": 1,
                "is_active": true,
                "module": "selfcheck",
                "name": "自查自纠",
                "parent_id": null,
                "resource_type": "menu",
                "sort_order": 1
            },
            {
                "code": "selfcheck.schemes.view",
                "description": "查看方案",
                "id": 2,
                "is_active": true,
                "module": "selfcheck",
                "name": "查看方案",
                "parent_id": 1,
                "resource_type": "page",
                "sort_order": 1
            },
            {
                "code": "selfcheck.schemes.create",
                "description": "创建方案",
                "id": 3,
                "is_active": true,
                "module": "selfcheck",
                "name": "创建方案",
                "parent_id": 2,
                "resource_type": "button",
                "sort_order": 1
            },
            {
                "code": "system",
                "description": "系统管理模块",
                "id": 4,
                "is_active": true,
                "module": "system",
                "name": "系统管理",
                "parent_id": null,
                "resource_type": "menu",
                "sort_order": 6
            },
            {
                "code": "system.role.view",
                "description": "查看角色",
                "id": 5,
                "is_active": true,
                "module": "system",
                "name": "角色管理",
                "parent_id": 4,
                "resource_type": "page",
                "sort_order": 2
            },
            {
                "code": "system.role.edit",
                "description": "编辑角色",
                "id": 6,
                "is_active": true,
                "module": "system",
                "name": "编辑角色",
                "parent_id": 5,
                "resource_type": "button",
                "sort_order": 2
            }
        ];

        function loadPermissions() {
            allPermissions = samplePermissions;
            document.getElementById('permissionData').textContent = JSON.stringify(allPermissions, null, 2);
            console.log('权限数据加载完成:', allPermissions.length, '个权限');
        }

        // 构建权限树结构
        function buildPermissionTree(permissions) {
            const tree = {};
            const permissionMap = {};
            
            console.log('开始构建权限树，权限数量:', permissions.length);
            
            // 创建权限映射表
            permissions.forEach(permission => {
                permissionMap[permission.id] = {
                    ...permission,
                    children: []
                };
            });
            
            console.log('权限映射表创建完成:', Object.keys(permissionMap).length);
            
            // 按模块分组并建立层级关系
            permissions.forEach(permission => {
                const module = permission.module || 'other';
                
                // 初始化模块
                if (!tree[module]) {
                    tree[module] = {
                        name: getModuleName(module),
                        code: module,
                        permissions: []
                    };
                }
                
                // 建立父子关系
                if (permission.parent_id && permissionMap[permission.parent_id]) {
                    // 有父权限，添加到父权限的children中
                    permissionMap[permission.parent_id].children.push(permissionMap[permission.id]);
                    console.log(`权限 ${permission.name} 添加到父权限 ${permissionMap[permission.parent_id].name} 下`);
                } else {
                    // 顶级权限，添加到模块的permissions中
                    tree[module].permissions.push(permissionMap[permission.id]);
                    console.log(`顶级权限 ${permission.name} 添加到模块 ${module} 下`);
                }
            });
            
            // 按sort_order排序
            Object.keys(tree).forEach(moduleKey => {
                tree[moduleKey].permissions.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
                sortPermissionChildren(tree[moduleKey].permissions);
            });
            
            console.log('权限树构建完成:', Object.keys(tree));
            return tree;
        }

        // 递归排序权限子项
        function sortPermissionChildren(permissions) {
            permissions.forEach(permission => {
                if (permission.children && permission.children.length > 0) {
                    permission.children.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
                    sortPermissionChildren(permission.children);
                }
            });
        }

        // 获取模块名称
        function getModuleName(module) {
            const moduleNames = {
                'system': '系统管理',
                'data': '数据处理',
                'excel': 'Excel工具',
                'database': '数据库工具',
                'rules': '规则管理',
                'selfcheck': '自查自纠'
            };
            return moduleNames[module] || module;
        }

        // 获取资源类型文本
        function getResourceTypeText(resourceType) {
            const typeNames = {
                'menu': '菜单',
                'page': '页面',
                'button': '按钮',
                'api': 'API'
            };
            return typeNames[resourceType] || resourceType || '其他';
        }

        // 渲染权限树
        function renderPermissionTree(tree, selectedPermissions = []) {
            let html = '<div class="permission-tree">';
            
            Object.keys(tree).forEach(moduleKey => {
                const module = tree[moduleKey];
                const moduleId = `module_${moduleKey}`;
                
                html += `
                    <div class="permission-module mb-3">
                        <div class="module-header d-flex align-items-center p-2 bg-light rounded">
                            <button type="button" class="btn btn-sm btn-link p-0 me-2 module-toggle" data-target="${moduleId}">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="form-check me-2">
                                <input class="form-check-input module-checkbox" type="checkbox" id="${moduleId}_checkbox" data-module="${moduleKey}">
                                <label class="form-check-label fw-bold" for="${moduleId}_checkbox">
                                    ${module.name}
                                </label>
                            </div>
                        </div>
                        <div class="module-content" id="${moduleId}" style="display: block;">
                            <div class="ps-4 pt-2">
                                ${renderPermissionItems(module.permissions, selectedPermissions, 0)}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        // 渲染权限项目
        function renderPermissionItems(permissions, selectedPermissions, level) {
            let html = '';
            
            permissions.forEach(permission => {
                const checked = selectedPermissions.includes(permission.id) ? 'checked' : '';
                const indent = level * 20;
                const hasChildren = permission.children && permission.children.length > 0;
                
                // 根据层级设置不同的样式
                let badgeClass = 'badge ms-1';
                
                switch(level) {
                    case 0: // 第二级：页面权限
                        badgeClass += ' bg-primary';
                        break;
                    case 1: // 第三级：按钮权限
                        badgeClass += ' bg-success';
                        break;
                    default:
                        badgeClass += ' bg-info';
                }
                
                html += `
                    <div class="permission-item mb-2" style="margin-left: ${indent}px;">
                        <div class="d-flex align-items-center">
                            ${hasChildren ? `
                                <button type="button" class="btn btn-sm btn-link p-0 me-2 permission-toggle" data-target="perm_${permission.id}">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            ` : '<span class="me-4"></span>'}
                            <div class="form-check">
                                <input class="form-check-input permission-checkbox" type="checkbox" value="${permission.id}" ${checked}
                                       data-parent="${permission.parent_id || ''}" data-level="${level}" data-code="${permission.code}">
                                <label class="form-check-label">
                                    <span class="permission-name">${permission.name}</span>
                                    ${permission.description ? `<small class="text-muted ms-1">(${permission.description})</small>` : ''}
                                    <span class="${badgeClass}">${getResourceTypeText(permission.resource_type)}</span>
                                    <small class="text-muted ms-1">${permission.code}</small>
                                </label>
                            </div>
                        </div>
                        ${hasChildren ? `
                            <div class="permission-children" id="perm_${permission.id}" style="display: block;">
                                ${renderPermissionItems(permission.children, selectedPermissions, level + 1)}
                            </div>
                        ` : ''}
                    </div>
                `;
            });
            
            return html;
        }

        function buildTree() {
            if (allPermissions.length === 0) {
                alert('请先加载权限数据');
                return;
            }
            
            console.log('开始构建权限树...');
            const tree = buildPermissionTree(allPermissions);
            console.log('权限树构建完成:', tree);
            
            const treeHtml = renderPermissionTree(tree, []);
            document.getElementById('permissionTree').innerHTML = treeHtml;
            
            // 绑定折叠事件
            $('.module-toggle').on('click', function() {
                const target = $(this).data('target');
                const content = $(`#${target}`);
                const icon = $(this).find('i');
                
                if (content.is(':visible')) {
                    content.slideUp();
                    icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                } else {
                    content.slideDown();
                    icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
                }
            });
            
            $('.permission-toggle').on('click', function() {
                const target = $(this).data('target');
                const content = $(`#${target}`);
                const icon = $(this).find('i');
                
                if (content.is(':visible')) {
                    content.slideUp();
                    icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                } else {
                    content.slideDown();
                    icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
                }
            });
        }

        // 页面加载完成后自动加载权限数据
        $(document).ready(function() {
            loadPermissions();
        });
    </script>
</body>
</html>
