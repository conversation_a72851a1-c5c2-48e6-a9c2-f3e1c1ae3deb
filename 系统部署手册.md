# MICRA自查自纠模块系统部署手册

**文档版本：** V1.0  
**编写日期：** 2025年1月7日  
**项目名称：** MICRA工具箱 - 自查自纠模块  
**文档类型：** 系统部署手册  

## 1. 部署概述

### 1.1 部署目标
本手册详细描述MICRA自查自纠模块的部署流程，包括环境准备、软件安装、配置设置和验证测试，确保系统能够稳定运行。

### 1.2 部署架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web服务器     │    │   应用服务器     │    │   数据库服务器   │
│                │    │                │    │                │
│ - Nginx 1.20+  │◄──►│ - Python 3.9+  │◄──►│ - Oracle 11g+  │
│ - 静态文件      │    │ - Flask 2.3+   │    │ - 数据存储      │
│ - 负载均衡      │    │ - Gunicorn     │    │ - 备份恢复      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.3 系统要求

#### 硬件要求
- **CPU：** 4核心以上，推荐8核心
- **内存：** 8GB以上，推荐16GB
- **存储：** 100GB以上可用空间
- **网络：** 千兆网卡

#### 软件要求
- **操作系统：** CentOS 7.6+ / Ubuntu 18.04+ / RHEL 7.6+
- **Python：** 3.9.0+
- **数据库：** Oracle 11g+ / PostgreSQL 12+
- **Web服务器：** Nginx 1.20+

## 2. 环境准备

### 2.1 操作系统配置

#### 2.1.1 系统更新
```bash
# CentOS/RHEL
sudo yum update -y
sudo yum install -y epel-release

# Ubuntu
sudo apt update && sudo apt upgrade -y
sudo apt install -y software-properties-common
```

#### 2.1.2 防火墙配置
```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload

# 或使用iptables
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 5000 -j ACCEPT
```

#### 2.1.3 系统用户创建
```bash
# 创建应用用户
sudo useradd -m -s /bin/bash micra
sudo usermod -aG wheel micra  # CentOS
sudo usermod -aG sudo micra   # Ubuntu

# 设置密码
sudo passwd micra
```

### 2.2 Python环境安装

#### 2.2.1 Python 3.9安装
```bash
# CentOS/RHEL
sudo yum install -y python39 python39-pip python39-devel

# Ubuntu
sudo apt install -y python3.9 python3.9-pip python3.9-dev python3.9-venv

# 验证安装
python3.9 --version
pip3.9 --version
```

#### 2.2.2 虚拟环境创建
```bash
# 切换到应用用户
sudo su - micra

# 创建项目目录
mkdir -p /home/<USER>/micra-app
cd /home/<USER>/micra-app

# 创建虚拟环境
python3.9 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 2.3 数据库环境

#### 2.3.1 Oracle数据库配置
```sql
-- 创建表空间
CREATE TABLESPACE MICRA_DATA
DATAFILE '/oracle/data/micra_data01.dbf' SIZE 1G
AUTOEXTEND ON NEXT 100M MAXSIZE 10G;

CREATE TABLESPACE MICRA_INDEX
DATAFILE '/oracle/data/micra_index01.dbf' SIZE 500M
AUTOEXTEND ON NEXT 50M MAXSIZE 5G;

-- 创建用户
CREATE USER micra_user IDENTIFIED BY "MicraPass123!"
DEFAULT TABLESPACE MICRA_DATA
TEMPORARY TABLESPACE TEMP;

-- 授权
GRANT CONNECT, RESOURCE TO micra_user;
GRANT CREATE VIEW, CREATE SEQUENCE, CREATE TRIGGER TO micra_user;
GRANT UNLIMITED TABLESPACE TO micra_user;
```

#### 2.3.2 Oracle客户端安装
```bash
# 下载Oracle Instant Client
wget https://download.oracle.com/otn_software/linux/instantclient/1921000/instantclient-basic-linux.x64-*********.0dbru.zip
wget https://download.oracle.com/otn_software/linux/instantclient/1921000/instantclient-devel-linux.x64-*********.0dbru.zip

# 解压安装
sudo mkdir -p /opt/oracle
sudo unzip instantclient-basic-linux.x64-*********.0dbru.zip -d /opt/oracle/
sudo unzip instantclient-devel-linux.x64-*********.0dbru.zip -d /opt/oracle/

# 配置环境变量
echo 'export ORACLE_HOME=/opt/oracle/instantclient_19_21' >> ~/.bashrc
echo 'export LD_LIBRARY_PATH=$ORACLE_HOME:$LD_LIBRARY_PATH' >> ~/.bashrc
echo 'export PATH=$ORACLE_HOME:$PATH' >> ~/.bashrc
source ~/.bashrc
```

## 3. 应用部署

### 3.1 代码部署

#### 3.1.1 获取源代码
```bash
# 从Git仓库克隆
cd /home/<USER>/micra-app
git clone https://github.com/your-org/micra-selfcheck.git .

# 或从压缩包解压
tar -xzf micra-selfcheck-v1.0.tar.gz
mv micra-selfcheck-v1.0/* .
```

#### 3.1.2 依赖安装
```bash
# 激活虚拟环境
source venv/bin/activate

# 安装Python依赖
pip install -r requirements.txt

# 安装Oracle数据库驱动
pip install oracledb

# 验证安装
pip list | grep -E "(Flask|oracledb|SQLAlchemy)"
```

### 3.2 配置文件设置

#### 3.2.1 应用配置
```bash
# 创建配置文件
cp config/config.example.py config/config.py

# 编辑配置文件
vim config/config.py
```

```python
# config/config.py
import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    
    # 数据库配置
    ORACLE_HOST = os.environ.get('ORACLE_HOST') or 'localhost'
    ORACLE_PORT = os.environ.get('ORACLE_PORT') or '1521'
    ORACLE_SERVICE = os.environ.get('ORACLE_SERVICE') or 'xe'
    ORACLE_USER = os.environ.get('ORACLE_USER') or 'micra_user'
    ORACLE_PASSWORD = os.environ.get('ORACLE_PASSWORD') or 'MicraPass123!'
    
    # 文件上传配置
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or '/home/<USER>/uploads'
    MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10MB
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or '/home/<USER>/logs/micra.log'

class ProductionConfig(Config):
    DEBUG = False
    TESTING = False

class DevelopmentConfig(Config):
    DEBUG = True
    TESTING = False

class TestingConfig(Config):
    DEBUG = True
    TESTING = True
```

#### 3.2.2 环境变量配置
```bash
# 创建环境变量文件
vim /home/<USER>/.env
```

```bash
# /home/<USER>/.env
export SECRET_KEY="your-production-secret-key"
export ORACLE_HOST="your-oracle-host"
export ORACLE_PORT="1521"
export ORACLE_SERVICE="your-service-name"
export ORACLE_USER="micra_user"
export ORACLE_PASSWORD="MicraPass123!"
export UPLOAD_FOLDER="/home/<USER>/uploads"
export LOG_LEVEL="INFO"
export LOG_FILE="/home/<USER>/logs/micra.log"
export FLASK_ENV="production"
```

```bash
# 加载环境变量
echo 'source /home/<USER>/.env' >> ~/.bashrc
source ~/.bashrc
```

### 3.3 目录结构创建
```bash
# 创建必要目录
mkdir -p /home/<USER>/uploads
mkdir -p /home/<USER>/logs
mkdir -p /home/<USER>/backups
mkdir -p /home/<USER>/temp

# 设置权限
chmod 755 /home/<USER>/uploads
chmod 755 /home/<USER>/logs
chmod 700 /home/<USER>/backups
```

### 3.4 数据库初始化

#### 3.4.1 创建数据库表
```bash
# 激活虚拟环境
source venv/bin/activate

# 运行数据库初始化脚本
python scripts/init_database.py

# 或手动执行SQL脚本
sqlplus micra_user/MicraPass123!@localhost:1521/xe @scripts/create_tables.sql
```

#### 3.4.2 初始数据导入
```bash
# 导入基础数据
python scripts/import_initial_data.py

# 创建管理员用户
python scripts/create_admin_user.py
```

## 4. Web服务器配置

### 4.1 Nginx安装配置

#### 4.1.1 Nginx安装
```bash
# CentOS/RHEL
sudo yum install -y nginx

# Ubuntu
sudo apt install -y nginx

# 启动并设置开机自启
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 4.1.2 Nginx配置
```bash
# 创建配置文件
sudo vim /etc/nginx/conf.d/micra.conf
```

```nginx
# /etc/nginx/conf.d/micra.conf
upstream micra_app {
    server 127.0.0.1:5000;
    # 如果有多个应用实例，可以添加更多server
    # server 127.0.0.1:5001;
    # server 127.0.0.1:5002;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态文件
    location /static {
        alias /home/<USER>/micra-app/app/static;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 文件上传
    location /uploads {
        alias /home/<USER>/uploads;
        internal;  # 仅内部访问
    }
    
    # 应用代理
    location / {
        proxy_pass http://micra_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 文件上传大小限制
        client_max_body_size 10M;
    }
}
```

#### 4.1.3 测试配置
```bash
# 测试Nginx配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

### 4.2 Gunicorn配置

#### 4.2.1 Gunicorn安装
```bash
# 激活虚拟环境
source venv/bin/activate

# 安装Gunicorn
pip install gunicorn
```

#### 4.2.2 Gunicorn配置文件
```bash
# 创建配置文件
vim /home/<USER>/micra-app/gunicorn.conf.py
```

```python
# gunicorn.conf.py
import multiprocessing

# 服务器配置
bind = "127.0.0.1:5000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 进程管理
preload_app = True
daemon = False
pidfile = "/home/<USER>/micra-app/gunicorn.pid"

# 日志配置
accesslog = "/home/<USER>/logs/gunicorn_access.log"
errorlog = "/home/<USER>/logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 安全配置
user = "micra"
group = "micra"
tmp_upload_dir = "/home/<USER>/temp"

# 性能配置
keepalive = 5
timeout = 30
graceful_timeout = 30
```

## 5. 系统服务配置

### 5.1 Systemd服务配置

#### 5.1.1 创建服务文件
```bash
sudo vim /etc/systemd/system/micra.service
```

```ini
[Unit]
Description=MICRA Self-Check Application
After=network.target oracle.service

[Service]
Type=notify
User=micra
Group=micra
WorkingDirectory=/home/<USER>/micra-app
Environment=PATH=/home/<USER>/micra-app/venv/bin
EnvironmentFile=/home/<USER>/.env
ExecStart=/home/<USER>/micra-app/venv/bin/gunicorn --config gunicorn.conf.py run:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 5.1.2 启动服务
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start micra

# 设置开机自启
sudo systemctl enable micra

# 查看服务状态
sudo systemctl status micra
```

### 5.2 日志轮转配置

#### 5.2.1 创建logrotate配置
```bash
sudo vim /etc/logrotate.d/micra
```

```
/home/<USER>/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 micra micra
    postrotate
        systemctl reload micra
    endscript
}
```

## 6. 监控和维护

### 6.1 健康检查脚本
```bash
# 创建健康检查脚本
vim /home/<USER>/scripts/health_check.sh
```

```bash
#!/bin/bash
# health_check.sh

# 检查应用服务
if ! systemctl is-active --quiet micra; then
    echo "CRITICAL: MICRA service is not running"
    exit 2
fi

# 检查HTTP响应
if ! curl -f -s http://localhost:5000/health > /dev/null; then
    echo "CRITICAL: MICRA application is not responding"
    exit 2
fi

# 检查数据库连接
if ! python3 /home/<USER>/scripts/check_db.py; then
    echo "CRITICAL: Database connection failed"
    exit 2
fi

echo "OK: All checks passed"
exit 0
```

### 6.2 备份脚本
```bash
# 创建备份脚本
vim /home/<USER>/scripts/backup.sh
```

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 数据库备份
expdp micra_user/MicraPass123! \
    directory=backup_dir \
    dumpfile=micra_${DATE}.dmp \
    logfile=micra_${DATE}.log \
    schemas=micra_user

# 应用文件备份
tar -czf ${BACKUP_DIR}/micra_app_${DATE}.tar.gz \
    --exclude='venv' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    /home/<USER>/micra-app

# 清理旧备份（保留30天）
find ${BACKUP_DIR} -name "*.dmp" -mtime +30 -delete
find ${BACKUP_DIR} -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: ${DATE}"
```

### 6.3 定时任务配置
```bash
# 编辑crontab
crontab -e
```

```cron
# 每天凌晨2点备份
0 2 * * * /home/<USER>/scripts/backup.sh >> /home/<USER>/logs/backup.log 2>&1

# 每5分钟健康检查
*/5 * * * * /home/<USER>/scripts/health_check.sh >> /home/<USER>/logs/health.log 2>&1

# 每天清理临时文件
0 3 * * * find /home/<USER>/temp -type f -mtime +1 -delete
```

## 7. 部署验证

### 7.1 功能测试
```bash
# 测试应用启动
curl -I http://localhost:5000/

# 测试登录页面
curl -s http://localhost:5000/auth/login | grep -q "登录"

# 测试API接口
curl -s http://localhost:5000/api/health | jq .
```

### 7.2 性能测试
```bash
# 安装压测工具
sudo yum install -y httpd-tools

# 并发测试
ab -n 1000 -c 10 http://localhost:5000/

# 长时间测试
ab -t 60 -c 5 http://localhost:5000/
```

### 7.3 安全检查
```bash
# 检查端口开放
nmap -sT localhost

# 检查文件权限
ls -la /home/<USER>/micra-app/
ls -la /home/<USER>/uploads/

# 检查进程运行用户
ps aux | grep gunicorn
```

---

**文档状态：** 已审核  
**批准人：** 运维负责人  
**批准日期：** 2025年1月7日  
**版本历史：**
- V1.0 (2025-01-07): 初始版本，包含完整部署流程
