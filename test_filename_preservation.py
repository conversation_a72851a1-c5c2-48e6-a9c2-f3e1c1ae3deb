#!/usr/bin/env python3
"""测试文件名保留策略的脚本"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.selfcheck.services import UploadService
from werkzeug.datastructures import FileStorage
import io

def test_filename_sanitization():
    """测试文件名清理功能"""
    print("=== 测试文件名清理功能 ===")
    
    test_cases = [
        ("正常文件.csv", "正常文件.csv"),
        ("测试/数据.csv", "测试_数据.csv"),
        ("文件\\备份.csv", "文件_备份.csv"),
        ("数据:版本.csv", "数据_版本.csv"),
        ("文件*副本.csv", "文件_副本.csv"),
        ("数据?文件.csv", "数据_文件.csv"),
        ('文件"名称.csv', "文件_名称.csv"),
        ("数据<新>.csv", "数据_新_.csv"),
        ("文件|备份.csv", "文件_备份.csv"),
        ("  .csv", "upload_file.csv"),
        ("", "upload_file"),
        ("北京市医保数据_2024年.csv", "北京市医保数据_2024年.csv"),
    ]
    
    for original, expected in test_cases:
        result = UploadService._sanitize_filename(original)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{original}' -> '{result}' (期望: '{expected}')")

def test_file_storage_with_original_names():
    """测试保留原始文件名的存储"""
    app = create_app()
    
    with app.app_context():
        print("\n=== 测试保留原始文件名的存储 ===")
        
        test_files = [
            "测试数据.csv",
            "北京市医保数据_2024.csv", 
            "飞检规则库/最新版本.csv",  # 包含危险字符
            "数据文件:备份.csv",        # 包含危险字符
            "自查规则*副本.csv",        # 包含危险字符
        ]
        
        for filename in test_files:
            print(f"\n测试文件: {filename}")
            
            # 创建测试文件
            csv_content = "rule_id,sql_content,city\n1,SELECT * FROM test,北京".encode('utf-8')
            test_file = FileStorage(
                stream=io.BytesIO(csv_content),
                filename=filename,
                content_type="text/csv"
            )
            
            try:
                # 测试保存
                upload_record = UploadService.save_upload(test_file, 1)
                
                print(f"  ✅ 保存成功!")
                print(f"  数据库文件名: {upload_record['file_name']}")
                print(f"  存储文件路径: {upload_record['file_path']}")
                
                # 提取存储的文件名
                stored_filename = os.path.basename(upload_record['file_path'])
                print(f"  存储文件名: {stored_filename}")
                
                # 检查文件是否存在
                if os.path.exists(upload_record['file_path']):
                    print(f"  ✅ 文件已保存到磁盘")
                    
                    # 检查文件名是否包含原始名称
                    if filename.replace('/', '_').replace(':', '_').replace('*', '_') in stored_filename:
                        print(f"  ✅ 存储文件名包含原始名称")
                    else:
                        print(f"  ❌ 存储文件名未包含原始名称")
                else:
                    print(f"  ❌ 文件未保存到磁盘")
                    
            except Exception as e:
                print(f"  ❌ 保存失败: {str(e)}")
                import traceback
                traceback.print_exc()

def test_filename_components():
    """测试文件名组成部分"""
    print("\n=== 测试文件名组成部分 ===")
    
    # 模拟时间戳
    timestamp = 1749217717
    
    test_cases = [
        "测试数据.csv",
        "北京市医保数据_2024.csv",
        "飞检规则库/最新版本.csv",
    ]
    
    for filename in test_cases:
        sanitized = UploadService._sanitize_filename(filename)
        final_name = f"{timestamp}_{sanitized}"
        
        print(f"原始: {filename}")
        print(f"清理: {sanitized}")
        print(f"最终: {final_name}")
        print()

if __name__ == '__main__':
    test_filename_sanitization()
    test_filename_components()
    test_file_storage_with_original_names()
