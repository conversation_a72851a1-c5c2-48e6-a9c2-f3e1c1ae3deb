package com.ruoyi.selfcheck.mapper;

import java.util.List;
import com.ruoyi.selfcheck.domain.UploadRecord;

/**
 * 数据上传记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface UploadRecordMapper 
{
    /**
     * 查询数据上传记录
     * 
     * @param uploadId 数据上传记录主键
     * @return 数据上传记录
     */
    public UploadRecord selectUploadRecordByUploadId(Long uploadId);

    /**
     * 查询数据上传记录列表
     * 
     * @param uploadRecord 数据上传记录
     * @return 数据上传记录集合
     */
    public List<UploadRecord> selectUploadRecordList(UploadRecord uploadRecord);

    /**
     * 新增数据上传记录
     * 
     * @param uploadRecord 数据上传记录
     * @return 结果
     */
    public int insertUploadRecord(UploadRecord uploadRecord);

    /**
     * 修改数据上传记录
     * 
     * @param uploadRecord 数据上传记录
     * @return 结果
     */
    public int updateUploadRecord(UploadRecord uploadRecord);

    /**
     * 删除数据上传记录
     * 
     * @param uploadId 数据上传记录主键
     * @return 结果
     */
    public int deleteUploadRecordByUploadId(Long uploadId);

    /**
     * 批量删除数据上传记录
     * 
     * @param uploadIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUploadRecordByUploadIds(Long[] uploadIds);

    /**
     * 根据用户ID查询上传记录
     * 
     * @param userId 用户ID
     * @return 上传记录列表
     */
    public List<UploadRecord> selectUploadRecordByUserId(Long userId);

    /**
     * 根据状态查询上传记录
     * 
     * @param status 状态
     * @return 上传记录列表
     */
    public List<UploadRecord> selectUploadRecordByStatus(String status);
}
