{% extends "base.html" %}

{% block title %}审计日志 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-history"></i> 审计日志</h4>
{% endblock %}

{% block content %}
<!-- 搜索过滤区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-filter"></i> 日志过滤
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="searchUser" class="form-label">用户</label>
                        <input type="text" class="form-control" id="searchUser" placeholder="用户名或真实姓名">
                    </div>
                    <div class="col-md-3">
                        <label for="searchAction" class="form-label">操作类型</label>
                        <select class="form-select" id="searchAction">
                            <option value="">所有操作</option>
                            <option value="login">登录</option>
                            <option value="logout">登出</option>
                            <option value="create_user">创建用户</option>
                            <option value="update_user">更新用户</option>
                            <option value="delete_user">删除用户</option>
                            <option value="create_role">创建角色</option>
                            <option value="update_role">更新角色</option>
                            <option value="delete_role">删除角色</option>
                            <option value="upload">文件上传</option>
                            <option value="download">文件下载</option>
                            <option value="access">页面访问</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="searchResource" class="form-label">资源类型</label>
                        <select class="form-select" id="searchResource">
                            <option value="">所有资源</option>
                            <option value="user">用户</option>
                            <option value="role">角色</option>
                            <option value="permission">权限</option>
                            <option value="excel_file">Excel文件</option>
                            <option value="data_file">数据文件</option>
                            <option value="sql_query">SQL查询</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="searchDateRange" class="form-label">时间范围</label>
                        <select class="form-select" id="searchDateRange">
                            <option value="">所有时间</option>
                            <option value="today">今天</option>
                            <option value="yesterday">昨天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                </div>
                
                <div class="row mt-3" id="customDateRange" style="display: none;">
                    <div class="col-md-3">
                        <label for="startDate" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="startDate">
                    </div>
                    <div class="col-md-3">
                        <label for="endDate" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="endDate">
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="searchLogs()">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="resetSearch()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                        <button type="button" class="btn btn-info ms-2" onclick="refreshLogs()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                        <button type="button" class="btn btn-success ms-2" onclick="exportLogs()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计信息区域 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h5 id="totalLogs">-</h5>
                <p class="mb-0">总日志数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h5 id="todayLogs">-</h5>
                <p class="mb-0">今日日志</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h5 id="activeUsers">-</h5>
                <p class="mb-0">活跃用户</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h5 id="successRate">-</h5>
                <p class="mb-0">成功率</p>
            </div>
        </div>
    </div>
</div>

<!-- 日志列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list"></i> 审计日志列表
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-sm" id="auditLogsTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>用户</th>
                                <th>操作</th>
                                <th>资源</th>
                                <th>资源ID</th>
                                <th>IP地址</th>
                                <th>用户代理</th>
                                <th>详情</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="auditLogsTableBody">
                            <!-- 日志数据将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="审计日志分页">
                    <ul class="pagination justify-content-center" id="auditLogsPagination">
                        <!-- 分页将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> 日志详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailContent">
                <!-- 日志详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let totalPages = 1;

// 页面加载完成后初始化
$(document).ready(function() {
    loadAuditLogs();
    loadStatistics();
    
    // 绑定时间范围变化事件
    $('#searchDateRange').change(function() {
        if ($(this).val() === 'custom') {
            $('#customDateRange').show();
        } else {
            $('#customDateRange').hide();
        }
    });
});

// 加载审计日志
function loadAuditLogs(page = 1) {
    currentPage = page;
    
    const searchParams = {
        page: page,
        per_page: 20,
        user: $('#searchUser').val(),
        action: $('#searchAction').val(),
        resource: $('#searchResource').val(),
        date_range: $('#searchDateRange').val(),
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val()
    };
    
    $.get('/admin/api/audit_logs', searchParams)
        .done(function(data) {
            displayAuditLogs(data.logs);
            updatePagination(data.current_page, data.pages, data.total);
        })
        .fail(function() {
            showAlert('加载审计日志失败', 'danger');
        });
}

// 显示审计日志
function displayAuditLogs(logs) {
    const tbody = $('#auditLogsTableBody');
    tbody.empty();
    
    logs.forEach(log => {
        const actionBadge = getActionBadge(log.action);
        const resourceBadge = getResourceBadge(log.resource);
        
        const row = `
            <tr>
                <td>${new Date(log.timestamp).toLocaleString()}</td>
                <td>
                    <div>
                        <strong>${log.user_name || '系统'}</strong>
                        <br><small class="text-muted">${log.user_real_name || ''}</small>
                    </div>
                </td>
                <td>${actionBadge}</td>
                <td>${resourceBadge}</td>
                <td>${log.resource_id || '-'}</td>
                <td>${log.ip_address || '-'}</td>
                <td title="${log.user_agent || '-'}">${truncateText(log.user_agent || '-', 20)}</td>
                <td title="${log.details || '-'}">${truncateText(log.details || '-', 30)}</td>
                <td>
                    <button class="btn btn-outline-info btn-sm" onclick="showLogDetail(${log.id})" title="详情">
                        <i class="fas fa-info"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 获取操作类型徽章
function getActionBadge(action) {
    const actionMap = {
        'login': { text: '登录', class: 'bg-success' },
        'logout': { text: '登出', class: 'bg-secondary' },
        'create_user': { text: '创建用户', class: 'bg-primary' },
        'update_user': { text: '更新用户', class: 'bg-warning' },
        'delete_user': { text: '删除用户', class: 'bg-danger' },
        'create_role': { text: '创建角色', class: 'bg-primary' },
        'update_role': { text: '更新角色', class: 'bg-warning' },
        'delete_role': { text: '删除角色', class: 'bg-danger' },
        'upload': { text: '上传', class: 'bg-info' },
        'download': { text: '下载', class: 'bg-info' },
        'access': { text: '访问', class: 'bg-light text-dark' }
    };
    
    const actionInfo = actionMap[action] || { text: action, class: 'bg-secondary' };
    return `<span class="badge ${actionInfo.class}">${actionInfo.text}</span>`;
}

// 获取资源类型徽章
function getResourceBadge(resource) {
    const resourceMap = {
        'user': { text: '用户', class: 'bg-primary' },
        'role': { text: '角色', class: 'bg-warning' },
        'permission': { text: '权限', class: 'bg-info' },
        'excel_file': { text: 'Excel文件', class: 'bg-success' },
        'data_file': { text: '数据文件', class: 'bg-success' },
        'sql_query': { text: 'SQL查询', class: 'bg-danger' }
    };
    
    const resourceInfo = resourceMap[resource] || { text: resource, class: 'bg-secondary' };
    return `<span class="badge ${resourceInfo.class}">${resourceInfo.text}</span>`;
}

// 截断文本
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// 更新分页
function updatePagination(currentPage, totalPages, totalItems) {
    const pagination = $('#auditLogsPagination');
    pagination.empty();
    
    // 上一页
    const prevDisabled = currentPage === 1 ? 'disabled' : '';
    pagination.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="loadAuditLogs(${currentPage - 1})">上一页</a>
        </li>
    `);
    
    // 页码
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const active = i === currentPage ? 'active' : '';
        pagination.append(`
            <li class="page-item ${active}">
                <a class="page-link" href="#" onclick="loadAuditLogs(${i})">${i}</a>
            </li>
        `);
    }
    
    // 下一页
    const nextDisabled = currentPage === totalPages ? 'disabled' : '';
    pagination.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="loadAuditLogs(${currentPage + 1})">下一页</a>
        </li>
    `);
    
    // 显示总数信息
    pagination.append(`
        <li class="page-item disabled">
            <span class="page-link">共 ${totalItems} 条记录</span>
        </li>
    `);
}

// 加载统计信息
function loadStatistics() {
    $.get('/admin/api/audit_logs/statistics')
        .done(function(data) {
            $('#totalLogs').text(data.total_logs || 0);
            $('#todayLogs').text(data.today_logs || 0);
            $('#activeUsers').text(data.active_users || 0);
            $('#successRate').text((data.success_rate || 0) + '%');
        })
        .fail(function() {
            console.error('加载统计信息失败');
        });
}

// 搜索日志
function searchLogs() {
    loadAuditLogs(1);
}

// 重置搜索
function resetSearch() {
    $('#searchUser').val('');
    $('#searchAction').val('');
    $('#searchResource').val('');
    $('#searchDateRange').val('');
    $('#startDate').val('');
    $('#endDate').val('');
    $('#customDateRange').hide();
    loadAuditLogs(1);
}

// 刷新日志
function refreshLogs() {
    loadAuditLogs(currentPage);
    loadStatistics();
}

// 显示日志详情
function showLogDetail(logId) {
    $.get(`/admin/api/audit_logs/${logId}`)
        .done(function(log) {
            const detailHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless table-sm">
                            <tr><th width="30%">日志ID:</th><td>${log.id}</td></tr>
                            <tr><th>时间:</th><td>${new Date(log.timestamp).toLocaleString()}</td></tr>
                            <tr><th>用户:</th><td>${log.user_name || '系统'} (${log.user_real_name || '-'})</td></tr>
                            <tr><th>操作:</th><td>${getActionBadge(log.action)}</td></tr>
                            <tr><th>资源:</th><td>${getResourceBadge(log.resource)}</td></tr>
                            <tr><th>资源ID:</th><td>${log.resource_id || '-'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless table-sm">
                            <tr><th width="30%">IP地址:</th><td>${log.ip_address || '-'}</td></tr>
                            <tr><th>用户代理:</th><td style="word-break: break-all;">${log.user_agent || '-'}</td></tr>
                            <tr><th>详情:</th><td style="word-break: break-all;">${log.details || '-'}</td></tr>
                        </table>
                    </div>
                </div>
            `;
            $('#logDetailContent').html(detailHtml);
            $('#logDetailModal').modal('show');
        })
        .fail(function() {
            showAlert('加载日志详情失败', 'danger');
        });
}

// 导出日志
function exportLogs() {
    const searchParams = {
        user: $('#searchUser').val(),
        action: $('#searchAction').val(),
        resource: $('#searchResource').val(),
        date_range: $('#searchDateRange').val(),
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val(),
        export: true
    };
    
    const queryString = $.param(searchParams);
    window.open(`/admin/api/audit_logs/export?${queryString}`, '_blank');
}

// 显示提示信息 - 使用右上角悬浮显示，避免遮挡用户菜单
function showAlert(message, type = 'info') {
    // 移除已存在的alert
    $('.alert.position-fixed').remove();

    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('body').append(alertHtml);

    // 自动关闭
    setTimeout(function() {
        $('.alert.position-fixed').fadeOut(500, function() {
            $(this).remove();
        });
    }, 4000);
}
</script>
{% endblock %}
