#!/usr/bin/env python3
"""
添加缺失的权限
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def add_missing_permissions():
    """添加缺失的权限"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            print("=== 添加缺失的权限 ===")
            
            # 需要添加的权限
            missing_permissions = [
                {
                    'code': 'selfcheck.tasks.delete',
                    'name': '删除任务',
                    'description': '删除自查任务',
                    'resource_type': 'button',
                    'parent_code': 'selfcheck.tasks'
                }
            ]
            
            for perm in missing_permissions:
                # 检查权限是否已存在
                check_query = "SELECT id FROM permissions WHERE code = :code"
                existing = db_manager.execute_query(check_query, {'code': perm['code']})
                
                if existing:
                    print(f"✅ 权限 {perm['code']} 已存在")
                    continue
                
                # 获取父权限ID
                parent_query = "SELECT id FROM permissions WHERE code = :parent_code"
                parent_result = db_manager.execute_query(parent_query, {'parent_code': perm['parent_code']})
                
                if not parent_result:
                    print(f"❌ 无法找到父权限: {perm['parent_code']}")
                    continue
                
                parent_id = parent_result[0]['id']
                
                # 插入新权限
                insert_query = """
                INSERT INTO permissions (id, name, code, description, module, resource_type, parent_id, is_active, created_at)
                VALUES (permissions_seq.NEXTVAL, :name, :code, :description, 'selfcheck', :resource_type, :parent_id, 1, CURRENT_TIMESTAMP)
                """
                
                db_manager.execute_update(insert_query, {
                    'name': perm['name'],
                    'code': perm['code'],
                    'description': perm['description'],
                    'resource_type': perm['resource_type'],
                    'parent_id': parent_id
                })
                
                print(f"✅ 已添加权限: {perm['code']} - {perm['name']}")
            
            print("\n=== 验证权限添加结果 ===")
            
            # 验证所有任务相关权限
            task_permissions = [
                'selfcheck.tasks.view',
                'selfcheck.tasks.create', 
                'selfcheck.tasks.delete',
                'selfcheck.tasks.execute',
                'selfcheck.tasks.result'
            ]
            
            for perm_code in task_permissions:
                query = "SELECT id, name, is_active FROM permissions WHERE code = :code"
                result = db_manager.execute_query(query, {'code': perm_code})
                
                if result:
                    perm = result[0]
                    active_text = '✅' if perm['is_active'] else '❌'
                    print(f"{active_text} {perm_code}: {perm['name']} (ID: {perm['id']})")
                else:
                    print(f"❌ {perm_code}: 不存在")
            
        except Exception as e:
            print(f"添加权限失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    add_missing_permissions()
