#!/usr/bin/env python3
"""
测试权限分配界面的HTML输出
"""

import requests
import json
from bs4 import BeautifulSoup

def test_permission_ui():
    """测试权限分配界面"""
    
    # 创建会话
    session = requests.Session()
    
    # 登录
    login_url = 'http://127.0.0.1:5002/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }
    
    # 获取登录页面以获取CSRF令牌
    login_page = session.get(login_url)
    soup = BeautifulSoup(login_page.text, 'html.parser')
    csrf_token = soup.find('input', {'name': 'csrf_token'})
    if csrf_token:
        login_data['csrf_token'] = csrf_token['value']
    
    # 执行登录
    login_response = session.post(login_url, data=login_data, allow_redirects=True)
    
    if '/index' not in login_response.url:
        print("❌ 登录失败")
        return
    
    print("✅ 登录成功")
    
    # 获取权限数据
    permissions_url = 'http://127.0.0.1:5002/admin/api/permissions'
    permissions_response = session.get(permissions_url)
    
    if permissions_response.status_code != 200:
        print("❌ 获取权限数据失败")
        return
    
    permissions_data = permissions_response.json()
    permissions = permissions_data['permissions']
    
    print(f"✅ 获取到 {len(permissions)} 个权限")
    
    # 模拟权限树构建
    def build_permission_tree(permissions):
        tree = {}
        permission_map = {}
        
        # 创建权限映射表
        for permission in permissions:
            permission_map[permission['id']] = {
                **permission,
                'children': []
            }
        
        # 按模块分组并建立层级关系
        for permission in permissions:
            module = permission.get('module', 'other')
            
            # 初始化模块
            if module not in tree:
                tree[module] = {
                    'name': get_module_name(module),
                    'code': module,
                    'permissions': []
                }
            
            # 建立父子关系
            if permission.get('parent_id') and permission_map.get(permission['parent_id']):
                # 有父权限，添加到父权限的children中
                permission_map[permission['parent_id']]['children'].append(permission_map[permission['id']])
            else:
                # 顶级权限，添加到模块的permissions中
                tree[module]['permissions'].append(permission_map[permission['id']])
        
        return tree
    
    def get_module_name(module):
        module_names = {
            'system': '系统管理',
            'data': '数据处理',
            'excel': 'Excel工具',
            'database': '数据库工具',
            'rules': '规则管理',
            'selfcheck': '自查自纠'
        }
        return module_names.get(module, module)
    
    def get_resource_type_text(resource_type):
        type_names = {
            'menu': '菜单',
            'page': '页面',
            'button': '按钮',
            'api': 'API'
        }
        return type_names.get(resource_type, resource_type or '其他')
    
    # 构建权限树
    permission_tree = build_permission_tree(permissions)
    
    print(f"\n权限树结构:")
    for module_key, module in permission_tree.items():
        print(f"模块: {module['name']} ({len(module['permissions'])}个顶级权限)")
        
        for perm in module['permissions'][:3]:  # 只显示前3个权限作为示例
            print(f"  - {perm['name']} [{get_resource_type_text(perm['resource_type'])}]")
            if perm.get('description') and perm['description'] != perm['name']:
                print(f"    描述: {perm['description']}")
            
            # 显示子权限
            if perm['children']:
                print(f"    子权限 ({len(perm['children'])}个):")
                for child in perm['children'][:2]:  # 只显示前2个子权限
                    print(f"      - {child['name']} [{get_resource_type_text(child['resource_type'])}]")
                    if child.get('description') and child['description'] != child['name']:
                        print(f"        描述: {child['description']}")
                if len(perm['children']) > 2:
                    print(f"      ... 还有 {len(perm['children']) - 2} 个子权限")
        
        if len(module['permissions']) > 3:
            print(f"  ... 还有 {len(module['permissions']) - 3} 个权限")
        print()
    
    # 测试HTML渲染（简化版）
    print("=== HTML渲染测试 ===")
    excel_module = permission_tree.get('excel')
    if excel_module:
        print(f"Excel工具模块权限渲染示例:")
        for perm in excel_module['permissions'][:2]:
            # 模拟HTML标签内容（不包含权限代码）
            badge_class = 'badge bg-primary' if perm['resource_type'] == 'menu' else 'badge bg-success'
            
            html_content = f"<span class=\"permission-name\">{perm['name']}</span>"
            if perm.get('description') and perm['description'] != perm['name']:
                html_content += f" <small class=\"text-muted\">(perm['description'])</small>"
            html_content += f" <span class=\"{badge_class}\">{get_resource_type_text(perm['resource_type'])}</span>"
            
            print(f"  权限HTML: {html_content}")
            
            # 子权限
            for child in perm['children'][:2]:
                child_badge_class = 'badge bg-success' if child['resource_type'] == 'button' else 'badge bg-info'
                child_html = f"    <span class=\"permission-name\">{child['name']}</span>"
                if child.get('description') and child['description'] != child['name']:
                    child_html += f" <small class=\"text-muted\">(child['description'])</small>"
                child_html += f" <span class=\"{child_badge_class}\">{get_resource_type_text(child['resource_type'])}</span>"
                print(f"  子权限HTML: {child_html}")

if __name__ == '__main__':
    try:
        test_permission_ui()
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
