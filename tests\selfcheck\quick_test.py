#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 运行核心功能的冒烟测试
"""

import unittest
import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from test_config import check_test_environment, TEST_CONFIG
from utils.api_client import SelfCheckAPIClient
from utils.data_generator import TestDataGenerator


class QuickSmokeTest(unittest.TestCase):
    """快速冒烟测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        print("=" * 60)
        print("自查自纠模块快速冒烟测试")
        print("=" * 60)
        
        # 检查测试环境
        if not check_test_environment():
            raise Exception("测试环境检查失败")
        
        cls.api_client = SelfCheckAPIClient(TEST_CONFIG['BASE_URL'])
        cls.data_generator = TestDataGenerator()
        cls.created_resources = []
        
        # 登录
        admin_user = TEST_CONFIG['ADMIN_USER']
        if not cls.api_client.login(admin_user['username'], admin_user['password']):
            raise Exception("管理员登录失败")
        
        print("✅ 测试环境准备完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 清理创建的测试资源
        print("\n清理测试资源...")
        for resource_type, resource_id in cls.created_resources:
            try:
                if resource_type == 'rule':
                    cls.api_client.delete_rule(resource_id)
                elif resource_type == 'scheme':
                    cls.api_client.delete_scheme(resource_id)
                # 可以添加更多资源类型的清理
            except:
                pass
        
        cls.api_client.close()
        print("✅ 测试资源清理完成")
    
    def test_01_application_health(self):
        """测试01 - 应用健康检查"""
        print("\n🔍 测试应用基本健康状态...")
        
        # 测试主页访问
        import requests
        response = requests.get(TEST_CONFIG['BASE_URL'])
        self.assertEqual(response.status_code, 200, "主页访问失败")
        
        # 测试自查自纠模块页面
        response = requests.get(f"{TEST_CONFIG['BASE_URL']}/selfcheck/")
        self.assertIn(response.status_code, [200, 302], "自查自纠模块访问失败")
        
        print("✅ 应用健康检查通过")
    
    def test_02_rules_basic_operations(self):
        """测试02 - 规则基本操作"""
        print("\n🔍 测试规则基本CRUD操作...")
        
        # 创建规则
        rule_data = {
            "rule_name": f"快速测试规则_{int(time.time())}",
            "rule_code": f"QUICK_TEST_{int(time.time())}",
            "rule_description": "快速测试创建的规则",
            "rule_type": "重复收费",
            "city": "北京",
            "sql_content": "SELECT * FROM test_table"
        }
        
        create_result = self.api_client.create_rule(rule_data)
        self.assertTrue(create_result.get('success', False), "规则创建失败")
        
        rule_id = create_result.get('rule', {}).get('id')
        self.assertIsNotNone(rule_id, "创建的规则缺少ID")
        self.created_resources.append(('rule', rule_id))
        
        # 查询规则
        get_result = self.api_client.get_rule(rule_id)
        self.assertTrue(get_result.get('success', False), "规则查询失败")
        
        # 更新规则
        update_data = {"rule_description": "更新后的规则描述"}
        update_result = self.api_client.update_rule(rule_id, update_data)
        self.assertTrue(update_result.get('success', False), "规则更新失败")
        
        print("✅ 规则基本操作测试通过")
    
    def test_03_rules_list_and_search(self):
        """测试03 - 规则列表和搜索"""
        print("\n🔍 测试规则列表查询和搜索功能...")
        
        # 获取规则列表
        list_result = self.api_client.get_rules(page=1, per_page=10)
        self.assertTrue(list_result.get('success', False), "规则列表查询失败")
        self.assertIn('rules', list_result, "规则列表响应格式错误")
        
        # 测试搜索功能
        search_result = self.api_client.get_rules(rule_name="测试")
        self.assertTrue(search_result.get('success', False), "规则搜索失败")
        
        print("✅ 规则列表和搜索测试通过")
    
    def test_04_importable_rules(self):
        """测试04 - 可导入规则查询"""
        print("\n🔍 测试可导入规则功能...")
        
        # 获取可导入规则列表
        import_list = self.api_client.get_importable_rules(page=1, per_page=5)
        self.assertTrue(import_list.get('success', False), "可导入规则列表查询失败")
        self.assertIn('rules', import_list, "可导入规则列表响应格式错误")
        
        # 测试过滤功能
        filtered_list = self.api_client.get_importable_rules(is_imported="false")
        self.assertTrue(filtered_list.get('success', False), "可导入规则过滤失败")
        
        print("✅ 可导入规则查询测试通过")
    
    def test_05_rule_import(self):
        """测试05 - 规则导入功能"""
        print("\n🔍 测试规则导入功能...")
        
        # 获取未导入的规则
        import_list = self.api_client.get_importable_rules(is_imported="false", per_page=1)
        
        if not import_list.get('success', False):
            print("⚠️ 无法获取可导入规则，跳过导入测试")
            return
        
        rules = import_list.get('rules', [])
        if not rules:
            print("⚠️ 没有可导入的规则，跳过导入测试")
            return
        
        rule = rules[0]
        compare_id = rule.get('compare_id')
        visit_type = rule.get('visit_type')
        
        if not compare_id or not visit_type:
            print("⚠️ 规则数据不完整，跳过导入测试")
            return
        
        # 执行导入
        import_result = self.api_client.import_rules([compare_id], [visit_type])
        
        # 验证导入结果（可能因为重复导入而失败，这是正常的）
        if import_result.get('success', False):
            print("✅ 规则导入测试通过")
        else:
            print("⚠️ 规则导入失败（可能是重复导入）")
    
    def test_06_schemes_basic_operations(self):
        """测试06 - 方案基本操作"""
        print("\n🔍 测试方案基本操作...")
        
        # 获取方案列表
        schemes_list = self.api_client.get_schemes(page=1, per_page=10)
        self.assertTrue(schemes_list.get('success', False), "方案列表查询失败")
        
        print("✅ 方案基本操作测试通过")
    
    def test_07_uploads_functionality(self):
        """测试07 - 上传功能"""
        print("\n🔍 测试上传功能...")
        
        # 获取上传记录列表
        uploads_list = self.api_client.get_uploads(page=1, per_page=10)
        self.assertTrue(uploads_list.get('success', False), "上传记录列表查询失败")
        
        print("✅ 上传功能测试通过")
    
    def test_08_tasks_functionality(self):
        """测试08 - 任务功能"""
        print("\n🔍 测试任务功能...")
        
        # 获取任务列表
        tasks_list = self.api_client.get_tasks(page=1, per_page=10)
        self.assertTrue(tasks_list.get('success', False), "任务列表查询失败")
        
        print("✅ 任务功能测试通过")
    
    def test_09_performance_basic(self):
        """测试09 - 基本性能测试"""
        print("\n🔍 测试基本性能...")
        
        # 测试规则列表查询性能
        start_time = time.time()
        list_result = self.api_client.get_rules(page=1, per_page=20)
        end_time = time.time()
        
        response_time = end_time - start_time
        max_time = TEST_CONFIG['PERFORMANCE_THRESHOLDS']['page_load_time']
        
        self.assertTrue(list_result.get('success', False), "性能测试查询失败")
        self.assertLessEqual(response_time, max_time, 
                           f"查询性能不达标: {response_time:.2f}s > {max_time}s")
        
        print(f"✅ 基本性能测试通过，响应时间: {response_time:.2f}s")


def run_quick_test():
    """运行快速测试"""
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(QuickSmokeTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    print("快速测试结果统计")
    print("=" * 60)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败详情:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误详情:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    status = "✅ 全部通过" if success else "❌ 存在失败"
    print(f"\n{status}")
    print("=" * 60)
    
    return success


if __name__ == '__main__':
    success = run_quick_test()
    sys.exit(0 if success else 1)
