#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试审计日志API
"""

import requests
import json
from datetime import datetime

def test_audit_api():
    """测试审计日志API"""
    base_url = "http://localhost:5002"
    
    print("=== 审计日志API测试 ===")
    
    # 创建session
    session = requests.Session()
    
    try:
        # 1. 获取登录页面
        print("1. 获取登录页面...")
        login_page = session.get(f"{base_url}/auth/login")
        print(f"   状态码: {login_page.status_code}")
        
        # 2. 执行登录
        print("2. 执行登录...")
        login_data = {
            'username': 'admin',
            'password': 'Admin123!'
        }
        
        login_response = session.post(
            f"{base_url}/auth/login",
            data=login_data,
            allow_redirects=True
        )
        print(f"   登录状态码: {login_response.status_code}")
        print(f"   最终URL: {login_response.url}")
        
        # 3. 测试审计日志API
        print("3. 测试审计日志API...")
        api_url = f"{base_url}/admin/api/audit_logs"
        api_response = session.get(api_url)
        print(f"   API状态码: {api_response.status_code}")
        
        if api_response.status_code == 200:
            try:
                data = api_response.json()
                print("   ✅ API响应成功")
                print(f"   总日志数: {data.get('total', 0)}")
                print(f"   当前页日志数: {len(data.get('logs', []))}")
                
                # 显示前3条日志
                logs = data.get('logs', [])
                if logs:
                    print("   最近的审计日志:")
                    for i, log in enumerate(logs[:3]):
                        timestamp = log.get('timestamp', 'N/A')
                        action = log.get('action', 'N/A')
                        resource = log.get('resource', 'N/A')
                        user_name = log.get('user_name', 'N/A')
                        print(f"     {i+1}. [{timestamp}] {user_name} - {action} - {resource}")
                else:
                    print("   暂无日志数据")
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                print(f"   响应内容: {api_response.text[:200]}...")
        else:
            print(f"   ❌ API请求失败: {api_response.status_code}")
            print(f"   响应内容: {api_response.text[:200]}...")
        
        # 4. 测试统计API
        print("4. 测试统计API...")
        stats_url = f"{base_url}/admin/api/audit_logs/statistics"
        stats_response = session.get(stats_url)
        print(f"   统计API状态码: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            try:
                stats = stats_response.json()
                print("   ✅ 统计API响应成功")
                print(f"   总日志数: {stats.get('total_logs', 0)}")
                print(f"   今日日志: {stats.get('today_logs', 0)}")
                print(f"   活跃用户: {stats.get('active_users', 0)}")
                print(f"   成功率: {stats.get('success_rate', 0)}%")
            except json.JSONDecodeError as e:
                print(f"   ❌ 统计JSON解析失败: {e}")
        else:
            print(f"   ❌ 统计API请求失败: {stats_response.status_code}")
        
        # 5. 访问审计日志页面
        print("5. 访问审计日志页面...")
        page_url = f"{base_url}/admin/audit_logs"
        page_response = session.get(page_url)
        print(f"   页面状态码: {page_response.status_code}")
        
        if page_response.status_code == 200:
            print("   ✅ 审计日志页面访问成功")
            # 检查页面是否包含关键元素
            if "审计日志" in page_response.text:
                print("   ✅ 页面包含审计日志标题")
            if "loadAuditLogs" in page_response.text:
                print("   ✅ 页面包含JavaScript函数")
        else:
            print(f"   ❌ 页面访问失败: {page_response.status_code}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        session.close()


def create_audit_log_data():
    """创建审计日志测试数据"""
    print("\n=== 创建审计日志测试数据 ===")
    
    try:
        from app import create_app, db
        from app.models.audit import AuditLog
        from app.models.user import User
        from datetime import datetime, timedelta
        
        app = create_app()
        with app.app_context():
            # 获取admin用户
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ 未找到admin用户")
                return
            
            print(f"找到admin用户: {admin_user.username}")
            
            # 检查现有日志数量
            count = AuditLog.query.count()
            print(f"当前审计日志数量: {count}")
            
            # 创建测试日志
            test_logs = [
                {
                    'action': 'login',
                    'resource': 'auth',
                    'details': '管理员登录系统',
                    'ip_address': '127.0.0.1'
                },
                {
                    'action': 'access',
                    'resource': 'admin_index',
                    'details': '访问系统管理首页',
                    'ip_address': '127.0.0.1'
                },
                {
                    'action': 'access',
                    'resource': 'audit_logs',
                    'details': '访问审计日志页面',
                    'ip_address': '127.0.0.1'
                },
                {
                    'action': 'create_user',
                    'resource': 'user',
                    'resource_id': '2',
                    'details': '创建新用户: test_user',
                    'ip_address': '*************'
                },
                {
                    'action': 'update_user',
                    'resource': 'user',
                    'resource_id': '2',
                    'details': '更新用户信息: test_user',
                    'ip_address': '*************'
                },
                {
                    'action': 'delete_rule',
                    'resource': 'selfcheck_rule',
                    'resource_id': '2182',
                    'details': '删除自查规则: 全国_住院_390_子宫颈管环扎术',
                    'ip_address': '127.0.0.1'
                },
                {
                    'action': 'upload_file',
                    'resource': 'data_file',
                    'resource_id': '1',
                    'details': '上传数据文件: sample_data.csv',
                    'ip_address': '*************'
                },
                {
                    'action': 'export_data',
                    'resource': 'audit_logs',
                    'details': '导出审计日志数据',
                    'ip_address': '127.0.0.1'
                }
            ]
            
            created_count = 0
            for i, log_data in enumerate(test_logs):
                # 创建时间稍有差异
                created_at = datetime.now() - timedelta(minutes=len(test_logs)-i)
                
                log = AuditLog(
                    user_id=admin_user.id,
                    action=log_data['action'],
                    resource=log_data['resource'],
                    resource_id=log_data.get('resource_id'),
                    details=log_data['details'],
                    ip_address=log_data.get('ip_address', '127.0.0.1'),
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    created_at=created_at
                )
                db.session.add(log)
                created_count += 1
            
            db.session.commit()
            print(f"✅ 成功创建 {created_count} 条测试审计日志")
            
            # 验证创建结果
            new_count = AuditLog.query.count()
            print(f"更新后审计日志总数: {new_count}")
            
            # 显示最新的几条日志
            recent_logs = AuditLog.query.order_by(AuditLog.created_at.desc()).limit(3).all()
            print("最新的审计日志:")
            for log in recent_logs:
                print(f"  {log.created_at.strftime('%Y-%m-%d %H:%M:%S')} - {log.action} - {log.resource} - {log.details}")
                
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 先创建测试数据
    create_audit_log_data()
    
    # 然后测试API
    test_audit_api()
