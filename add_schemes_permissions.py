#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.permission import Permission

def add_schemes_permissions():
    """添加方案管理权限"""
    app = create_app()
    
    with app.app_context():
        try:
            print('=== 添加方案管理权限 ===')
            
            # 获取自查自纠模块的父权限
            selfcheck_parent = Permission.query.filter_by(code='selfcheck').first()
            if not selfcheck_parent:
                print('错误：找不到自查自纠父权限')
                return
            
            # 检查是否已存在方案管理权限
            schemes_perm = Permission.query.filter_by(code='selfcheck.schemes').first()
            if not schemes_perm:
                # 创建方案管理权限
                schemes_perm = Permission(
                    name='方案管理',
                    code='selfcheck.schemes',
                    module='selfcheck',
                    resource_type='menu',
                    parent_id=selfcheck_parent.id,
                    sort_order=1,  # 放在第一位
                    is_active=True
                )
                db.session.add(schemes_perm)
                db.session.flush()
                print(f'✅ 创建方案管理权限: {schemes_perm.name} (ID: {schemes_perm.id})')
                
                # 更新其他权限的排序
                other_perms = Permission.query.filter_by(
                    parent_id=selfcheck_parent.id,
                    module='selfcheck'
                ).filter(Permission.id != schemes_perm.id).all()
                
                for perm in other_perms:
                    if perm.code == 'selfcheck.rules':
                        perm.sort_order = 2
                    elif perm.code == 'selfcheck.upload':
                        perm.sort_order = 3
                        # 同时修改名称和代码以保持一致性
                        perm.name = '数据上传'
                        perm.code = 'selfcheck.uploads'
                    elif perm.code == 'selfcheck.tasks':
                        perm.sort_order = 4
                
                # 更新upload相关的子权限代码
                upload_children = Permission.query.filter_by(parent_id=62).all()  # 62是原upload权限的ID
                for child in upload_children:
                    if child.code.startswith('selfcheck.upload.'):
                        child.code = child.code.replace('selfcheck.upload.', 'selfcheck.uploads.')
                        print(f'更新子权限代码: {child.name} -> {child.code}')
            else:
                print(f'✅ 方案管理权限已存在: {schemes_perm.name} (ID: {schemes_perm.id})')
            
            # 创建方案管理的子权限
            schemes_children = [
                {'name': '查看方案', 'code': 'selfcheck.schemes.view', 'resource_type': 'page', 'sort_order': 1},
                {'name': '创建方案', 'code': 'selfcheck.schemes.create', 'resource_type': 'button', 'sort_order': 2},
                {'name': '编辑方案', 'code': 'selfcheck.schemes.edit', 'resource_type': 'button', 'sort_order': 3},
                {'name': '删除方案', 'code': 'selfcheck.schemes.delete', 'resource_type': 'button', 'sort_order': 4},
                {'name': '管理方案规则', 'code': 'selfcheck.schemes.manage_rules', 'resource_type': 'button', 'sort_order': 5},
                {'name': '查看统计', 'code': 'selfcheck.schemes.statistics', 'resource_type': 'button', 'sort_order': 6}
            ]
            
            for child_config in schemes_children:
                existing_child = Permission.query.filter_by(code=child_config['code']).first()
                if not existing_child:
                    child_perm = Permission(
                        name=child_config['name'],
                        code=child_config['code'],
                        module='selfcheck',
                        resource_type=child_config['resource_type'],
                        parent_id=schemes_perm.id,
                        sort_order=child_config['sort_order'],
                        is_active=True
                    )
                    db.session.add(child_perm)
                    print(f'✅ 创建子权限: {child_perm.name} ({child_perm.code})')
                else:
                    # 更新现有权限
                    existing_child.name = child_config['name']
                    existing_child.resource_type = child_config['resource_type']
                    existing_child.parent_id = schemes_perm.id
                    existing_child.sort_order = child_config['sort_order']
                    existing_child.is_active = True
                    print(f'✅ 更新子权限: {existing_child.name} ({existing_child.code})')
            
            db.session.commit()
            print('✅ 方案管理权限添加完成！')
            
            # 显示最终的权限结构
            print('\n=== 最终的selfcheck权限结构 ===')
            selfcheck_perms = Permission.query.filter_by(module='selfcheck').order_by(
                Permission.parent_id.asc().nullsfirst(), 
                Permission.sort_order.asc()
            ).all()
            
            for perm in selfcheck_perms:
                indent = '  ' if perm.parent_id else ''
                parent_info = f' (父ID: {perm.parent_id})' if perm.parent_id else ''
                print(f'{indent}ID: {perm.id}, 名称: {perm.name}, 代码: {perm.code}, 类型: {perm.resource_type}, 排序: {perm.sort_order}{parent_info}')
                
        except Exception as e:
            print(f"添加失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    add_schemes_permissions()
