"""规则管理服务类"""

import os
import re
import logging
import pandas as pd
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import current_app
from app.utils.database import db_manager, execute_rules_query, handle_db_error
from app.models.audit import AuditLog

class RuleService:
    """规则管理服务类"""
    
    @staticmethod
    def get_all_rules(page=1, per_page=20, **filters):
        """获取所有规则（分页）- 对照app.py第2336行的实现"""
        try:
            with db_manager.get_connection() as conn:
                # 如果有过滤条件，使用搜索查询（对照app.py第3497行）
                if any(filters.values()):
                    return RuleService._get_filtered_rules(page, per_page, filters, conn)

                # 无过滤条件时，使用完整的查询（对照app.py第2336行）
                query = """
                WITH rule_cities AS (
                SELECT 
                    规则id,
                    to_char(wm_concat(DISTINCT 城市))  AS 城市列表,
                    to_char(wm_concat(DISTINCT 规则来源)) AS 规则来源列表
                FROM 规则医保编码对照
                GROUP BY 规则id
                )
                SELECT
                    a.id as "ID",
                    a.序号,
                    a.规则名称,
                    a.适用范围,
                    rc.城市列表 as 城市,
                    rc.规则来源列表 as 规则来源,
                    a.行为认定,
                    b.规则内涵,
                    a.发布时间,
                    a.生效日期,
                    a.失效日期,
                    a.涉及科室,
                    a.违规数量,
                    a.违规天数,
                    a.违规小时数,
                    a.违规金额,
                    a.年龄,
                    a.类型,
                    a.规则类型,
                    b.医保编码1,
                    b.医保名称1,
                    b.医保编码2,
                    b.医保名称2,
                    a.国家医保编码1,
                    a.国家医保名称1,
                    a.国家医保编码2,
                    a.国家医保名称2,
                    b.物价编码,
                    a.首次进入问题清单年份,
                    a.用途,
                    a.备注
                FROM 飞检规则知识库 a
                LEFT JOIN rule_cities rc ON a.id = rc.规则id 
                LEFT JOIN (
                    SELECT 规则id, 医保编码1, 医保名称1, 医保编码2, 医保名称2, 物价编码,规则内涵,
                           ROW_NUMBER() OVER (PARTITION BY 规则id ORDER BY 对照id) AS rn
                    FROM 规则医保编码对照
                ) b ON a.id = b.规则id AND b.rn = 1
                ORDER BY a.id DESC
                """

                df = execute_rules_query(conn, query)

                # 计算分页
                total = len(df) if not df.empty else 0
                start_idx = (page - 1) * per_page
                end_idx = start_idx + per_page

                # 分页处理
                if not df.empty:
                    paginated_df = df.iloc[start_idx:end_idx]
                    rules = []
                    for _, row in paginated_df.iterrows():
                        rule = {}
                        for col in paginated_df.columns:
                            value = row[col]
                            if pd.isna(value):
                                rule[col] = ''
                            else:
                                rule[col] = str(value) if isinstance(value, (int, float)) else value
                        rules.append(rule)
                else:
                    rules = []

                return {
                    'rules': rules,
                    'total': total,
                    'page': page,
                    'per_page': per_page,
                    'pages': (total + per_page - 1) // per_page
                }
        except Exception as e:
            logging.error(f"获取规则列表失败: {str(e)}")
            raise

    @staticmethod
    def _get_filtered_rules(page, per_page, filters, conn):
        """获取过滤后的规则列表 - 对照app.py第3497行的实现"""
        try:
            query = """
            SELECT
                b.对照id as id,
                b.序号_S as 序号,
                a.行为认定,
                b.规则内涵,
                a.规则名称,
                a.适用范围,
                b.城市,
                b.规则来源,
                b.医保编码1,
                b.医保名称1,
                b.医保编码2,
                b.医保名称2,
                a.违规数量,
                a.违规金额,
                a.类型,
                a.规则类型,
                a.排除诊断,
                a.排除科室,
                a.包含诊断,
                a.包含科室,
                a.时间类型,
                b.物价编码,
                a.备注,
                a.性别,
                a.用途
            FROM 飞检规则知识库 a, 规则医保编码对照 b
            WHERE a.id = b.规则id
            """
            params = {}

            # 添加过滤条件
            if filters.get('behavior_type'):
                query += " AND a.行为认定 = :behavior_type"
                params['behavior_type'] = filters['behavior_type']

            if filters.get('city'):
                query += " AND b.城市 = :city"
                params['city'] = filters['city']

            if filters.get('rule_source'):
                query += " AND b.规则来源 = :rule_source"
                params['rule_source'] = filters['rule_source']

            if filters.get('rule_name'):
                query += " AND a.规则名称 LIKE :rule_name"
                params['rule_name'] = f"%{filters['rule_name']}%"

            if filters.get('type'):
                query += " AND a.类型 = :type"
                params['type'] = filters['type']

            if filters.get('rule_type'):
                query += " AND a.规则类型 = :rule_type"
                params['rule_type'] = filters['rule_type']

            query += " ORDER BY a.id"

            df = execute_rules_query(conn, query, params)

            # 计算分页
            total = len(df) if not df.empty else 0
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page

            # 分页处理
            if not df.empty:
                paginated_df = df.iloc[start_idx:end_idx]
                rules = []
                for _, row in paginated_df.iterrows():
                    rule = {}
                    for col in paginated_df.columns:
                        value = row[col]
                        if pd.isna(value):
                            rule[col] = ''
                        else:
                            rule[col] = str(value) if isinstance(value, (int, float)) else value
                    rules.append(rule)
            else:
                rules = []

            return {
                'rules': rules,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page
            }
        except Exception as e:
            logging.error(f"获取过滤规则列表失败: {str(e)}")
            raise

    @staticmethod
    def get_rule_by_id(rule_id):
        """根据ID获取规则详情"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT
                    b.对照id,
                    a.id as "ID",
                    a.序号,
                    a.规则名称,
                    a.适用范围,
                    b.城市,
                    b.规则来源,
                    a.行为认定,
                    b.规则内涵,
                    a.涉及科室,
                    a.违规数量,
                    a.违规天数,
                    a.违规小时数,
                    a.违规金额,
                    a.年龄,
                    a.类型,
                    a.规则类型,
                    b.医保编码1,
                    b.医保名称1,
                    b.医保编码2,
                    b.医保名称2,
                    a.国家医保编码1,
                    a.国家医保名称1,
                    a.国家医保编码2,
                    a.国家医保名称2,
                    a.排除诊断,
                    a.排除科室,
                    a.包含诊断,
                    a.包含科室,
                    a.时间类型,
                    b.物价编码,
                    a.备注,
                    a.性别,
                    a.用途
                FROM 飞检规则知识库 a, 规则医保编码对照 b
                WHERE a.id = b.规则id AND a.id = :rule_id
                """
                
                df = execute_rules_query(conn, query, {'rule_id': rule_id})
                return df.to_dict('records') if not df.empty else []
        except Exception as e:
            logging.error(f"获取规则详情失败: {str(e)}")
            raise

    @staticmethod
    def search_rules(filters):
        """搜索规则"""
        try:
            with db_manager.get_connection() as conn:
                base_query = """
                SELECT
                    b.对照id,
                    a.id as ID,
                    b.序号_S as 序号,
                    a.行为认定,
                    b.规则内涵,
                    a.规则名称,
                    a.适用范围,
                    b.城市,
                    b.规则来源,
                    b.医保编码1,
                    b.医保名称1,
                    b.医保编码2,
                    b.医保名称2,
                    a.违规数量,
                    a.违规金额,
                    a.类型,
                    a.规则类型,
                    a.排除诊断,
                    a.排除科室,
                    a.包含诊断,
                    a.包含科室,
                    a.时间类型,
                    b.物价编码,
                    a.备注,
                    a.性别,
                    a.用途
                FROM 飞检规则知识库 a, 规则医保编码对照 b
                WHERE a.id = b.规则id
                """
                
                conditions = []
                params = {}
                
                # 添加搜索条件
                if filters.get('behavior_type'):
                    conditions.append("a.行为认定 LIKE :behavior_type")
                    params['behavior_type'] = f"%{filters['behavior_type']}%"

                if filters.get('city'):
                    conditions.append("b.城市 = :city")
                    params['city'] = filters['city']

                if filters.get('rule_source'):
                    conditions.append("b.规则来源 = :rule_source")
                    params['rule_source'] = filters['rule_source']

                if filters.get('rule_name'):
                    conditions.append("a.规则名称 LIKE :rule_name")
                    params['rule_name'] = f"%{filters['rule_name']}%"

                if filters.get('rule_content'):
                    conditions.append("b.规则内涵 LIKE :rule_content")
                    params['rule_content'] = f"%{filters['rule_content']}%"

                if filters.get('type'):
                    if filters['type'] == '__unset__':
                        conditions.append("(a.类型 IS NULL OR a.类型 = '')")
                    else:
                        conditions.append("a.类型 = :type")
                        params['type'] = filters['type']

                if filters.get('rule_type'):
                    if filters['rule_type'] == '__unset__':
                        conditions.append("(a.规则类型 IS NULL OR a.规则类型 = '')")
                    else:
                        conditions.append("a.规则类型 = :rule_type")
                        params['rule_type'] = filters['rule_type']
                
                if conditions:
                    base_query += " AND " + " AND ".join(conditions)
                
                base_query += " ORDER BY a.id"
                
                df = execute_rules_query(conn, base_query, params)
                return df.to_dict('records') if not df.empty else []
        except Exception as e:
            logging.error(f"搜索规则失败: {str(e)}")
            raise

    @staticmethod
    def get_behavior_types():
        """获取行为认定类型列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 行为认定
                FROM 飞检规则知识库
                WHERE 行为认定 IS NOT NULL
                ORDER BY 行为认定
                """
                df = execute_rules_query(conn, query)
                return df['行为认定'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取行为认定类型失败: {str(e)}")
            raise

    @staticmethod
    def get_cities():
        """获取城市列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 城市
                FROM 规则医保编码对照
                WHERE 城市 IS NOT NULL
                ORDER BY 城市
                """
                df = execute_rules_query(conn, query)
                return df['城市'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取城市列表失败: {str(e)}")
            raise

    @staticmethod
    def get_rule_sources():
        """获取规则来源列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 规则来源
                FROM 规则医保编码对照
                WHERE 规则来源 IS NOT NULL
                ORDER BY 规则来源
                """
                df = execute_rules_query(conn, query)
                return df['规则来源'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取规则来源列表失败: {str(e)}")
            raise

    @staticmethod
    def get_type_types():
        """获取类型列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 类型
                FROM 飞检规则知识库
                WHERE 类型 IS NOT NULL
                ORDER BY 类型
                """
                df = execute_rules_query(conn, query)
                return df['类型'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取类型列表失败: {str(e)}")
            # 返回默认值
            return ['药品', '诊疗', '检查', '手术', '材料', '其他']

    @staticmethod
    def get_rule_type_types():
        """获取规则类型列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 规则类型
                FROM 飞检规则知识库
                WHERE 规则类型 IS NOT NULL
                ORDER BY 规则类型
                """
                df = execute_rules_query(conn, query)
                return df['规则类型'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取规则类型列表失败: {str(e)}")
            # 返回默认值
            return ['基础规则', '复合规则', '统计规则', '关联规则', '时间规则', '频次规则']

    @staticmethod
    def create_rule(rule_data):
        """创建新规则"""
        try:
            with db_manager.get_connection() as conn:
                # 获取下一个ID
                next_id = RuleService.get_next_rule_id()

                # 插入到飞检规则知识库表
                insert_query = """
                INSERT INTO 飞检规则知识库 (
                    ID, 规则名称, 规则内涵, 类型, 规则类型, 行为认定,
                    违规数量, 违规金额, 年龄, 性别, 包含科室, 排除科室,
                    包含诊断, 排除诊断, 备注
                ) VALUES (
                    :id, :rule_name, :rule_content, :type, :rule_type, :behavior,
                    :violation_count, :violation_amount, :age, :gender, :include_dept, :exclude_dept,
                    :include_diag, :exclude_diag, :remark
                )
                """

                params = {
                    'id': next_id,
                    'rule_name': rule_data.get('规则名称', ''),
                    'rule_content': rule_data.get('规则内涵', ''),
                    'type': rule_data.get('类型', ''),
                    'rule_type': rule_data.get('规则类型', ''),
                    'behavior': rule_data.get('行为认定', ''),
                    'violation_count': rule_data.get('违规数量', ''),
                    'violation_amount': rule_data.get('违规金额', ''),
                    'age': rule_data.get('年龄', ''),
                    'gender': rule_data.get('性别', ''),
                    'include_dept': rule_data.get('包含科室', ''),
                    'exclude_dept': rule_data.get('排除科室', ''),
                    'include_diag': rule_data.get('包含诊断', ''),
                    'exclude_diag': rule_data.get('排除诊断', ''),
                    'remark': rule_data.get('备注', '')
                }

                with conn.cursor() as cursor:
                    cursor.execute(insert_query, params)

                # 如果有医保编码信息，插入到对照表
                if rule_data.get('医保编码1') or rule_data.get('医保名称1'):
                    insert_mapping_query = """
                    INSERT INTO 规则医保编码对照 (
                        规则id, 序号_S, 城市, 规则来源, 医保编码1, 医保名称1,
                        医保编码2, 医保名称2, 用途
                    ) VALUES (
                        :rule_id, :seq, :city, :rule_source, :code1, :name1,
                        :code2, :name2, :usage
                    )
                    """

                    mapping_params = {
                        'rule_id': next_id,
                        'seq': rule_data.get('序号', ''),
                        'city': rule_data.get('城市', ''),
                        'rule_source': rule_data.get('规则来源', ''),
                        'code1': rule_data.get('医保编码1', ''),
                        'name1': rule_data.get('医保名称1', ''),
                        'code2': rule_data.get('医保编码2', ''),
                        'name2': rule_data.get('医保名称2', ''),
                        'usage': rule_data.get('用途', '')
                    }

                    with conn.cursor() as cursor:
                        cursor.execute(insert_mapping_query, mapping_params)

                conn.commit()

                return {
                    'success': True,
                    'message': '规则创建成功',
                    'id': next_id
                }

        except Exception as e:
            logging.error(f"创建规则失败: {str(e)}")
            return {
                'success': False,
                'error': f'创建规则失败: {str(e)}'
            }

    @staticmethod
    def update_rule(rule_id, rule_data):
        """更新规则"""
        try:
            with db_manager.get_connection() as conn:
                # 更新飞检规则知识库表
                update_query = """
                UPDATE 飞检规则知识库 SET
                    规则名称 = :rule_name,
                    规则内涵 = :rule_content,
                    类型 = :type,
                    规则类型 = :rule_type,
                    行为认定 = :behavior,
                    违规数量 = :violation_count,
                    违规金额 = :violation_amount,
                    年龄 = :age,
                    性别 = :gender,
                    包含科室 = :include_dept,
                    排除科室 = :exclude_dept,
                    包含诊断 = :include_diag,
                    排除诊断 = :exclude_diag,
                    备注 = :remark
                WHERE ID = :id
                """

                params = {
                    'id': rule_id,
                    'rule_name': rule_data.get('规则名称', ''),
                    'rule_content': rule_data.get('规则内涵', ''),
                    'type': rule_data.get('类型', ''),
                    'rule_type': rule_data.get('规则类型', ''),
                    'behavior': rule_data.get('行为认定', ''),
                    'violation_count': rule_data.get('违规数量', ''),
                    'violation_amount': rule_data.get('违规金额', ''),
                    'age': rule_data.get('年龄', ''),
                    'gender': rule_data.get('性别', ''),
                    'include_dept': rule_data.get('包含科室', ''),
                    'exclude_dept': rule_data.get('排除科室', ''),
                    'include_diag': rule_data.get('包含诊断', ''),
                    'exclude_diag': rule_data.get('排除诊断', ''),
                    'remark': rule_data.get('备注', '')
                }

                with conn.cursor() as cursor:
                    cursor.execute(update_query, params)

                # 更新医保编码对照表
                update_mapping_query = """
                UPDATE 规则医保编码对照 SET
                    序号_S = :seq,
                    城市 = :city,
                    规则来源 = :rule_source,
                    医保编码1 = :code1,
                    医保名称1 = :name1,
                    医保编码2 = :code2,
                    医保名称2 = :name2,
                    用途 = :usage
                WHERE 规则id = :rule_id
                """

                mapping_params = {
                    'rule_id': rule_id,
                    'seq': rule_data.get('序号', ''),
                    'city': rule_data.get('城市', ''),
                    'rule_source': rule_data.get('规则来源', ''),
                    'code1': rule_data.get('医保编码1', ''),
                    'name1': rule_data.get('医保名称1', ''),
                    'code2': rule_data.get('医保编码2', ''),
                    'name2': rule_data.get('医保名称2', ''),
                    'usage': rule_data.get('用途', '')
                }

                with conn.cursor() as cursor:
                    cursor.execute(update_mapping_query, mapping_params)

                conn.commit()

                return {
                    'success': True,
                    'message': '规则更新成功'
                }

        except Exception as e:
            logging.error(f"更新规则失败: {str(e)}")
            return {
                'success': False,
                'error': f'更新规则失败: {str(e)}'
            }

    @staticmethod
    def delete_rule(rule_id):
        """删除规则"""
        try:
            with db_manager.get_connection() as conn:
                # 先删除医保编码对照表中的记录
                delete_mapping_query = "DELETE FROM 规则医保编码对照 WHERE 规则id = :rule_id"
                with conn.cursor() as cursor:
                    cursor.execute(delete_mapping_query, {'rule_id': rule_id})

                # 再删除飞检规则知识库表中的记录
                delete_rule_query = "DELETE FROM 飞检规则知识库 WHERE ID = :rule_id"
                with conn.cursor() as cursor:
                    cursor.execute(delete_rule_query, {'rule_id': rule_id})

                conn.commit()

                return {
                    'success': True,
                    'message': '规则删除成功'
                }

        except Exception as e:
            logging.error(f"删除规则失败: {str(e)}")
            return {
                'success': False,
                'error': f'删除规则失败: {str(e)}'
            }

    @staticmethod
    def get_next_rule_id():
        """获取下一个规则ID"""
        try:
            with db_manager.get_connection() as conn:
                # 如果有序列，使用序列
                try:
                    query = "SELECT 飞检规则知识库ID_SEQ.NEXTVAL as next_id FROM DUAL"
                    with conn.cursor() as cursor:
                        cursor.execute(query)
                        result = cursor.fetchone()
                        return result[0] if result else 1
                except:
                    # 如果没有序列，使用MAX+1
                    query = "SELECT NVL(MAX(ID), 0) + 1 as next_id FROM 飞检规则知识库"
                    with conn.cursor() as cursor:
                        cursor.execute(query)
                        result = cursor.fetchone()
                        return result[0] if result else 1
        except Exception as e:
            logging.error(f"获取下一个规则ID失败: {str(e)}")
            return 1

    @staticmethod
    def import_rules(rules_data):
        """导入规则 - 对照app.py第3077行的实现"""
        try:
            success_count = 0
            error_count = 0
            errors = []

            with db_manager.get_connection() as conn:
                for rule in rules_data:
                    try:
                        rule_name = rule.get('规则名称', '')
                        if not rule_name:
                            error_count += 1
                            errors.append('规则名称不能为空')
                            continue

                        # 检查规则是否已存在
                        check_query = """
                        SELECT ID FROM 飞检规则知识库 WHERE 规则名称 = :rule_name
                        """
                        with conn.cursor() as cursor:
                            cursor.execute(check_query, {'rule_name': rule_name})
                            existing_rule = cursor.fetchone()

                        if existing_rule:
                            # 更新现有规则
                            rule_id = existing_rule[0]
                            RuleService.update_rule(rule_id, rule)
                        else:
                            # 创建新规则
                            RuleService.create_rule(rule)

                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'导入规则 {rule.get("规则名称", "")} 失败: {str(e)}')
                        logging.error(f"导入单个规则失败: {str(e)}")

                conn.commit()

            return {
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors
            }
        except Exception as e:
            logging.error(f"导入规则失败: {str(e)}")
            raise

    @staticmethod
    def preview_import_rules(rules_data):
        """预览导入规则 - 对照app.py第2982行的实现"""
        try:
            preview_result = {
                'total_rules': len(rules_data),
                'new_rules': 0,
                'existing_rules': 0,
                'invalid_rules': 0,
                'details': []
            }

            with db_manager.get_connection() as conn:
                for rule in rules_data:
                    rule_name = rule.get('规则名称', '')
                    if not rule_name:
                        preview_result['invalid_rules'] += 1
                        preview_result['details'].append({
                            'rule_name': '未知',
                            'status': 'invalid',
                            'message': '规则名称不能为空'
                        })
                        continue

                    # 检查规则是否已存在
                    check_query = """
                    SELECT ID FROM 飞检规则知识库 WHERE 规则名称 = :rule_name
                    """
                    with conn.cursor() as cursor:
                        cursor.execute(check_query, {'rule_name': rule_name})
                        existing_rule = cursor.fetchone()

                    if existing_rule:
                        preview_result['existing_rules'] += 1
                        preview_result['details'].append({
                            'rule_name': rule_name,
                            'status': 'existing',
                            'message': '规则已存在，将被更新'
                        })
                    else:
                        preview_result['new_rules'] += 1
                        preview_result['details'].append({
                            'rule_name': rule_name,
                            'status': 'new',
                            'message': '新规则，将被创建'
                        })

            return preview_result
        except Exception as e:
            logging.error(f"预览导入规则失败: {str(e)}")
            raise

    @staticmethod
    def merge_rules(source_rule_id, target_rule_id):
        """合并规则 - 对照app.py第5652行的实现"""
        try:
            with db_manager.get_connection() as conn:
                # 获取源规则的对照信息
                source_query = """
                SELECT 对照id, a.id as rule_id, 规则名称, b.城市
                FROM 飞检规则知识库 a, 规则医保编码对照 b
                WHERE a.id = b.规则id AND a.id = :id
                """
                with conn.cursor() as cursor:
                    cursor.execute(source_query, {'id': source_rule_id})
                    source_rules = cursor.fetchall()

                if not source_rules:
                    raise Exception('源规则不存在')

                # 检查目标规则是否存在
                target_query = """
                SELECT id as rule_id FROM 飞检规则知识库 WHERE id = :id
                """
                with conn.cursor() as cursor:
                    cursor.execute(target_query, {'id': target_rule_id})
                    target_rule = cursor.fetchone()

                if not target_rule:
                    raise Exception('目标规则不存在')

                # 更新对照表，将源规则的对照记录指向目标规则
                updated_mappings = 0
                for compare_id, current_rule_id, rule_name, city in source_rules:
                    try:
                        update_query = """
                        UPDATE 规则医保编码对照
                        SET 规则id = :target_rule_id, 原规则名称 = :original_rule_name
                        WHERE 对照id = :compare_id
                        """
                        with conn.cursor() as cursor:
                            cursor.execute(update_query, {
                                'target_rule_id': target_rule_id,
                                'original_rule_name': rule_name,
                                'compare_id': compare_id
                            })
                        updated_mappings += 1
                    except Exception as e:
                        logging.error(f"更新对照记录失败: {str(e)}")

                # 删除源规则
                if updated_mappings > 0:
                    delete_query = "DELETE FROM 飞检规则知识库 WHERE id = :rule_id"
                    with conn.cursor() as cursor:
                        cursor.execute(delete_query, {'rule_id': source_rule_id})

                conn.commit()

                return {
                    'updated_mappings': updated_mappings,
                    'message': f'成功合并规则，更新了 {updated_mappings} 条对照记录'
                }
        except Exception as e:
            logging.error(f"合并规则失败: {str(e)}")
            raise

    @staticmethod
    def search_import_rules(keyword):
        """搜索可导入的规则 - 对照app.py第5191行的实现"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT a.id, a.规则名称, b.规则来源, b.规则内涵
                FROM 飞检规则知识库 a, 规则医保编码对照 b
                WHERE a.id = b.规则id
                AND a.规则名称 LIKE :keyword
                ORDER BY a.id DESC
                """

                df = execute_rules_query(conn, query, {'keyword': f'%{keyword}%'})
                return df.to_dict('records') if not df.empty else []
        except Exception as e:
            logging.error(f"搜索导入规则失败: {str(e)}")
            raise

    @staticmethod
    def test_sql(sql, database_type='oracle', host_ip='', schema=''):
        """测试SQL功能 - 对照app.py第3845行的实现"""
        try:
            # 为了安全起见，禁止执行危险的SQL操作
            sql_upper = sql.strip().upper()
            dangerous_keywords = ['UPDATE', 'DELETE', 'INSERT', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE', 'GRANT', 'REVOKE']

            for keyword in dangerous_keywords:
                if keyword in sql_upper:
                    raise Exception(f'出于安全考虑，不允许执行包含 {keyword} 的语句')

            if database_type.lower() == 'oracle':
                return RuleService._test_oracle_sql(sql, host_ip, schema)
            else:
                raise Exception(f'不支持的数据库类型: {database_type}')

        except Exception as e:
            logging.error(f"测试SQL失败: {str(e)}")
            raise

    @staticmethod
    def _test_oracle_sql(sql, host_ip='', schema=''):
        """测试Oracle SQL"""
        try:
            import oracledb

            if host_ip and host_ip != 'default':
                # 使用指定主机的Oracle连接
                dsn = f"{host_ip}:1521/orcl"
                with oracledb.connect(
                    user="datachange",
                    password="drgs2019",
                    dsn=dsn
                ) as conn:
                    return RuleService._execute_test_sql(conn, sql, schema, f'Oracle ({host_ip})')
            else:
                # 使用默认Oracle连接
                with db_manager.get_connection() as conn:
                    return RuleService._execute_test_sql(conn, sql, schema, 'Oracle (默认)')

        except Exception as e:
            logging.error(f"测试Oracle SQL失败: {str(e)}")
            raise

    @staticmethod
    def _execute_test_sql(conn, sql, schema='', database_info=''):
        """执行测试SQL"""
        try:
            with conn.cursor() as cursor:
                # 如果指定了schema，切换到该schema
                if schema:
                    cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {schema}")

                # 为了提高查询速度，自动添加行数限制
                limited_sql = sql.strip()
                if limited_sql.endswith(';'):
                    limited_sql = limited_sql[:-1]

                # 对于Oracle，使用子查询包装的方式添加ROWNUM限制
                if 'SELECT' in limited_sql.upper() and 'ROWNUM' not in limited_sql.upper():
                    limited_sql = f"SELECT * FROM ({limited_sql}) WHERE ROWNUM <= 10"

                cursor.execute(limited_sql)

                # 获取查询结果
                columns = [col[0] for col in cursor.description] if cursor.description else []
                rows = cursor.fetchall()

                schema_info = f" - {schema}" if schema else ""
                return {
                    'database': f'{database_info}{schema_info}',
                    'affected_rows': len(rows),
                    'columns': columns,
                    'data': rows[:10]  # 限制返回前10行数据
                }

        except Exception as e:
            logging.error(f"执行测试SQL失败: {str(e)}")
            raise

    @staticmethod
    def get_database_schemas(database_type='oracle', host_ip=''):
        """获取数据库Schema列表 - 对照app.py第3768行的实现"""
        try:
            if database_type.lower() == 'oracle':
                return RuleService._get_oracle_schemas(host_ip)
            else:
                raise Exception(f'不支持的数据库类型: {database_type}')

        except Exception as e:
            logging.error(f"获取数据库Schema失败: {str(e)}")
            raise

    @staticmethod
    def _get_oracle_schemas(host_ip=''):
        """获取Oracle Schema列表"""
        try:
            import oracledb

            if host_ip and host_ip != 'default':
                # 使用指定主机的Oracle连接
                dsn = f"{host_ip}:1521/orcl"
                with oracledb.connect(
                    user="datachange",
                    password="drgs2019",
                    dsn=dsn
                ) as conn:
                    return RuleService._fetch_oracle_schemas(conn)
            else:
                # 使用默认Oracle连接
                with db_manager.get_connection() as conn:
                    return RuleService._fetch_oracle_schemas(conn)

        except Exception as e:
            logging.error(f"获取Oracle Schema失败: {str(e)}")
            raise

    @staticmethod
    def _fetch_oracle_schemas(conn):
        """从Oracle连接中获取Schema列表"""
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT username FROM all_users
                    WHERE username NOT IN ('ANONYMOUS','APPQOSSYS','AUDSYS','CTXSYS','DBSFWUSER','DBSNMP','DIP','DVF','DVSYS','ENGINE','GGSYS','GSMADMIN_INTERNAL','GSMCATUSER','GSMROOTUSER','GSMUSER','HR','LBACSYS','MDDATA','MDSYS','OJVMSYS','OLAPSYS','ORACLE_OCM','ORDDATA','ORDPLUGINS','ORDSYS','OUTLN','REMOTE_SCHEDULER_AGENT','SI_INFORMTN_SCHEMA','SYS','SYS$UMF','SYSBACKUP','SYSDG','SYSKM','SYSRAC','SYSTEM','WMSYS','XDB','XS$NULL')
                    ORDER BY username
                """)
                rows = cursor.fetchall()
                return [row[0] for row in rows]

        except Exception as e:
            logging.error(f"从Oracle连接获取Schema失败: {str(e)}")
            raise

class SQLTemplateService:
    """SQL模板服务类"""
    
    @staticmethod
    def list_sql_templates(template_type='rule'):
        """列出SQL模板"""
        try:
            template_dirs = {
                'rule': os.path.join('templates', 'rule'),
                'manual': os.path.join('templates', 'manual'),
                'excel': os.path.join('templates', 'excel')
            }
            
            template_dir = template_dirs.get(template_type)
            if not template_dir or not os.path.exists(template_dir):
                return []
            
            templates = []
            for file in os.listdir(template_dir):
                if file.endswith('.sql'):
                    templates.append(file)
            
            return sorted(templates)
        except Exception as e:
            logging.error(f"列出SQL模板失败: {str(e)}")
            return []

    @staticmethod
    def get_template_variables(template_path, template_type='manual'):
        """获取模板变量"""
        try:
            if not os.path.exists(template_path):
                return []
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if template_type == 'manual':
                # 使用正则表达式匹配 {variable} 格式的变量
                variables = re.findall(r'\{(\w+)\}', content)
                return list(set(variables))  # 去重
            
            return []
        except Exception as e:
            logging.error(f"获取模板变量失败: {str(e)}")
            return []

    @staticmethod
    def render_sql_template(template_content, rule_data):
        """渲染SQL模板"""
        try:
            # 定义替换映射
            replacements = {
                "{规则名称}": str(rule_data.get("规则名称", "")),
                "{医保编码1}": str(rule_data.get("医保编码1", "")),
                "{医保名称1}": str(rule_data.get("医保名称1", "")),
                "{医保编码2}": str(rule_data.get("医保编码2", "")),
                "{医保名称2}": str(rule_data.get("医保名称2", "")),
                "{国家医保编码1}": str(rule_data.get("国家医保编码1", "")),
                "{国家医保名称1}": str(rule_data.get("国家医保名称1", "")),
                "{国家医保编码2}": str(rule_data.get("国家医保编码2", "")),
                "{国家医保名称2}": str(rule_data.get("国家医保名称2", "")),
                "{违规数量}": str(rule_data.get("违规数量", "")),
                "{违规天数}": str(rule_data.get("违规天数", "")),
                "{违规小时数}": str(rule_data.get("违规小时数", "")),
                "{违规金额}": str(rule_data.get("违规金额", "")),
                "{年龄}": str(rule_data.get("年龄", "")),
                "{性别}": str(rule_data.get("性别", "")),
                "{排除科室}": str(rule_data.get("排除科室", "")),
                "{包含科室}": str(rule_data.get("包含科室", "")),
                "{排除诊断}": str(rule_data.get("排除诊断", "")),
                "{包含诊断}": str(rule_data.get("包含诊断", "")),
                "{时间类型}": str(rule_data.get("时间类型", "")),
                "{物价编码}": str(rule_data.get("物价编码", "")),
                "{备注}": str(rule_data.get("备注", ""))
            }
            
            # 执行替换
            result = template_content
            for placeholder, value in replacements.items():
                result = result.replace(placeholder, value)
                
            return result
        except Exception as e:
            logging.error(f"渲染SQL模板失败: {str(e)}")
            return template_content


class ConverterService:
    """智能转换工具服务类 - 遵循项目标准"""

    # 支持的文件格式
    SUPPORTED_FORMATS = ['xlsx', 'xls', 'pdf', 'doc', 'docx']

    # 规则条目识别模式
    RULE_PATTERNS = [
        r'不得|禁止|严禁|不允许|不可',  # 禁止性词汇
        r'超出|超过|违反|不符合',      # 违规性词汇
        r'重复|多次|频繁',            # 频次性词汇
        r'虚假|伪造|冒用',            # 欺诈性词汇
    ]

    # 医保相关关键词
    MEDICAL_KEYWORDS = [
        '医保', '医疗保险', '基金', '报销', '结算', '费用',
        '诊疗', '药品', '耗材', '检查', '治疗', '手术',
        '住院', '门诊', '急诊', '康复', '护理'
    ]

    @staticmethod
    @handle_db_error
    def upload_file(file, user_id: int) -> Dict[str, Any]:
        """上传文件 - 遵循项目标准"""
        try:
            if not file or not file.filename:
                return {'success': False, 'error': '没有选择文件'}

            # 检查文件格式
            file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
            if file_ext not in ConverterService.SUPPORTED_FORMATS:
                return {'success': False, 'error': f'不支持的文件格式，支持格式：{", ".join(ConverterService.SUPPORTED_FORMATS)}'}

            # 创建上传目录
            upload_dir = os.path.join('uploads', 'converter', str(user_id))
            os.makedirs(upload_dir, exist_ok=True)

            # 生成唯一文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{file.filename}"
            file_path = os.path.join(upload_dir, filename)

            # 保存文件
            file.save(file_path)

            # 记录到数据库
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 先获取序列值
                    cursor.execute("SELECT converter_uploads_seq.NEXTVAL FROM dual")
                    upload_id = cursor.fetchone()[0]

                    # 插入记录
                    insert_sql = """
                    INSERT INTO converter_uploads (
                        id, user_id, original_filename, file_path, file_size, file_type,
                        status, upload_time
                    ) VALUES (
                        :upload_id, :user_id, :original_filename, :file_path,
                        :file_size, :file_type, 'uploaded', CURRENT_TIMESTAMP
                    )
                    """

                    file_size = os.path.getsize(file_path)
                    params = {
                        'upload_id': upload_id,
                        'user_id': user_id,
                        'original_filename': file.filename,
                        'file_path': file_path,
                        'file_size': file_size,
                        'file_type': file_ext
                    }

                    cursor.execute(insert_sql, params)

                conn.commit()

            logging.info(f"文件上传成功: {file.filename}, ID: {upload_id}")

            return {
                'success': True,
                'upload_id': upload_id,
                'filename': file.filename,
                'file_size': file_size,
                'file_type': file_ext,
                'message': '文件上传成功'
            }

        except Exception as e:
            logging.error(f"文件上传失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'文件上传失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def parse_file(upload_id: int) -> Dict[str, Any]:
        """解析文件内容 - 遵循项目标准"""
        try:
            # 获取上传记录
            with db_manager.get_connection() as conn:
                upload_query = "SELECT * FROM converter_uploads WHERE id = :upload_id"
                df = execute_rules_query(conn, upload_query, {'upload_id': upload_id})

                if df.empty:
                    return {'success': False, 'error': '上传记录不存在'}

                upload = df.iloc[0]
                # 使用列索引而不是列名，因为Oracle可能返回大写列名
                file_path = upload.iloc[3]  # file_path是第4列
                file_type = upload.iloc[5]  # file_type是第6列

                # 更新状态为解析中
                ConverterService._update_status(upload_id, 'parsing', '正在解析文件...')

                # 根据文件类型选择解析方法 - 新的表格解析流程
                if file_type in ['xlsx', 'xls']:
                    result = ConverterService._parse_excel_to_table(file_path)
                elif file_type == 'pdf':
                    result = ConverterService._parse_pdf_to_table(file_path)
                elif file_type in ['doc', 'docx']:
                    result = ConverterService._parse_word_to_table(file_path)
                else:
                    return {'success': False, 'error': f'不支持的文件类型: {file_type}'}

                if result['success']:
                    # 保存原始表格数据到数据库
                    save_result = ConverterService._save_raw_table_data(upload_id, result['table_data'])

                    if save_result['success']:
                        ConverterService._update_status(upload_id, 'table_parsed', f'表格解析完成，提取 {len(result["table_data"]["rows"])} 行数据')

                        return {
                            'success': True,
                            'table_data': result['table_data'],
                            'rows_count': len(result['table_data']['rows']),
                            'message': f'表格解析成功，提取 {len(result["table_data"]["rows"])} 行数据',
                            'next_step': 'view_table'  # 下一步：查看表格内容
                        }
                    else:
                        ConverterService._update_status(upload_id, 'failed', f'保存表格数据失败: {save_result["error"]}')
                        return save_result
                else:
                    ConverterService._update_status(upload_id, 'failed', f'文件解析失败: {result["error"]}')
                    return result

        except Exception as e:
            error_msg = f"文件解析失败: {str(e)}"
            logging.error(error_msg, exc_info=True)
            ConverterService._update_status(upload_id, 'failed', error_msg)
            return {'success': False, 'error': error_msg}

    @staticmethod
    def _parse_excel(file_path: str) -> Dict[str, Any]:
        """解析Excel文件"""
        try:
            logging.info(f"开始解析Excel文件: {file_path}")

            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            all_content = []

            for sheet_name in excel_file.sheet_names:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)

                    # 转换为文本内容
                    for _, row in df.iterrows():
                        row_text = ' '.join([str(cell) for cell in row.values if pd.notna(cell)])
                        if row_text.strip():
                            all_content.append({
                                'sheet': sheet_name,
                                'content': row_text.strip(),
                                'type': 'table_row'
                            })

                except Exception as e:
                    logging.warning(f"解析工作表 {sheet_name} 失败: {str(e)}")
                    continue

            logging.info(f"Excel解析完成，提取 {len(all_content)} 行内容")
            return {'success': True, 'content': all_content}

        except Exception as e:
            logging.error(f"Excel解析失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'Excel解析失败: {str(e)}'}

    @staticmethod
    def _parse_pdf(file_path: str) -> Dict[str, Any]:
        """解析PDF文件"""
        try:
            logging.info(f"开始解析PDF文件: {file_path}")

            # 这里需要安装PyPDF2或pdfplumber库
            try:
                import PyPDF2

                all_content = []
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)

                    for page_num, page in enumerate(pdf_reader.pages):
                        try:
                            text = page.extract_text()
                            if text.strip():
                                # 按行分割
                                lines = text.split('\n')
                                for line in lines:
                                    if line.strip():
                                        all_content.append({
                                            'page': page_num + 1,
                                            'content': line.strip(),
                                            'type': 'text_line'
                                        })
                        except Exception as e:
                            logging.warning(f"解析PDF第{page_num + 1}页失败: {str(e)}")
                            continue

                logging.info(f"PDF解析完成，提取 {len(all_content)} 行内容")
                return {'success': True, 'content': all_content}

            except ImportError:
                return {'success': False, 'error': 'PDF解析功能需要安装PyPDF2库'}

        except Exception as e:
            logging.error(f"PDF解析失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'PDF解析失败: {str(e)}'}

    @staticmethod
    def _parse_word(file_path: str) -> Dict[str, Any]:
        """解析Word文件"""
        try:
            logging.info(f"开始解析Word文件: {file_path}")

            # 这里需要安装python-docx库
            try:
                from docx import Document

                doc = Document(file_path)
                all_content = []

                # 解析段落
                for para_num, paragraph in enumerate(doc.paragraphs):
                    if paragraph.text.strip():
                        all_content.append({
                            'paragraph': para_num + 1,
                            'content': paragraph.text.strip(),
                            'type': 'paragraph'
                        })

                # 解析表格
                for table_num, table in enumerate(doc.tables):
                    for row_num, row in enumerate(table.rows):
                        row_text = ' '.join([cell.text.strip() for cell in row.cells if cell.text.strip()])
                        if row_text:
                            all_content.append({
                                'table': table_num + 1,
                                'row': row_num + 1,
                                'content': row_text,
                                'type': 'table_row'
                            })

                logging.info(f"Word解析完成，提取 {len(all_content)} 项内容")
                return {'success': True, 'content': all_content}

            except ImportError:
                return {'success': False, 'error': 'Word解析功能需要安装python-docx库'}

        except Exception as e:
            logging.error(f"Word解析失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'Word解析失败: {str(e)}'}

    @staticmethod
    def _extract_rules(content_list: List[Dict[str, Any]], upload_info: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """智能提取规则条目 - 基于AI语义理解"""
        try:
            logging.info("开始智能提取规则条目")

            # 从文件名提取规则来源
            if upload_info is not None and hasattr(upload_info, 'get'):
                source_filename = upload_info.get('original_filename', '')
            elif upload_info is not None and hasattr(upload_info, 'iloc'):
                # 如果是pandas Series，使用iloc访问
                source_filename = str(upload_info.iloc[2]) if len(upload_info) > 2 else ''
            else:
                source_filename = ''

            rule_source = ConverterService._extract_rule_source(source_filename)

            # 使用增强的本地AI提取规则（暂时禁用Gemini API）
            rules = ConverterService._enhanced_ai_extract_rules(content_list, rule_source, upload_info)

            logging.info(f"规则提取完成，共识别 {len(rules)} 条规则")
            return rules

        except Exception as e:
            logging.error(f"规则提取失败: {str(e)}", exc_info=True)
            return []

    @staticmethod
    def _extract_rule_source(filename: str) -> str:
        """从文件名提取规则来源"""
        # 移除文件扩展名
        name = filename.rsplit('.', 1)[0] if '.' in filename else filename

        # 移除时间戳前缀（如果有）
        import re
        name = re.sub(r'^\d{8}_\d{6}_', '', name)

        # 标准化名称
        if '安徽' in name:
            return '安徽省定点医药机构违法违规使用医保基金自查自纠问题清单(2025)'
        elif '国家' in name and '负面清单' in name:
            return '国家医保局医保基金使用负面清单'
        elif '飞检' in name:
            return '国家飞检问题清单'
        else:
            return name

    @staticmethod
    def _parse_table_structure(content_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析表格结构"""
        try:
            table_rows = []
            headers = []

            for item in content_list:
                content = item['content'].strip()
                if not content:
                    continue

                # 检查是否是表头
                if any(keyword in content for keyword in ['序号', '领域', '违规性质', '具体内容', '政策依据']):
                    # 分割表头
                    headers = [h.strip() for h in content.split() if h.strip()]
                    continue

                # 检查是否是数据行（以数字开头）
                if content[0].isdigit() and len(headers) > 0:
                    # 尝试分割数据行
                    parts = content.split('\t')  # 先尝试制表符分割
                    if len(parts) < 3:
                        parts = content.split('  ')  # 再尝试双空格分割

                    if len(parts) >= 3:
                        row_data = {
                            'row_number': len(table_rows) + 1,
                            'content': content,
                            'parts': parts,
                            'source_info': item
                        }
                        table_rows.append(row_data)

            return table_rows if len(table_rows) > 0 else None

        except Exception as e:
            logging.warning(f"表格结构解析失败: {str(e)}")
            return None

    @staticmethod
    def _extract_from_table(table_data: List[Dict[str, Any]], rule_source: str, upload_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从表格数据中提取规则"""
        rules = []

        for row in table_data:
            parts = row['parts']

            if len(parts) >= 4:
                # 按照安徽省格式解析：序号、领域、违规性质、具体内容、政策依据
                rule = {
                    'id': row['row_number'],
                    'original_content': row['content'],

                    # 标准化字段
                    'rule_source': rule_source,
                    'city': '合肥',  # 默认城市，可以从文件名或内容中提取
                    'sequence_number': ConverterService._safe_int(parts[0]),
                    'department': parts[1] if len(parts) > 1 else '',
                    'violation_type': parts[2] if len(parts) > 2 else '',
                    'rule_name': parts[3] if len(parts) > 3 else '',
                    'rule_content': parts[4] if len(parts) > 4 else '',
                    'medical_name1': ConverterService._extract_medical_name1(parts[3] if len(parts) > 3 else ''),
                    'medical_name2': ConverterService._extract_medical_name2(parts[3] if len(parts) > 3 else ''),
                    'violation_count': '',
                    'type': parts[2] if len(parts) > 2 else '',
                    'time_type': ConverterService._extract_time_type(parts[3] if len(parts) > 3 else ''),
                    'item_count': '',
                    'age': '',
                    'gender': '',
                    'exclude_diagnosis': '',
                    'exclude_department': '',
                    'include_diagnosis': '',
                    'other': '',

                    # 元数据
                    'confidence': ConverterService._calculate_confidence(row['content']),
                    'status': 'pending_review',
                    'source_info': row['source_info'],
                    'created_at': datetime.now().isoformat()
                }

                rules.append(rule)

        return rules

    @staticmethod
    def _extract_from_text(content_list: List[Dict[str, Any]], rule_source: str, upload_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从文本内容中提取规则"""
        rules = []
        rule_id = 1

        for item in content_list:
            content = item['content']

            # 检查是否包含规则特征
            if ConverterService._is_rule_content(content):
                rule = {
                    'id': rule_id,
                    'original_content': content,

                    # 标准化字段
                    'rule_source': rule_source,
                    'city': '合肥',
                    'sequence_number': rule_id,
                    'department': ConverterService._extract_medical_category(content),
                    'violation_type': ConverterService._classify_rule_type(content),
                    'rule_name': ConverterService._extract_rule_name(content),
                    'rule_content': content,
                    'medical_name1': ConverterService._extract_medical_name1(content),
                    'medical_name2': ConverterService._extract_medical_name2(content),
                    'violation_count': '',
                    'type': ConverterService._classify_rule_type(content),
                    'time_type': ConverterService._extract_time_type(content),
                    'item_count': '',
                    'age': '',
                    'gender': '',
                    'exclude_diagnosis': '',
                    'exclude_department': '',
                    'include_diagnosis': '',
                    'other': '',

                    # 元数据
                    'confidence': ConverterService._calculate_confidence(content),
                    'status': 'pending_review',
                    'source_info': item,
                    'created_at': datetime.now().isoformat()
                }

                rules.append(rule)
                rule_id += 1

        return rules

    @staticmethod
    def _safe_int(value: str) -> int:
        """安全转换为整数"""
        try:
            return int(value.strip())
        except:
            return 0

    @staticmethod
    def _extract_medical_name1(content: str) -> str:
        """提取医保名称1"""
        # 查找医保项目名称的模式
        import re

        # 查找引号中的内容
        quotes_match = re.search(r'"([^"]+)"', content)
        if quotes_match:
            return quotes_match.group(1)

        # 查找括号中的内容
        bracket_match = re.search(r'（([^）]+)）', content)
        if bracket_match:
            return bracket_match.group(1)

        # 查找常见医保项目关键词
        medical_keywords = ['监护', '造影', '成形术', '支架', '手术', '治疗', '检查', '化验']
        for keyword in medical_keywords:
            if keyword in content:
                # 提取包含关键词的短语
                words = content.split()
                for word in words:
                    if keyword in word and len(word) > 2:
                        return word

        return ''

    @staticmethod
    def _extract_medical_name2(content: str) -> str:
        """提取医保名称2"""
        # 查找"重复收取"、"同时收取"等关键词后的内容
        import re

        patterns = [
            r'重复收取[^，。]*([^，。]+)',
            r'同时收取[^，。]*([^，。]+)',
            r'重复.*?([^，。]*费用)',
        ]

        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1).strip()

        return ''

    @staticmethod
    def _extract_time_type(content: str) -> str:
        """提取时间类型"""
        time_keywords = {
            '分钟': '分钟',
            '小时': '小时',
            '天': '天',
            '日': '天',
            '周': '周',
            '月': '月',
            '年': '年'
        }

        for keyword, time_type in time_keywords.items():
            if keyword in content:
                return time_type

        return ''

    @staticmethod
    def _is_rule_content(content: str) -> bool:
        """判断内容是否为规则条目"""
        # 检查是否包含规则特征词汇
        for pattern in ConverterService.RULE_PATTERNS:
            if re.search(pattern, content):
                # 同时检查是否包含医保相关词汇
                for keyword in ConverterService.MEDICAL_KEYWORDS:
                    if keyword in content:
                        return True
        return False

    @staticmethod
    def _extract_rule_name(content: str) -> str:
        """提取规则名称"""
        # 简单提取：取前50个字符作为规则名称
        name = content[:50].strip()
        if len(content) > 50:
            name += "..."
        return name

    @staticmethod
    def _classify_rule_type(content: str) -> str:
        """分类规则类型"""
        if re.search(r'不得|禁止|严禁', content):
            return '禁止性规则'
        elif re.search(r'超出|超过|违反', content):
            return '限制性规则'
        elif re.search(r'重复|多次|频繁', content):
            return '频次性规则'
        elif re.search(r'虚假|伪造|冒用', content):
            return '欺诈性规则'
        else:
            return '其他规则'

    @staticmethod
    def _extract_medical_category(content: str) -> str:
        """提取医疗类别"""
        categories = {
            '药品': ['药品', '药物', '处方', '用药'],
            '诊疗': ['诊疗', '诊断', '治疗', '手术'],
            '检查': ['检查', '检验', '化验', '影像'],
            '耗材': ['耗材', '器械', '设备'],
            '住院': ['住院', '病房', '床位'],
            '门诊': ['门诊', '急诊', '挂号']
        }

        for category, keywords in categories.items():
            for keyword in keywords:
                if keyword in content:
                    return category

        return '通用'

    @staticmethod
    def _assess_severity(content: str) -> str:
        """评估严重程度"""
        high_severity_words = ['严禁', '禁止', '违法', '欺诈', '虚假']
        medium_severity_words = ['不得', '不允许', '违反', '超出']

        for word in high_severity_words:
            if word in content:
                return '高'

        for word in medium_severity_words:
            if word in content:
                return '中'

        return '低'

    @staticmethod
    def _calculate_confidence(content: str) -> float:
        """计算识别置信度"""
        confidence = 0.0

        # 基础分数
        confidence += 0.3

        # 包含规则特征词汇
        for pattern in ConverterService.RULE_PATTERNS:
            if re.search(pattern, content):
                confidence += 0.2
                break

        # 包含医保关键词
        medical_count = sum(1 for keyword in ConverterService.MEDICAL_KEYWORDS if keyword in content)
        confidence += min(medical_count * 0.1, 0.3)

        # 内容长度合理性
        if 20 <= len(content) <= 200:
            confidence += 0.2

        return min(confidence, 1.0)

    @staticmethod
    @handle_db_error
    def _save_parse_result(upload_id: int, rules: List[Dict[str, Any]]) -> Dict[str, Any]:
        """保存解析结果 - 支持标准化字段"""
        try:
            # 保存规则到数据库
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    for rule in rules:
                        # 先获取序列值
                        cursor.execute("SELECT converter_rules_seq.NEXTVAL FROM dual")
                        rule_id = cursor.fetchone()[0]

                        # 分步插入，先插入基本字段，再更新CLOB字段
                        insert_sql = """
                        INSERT INTO converter_rules (
                            id, upload_id, rule_source, city, sequence_number, department,
                            violation_type, medical_name1, medical_name2,
                            violation_count, type, time_type, item_count, age, gender,
                            exclude_department, confidence, status, created_at
                        ) VALUES (
                            :rule_id, :upload_id, :rule_source, :city, :sequence_number, :department,
                            :violation_type, :medical_name1, :medical_name2,
                            :violation_count, :type, :time_type, :item_count, :age, :gender,
                            :exclude_department, :confidence, :status, CURRENT_TIMESTAMP
                        )
                        """

                        # 基本字段参数
                        basic_params = {
                            'rule_id': rule_id,
                            'upload_id': upload_id,
                            'rule_source': rule.get('rule_source', ''),
                            'city': rule.get('city', ''),
                            'sequence_number': rule.get('sequence_number', 0),
                            'department': rule.get('department', ''),
                            'violation_type': rule.get('violation_type', ''),
                            'medical_name1': rule.get('medical_name1', ''),
                            'medical_name2': rule.get('medical_name2', ''),
                            'violation_count': rule.get('violation_count', ''),
                            'type': rule.get('type', ''),
                            'time_type': rule.get('time_type', ''),
                            'item_count': rule.get('item_count', ''),
                            'age': rule.get('age', ''),
                            'gender': rule.get('gender', ''),
                            'exclude_department': rule.get('exclude_department', ''),
                            'confidence': rule.get('confidence', 0.0),
                            'status': rule.get('status', 'pending_review')
                        }

                        # 执行基本字段插入
                        cursor.execute(insert_sql, basic_params)

                        # 更新CLOB字段
                        update_clob_sql = """
                        UPDATE converter_rules SET
                            rule_name = :rule_name,
                            rule_content = :rule_content,
                            exclude_diagnosis = :exclude_diagnosis,
                            include_diagnosis = :include_diagnosis,
                            other = :other,
                            original_content = :original_content,
                            source_info = :source_info
                        WHERE id = :rule_id
                        """

                        clob_params = {
                            'rule_id': rule_id,
                            'rule_name': rule.get('rule_name', ''),
                            'rule_content': rule.get('rule_content', ''),
                            'exclude_diagnosis': rule.get('exclude_diagnosis', ''),
                            'include_diagnosis': rule.get('include_diagnosis', ''),
                            'other': rule.get('other', ''),
                            'original_content': rule.get('original_content', ''),
                            'source_info': json.dumps(rule.get('source_info', {}), ensure_ascii=False)
                        }

                        cursor.execute(update_clob_sql, clob_params)

                conn.commit()

            logging.info(f"保存解析结果成功，共 {len(rules)} 条规则")
            return {'success': True, 'message': f'保存 {len(rules)} 条规则成功'}

        except Exception as e:
            logging.error(f"保存解析结果失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'保存解析结果失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def _update_status(upload_id: int, status: str, message: str = None):
        """更新上传记录状态"""
        try:
            with db_manager.get_connection() as conn:
                update_sql = """
                UPDATE converter_uploads
                SET status = :status, status_message = :message, updated_at = CURRENT_TIMESTAMP
                WHERE id = :upload_id
                """

                with conn.cursor() as cursor:
                    cursor.execute(update_sql, {
                        'upload_id': upload_id,
                        'status': status,
                        'message': message
                    })

                conn.commit()

        except Exception as e:
            logging.error(f"更新状态失败: {str(e)}")

    @staticmethod
    @handle_db_error
    def get_upload_list(user_id: int, page: int = 1, per_page: int = 10) -> Dict[str, Any]:
        """获取上传列表"""
        try:
            with db_manager.get_connection() as conn:
                # 获取总数
                count_sql = "SELECT COUNT(*) FROM converter_uploads WHERE user_id = :user_id"
                count_df = execute_rules_query(conn, count_sql, {'user_id': user_id})
                total = int(count_df.iloc[0, 0]) if not count_df.empty else 0

                # 获取列表 - 使用Oracle兼容的分页语法
                start_row = (page - 1) * per_page + 1
                end_row = page * per_page
                list_sql = """
                SELECT * FROM (
                    SELECT ROWNUM rn, t.* FROM (
                        SELECT id, original_filename, file_size, file_type, status, status_message,
                               upload_time, updated_at,
                               (SELECT COUNT(*) FROM converter_rules WHERE upload_id = converter_uploads.id) as rules_count
                        FROM converter_uploads
                        WHERE user_id = :user_id
                        ORDER BY upload_time DESC
                    ) t WHERE ROWNUM <= :end_row
                ) WHERE rn >= :start_row
                """

                uploads_df = execute_rules_query(conn, list_sql, {
                    'user_id': user_id,
                    'start_row': start_row,
                    'end_row': end_row
                })

                # 转换为字典，处理列名问题
                uploads = []
                if not uploads_df.empty:
                    for _, row in uploads_df.iterrows():
                        # 使用相同的安全转换函数
                        def safe_convert(value, target_type=str):
                            """安全转换值，处理LOB和None"""
                            if value is None:
                                return '' if target_type == str else (0 if target_type in [int, float] else '')

                            # 处理Oracle LOB对象
                            if hasattr(value, 'read'):
                                try:
                                    value = value.read()
                                except:
                                    value = ''

                            # 处理pandas特殊类型
                            if hasattr(value, 'item'):
                                try:
                                    value = value.item()
                                except:
                                    pass

                            # 转换为目标类型
                            try:
                                if target_type == str:
                                    return str(value) if value is not None else ''
                                elif target_type == int:
                                    return int(float(value)) if value is not None else 0
                                elif target_type == float:
                                    return float(value) if value is not None else 0.0
                                else:
                                    return str(value) if value is not None else ''
                            except:
                                return '' if target_type == str else 0

                        upload = {
                            'id': safe_convert(row.iloc[1], int),  # id列
                            'original_filename': safe_convert(row.iloc[2], str),  # original_filename列
                            'file_size': safe_convert(row.iloc[3], int),  # file_size列
                            'file_type': safe_convert(row.iloc[4], str),  # file_type列
                            'status': safe_convert(row.iloc[5], str),  # status列
                            'status_message': safe_convert(row.iloc[6] if len(row) > 6 else None, str),  # status_message列
                            'upload_time': safe_convert(row.iloc[7] if len(row) > 7 else None, str),  # upload_time列
                            'rules_count': safe_convert(row.iloc[8] if len(row) > 8 else None, int)  # rules_count列
                        }
                        uploads.append(upload)

                return {
                    'success': True,
                    'uploads': uploads,
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': total,
                        'pages': (total + per_page - 1) // per_page if total > 0 else 0
                    }
                }

        except Exception as e:
            logging.error(f"获取上传列表失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'获取上传列表失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def get_rules(upload_id: int, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """获取解析的规则列表"""
        try:
            with db_manager.get_connection() as conn:
                # 获取总数
                count_sql = "SELECT COUNT(*) FROM converter_rules WHERE upload_id = :upload_id"
                count_df = execute_rules_query(conn, count_sql, {'upload_id': upload_id})
                total = int(count_df.iloc[0, 0]) if not count_df.empty else 0

                # 获取规则列表 - 返回标准化字段
                start_row = (page - 1) * per_page + 1
                end_row = page * per_page
                rules_sql = """
                SELECT * FROM (
                    SELECT ROWNUM rn, t.* FROM (
                        SELECT id, rule_source, city, sequence_number, department, violation_type,
                               CASE WHEN DBMS_LOB.GETLENGTH(rule_name) > 200
                                    THEN SUBSTR(DBMS_LOB.SUBSTR(rule_name, 200, 1), 1, 200) || '...'
                                    ELSE DBMS_LOB.SUBSTR(rule_name, 4000, 1)
                               END as rule_name,
                               CASE WHEN DBMS_LOB.GETLENGTH(rule_content) > 500
                                    THEN SUBSTR(DBMS_LOB.SUBSTR(rule_content, 500, 1), 1, 500) || '...'
                                    ELSE DBMS_LOB.SUBSTR(rule_content, 4000, 1)
                               END as rule_content,
                               medical_name1, medical_name2, violation_count, type, time_type,
                               item_count, age, gender, exclude_diagnosis, exclude_department,
                               include_diagnosis, other, confidence, status,
                               TO_CHAR(created_at, 'YYYY-MM-DD HH24:MI:SS') as created_at,
                               TO_CHAR(updated_at, 'YYYY-MM-DD HH24:MI:SS') as updated_at
                        FROM converter_rules
                        WHERE upload_id = :upload_id
                        ORDER BY sequence_number ASC, confidence DESC
                    ) t WHERE ROWNUM <= :end_row
                ) WHERE rn >= :start_row
                """

                rules_df = execute_rules_query(conn, rules_sql, {
                    'upload_id': upload_id,
                    'start_row': start_row,
                    'end_row': end_row
                })

                # 转换为字典，返回标准化字段
                rules = []
                if not rules_df.empty:
                    for _, row in rules_df.iterrows():
                        rule = {
                            'id': int(row.iloc[1]) if row.iloc[1] is not None else 0,
                            'rule_source': str(row.iloc[2]) if row.iloc[2] is not None else '',
                            'city': str(row.iloc[3]) if row.iloc[3] is not None else '',
                            'sequence_number': int(row.iloc[4]) if row.iloc[4] is not None else 0,
                            'department': str(row.iloc[5]) if row.iloc[5] is not None else '',
                            'violation_type': str(row.iloc[6]) if row.iloc[6] is not None else '',
                            'rule_name': str(row.iloc[7]) if row.iloc[7] is not None else '',
                            'rule_content': str(row.iloc[8]) if row.iloc[8] is not None else '',
                            'medical_name1': str(row.iloc[9]) if row.iloc[9] is not None else '',
                            'medical_name2': str(row.iloc[10]) if row.iloc[10] is not None else '',
                            'violation_count': str(row.iloc[11]) if row.iloc[11] is not None else '',
                            'type': str(row.iloc[12]) if row.iloc[12] is not None else '',
                            'time_type': str(row.iloc[13]) if row.iloc[13] is not None else '',
                            'item_count': str(row.iloc[14]) if row.iloc[14] is not None else '',
                            'age': str(row.iloc[15]) if row.iloc[15] is not None else '',
                            'gender': str(row.iloc[16]) if row.iloc[16] is not None else '',
                            'exclude_diagnosis': str(row.iloc[17]) if row.iloc[17] is not None else '',
                            'exclude_department': str(row.iloc[18]) if row.iloc[18] is not None else '',
                            'include_diagnosis': str(row.iloc[19]) if row.iloc[19] is not None else '',
                            'other': str(row.iloc[20]) if row.iloc[20] is not None else '',
                            'confidence': float(row.iloc[21]) if row.iloc[21] is not None else 0.0,
                            'status': str(row.iloc[22]) if row.iloc[22] is not None else '',
                            'created_at': str(row.iloc[23]) if row.iloc[23] is not None else '',
                            'updated_at': str(row.iloc[24]) if len(row) > 24 and row.iloc[24] is not None else ''
                        }
                        rules.append(rule)

                return {
                    'success': True,
                    'rules': rules,
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': total,
                        'pages': (total + per_page - 1) // per_page if total > 0 else 0
                    }
                }

        except Exception as e:
            logging.error(f"获取规则列表失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'获取规则列表失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def update_rule(rule_id: int, rule_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新规则"""
        try:
            with db_manager.get_connection() as conn:
                update_sql = """
                UPDATE converter_rules
                SET rule_name = :rule_name, rule_description = :rule_description,
                    rule_type = :rule_type, medical_category = :medical_category,
                    severity = :severity, status = :status, updated_at = CURRENT_TIMESTAMP
                WHERE id = :rule_id
                """

                params = {
                    'rule_id': rule_id,
                    'rule_name': rule_data.get('rule_name'),
                    'rule_description': rule_data.get('rule_description'),
                    'rule_type': rule_data.get('rule_type'),
                    'medical_category': rule_data.get('medical_category'),
                    'severity': rule_data.get('severity'),
                    'status': rule_data.get('status', 'reviewed')
                }

                with conn.cursor() as cursor:
                    cursor.execute(update_sql, params)

                conn.commit()

                return {'success': True, 'message': '规则更新成功'}

        except Exception as e:
            logging.error(f"更新规则失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'更新规则失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def delete_rule(rule_id: int) -> Dict[str, Any]:
        """删除规则"""
        try:
            with db_manager.get_connection() as conn:
                delete_sql = "DELETE FROM converter_rules WHERE id = :rule_id"

                with conn.cursor() as cursor:
                    cursor.execute(delete_sql, {'rule_id': rule_id})

                conn.commit()

                return {'success': True, 'message': '规则删除成功'}

        except Exception as e:
            logging.error(f"删除规则失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'删除规则失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def export_rules(upload_id: int) -> Dict[str, Any]:
        """导出规则为Excel文件"""
        try:
            with db_manager.get_connection() as conn:
                # 获取所有规则 - 按标准化格式导出
                rules_sql = """
                SELECT rule_source, city, sequence_number, department, violation_type,
                       DBMS_LOB.SUBSTR(rule_name, 4000, 1) as rule_name,
                       DBMS_LOB.SUBSTR(rule_content, 4000, 1) as rule_content,
                       medical_name1, medical_name2, violation_count, type, time_type,
                       item_count, age, gender,
                       DBMS_LOB.SUBSTR(exclude_diagnosis, 4000, 1) as exclude_diagnosis,
                       exclude_department,
                       DBMS_LOB.SUBSTR(include_diagnosis, 4000, 1) as include_diagnosis,
                       DBMS_LOB.SUBSTR(other, 4000, 1) as other
                FROM converter_rules
                WHERE upload_id = :upload_id AND status != 'deleted'
                ORDER BY sequence_number ASC, confidence DESC
                """

                rules_df = execute_rules_query(conn, rules_sql, {'upload_id': upload_id})

                if rules_df.empty:
                    return {'success': False, 'error': '没有可导出的规则'}

                # 重命名列为标准格式
                rules_df.columns = [
                    '规则来源', '城市', '序号', '涉及科室', '行为认定', '规则名称', '规则内涵',
                    '医保名称1', '医保名称2', '违规数量', '类型', '时间类型', '项目数量',
                    '年龄', '性别', '排除诊断', '排除科室', '包含诊断', '其他'
                ]

                # 生成文件路径
                export_dir = os.path.join('exports', 'converter')
                os.makedirs(export_dir, exist_ok=True)

                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"负面清单规则_{upload_id}_{timestamp}.xlsx"
                file_path = os.path.join(export_dir, filename)

                # 导出Excel
                rules_df.to_excel(file_path, index=False, engine='openpyxl')

                logging.info(f"规则导出成功: {file_path}")

                return {
                    'success': True,
                    'file_path': file_path,
                    'filename': filename,
                    'rules_count': len(rules_df),
                    'message': f'成功导出 {len(rules_df)} 条规则'
                }

        except Exception as e:
            logging.error(f"导出规则失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'导出规则失败: {str(e)}'}

    @staticmethod
    def _ai_extract_rules(content_list: List[Dict[str, Any]], rule_source: str, upload_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """使用AI语义理解提取规则"""
        rules = []
        rule_id = 1

        # 合并所有内容为文本块
        all_content = []
        for item in content_list:
            content = item['content'].strip()
            if content and len(content) > 10:  # 过滤太短的内容
                all_content.append({
                    'text': content,
                    'source': item
                })

        logging.info(f"开始AI解析，共 {len(all_content)} 个文本块")

        for item in all_content:
            text = item['text']

            # 使用多种模式识别规则内容
            if ConverterService._is_potential_rule(text):
                # 使用AI语义分析提取字段
                extracted_rule = ConverterService._ai_analyze_text(text, rule_source, rule_id, item['source'])

                if extracted_rule:
                    rules.append(extracted_rule)
                    rule_id += 1

        return rules

    @staticmethod
    def _is_potential_rule(text: str) -> bool:
        """判断文本是否可能包含规则"""
        # 规则特征词汇
        rule_indicators = [
            # 禁止性词汇
            '不得', '禁止', '严禁', '不允许', '不可', '不能',
            # 违规性词汇
            '违反', '违规', '超出', '超过', '不符合', '不当',
            # 重复性词汇
            '重复', '多次', '频繁', '反复',
            # 欺诈性词汇
            '虚假', '伪造', '冒用', '骗取', '套取',
            # 医保相关
            '医保', '医疗保险', '基金', '报销', '结算',
            # 医疗行为
            '诊疗', '治疗', '手术', '检查', '化验', '用药'
        ]

        # 检查是否包含规则特征
        has_rule_indicator = any(indicator in text for indicator in rule_indicators)

        # 检查长度合理性（调整长度限制）
        reasonable_length = 10 <= len(text) <= 1000

        # 排除明显的非规则内容
        exclude_patterns = [
            '目录', '索引', '页码', '第.*页', '共.*页',
            '附件', '说明', '注：', '备注',
            '联系方式', '电话', '地址', '邮箱'
        ]

        has_exclude = any(pattern in text for pattern in exclude_patterns)

        return has_rule_indicator and reasonable_length and not has_exclude

    @staticmethod
    def _ai_analyze_text(text: str, rule_source: str, rule_id: int, source_info: Dict) -> Dict[str, Any]:
        """AI语义分析文本，提取标准化字段"""
        try:
            # 基础信息
            rule = {
                'id': rule_id,
                'original_content': text,
                'rule_source': rule_source,
                'city': ConverterService._extract_city(text, rule_source),
                'sequence_number': rule_id,
                'source_info': source_info,
                'confidence': 0.0,
                'status': 'pending_review',
                'created_at': datetime.now().isoformat()
            }

            # AI语义分析提取各字段
            rule.update({
                'department': ConverterService._ai_extract_department(text),
                'violation_type': ConverterService._ai_extract_violation_type(text),
                'rule_name': ConverterService._ai_extract_rule_name(text),
                'rule_content': text,  # 完整内容作为规则内涵
                'medical_name1': ConverterService._ai_extract_medical_name1(text),
                'medical_name2': ConverterService._ai_extract_medical_name2(text),
                'type': ConverterService._ai_extract_violation_type(text),
                'time_type': ConverterService._ai_extract_time_type(text),
                'violation_count': '',
                'item_count': '',
                'age': '',
                'gender': '',
                'exclude_diagnosis': '',
                'exclude_department': '',
                'include_diagnosis': '',
                'other': ''
            })

            # 计算置信度
            rule['confidence'] = ConverterService._calculate_ai_confidence(rule, text)

            return rule

        except Exception as e:
            logging.error(f"AI分析文本失败: {str(e)}")
            return None

    @staticmethod
    def _extract_city(text: str, rule_source: str) -> str:
        """提取城市信息"""
        if '安徽' in rule_source:
            return '合肥'
        elif '北京' in text or '北京' in rule_source:
            return '北京'
        elif '上海' in text or '上海' in rule_source:
            return '上海'
        else:
            return '合肥'  # 默认

    @staticmethod
    def _ai_extract_department(text: str) -> str:
        """AI提取涉及科室"""
        departments = {
            '心血管内科': ['心血管', '心脏', '冠心病', '心电', '心肌', '血管'],
            '神经内科': ['神经', '脑血管', '脑梗', '脑出血', '癫痫'],
            '消化内科': ['消化', '胃肠', '肝脏', '胆囊', '胰腺'],
            '呼吸内科': ['呼吸', '肺部', '气管', '支气管', '肺炎'],
            '内分泌科': ['糖尿病', '甲状腺', '内分泌', '激素'],
            '骨科': ['骨折', '关节', '脊柱', '骨科', '外伤'],
            '外科': ['手术', '切除', '缝合', '外科'],
            '妇产科': ['妇科', '产科', '分娩', '子宫', '卵巢'],
            '儿科': ['儿科', '小儿', '新生儿', '婴儿'],
            '眼科': ['眼科', '视力', '白内障', '青光眼'],
            '耳鼻喉科': ['耳鼻喉', '听力', '鼻炎', '咽喉'],
            '皮肤科': ['皮肤', '湿疹', '皮炎', '过敏'],
            '泌尿外科': ['泌尿', '肾脏', '膀胱', '前列腺'],
            '肿瘤科': ['肿瘤', '癌症', '化疗', '放疗'],
            '急诊科': ['急诊', '抢救', '急救'],
            '康复科': ['康复', '理疗', '针灸', '推拿'],
            '中医科': ['中医', '中药', '针灸', '推拿'],
            '检验科': ['检验', '化验', '血常规', '尿常规'],
            '影像科': ['影像', 'CT', 'MRI', 'X光', 'B超'],
            '药剂科': ['药剂', '药房', '配药']
        }

        for dept, keywords in departments.items():
            for keyword in keywords:
                if keyword in text:
                    return dept

        return '通用'

    @staticmethod
    def _ai_extract_violation_type(text: str) -> str:
        """AI提取违规类型"""
        violation_types = {
            '重复收费': ['重复收费', '重复收取', '重复计费', '多次收费'],
            '超标准收费': ['超标准', '超出标准', '高于标准', '超额收费'],
            '分解收费': ['分解收费', '分项收费', '拆分收费'],
            '虚假收费': ['虚假', '伪造', '编造', '不实'],
            '套用收费': ['套用', '冒用', '错用', '滥用'],
            '超范围收费': ['超范围', '超出范围', '不在范围'],
            '违规用药': ['违规用药', '超适应症', '无指征用药'],
            '过度医疗': ['过度医疗', '过度检查', '过度治疗'],
            '挂床住院': ['挂床', '虚假住院', '无指征住院'],
            '串换药品': ['串换', '以次充好', '替代使用']
        }

        for vtype, keywords in violation_types.items():
            for keyword in keywords:
                if keyword in text:
                    return vtype

        # 使用原有的分类逻辑作为备选
        if re.search(r'重复|多次|频繁', text):
            return '重复收费'
        elif re.search(r'超出|超过|违反', text):
            return '超标准收费'
        elif re.search(r'虚假|伪造|冒用', text):
            return '虚假收费'
        elif re.search(r'不得|禁止|严禁', text):
            return '违规操作'

        return '其他违规'

    @staticmethod
    def _ai_extract_rule_name(text: str) -> str:
        """AI提取规则名称"""
        # 尝试提取规则的核心描述
        import re

        # 如果文本包含句号，取第一句作为规则名称
        sentences = text.split('。')
        if len(sentences) > 1 and len(sentences[0]) > 10:
            rule_name = sentences[0].strip()
        else:
            # 否则取前100个字符
            rule_name = text[:100].strip()

        # 清理规则名称
        rule_name = re.sub(r'^[0-9]+[、.]?\s*', '', rule_name)  # 移除开头的序号
        rule_name = re.sub(r'^\s*[（(].*?[）)]\s*', '', rule_name)  # 移除开头的括号内容

        return rule_name if rule_name else text[:50]

    @staticmethod
    def _ai_extract_medical_name1(text: str) -> str:
        """AI提取医保名称1"""
        import re

        # 查找引号中的医保项目名称
        quotes_patterns = [
            r'"([^"]+)"',  # 双引号
            r'"([^"]+)"',  # 中文双引号
            r'「([^」]+)」',  # 中文书名号
            r'《([^》]+)》'   # 中文书名号
        ]

        for pattern in quotes_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if any(keyword in match for keyword in ['监护', '造影', '术', '检查', '治疗', '费']):
                    return match

        # 查找括号中的编码对应的项目
        bracket_pattern = r'（编码[^）]*）'
        bracket_matches = re.findall(bracket_pattern, text)
        if bracket_matches:
            # 查找编码前的项目名称
            for match in bracket_matches:
                start_pos = text.find(match)
                if start_pos > 10:
                    before_text = text[:start_pos]
                    # 提取最后一个引号中的内容
                    quote_match = re.search(r'"([^"]+)"[^"]*$', before_text)
                    if quote_match:
                        return quote_match.group(1)

        return ''

    @staticmethod
    def _ai_extract_medical_name2(text: str) -> str:
        """AI提取医保名称2"""
        import re

        # 查找重复收费相关的第二个医保项目
        patterns = [
            r'重复收取[^，。]*?([^，。]*(?:费用|费|术|检查|治疗|监护))',
            r'同时收取[^，。]*?([^，。]*(?:费用|费|术|检查|治疗|监护))',
            r'重复.*?([^，。]*(?:费用|费|术|检查|治疗|监护))',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                cleaned = re.sub(r'[的了等]', '', match.strip())
                if len(cleaned) > 2:
                    return cleaned

        return ''

    @staticmethod
    def _ai_extract_time_type(text: str) -> str:
        """AI提取时间类型"""
        time_patterns = {
            '分钟': [r'\d+分钟', '分钟', '每分钟'],
            '小时': [r'\d+小时', '小时', '每小时'],
            '天': [r'\d+天', r'\d+日', '天', '日', '每天', '每日'],
            '周': [r'\d+周', '周', '每周'],
            '月': [r'\d+月', '月', '每月'],
            '年': [r'\d+年', '年', '每年'],
            '次': [r'\d+次', '次', '每次']
        }

        for time_type, patterns in time_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text):
                    return time_type

        return ''

    @staticmethod
    def _calculate_ai_confidence(rule: Dict[str, Any], text: str) -> float:
        """计算AI提取的置信度"""
        confidence = 0.0

        # 基础分数
        confidence += 0.2

        # 规则名称质量
        if rule.get('rule_name') and len(rule['rule_name']) > 10:
            confidence += 0.2

        # 科室识别
        if rule.get('department') and rule['department'] != '通用':
            confidence += 0.15

        # 违规类型识别
        if rule.get('violation_type') and rule['violation_type'] != '其他违规':
            confidence += 0.15

        # 医保名称提取
        if rule.get('medical_name1'):
            confidence += 0.15
        if rule.get('medical_name2'):
            confidence += 0.1

        # 文本长度合理性
        if 50 <= len(text) <= 300:
            confidence += 0.15

        return min(confidence, 1.0)

    @staticmethod
    def _gemini_extract_rules(content_list: List[Dict[str, Any]], rule_source: str, upload_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """使用Gemini API提取规则"""
        try:
            import requests
            import json
            import time
            from app.config.gemini_config import (
                GEMINI_API_KEY, GEMINI_API_URL, GEMINI_TIMEOUT,
                GEMINI_BATCH_SIZE, GEMINI_DELAY, GEMINI_PROMPT_TEMPLATE
            )

            rules = []
            rule_id = 1

            # 分批处理内容
            batch_size = GEMINI_BATCH_SIZE
            for i in range(0, len(content_list), batch_size):
                batch = content_list[i:i+batch_size]

                # 构建批次文本
                batch_text = "\n".join([f"{j+1}. {item['content']}" for j, item in enumerate(batch)])

                # 构建完整提示词
                full_prompt = GEMINI_PROMPT_TEMPLATE + batch_text

                # 调用Gemini API
                headers = {
                    'Content-Type': 'application/json',
                }

                data = {
                    "contents": [{
                        "parts": [{
                            "text": full_prompt
                        }]
                    }]
                }

                try:
                    response = requests.post(
                        f"{GEMINI_API_URL}?key={GEMINI_API_KEY}",
                        headers=headers,
                        json=data,
                        timeout=GEMINI_TIMEOUT
                    )

                    if response.status_code == 200:
                        result = response.json()

                        # 解析Gemini返回的结果
                        if 'candidates' in result and len(result['candidates']) > 0:
                            content = result['candidates'][0]['content']['parts'][0]['text']

                            # 尝试解析JSON
                            try:
                                # 提取JSON部分
                                json_start = content.find('{')
                                json_end = content.rfind('}') + 1
                                if json_start >= 0 and json_end > json_start:
                                    json_content = content[json_start:json_end]
                                    parsed_result = json.loads(json_content)

                                    # 处理提取的规则
                                    if 'rules' in parsed_result:
                                        for rule_data in parsed_result['rules']:
                                            rule = {
                                                'id': rule_id,
                                                'original_content': batch_text,
                                                'rule_source': rule_source,
                                                'city': rule_data.get('city', '合肥'),
                                                'sequence_number': rule_data.get('sequence_number', rule_id),
                                                'department': rule_data.get('department', ''),
                                                'violation_type': rule_data.get('violation_type', ''),
                                                'rule_name': rule_data.get('rule_name', ''),
                                                'rule_content': rule_data.get('rule_content', ''),
                                                'medical_name1': rule_data.get('medical_name1', ''),
                                                'medical_name2': rule_data.get('medical_name2', ''),
                                                'violation_count': '',
                                                'type': rule_data.get('type', rule_data.get('violation_type', '')),
                                                'time_type': rule_data.get('time_type', ''),
                                                'item_count': '',
                                                'age': '',
                                                'gender': '',
                                                'exclude_diagnosis': '',
                                                'exclude_department': '',
                                                'include_diagnosis': '',
                                                'other': '',
                                                'confidence': rule_data.get('confidence', 0.8),
                                                'status': 'pending_review',
                                                'source_info': {'batch': i, 'gemini_processed': True},
                                                'created_at': datetime.now().isoformat()
                                            }
                                            rules.append(rule)
                                            rule_id += 1

                            except json.JSONDecodeError as e:
                                logging.warning(f"Gemini返回的JSON解析失败: {str(e)}")
                                logging.warning(f"原始内容: {content}")
                    else:
                        logging.error(f"Gemini API调用失败: {response.status_code} - {response.text}")

                except requests.RequestException as e:
                    logging.error(f"Gemini API请求异常: {str(e)}")

                # 添加延迟避免API限制
                time.sleep(GEMINI_DELAY)

            logging.info(f"Gemini API解析完成，共提取 {len(rules)} 条规则")
            return rules

        except Exception as e:
            logging.error(f"Gemini API解析失败: {str(e)}", exc_info=True)
            # 如果Gemini API失败，回退到本地AI解析
            return ConverterService._ai_extract_rules(content_list, rule_source, upload_info)

    @staticmethod
    def _enhanced_ai_extract_rules(content_list: List[Dict[str, Any]], rule_source: str, upload_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """增强的本地AI规则提取"""
        try:
            logging.info("开始增强AI解析")

            rules = []
            rule_id = 1

            # 预处理内容，合并相关文本
            processed_content = ConverterService._preprocess_content(content_list)

            # 智能分段和规则识别
            rule_segments = ConverterService._segment_rules(processed_content)

            logging.info(f"识别到 {len(rule_segments)} 个规则段落")

            for segment in rule_segments:
                # 深度分析每个段落
                rule_data = ConverterService._deep_analyze_segment(segment, rule_source, rule_id)

                if rule_data and rule_data.get('confidence', 0) > 0.3:  # 降低置信度阈值
                    rules.append(rule_data)
                    rule_id += 1

            logging.info(f"增强AI解析完成，共提取 {len(rules)} 条规则")
            return rules

        except Exception as e:
            logging.error(f"增强AI解析失败: {str(e)}", exc_info=True)
            # 回退到原始AI解析
            return ConverterService._ai_extract_rules(content_list, rule_source, upload_info)

    @staticmethod
    def _preprocess_content(content_list: List[Dict[str, Any]]) -> List[str]:
        """预处理内容，清理和合并"""
        processed = []

        for item in content_list:
            content = item['content'].strip()

            # 跳过无用内容
            if len(content) < 5:
                continue

            # 跳过明显的非规则内容
            skip_patterns = [
                r'^\d+$',  # 纯数字
                r'^第.*页$',  # 页码
                r'^目录$',  # 目录
                r'^附件',  # 附件
                r'联系.*?电话',  # 联系方式
                r'^\s*$'  # 空白
            ]

            should_skip = False
            for pattern in skip_patterns:
                if re.search(pattern, content):
                    should_skip = True
                    break

            if not should_skip:
                processed.append(content)

        return processed

    @staticmethod
    def _segment_rules(content_list: List[str]) -> List[Dict[str, Any]]:
        """智能分段，识别规则边界"""
        segments = []
        current_segment = ""

        for content in content_list:
            # 检查是否是新规则的开始
            if ConverterService._is_rule_start(content):
                # 保存当前段落
                if current_segment.strip():
                    segments.append({
                        'text': current_segment.strip(),
                        'type': 'rule'
                    })

                # 开始新段落
                current_segment = content
            else:
                # 继续当前段落
                if current_segment:
                    current_segment += " " + content
                else:
                    current_segment = content

        # 保存最后一个段落
        if current_segment.strip():
            segments.append({
                'text': current_segment.strip(),
                'type': 'rule'
            })

        return segments

    @staticmethod
    def _is_rule_start(content: str) -> bool:
        """判断是否是规则开始"""
        # 规则开始的模式
        start_patterns = [
            r'^\d+[、.]',  # 数字编号
            r'^[（(]\d+[）)]',  # 括号编号
            r'^[一二三四五六七八九十]+[、.]',  # 中文数字
            r'^严禁',  # 禁止性开头
            r'^不得',  # 不得开头
            r'^禁止',  # 禁止开头
            r'^违反',  # 违反开头
        ]

        for pattern in start_patterns:
            if re.search(pattern, content):
                return True

        return False

    @staticmethod
    def _deep_analyze_segment(segment: Dict[str, Any], rule_source: str, rule_id: int) -> Dict[str, Any]:
        """深度分析规则段落"""
        text = segment['text']

        # 基础规则检查
        if not ConverterService._is_potential_rule(text):
            return None

        try:
            # 创建基础规则对象
            rule = {
                'id': rule_id,
                'original_content': text,
                'rule_source': rule_source,
                'city': ConverterService._extract_city(text, rule_source),
                'sequence_number': ConverterService._extract_sequence_number(text, rule_id),
                'source_info': {'segment_type': segment['type'], 'enhanced_ai': True},
                'status': 'pending_review',
                'created_at': datetime.now().isoformat()
            }

            # 深度字段提取
            rule.update({
                'department': ConverterService._enhanced_extract_department(text),
                'violation_type': ConverterService._enhanced_extract_violation_type(text),
                'rule_name': ConverterService._enhanced_extract_rule_name(text),
                'rule_content': text,  # 完整内容作为规则内涵
                'medical_name1': ConverterService._enhanced_extract_medical_name1(text),
                'medical_name2': ConverterService._enhanced_extract_medical_name2(text),
                'type': ConverterService._enhanced_extract_violation_type(text),
                'time_type': ConverterService._enhanced_extract_time_type(text),
                'violation_count': '',
                'item_count': '',
                'age': '',
                'gender': '',
                'exclude_diagnosis': '',
                'exclude_department': '',
                'include_diagnosis': '',
                'other': ''
            })

            # 计算增强置信度
            rule['confidence'] = ConverterService._calculate_enhanced_confidence(rule, text)

            return rule

        except Exception as e:
            logging.error(f"深度分析段落失败: {str(e)}")
            return None

    @staticmethod
    def _extract_sequence_number(text: str, default_id: int) -> int:
        """提取序号"""
        import re

        # 查找数字序号
        patterns = [
            r'^(\d+)[、.]',
            r'^[（(](\d+)[）)]',
            r'第(\d+)条',
            r'(\d+)、'
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    return int(match.group(1))
                except:
                    pass

        return default_id

    @staticmethod
    def _enhanced_extract_department(text: str) -> str:
        """增强的科室提取"""
        # 扩展的科室关键词映射
        department_keywords = {
            '心血管内科': ['心血管', '心脏', '冠心病', '心电', '心肌', '血管', '心律', '心衰', '心梗'],
            '神经内科': ['神经', '脑血管', '脑梗', '脑出血', '癫痫', '帕金森', '脑卒中', '神经系统'],
            '消化内科': ['消化', '胃肠', '肝脏', '胆囊', '胰腺', '胃镜', '肠镜', '消化道'],
            '呼吸内科': ['呼吸', '肺部', '气管', '支气管', '肺炎', '哮喘', '肺功能'],
            '内分泌科': ['糖尿病', '甲状腺', '内分泌', '激素', '胰岛素', '血糖'],
            '肾内科': ['肾脏', '肾功能', '尿毒症', '透析', '肾炎'],
            '血液科': ['血液', '白血病', '贫血', '血小板', '造血'],
            '风湿免疫科': ['风湿', '免疫', '关节炎', '红斑狼疮'],
            '骨科': ['骨折', '关节', '脊柱', '骨科', '外伤', '骨头', '韧带'],
            '普外科': ['手术', '切除', '缝合', '外科', '阑尾', '疝气'],
            '胸外科': ['胸外', '肺部手术', '胸腔'],
            '神经外科': ['神经外科', '脑外科', '颅脑'],
            '泌尿外科': ['泌尿', '肾脏', '膀胱', '前列腺', '结石'],
            '心胸外科': ['心胸', '心脏手术', '胸部'],
            '妇产科': ['妇科', '产科', '分娩', '子宫', '卵巢', '妇女', '孕妇'],
            '儿科': ['儿科', '小儿', '新生儿', '婴儿', '儿童'],
            '眼科': ['眼科', '视力', '白内障', '青光眼', '眼部'],
            '耳鼻喉科': ['耳鼻喉', '听力', '鼻炎', '咽喉', '耳朵'],
            '口腔科': ['口腔', '牙科', '牙齿', '口腔医学'],
            '皮肤科': ['皮肤', '湿疹', '皮炎', '过敏', '皮肤病'],
            '肿瘤科': ['肿瘤', '癌症', '化疗', '放疗', '恶性'],
            '急诊科': ['急诊', '抢救', '急救', '急性'],
            '麻醉科': ['麻醉', '手术麻醉'],
            '康复科': ['康复', '理疗', '针灸', '推拿', '物理治疗'],
            '中医科': ['中医', '中药', '针灸', '推拿', '中医药'],
            '检验科': ['检验', '化验', '血常规', '尿常规', '生化'],
            '影像科': ['影像', 'CT', 'MRI', 'X光', 'B超', '超声', '放射'],
            '病理科': ['病理', '活检', '病理检查'],
            '药剂科': ['药剂', '药房', '配药', '药学'],
            '护理部': ['护理', '护士', '护理部'],
            'ICU': ['ICU', '重症', '监护', '重症监护'],
            '手术室': ['手术室', '手术', '术中']
        }

        # 多层匹配
        for dept, keywords in department_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    return dept

        # 如果没有明确匹配，尝试模糊匹配
        if any(word in text for word in ['内科', '外科', '科室']):
            return '通用'

        return '通用'

    @staticmethod
    def _enhanced_extract_violation_type(text: str) -> str:
        """增强的违规类型提取"""
        violation_patterns = {
            '重复收费': [
                '重复收费', '重复收取', '重复计费', '多次收费', '重复开具',
                '重复.*?费用', '重复.*?收取', '同时收取.*?费用'
            ],
            '超标准收费': [
                '超标准', '超出标准', '高于标准', '超额收费', '超过.*?标准',
                '违反.*?标准', '不符合.*?标准'
            ],
            '分解收费': [
                '分解收费', '分项收费', '拆分收费', '分别收费',
                '分解.*?项目', '拆分.*?费用'
            ],
            '虚假收费': [
                '虚假', '伪造', '编造', '不实', '虚构',
                '虚假.*?费用', '伪造.*?记录'
            ],
            '套用收费': [
                '套用', '冒用', '错用', '滥用', '套取',
                '套用.*?编码', '冒用.*?项目'
            ],
            '超范围收费': [
                '超范围', '超出范围', '不在范围', '范围外',
                '超出.*?范围', '不符合.*?范围'
            ],
            '违规用药': [
                '违规用药', '超适应症', '无指征用药', '滥用药物',
                '违规.*?药品', '不当.*?用药'
            ],
            '过度医疗': [
                '过度医疗', '过度检查', '过度治疗', '不必要.*?检查',
                '过度.*?诊疗', '无指征.*?治疗'
            ],
            '挂床住院': [
                '挂床', '虚假住院', '无指征住院', '不符合.*?住院',
                '挂床.*?住院', '虚假.*?入院'
            ],
            '串换药品': [
                '串换', '以次充好', '替代使用', '串换.*?药品',
                '以.*?充.*?', '替换.*?药物'
            ],
            '诱导消费': [
                '诱导', '强制消费', '诱导.*?消费', '强制.*?检查'
            ],
            '不合理诊疗': [
                '不合理', '不当诊疗', '不规范', '违规诊疗',
                '不合理.*?诊疗', '不当.*?治疗'
            ]
        }

        # 按优先级匹配
        for vtype, patterns in violation_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text):
                    return vtype

        # 基于禁止性词汇的分类
        if re.search(r'不得|禁止|严禁', text):
            return '违规操作'

        return '其他违规'

    @staticmethod
    def _enhanced_extract_rule_name(text: str) -> str:
        """增强的规则名称提取"""
        import re

        # 清理文本
        cleaned_text = text.strip()

        # 移除序号
        cleaned_text = re.sub(r'^[0-9]+[、.]?\s*', '', cleaned_text)
        cleaned_text = re.sub(r'^[（(][0-9]+[）)]\s*', '', cleaned_text)
        cleaned_text = re.sub(r'^[一二三四五六七八九十]+[、.]?\s*', '', cleaned_text)

        # 提取核心规则描述
        sentences = cleaned_text.split('。')

        if len(sentences) > 1:
            # 取第一句作为规则名称
            rule_name = sentences[0].strip()
        else:
            # 如果没有句号，取前80个字符
            rule_name = cleaned_text[:80].strip()

        # 进一步清理
        rule_name = re.sub(r'^\s*[（(].*?[）)]\s*', '', rule_name)  # 移除开头括号
        rule_name = re.sub(r'\s+', ' ', rule_name)  # 合并空格

        return rule_name if rule_name else text[:50]

    @staticmethod
    def _enhanced_extract_medical_name1(text: str) -> str:
        """增强的医保名称1提取"""
        import re

        # 医保项目名称模式
        medical_patterns = [
            r'"([^"]+)"',  # 双引号
            r'"([^"]+)"',  # 中文双引号
            r'「([^」]+)」',  # 中文书名号
            r'《([^》]+)》',   # 中文书名号
            r'【([^】]+)】',   # 中文方括号
            r'（编码[^）]*）前的([^，。]+)',  # 编码前的项目名
        ]

        for pattern in medical_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                # 检查是否是医保项目
                if any(keyword in match for keyword in [
                    '监护', '造影', '术', '检查', '治疗', '费', '诊疗',
                    '手术', '化验', '检验', '护理', '麻醉', '材料'
                ]):
                    return match

        # 查找常见医保项目关键词
        medical_keywords = [
            '心电监护', '动态血压监测', '动态心电图', '血常规', '尿常规',
            '生化检查', 'CT检查', 'MRI检查', 'B超检查', '胃镜检查',
            '肠镜检查', '手术费', '麻醉费', '护理费', '床位费',
            '诊查费', '治疗费', '材料费', '药品费'
        ]

        for keyword in medical_keywords:
            if keyword in text:
                return keyword

        return ''

    @staticmethod
    def _enhanced_extract_medical_name2(text: str) -> str:
        """增强的医保名称2提取"""
        import re

        # 查找重复收费相关的第二个医保项目
        patterns = [
            r'重复收取[^，。]*?([^，。]*(?:费用|费|术|检查|治疗|监护))',
            r'同时收取[^，。]*?([^，。]*(?:费用|费|术|检查|治疗|监护))',
            r'重复.*?和.*?([^，。]*(?:费用|费|术|检查|治疗|监护))',
            r'.*?或.*?([^，。]*(?:费用|费|术|检查|治疗|监护))',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                cleaned = re.sub(r'[的了等及和或]', '', match.strip())
                if len(cleaned) > 2 and cleaned != ConverterService._enhanced_extract_medical_name1(text):
                    return cleaned

        return ''

    @staticmethod
    def _enhanced_extract_time_type(text: str) -> str:
        """增强的时间类型提取"""
        time_patterns = {
            '分钟': [r'\d+分钟', '分钟', '每分钟', '按分钟'],
            '小时': [r'\d+小时', '小时', '每小时', '按小时'],
            '天': [r'\d+天', r'\d+日', '天', '日', '每天', '每日', '按天', '按日'],
            '周': [r'\d+周', '周', '每周', '按周'],
            '月': [r'\d+月', '月', '每月', '按月'],
            '年': [r'\d+年', '年', '每年', '按年'],
            '次': [r'\d+次', '次', '每次', '按次', '单次'],
            '项': [r'\d+项', '项', '每项', '按项'],
            '例': [r'\d+例', '例', '每例', '按例']
        }

        for time_type, patterns in time_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text):
                    return time_type

        return ''

    @staticmethod
    def _calculate_enhanced_confidence(rule: Dict[str, Any], text: str) -> float:
        """计算增强置信度"""
        confidence = 0.0

        # 基础分数
        confidence += 0.1

        # 规则名称质量 (30%)
        rule_name = rule.get('rule_name', '')
        if rule_name and len(rule_name) > 5:
            confidence += 0.3
            if len(rule_name) > 15:
                confidence += 0.1

        # 科室识别 (20%)
        department = rule.get('department', '')
        if department and department != '通用':
            confidence += 0.2

        # 违规类型识别 (20%)
        violation_type = rule.get('violation_type', '')
        if violation_type and violation_type != '其他违规':
            confidence += 0.2

        # 医保名称提取 (15%)
        if rule.get('medical_name1'):
            confidence += 0.1
        if rule.get('medical_name2'):
            confidence += 0.05

        # 时间类型 (5%)
        if rule.get('time_type'):
            confidence += 0.05

        # 文本质量 (10%)
        if 20 <= len(text) <= 200:
            confidence += 0.1
        elif 200 < len(text) <= 500:
            confidence += 0.05

        # 关键词密度
        rule_keywords = ['不得', '禁止', '严禁', '违反', '违规', '重复', '虚假', '医保', '基金']
        keyword_count = sum(1 for keyword in rule_keywords if keyword in text)
        confidence += min(keyword_count * 0.02, 0.1)

        return min(confidence, 1.0)

    @staticmethod
    @handle_db_error
    def delete_upload(upload_id: int) -> Dict[str, Any]:
        """删除上传记录及相关规则"""
        try:
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 先删除相关的规则记录
                    delete_rules_sql = "DELETE FROM converter_rules WHERE upload_id = :upload_id"
                    cursor.execute(delete_rules_sql, {'upload_id': upload_id})

                    # 删除上传记录
                    delete_upload_sql = "DELETE FROM converter_uploads WHERE id = :upload_id"
                    cursor.execute(delete_upload_sql, {'upload_id': upload_id})

                    # 检查是否删除成功
                    if cursor.rowcount == 0:
                        return {'success': False, 'error': '上传记录不存在'}

                conn.commit()

            logging.info(f"删除上传记录成功: {upload_id}")
            return {'success': True, 'message': '删除成功'}

        except Exception as e:
            logging.error(f"删除上传记录失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'删除失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def update_rule(rule_id: int, rule_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新规则"""
        try:
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 构建更新SQL
                    update_fields = []
                    params = {'rule_id': rule_id}

                    # 处理各种字段
                    field_mapping = {
                        'rule_source': 'rule_source',
                        'city': 'city',
                        'sequence_number': 'sequence_number',
                        'department': 'department',
                        'violation_type': 'violation_type',
                        'rule_name': 'rule_name',
                        'rule_content': 'rule_content',
                        'medical_name1': 'medical_name1',
                        'medical_name2': 'medical_name2',
                        'violation_count': 'violation_count',
                        'type': 'type',
                        'time_type': 'time_type',
                        'item_count': 'item_count',
                        'age': 'age',
                        'gender': 'gender',
                        'exclude_diagnosis': 'exclude_diagnosis',
                        'exclude_department': 'exclude_department',
                        'include_diagnosis': 'include_diagnosis',
                        'other': 'other',
                        'confidence': 'confidence',
                        'status': 'status'
                    }

                    for field, db_field in field_mapping.items():
                        if field in rule_data:
                            update_fields.append(f"{db_field} = :{field}")
                            params[field] = rule_data[field]

                    if not update_fields:
                        return {'success': False, 'error': '没有要更新的字段'}

                    # 添加更新时间
                    update_fields.append("updated_at = CURRENT_TIMESTAMP")

                    update_sql = f"""
                    UPDATE converter_rules
                    SET {', '.join(update_fields)}
                    WHERE id = :rule_id
                    """

                    cursor.execute(update_sql, params)

                    if cursor.rowcount == 0:
                        return {'success': False, 'error': '规则不存在'}

                conn.commit()

            logging.info(f"更新规则成功: {rule_id}")
            return {'success': True, 'message': '更新成功'}

        except Exception as e:
            logging.error(f"更新规则失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'更新失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def delete_rule(rule_id: int) -> Dict[str, Any]:
        """删除规则"""
        try:
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    delete_sql = "DELETE FROM converter_rules WHERE id = :rule_id"
                    cursor.execute(delete_sql, {'rule_id': rule_id})

                    if cursor.rowcount == 0:
                        return {'success': False, 'error': '规则不存在'}

                conn.commit()

            logging.info(f"删除规则成功: {rule_id}")
            return {'success': True, 'message': '删除成功'}

        except Exception as e:
            logging.error(f"删除规则失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'删除失败: {str(e)}'}

    # ==================== 新的表格解析方法 ====================

    @staticmethod
    def _parse_excel_to_table(file_path: str) -> Dict[str, Any]:
        """解析Excel文件为表格格式"""
        try:
            logging.info(f"开始解析Excel文件为表格: {file_path}")

            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            table_data = {
                'headers': [],
                'rows': [],
                'metadata': {
                    'file_type': 'excel',
                    'sheets': list(excel_file.sheet_names)
                }
            }

            # 处理第一个工作表
            sheet_name = excel_file.sheet_names[0]
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            # 获取表头
            headers = [str(col) for col in df.columns]
            table_data['headers'] = headers

            # 获取数据行
            for index, row in df.iterrows():
                row_data = {
                    'row_number': index + 1,
                    'cells': [str(cell) if pd.notna(cell) else '' for cell in row.values],
                    'original_content': ' | '.join([str(cell) if pd.notna(cell) else '' for cell in row.values])
                }
                table_data['rows'].append(row_data)

            logging.info(f"Excel解析完成，提取 {len(table_data['rows'])} 行数据")
            return {'success': True, 'table_data': table_data}

        except Exception as e:
            logging.error(f"Excel表格解析失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'Excel表格解析失败: {str(e)}'}

    @staticmethod
    def _parse_word_to_table(file_path: str) -> Dict[str, Any]:
        """解析Word文件为表格格式"""
        try:
            logging.info(f"开始解析Word文件为表格: {file_path}")

            try:
                from docx import Document

                doc = Document(file_path)
                table_data = {
                    'headers': [],
                    'rows': [],
                    'metadata': {
                        'file_type': 'word',
                        'tables_count': len(doc.tables)
                    }
                }

                # 如果有表格，解析第一个表格
                if doc.tables:
                    table = doc.tables[0]

                    # 获取表头（第一行）
                    if table.rows:
                        header_row = table.rows[0]
                        headers = [cell.text.strip() for cell in header_row.cells]
                        table_data['headers'] = headers

                        # 获取数据行（从第二行开始）
                        for row_index, row in enumerate(table.rows[1:], 1):
                            cells = [cell.text.strip() for cell in row.cells]
                            row_data = {
                                'row_number': row_index,
                                'cells': cells,
                                'original_content': ' | '.join(cells)
                            }
                            table_data['rows'].append(row_data)
                else:
                    # 如果没有表格，将段落作为行处理
                    for para_index, paragraph in enumerate(doc.paragraphs, 1):
                        if paragraph.text.strip():
                            row_data = {
                                'row_number': para_index,
                                'cells': [paragraph.text.strip()],
                                'original_content': paragraph.text.strip()
                            }
                            table_data['rows'].append(row_data)

                logging.info(f"Word解析完成，提取 {len(table_data['rows'])} 行数据")
                return {'success': True, 'table_data': table_data}

            except ImportError:
                return {'success': False, 'error': 'Word解析功能需要安装python-docx库'}

        except Exception as e:
            logging.error(f"Word表格解析失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'Word表格解析失败: {str(e)}'}

    @staticmethod
    def _parse_pdf_to_table(file_path: str) -> Dict[str, Any]:
        """解析PDF文件为表格格式"""
        try:
            logging.info(f"开始解析PDF文件为表格: {file_path}")

            try:
                import PyPDF2

                table_data = {
                    'headers': [],
                    'rows': [],
                    'metadata': {
                        'file_type': 'pdf'
                    }
                }

                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)

                    for page_num, page in enumerate(pdf_reader.pages):
                        text = page.extract_text()
                        if text.strip():
                            lines = text.split('\n')
                            for line_num, line in enumerate(lines, 1):
                                if line.strip():
                                    row_data = {
                                        'row_number': len(table_data['rows']) + 1,
                                        'cells': [line.strip()],
                                        'original_content': line.strip(),
                                        'page': page_num + 1
                                    }
                                    table_data['rows'].append(row_data)

                logging.info(f"PDF解析完成，提取 {len(table_data['rows'])} 行数据")
                return {'success': True, 'table_data': table_data}

            except ImportError:
                return {'success': False, 'error': 'PDF解析功能需要安装PyPDF2库'}

        except Exception as e:
            logging.error(f"PDF表格解析失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'PDF表格解析失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def _save_raw_table_data(upload_id: int, table_data: Dict[str, Any]) -> Dict[str, Any]:
        """保存原始表格数据"""
        try:
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 保存表格元数据
                    metadata_sql = """
                    UPDATE converter_uploads
                    SET table_headers = :headers,
                        table_metadata = :metadata,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :upload_id
                    """

                    cursor.execute(metadata_sql, {
                        'upload_id': upload_id,
                        'headers': json.dumps(table_data['headers'], ensure_ascii=False),
                        'metadata': json.dumps(table_data['metadata'], ensure_ascii=False)
                    })

                    # 保存表格行数据
                    for row in table_data['rows']:
                        row_sql = """
                        INSERT INTO converter_table_rows
                        (upload_id, row_number, cells, original_content, created_at)
                        VALUES (:upload_id, :row_number, :cells, :original_content, CURRENT_TIMESTAMP)
                        """

                        cursor.execute(row_sql, {
                            'upload_id': upload_id,
                            'row_number': row['row_number'],
                            'cells': json.dumps(row['cells'], ensure_ascii=False),
                            'original_content': row['original_content']
                        })

                conn.commit()

            logging.info(f"保存表格数据成功: {upload_id}, {len(table_data['rows'])} 行")
            return {'success': True, 'message': '表格数据保存成功'}

        except Exception as e:
            logging.error(f"保存表格数据失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'保存表格数据失败: {str(e)}'}

    @staticmethod
    @handle_db_error
    def get_raw_table_data(upload_id: int) -> Dict[str, Any]:
        """获取原始表格数据"""
        try:
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 获取表格元数据
                    upload_sql = """
                    SELECT table_headers, table_metadata, original_filename, status
                    FROM converter_uploads
                    WHERE id = :upload_id
                    """
                    cursor.execute(upload_sql, {'upload_id': upload_id})
                    upload_row = cursor.fetchone()

                    if not upload_row:
                        return {'success': False, 'error': '上传记录不存在'}

                    # 获取表格行数据
                    rows_sql = """
                    SELECT row_number, cells, original_content
                    FROM converter_table_rows
                    WHERE upload_id = :upload_id
                    ORDER BY row_number
                    """
                    cursor.execute(rows_sql, {'upload_id': upload_id})
                    rows = cursor.fetchall()

                    # 构建表格数据
                    table_data = {
                        'filename': upload_row[2],
                        'status': upload_row[3],
                        'headers': json.loads(upload_row[0]) if upload_row[0] else [],
                        'metadata': json.loads(upload_row[1]) if upload_row[1] else {},
                        'rows': []
                    }

                    for row in rows:
                        table_data['rows'].append({
                            'row_number': row[0],
                            'cells': json.loads(row[1]),
                            'original_content': row[2]
                        })

            return {'success': True, 'table_data': table_data}

        except Exception as e:
            logging.error(f"获取原始表格数据失败: {str(e)}", exc_info=True)
            return {'success': False, 'error': f'获取表格数据失败: {str(e)}'}

    @staticmethod
    def convert_table_to_markdown(table_data: Dict[str, Any]) -> str:
        """将表格数据转换为Markdown格式"""
        try:
            markdown_lines = []

            # 添加文件信息
            markdown_lines.append(f"# 📊 {table_data['filename']}")
            markdown_lines.append("")
            markdown_lines.append(f"**状态**: {table_data['status']}")
            markdown_lines.append(f"**总行数**: {len(table_data['rows'])}")
            markdown_lines.append("")

            # 构建表格
            if table_data['headers']:
                # 有表头的情况
                headers = table_data['headers']
                markdown_lines.append("| " + " | ".join(headers) + " |")
                markdown_lines.append("| " + " | ".join(["---"] * len(headers)) + " |")

                for row in table_data['rows']:
                    cells = row['cells']
                    # 确保单元格数量与表头一致
                    while len(cells) < len(headers):
                        cells.append("")
                    cells = cells[:len(headers)]  # 截断多余的单元格

                    # 清理单元格内容，避免markdown冲突
                    cleaned_cells = []
                    for cell in cells:
                        cell_str = str(cell).replace("|", "\\|").replace("\n", " ").strip()
                        if len(cell_str) > 50:
                            cell_str = cell_str[:47] + "..."
                        cleaned_cells.append(cell_str)

                    markdown_lines.append("| " + " | ".join(cleaned_cells) + " |")
            else:
                # 无表头的情况，显示为编号列表
                markdown_lines.append("## 📝 内容列表")
                markdown_lines.append("")

                for row in table_data['rows']:
                    content = row['original_content'].strip()
                    if content:
                        markdown_lines.append(f"{row['row_number']}. {content}")

            return "\n".join(markdown_lines)

        except Exception as e:
            logging.error(f"转换Markdown失败: {str(e)}", exc_info=True)
            return f"转换失败: {str(e)}"
