"""规则管理服务类"""

import os
import re
import logging
import pandas as pd
from flask import current_app
from app.utils.database import db_manager, execute_rules_query, handle_db_error
from app.models.audit import AuditLog

class RuleService:
    """规则管理服务类"""
    
    @staticmethod
    def get_all_rules(page=1, per_page=20, **filters):
        """获取所有规则（分页）- 对照app.py第2336行的实现"""
        try:
            with db_manager.get_connection() as conn:
                # 如果有过滤条件，使用搜索查询（对照app.py第3497行）
                if any(filters.values()):
                    return RuleService._get_filtered_rules(page, per_page, filters, conn)

                # 无过滤条件时，使用完整的查询（对照app.py第2336行）
                query = """
                WITH rule_cities AS (
                SELECT 
                    规则id,
                    to_char(wm_concat(DISTINCT 城市))  AS 城市列表,
                    to_char(wm_concat(DISTINCT 规则来源)) AS 规则来源列表
                FROM 规则医保编码对照
                GROUP BY 规则id
                )
                SELECT
                    a.id as "ID",
                    a.序号,
                    a.规则名称,
                    a.适用范围,
                    rc.城市列表 as 城市,
                    rc.规则来源列表 as 规则来源,
                    a.行为认定,
                    b.规则内涵,
                    a.发布时间,
                    a.生效日期,
                    a.失效日期,
                    a.涉及科室,
                    a.违规数量,
                    a.违规天数,
                    a.违规小时数,
                    a.违规金额,
                    a.年龄,
                    a.类型,
                    a.规则类型,
                    b.医保编码1,
                    b.医保名称1,
                    b.医保编码2,
                    b.医保名称2,
                    a.国家医保编码1,
                    a.国家医保名称1,
                    a.国家医保编码2,
                    a.国家医保名称2,
                    b.物价编码,
                    a.首次进入问题清单年份,
                    a.用途,
                    a.备注
                FROM 飞检规则知识库 a
                LEFT JOIN rule_cities rc ON a.id = rc.规则id 
                LEFT JOIN (
                    SELECT 规则id, 医保编码1, 医保名称1, 医保编码2, 医保名称2, 物价编码,规则内涵,
                           ROW_NUMBER() OVER (PARTITION BY 规则id ORDER BY 对照id) AS rn
                    FROM 规则医保编码对照
                ) b ON a.id = b.规则id AND b.rn = 1
                ORDER BY a.id DESC
                """

                df = execute_rules_query(conn, query)

                # 计算分页
                total = len(df) if not df.empty else 0
                start_idx = (page - 1) * per_page
                end_idx = start_idx + per_page

                # 分页处理
                if not df.empty:
                    paginated_df = df.iloc[start_idx:end_idx]
                    rules = []
                    for _, row in paginated_df.iterrows():
                        rule = {}
                        for col in paginated_df.columns:
                            value = row[col]
                            if pd.isna(value):
                                rule[col] = ''
                            else:
                                rule[col] = str(value) if isinstance(value, (int, float)) else value
                        rules.append(rule)
                else:
                    rules = []

                return {
                    'rules': rules,
                    'total': total,
                    'page': page,
                    'per_page': per_page,
                    'pages': (total + per_page - 1) // per_page
                }
        except Exception as e:
            logging.error(f"获取规则列表失败: {str(e)}")
            raise

    @staticmethod
    def _get_filtered_rules(page, per_page, filters, conn):
        """获取过滤后的规则列表 - 对照app.py第3497行的实现"""
        try:
            query = """
            SELECT
                b.对照id as id,
                b.序号_S as 序号,
                a.行为认定,
                b.规则内涵,
                a.规则名称,
                a.适用范围,
                b.城市,
                b.规则来源,
                b.医保编码1,
                b.医保名称1,
                b.医保编码2,
                b.医保名称2,
                a.违规数量,
                a.违规金额,
                a.类型,
                a.规则类型,
                a.排除诊断,
                a.排除科室,
                a.包含诊断,
                a.包含科室,
                a.时间类型,
                b.物价编码,
                a.备注,
                a.性别,
                a.用途
            FROM 飞检规则知识库 a, 规则医保编码对照 b
            WHERE a.id = b.规则id
            """
            params = {}

            # 添加过滤条件
            if filters.get('behavior_type'):
                query += " AND a.行为认定 = :behavior_type"
                params['behavior_type'] = filters['behavior_type']

            if filters.get('city'):
                query += " AND b.城市 = :city"
                params['city'] = filters['city']

            if filters.get('rule_source'):
                query += " AND b.规则来源 = :rule_source"
                params['rule_source'] = filters['rule_source']

            if filters.get('rule_name'):
                query += " AND a.规则名称 LIKE :rule_name"
                params['rule_name'] = f"%{filters['rule_name']}%"

            if filters.get('type'):
                query += " AND a.类型 = :type"
                params['type'] = filters['type']

            if filters.get('rule_type'):
                query += " AND a.规则类型 = :rule_type"
                params['rule_type'] = filters['rule_type']

            query += " ORDER BY a.id"

            df = execute_rules_query(conn, query, params)

            # 计算分页
            total = len(df) if not df.empty else 0
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page

            # 分页处理
            if not df.empty:
                paginated_df = df.iloc[start_idx:end_idx]
                rules = []
                for _, row in paginated_df.iterrows():
                    rule = {}
                    for col in paginated_df.columns:
                        value = row[col]
                        if pd.isna(value):
                            rule[col] = ''
                        else:
                            rule[col] = str(value) if isinstance(value, (int, float)) else value
                    rules.append(rule)
            else:
                rules = []

            return {
                'rules': rules,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page
            }
        except Exception as e:
            logging.error(f"获取过滤规则列表失败: {str(e)}")
            raise

    @staticmethod
    def get_rule_by_id(rule_id):
        """根据ID获取规则详情"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT
                    b.对照id,
                    a.id as "ID",
                    a.序号,
                    a.规则名称,
                    a.适用范围,
                    b.城市,
                    b.规则来源,
                    a.行为认定,
                    b.规则内涵,
                    a.涉及科室,
                    a.违规数量,
                    a.违规天数,
                    a.违规小时数,
                    a.违规金额,
                    a.年龄,
                    a.类型,
                    a.规则类型,
                    b.医保编码1,
                    b.医保名称1,
                    b.医保编码2,
                    b.医保名称2,
                    a.国家医保编码1,
                    a.国家医保名称1,
                    a.国家医保编码2,
                    a.国家医保名称2,
                    a.排除诊断,
                    a.排除科室,
                    a.包含诊断,
                    a.包含科室,
                    a.时间类型,
                    b.物价编码,
                    a.备注,
                    a.性别,
                    a.用途
                FROM 飞检规则知识库 a, 规则医保编码对照 b
                WHERE a.id = b.规则id AND a.id = :rule_id
                """
                
                df = execute_rules_query(conn, query, {'rule_id': rule_id})
                return df.to_dict('records') if not df.empty else []
        except Exception as e:
            logging.error(f"获取规则详情失败: {str(e)}")
            raise

    @staticmethod
    def search_rules(filters):
        """搜索规则"""
        try:
            with db_manager.get_connection() as conn:
                base_query = """
                SELECT
                    b.对照id,
                    a.id as ID,
                    b.序号_S as 序号,
                    a.行为认定,
                    b.规则内涵,
                    a.规则名称,
                    a.适用范围,
                    b.城市,
                    b.规则来源,
                    b.医保编码1,
                    b.医保名称1,
                    b.医保编码2,
                    b.医保名称2,
                    a.违规数量,
                    a.违规金额,
                    a.类型,
                    a.规则类型,
                    a.排除诊断,
                    a.排除科室,
                    a.包含诊断,
                    a.包含科室,
                    a.时间类型,
                    b.物价编码,
                    a.备注,
                    a.性别,
                    a.用途
                FROM 飞检规则知识库 a, 规则医保编码对照 b
                WHERE a.id = b.规则id
                """
                
                conditions = []
                params = {}
                
                # 添加搜索条件
                if filters.get('behavior_type'):
                    conditions.append("a.行为认定 LIKE :behavior_type")
                    params['behavior_type'] = f"%{filters['behavior_type']}%"

                if filters.get('city'):
                    conditions.append("b.城市 = :city")
                    params['city'] = filters['city']

                if filters.get('rule_source'):
                    conditions.append("b.规则来源 = :rule_source")
                    params['rule_source'] = filters['rule_source']

                if filters.get('rule_name'):
                    conditions.append("a.规则名称 LIKE :rule_name")
                    params['rule_name'] = f"%{filters['rule_name']}%"

                if filters.get('rule_content'):
                    conditions.append("b.规则内涵 LIKE :rule_content")
                    params['rule_content'] = f"%{filters['rule_content']}%"

                if filters.get('type'):
                    if filters['type'] == '__unset__':
                        conditions.append("(a.类型 IS NULL OR a.类型 = '')")
                    else:
                        conditions.append("a.类型 = :type")
                        params['type'] = filters['type']

                if filters.get('rule_type'):
                    if filters['rule_type'] == '__unset__':
                        conditions.append("(a.规则类型 IS NULL OR a.规则类型 = '')")
                    else:
                        conditions.append("a.规则类型 = :rule_type")
                        params['rule_type'] = filters['rule_type']
                
                if conditions:
                    base_query += " AND " + " AND ".join(conditions)
                
                base_query += " ORDER BY a.id"
                
                df = execute_rules_query(conn, base_query, params)
                return df.to_dict('records') if not df.empty else []
        except Exception as e:
            logging.error(f"搜索规则失败: {str(e)}")
            raise

    @staticmethod
    def get_behavior_types():
        """获取行为认定类型列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 行为认定
                FROM 飞检规则知识库
                WHERE 行为认定 IS NOT NULL
                ORDER BY 行为认定
                """
                df = execute_rules_query(conn, query)
                return df['行为认定'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取行为认定类型失败: {str(e)}")
            raise

    @staticmethod
    def get_cities():
        """获取城市列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 城市
                FROM 规则医保编码对照
                WHERE 城市 IS NOT NULL
                ORDER BY 城市
                """
                df = execute_rules_query(conn, query)
                return df['城市'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取城市列表失败: {str(e)}")
            raise

    @staticmethod
    def get_rule_sources():
        """获取规则来源列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 规则来源
                FROM 规则医保编码对照
                WHERE 规则来源 IS NOT NULL
                ORDER BY 规则来源
                """
                df = execute_rules_query(conn, query)
                return df['规则来源'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取规则来源列表失败: {str(e)}")
            raise

    @staticmethod
    def get_type_types():
        """获取类型列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 类型
                FROM 飞检规则知识库
                WHERE 类型 IS NOT NULL
                ORDER BY 类型
                """
                df = execute_rules_query(conn, query)
                return df['类型'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取类型列表失败: {str(e)}")
            # 返回默认值
            return ['药品', '诊疗', '检查', '手术', '材料', '其他']

    @staticmethod
    def get_rule_type_types():
        """获取规则类型列表"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT DISTINCT 规则类型
                FROM 飞检规则知识库
                WHERE 规则类型 IS NOT NULL
                ORDER BY 规则类型
                """
                df = execute_rules_query(conn, query)
                return df['规则类型'].tolist() if not df.empty else []
        except Exception as e:
            logging.error(f"获取规则类型列表失败: {str(e)}")
            # 返回默认值
            return ['基础规则', '复合规则', '统计规则', '关联规则', '时间规则', '频次规则']

    @staticmethod
    def create_rule(rule_data):
        """创建新规则"""
        try:
            with db_manager.get_connection() as conn:
                # 获取下一个ID
                next_id = RuleService.get_next_rule_id()

                # 插入到飞检规则知识库表
                insert_query = """
                INSERT INTO 飞检规则知识库 (
                    ID, 规则名称, 规则内涵, 类型, 规则类型, 行为认定,
                    违规数量, 违规金额, 年龄, 性别, 包含科室, 排除科室,
                    包含诊断, 排除诊断, 备注
                ) VALUES (
                    :id, :rule_name, :rule_content, :type, :rule_type, :behavior,
                    :violation_count, :violation_amount, :age, :gender, :include_dept, :exclude_dept,
                    :include_diag, :exclude_diag, :remark
                )
                """

                params = {
                    'id': next_id,
                    'rule_name': rule_data.get('规则名称', ''),
                    'rule_content': rule_data.get('规则内涵', ''),
                    'type': rule_data.get('类型', ''),
                    'rule_type': rule_data.get('规则类型', ''),
                    'behavior': rule_data.get('行为认定', ''),
                    'violation_count': rule_data.get('违规数量', ''),
                    'violation_amount': rule_data.get('违规金额', ''),
                    'age': rule_data.get('年龄', ''),
                    'gender': rule_data.get('性别', ''),
                    'include_dept': rule_data.get('包含科室', ''),
                    'exclude_dept': rule_data.get('排除科室', ''),
                    'include_diag': rule_data.get('包含诊断', ''),
                    'exclude_diag': rule_data.get('排除诊断', ''),
                    'remark': rule_data.get('备注', '')
                }

                with conn.cursor() as cursor:
                    cursor.execute(insert_query, params)

                # 如果有医保编码信息，插入到对照表
                if rule_data.get('医保编码1') or rule_data.get('医保名称1'):
                    insert_mapping_query = """
                    INSERT INTO 规则医保编码对照 (
                        规则id, 序号_S, 城市, 规则来源, 医保编码1, 医保名称1,
                        医保编码2, 医保名称2, 用途
                    ) VALUES (
                        :rule_id, :seq, :city, :rule_source, :code1, :name1,
                        :code2, :name2, :usage
                    )
                    """

                    mapping_params = {
                        'rule_id': next_id,
                        'seq': rule_data.get('序号', ''),
                        'city': rule_data.get('城市', ''),
                        'rule_source': rule_data.get('规则来源', ''),
                        'code1': rule_data.get('医保编码1', ''),
                        'name1': rule_data.get('医保名称1', ''),
                        'code2': rule_data.get('医保编码2', ''),
                        'name2': rule_data.get('医保名称2', ''),
                        'usage': rule_data.get('用途', '')
                    }

                    with conn.cursor() as cursor:
                        cursor.execute(insert_mapping_query, mapping_params)

                conn.commit()

                return {
                    'success': True,
                    'message': '规则创建成功',
                    'id': next_id
                }

        except Exception as e:
            logging.error(f"创建规则失败: {str(e)}")
            return {
                'success': False,
                'error': f'创建规则失败: {str(e)}'
            }

    @staticmethod
    def update_rule(rule_id, rule_data):
        """更新规则"""
        try:
            with db_manager.get_connection() as conn:
                # 更新飞检规则知识库表
                update_query = """
                UPDATE 飞检规则知识库 SET
                    规则名称 = :rule_name,
                    规则内涵 = :rule_content,
                    类型 = :type,
                    规则类型 = :rule_type,
                    行为认定 = :behavior,
                    违规数量 = :violation_count,
                    违规金额 = :violation_amount,
                    年龄 = :age,
                    性别 = :gender,
                    包含科室 = :include_dept,
                    排除科室 = :exclude_dept,
                    包含诊断 = :include_diag,
                    排除诊断 = :exclude_diag,
                    备注 = :remark
                WHERE ID = :id
                """

                params = {
                    'id': rule_id,
                    'rule_name': rule_data.get('规则名称', ''),
                    'rule_content': rule_data.get('规则内涵', ''),
                    'type': rule_data.get('类型', ''),
                    'rule_type': rule_data.get('规则类型', ''),
                    'behavior': rule_data.get('行为认定', ''),
                    'violation_count': rule_data.get('违规数量', ''),
                    'violation_amount': rule_data.get('违规金额', ''),
                    'age': rule_data.get('年龄', ''),
                    'gender': rule_data.get('性别', ''),
                    'include_dept': rule_data.get('包含科室', ''),
                    'exclude_dept': rule_data.get('排除科室', ''),
                    'include_diag': rule_data.get('包含诊断', ''),
                    'exclude_diag': rule_data.get('排除诊断', ''),
                    'remark': rule_data.get('备注', '')
                }

                with conn.cursor() as cursor:
                    cursor.execute(update_query, params)

                # 更新医保编码对照表
                update_mapping_query = """
                UPDATE 规则医保编码对照 SET
                    序号_S = :seq,
                    城市 = :city,
                    规则来源 = :rule_source,
                    医保编码1 = :code1,
                    医保名称1 = :name1,
                    医保编码2 = :code2,
                    医保名称2 = :name2,
                    用途 = :usage
                WHERE 规则id = :rule_id
                """

                mapping_params = {
                    'rule_id': rule_id,
                    'seq': rule_data.get('序号', ''),
                    'city': rule_data.get('城市', ''),
                    'rule_source': rule_data.get('规则来源', ''),
                    'code1': rule_data.get('医保编码1', ''),
                    'name1': rule_data.get('医保名称1', ''),
                    'code2': rule_data.get('医保编码2', ''),
                    'name2': rule_data.get('医保名称2', ''),
                    'usage': rule_data.get('用途', '')
                }

                with conn.cursor() as cursor:
                    cursor.execute(update_mapping_query, mapping_params)

                conn.commit()

                return {
                    'success': True,
                    'message': '规则更新成功'
                }

        except Exception as e:
            logging.error(f"更新规则失败: {str(e)}")
            return {
                'success': False,
                'error': f'更新规则失败: {str(e)}'
            }

    @staticmethod
    def delete_rule(rule_id):
        """删除规则"""
        try:
            with db_manager.get_connection() as conn:
                # 先删除医保编码对照表中的记录
                delete_mapping_query = "DELETE FROM 规则医保编码对照 WHERE 规则id = :rule_id"
                with conn.cursor() as cursor:
                    cursor.execute(delete_mapping_query, {'rule_id': rule_id})

                # 再删除飞检规则知识库表中的记录
                delete_rule_query = "DELETE FROM 飞检规则知识库 WHERE ID = :rule_id"
                with conn.cursor() as cursor:
                    cursor.execute(delete_rule_query, {'rule_id': rule_id})

                conn.commit()

                return {
                    'success': True,
                    'message': '规则删除成功'
                }

        except Exception as e:
            logging.error(f"删除规则失败: {str(e)}")
            return {
                'success': False,
                'error': f'删除规则失败: {str(e)}'
            }

    @staticmethod
    def get_next_rule_id():
        """获取下一个规则ID"""
        try:
            with db_manager.get_connection() as conn:
                # 如果有序列，使用序列
                try:
                    query = "SELECT 飞检规则知识库ID_SEQ.NEXTVAL as next_id FROM DUAL"
                    with conn.cursor() as cursor:
                        cursor.execute(query)
                        result = cursor.fetchone()
                        return result[0] if result else 1
                except:
                    # 如果没有序列，使用MAX+1
                    query = "SELECT NVL(MAX(ID), 0) + 1 as next_id FROM 飞检规则知识库"
                    with conn.cursor() as cursor:
                        cursor.execute(query)
                        result = cursor.fetchone()
                        return result[0] if result else 1
        except Exception as e:
            logging.error(f"获取下一个规则ID失败: {str(e)}")
            return 1

    @staticmethod
    def import_rules(rules_data):
        """导入规则 - 对照app.py第3077行的实现"""
        try:
            success_count = 0
            error_count = 0
            errors = []

            with db_manager.get_connection() as conn:
                for rule in rules_data:
                    try:
                        rule_name = rule.get('规则名称', '')
                        if not rule_name:
                            error_count += 1
                            errors.append('规则名称不能为空')
                            continue

                        # 检查规则是否已存在
                        check_query = """
                        SELECT ID FROM 飞检规则知识库 WHERE 规则名称 = :rule_name
                        """
                        with conn.cursor() as cursor:
                            cursor.execute(check_query, {'rule_name': rule_name})
                            existing_rule = cursor.fetchone()

                        if existing_rule:
                            # 更新现有规则
                            rule_id = existing_rule[0]
                            RuleService.update_rule(rule_id, rule)
                        else:
                            # 创建新规则
                            RuleService.create_rule(rule)

                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'导入规则 {rule.get("规则名称", "")} 失败: {str(e)}')
                        logging.error(f"导入单个规则失败: {str(e)}")

                conn.commit()

            return {
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors
            }
        except Exception as e:
            logging.error(f"导入规则失败: {str(e)}")
            raise

    @staticmethod
    def preview_import_rules(rules_data):
        """预览导入规则 - 对照app.py第2982行的实现"""
        try:
            preview_result = {
                'total_rules': len(rules_data),
                'new_rules': 0,
                'existing_rules': 0,
                'invalid_rules': 0,
                'details': []
            }

            with db_manager.get_connection() as conn:
                for rule in rules_data:
                    rule_name = rule.get('规则名称', '')
                    if not rule_name:
                        preview_result['invalid_rules'] += 1
                        preview_result['details'].append({
                            'rule_name': '未知',
                            'status': 'invalid',
                            'message': '规则名称不能为空'
                        })
                        continue

                    # 检查规则是否已存在
                    check_query = """
                    SELECT ID FROM 飞检规则知识库 WHERE 规则名称 = :rule_name
                    """
                    with conn.cursor() as cursor:
                        cursor.execute(check_query, {'rule_name': rule_name})
                        existing_rule = cursor.fetchone()

                    if existing_rule:
                        preview_result['existing_rules'] += 1
                        preview_result['details'].append({
                            'rule_name': rule_name,
                            'status': 'existing',
                            'message': '规则已存在，将被更新'
                        })
                    else:
                        preview_result['new_rules'] += 1
                        preview_result['details'].append({
                            'rule_name': rule_name,
                            'status': 'new',
                            'message': '新规则，将被创建'
                        })

            return preview_result
        except Exception as e:
            logging.error(f"预览导入规则失败: {str(e)}")
            raise

    @staticmethod
    def merge_rules(source_rule_id, target_rule_id):
        """合并规则 - 对照app.py第5652行的实现"""
        try:
            with db_manager.get_connection() as conn:
                # 获取源规则的对照信息
                source_query = """
                SELECT 对照id, a.id as rule_id, 规则名称, b.城市
                FROM 飞检规则知识库 a, 规则医保编码对照 b
                WHERE a.id = b.规则id AND a.id = :id
                """
                with conn.cursor() as cursor:
                    cursor.execute(source_query, {'id': source_rule_id})
                    source_rules = cursor.fetchall()

                if not source_rules:
                    raise Exception('源规则不存在')

                # 检查目标规则是否存在
                target_query = """
                SELECT id as rule_id FROM 飞检规则知识库 WHERE id = :id
                """
                with conn.cursor() as cursor:
                    cursor.execute(target_query, {'id': target_rule_id})
                    target_rule = cursor.fetchone()

                if not target_rule:
                    raise Exception('目标规则不存在')

                # 更新对照表，将源规则的对照记录指向目标规则
                updated_mappings = 0
                for compare_id, current_rule_id, rule_name, city in source_rules:
                    try:
                        update_query = """
                        UPDATE 规则医保编码对照
                        SET 规则id = :target_rule_id, 原规则名称 = :original_rule_name
                        WHERE 对照id = :compare_id
                        """
                        with conn.cursor() as cursor:
                            cursor.execute(update_query, {
                                'target_rule_id': target_rule_id,
                                'original_rule_name': rule_name,
                                'compare_id': compare_id
                            })
                        updated_mappings += 1
                    except Exception as e:
                        logging.error(f"更新对照记录失败: {str(e)}")

                # 删除源规则
                if updated_mappings > 0:
                    delete_query = "DELETE FROM 飞检规则知识库 WHERE id = :rule_id"
                    with conn.cursor() as cursor:
                        cursor.execute(delete_query, {'rule_id': source_rule_id})

                conn.commit()

                return {
                    'updated_mappings': updated_mappings,
                    'message': f'成功合并规则，更新了 {updated_mappings} 条对照记录'
                }
        except Exception as e:
            logging.error(f"合并规则失败: {str(e)}")
            raise

    @staticmethod
    def search_import_rules(keyword):
        """搜索可导入的规则 - 对照app.py第5191行的实现"""
        try:
            with db_manager.get_connection() as conn:
                query = """
                SELECT a.id, a.规则名称, b.规则来源, b.规则内涵
                FROM 飞检规则知识库 a, 规则医保编码对照 b
                WHERE a.id = b.规则id
                AND a.规则名称 LIKE :keyword
                ORDER BY a.id DESC
                """

                df = execute_rules_query(conn, query, {'keyword': f'%{keyword}%'})
                return df.to_dict('records') if not df.empty else []
        except Exception as e:
            logging.error(f"搜索导入规则失败: {str(e)}")
            raise

    @staticmethod
    def test_sql(sql, database_type='oracle', host_ip='', schema=''):
        """测试SQL功能 - 对照app.py第3845行的实现"""
        try:
            # 为了安全起见，禁止执行危险的SQL操作
            sql_upper = sql.strip().upper()
            dangerous_keywords = ['UPDATE', 'DELETE', 'INSERT', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE', 'GRANT', 'REVOKE']

            for keyword in dangerous_keywords:
                if keyword in sql_upper:
                    raise Exception(f'出于安全考虑，不允许执行包含 {keyword} 的语句')

            if database_type.lower() == 'oracle':
                return RuleService._test_oracle_sql(sql, host_ip, schema)
            else:
                raise Exception(f'不支持的数据库类型: {database_type}')

        except Exception as e:
            logging.error(f"测试SQL失败: {str(e)}")
            raise

    @staticmethod
    def _test_oracle_sql(sql, host_ip='', schema=''):
        """测试Oracle SQL"""
        try:
            import oracledb

            if host_ip and host_ip != 'default':
                # 使用指定主机的Oracle连接
                dsn = f"{host_ip}:1521/orcl"
                with oracledb.connect(
                    user="datachange",
                    password="drgs2019",
                    dsn=dsn
                ) as conn:
                    return RuleService._execute_test_sql(conn, sql, schema, f'Oracle ({host_ip})')
            else:
                # 使用默认Oracle连接
                with db_manager.get_connection() as conn:
                    return RuleService._execute_test_sql(conn, sql, schema, 'Oracle (默认)')

        except Exception as e:
            logging.error(f"测试Oracle SQL失败: {str(e)}")
            raise

    @staticmethod
    def _execute_test_sql(conn, sql, schema='', database_info=''):
        """执行测试SQL"""
        try:
            with conn.cursor() as cursor:
                # 如果指定了schema，切换到该schema
                if schema:
                    cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {schema}")

                # 为了提高查询速度，自动添加行数限制
                limited_sql = sql.strip()
                if limited_sql.endswith(';'):
                    limited_sql = limited_sql[:-1]

                # 对于Oracle，使用子查询包装的方式添加ROWNUM限制
                if 'SELECT' in limited_sql.upper() and 'ROWNUM' not in limited_sql.upper():
                    limited_sql = f"SELECT * FROM ({limited_sql}) WHERE ROWNUM <= 10"

                cursor.execute(limited_sql)

                # 获取查询结果
                columns = [col[0] for col in cursor.description] if cursor.description else []
                rows = cursor.fetchall()

                schema_info = f" - {schema}" if schema else ""
                return {
                    'database': f'{database_info}{schema_info}',
                    'affected_rows': len(rows),
                    'columns': columns,
                    'data': rows[:10]  # 限制返回前10行数据
                }

        except Exception as e:
            logging.error(f"执行测试SQL失败: {str(e)}")
            raise

    @staticmethod
    def get_database_schemas(database_type='oracle', host_ip=''):
        """获取数据库Schema列表 - 对照app.py第3768行的实现"""
        try:
            if database_type.lower() == 'oracle':
                return RuleService._get_oracle_schemas(host_ip)
            else:
                raise Exception(f'不支持的数据库类型: {database_type}')

        except Exception as e:
            logging.error(f"获取数据库Schema失败: {str(e)}")
            raise

    @staticmethod
    def _get_oracle_schemas(host_ip=''):
        """获取Oracle Schema列表"""
        try:
            import oracledb

            if host_ip and host_ip != 'default':
                # 使用指定主机的Oracle连接
                dsn = f"{host_ip}:1521/orcl"
                with oracledb.connect(
                    user="datachange",
                    password="drgs2019",
                    dsn=dsn
                ) as conn:
                    return RuleService._fetch_oracle_schemas(conn)
            else:
                # 使用默认Oracle连接
                with db_manager.get_connection() as conn:
                    return RuleService._fetch_oracle_schemas(conn)

        except Exception as e:
            logging.error(f"获取Oracle Schema失败: {str(e)}")
            raise

    @staticmethod
    def _fetch_oracle_schemas(conn):
        """从Oracle连接中获取Schema列表"""
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT username FROM all_users
                    WHERE username NOT IN ('ANONYMOUS','APPQOSSYS','AUDSYS','CTXSYS','DBSFWUSER','DBSNMP','DIP','DVF','DVSYS','ENGINE','GGSYS','GSMADMIN_INTERNAL','GSMCATUSER','GSMROOTUSER','GSMUSER','HR','LBACSYS','MDDATA','MDSYS','OJVMSYS','OLAPSYS','ORACLE_OCM','ORDDATA','ORDPLUGINS','ORDSYS','OUTLN','REMOTE_SCHEDULER_AGENT','SI_INFORMTN_SCHEMA','SYS','SYS$UMF','SYSBACKUP','SYSDG','SYSKM','SYSRAC','SYSTEM','WMSYS','XDB','XS$NULL')
                    ORDER BY username
                """)
                rows = cursor.fetchall()
                return [row[0] for row in rows]

        except Exception as e:
            logging.error(f"从Oracle连接获取Schema失败: {str(e)}")
            raise

class SQLTemplateService:
    """SQL模板服务类"""
    
    @staticmethod
    def list_sql_templates(template_type='rule'):
        """列出SQL模板"""
        try:
            template_dirs = {
                'rule': os.path.join('templates', 'rule'),
                'manual': os.path.join('templates', 'manual'),
                'excel': os.path.join('templates', 'excel')
            }
            
            template_dir = template_dirs.get(template_type)
            if not template_dir or not os.path.exists(template_dir):
                return []
            
            templates = []
            for file in os.listdir(template_dir):
                if file.endswith('.sql'):
                    templates.append(file)
            
            return sorted(templates)
        except Exception as e:
            logging.error(f"列出SQL模板失败: {str(e)}")
            return []

    @staticmethod
    def get_template_variables(template_path, template_type='manual'):
        """获取模板变量"""
        try:
            if not os.path.exists(template_path):
                return []
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if template_type == 'manual':
                # 使用正则表达式匹配 {variable} 格式的变量
                variables = re.findall(r'\{(\w+)\}', content)
                return list(set(variables))  # 去重
            
            return []
        except Exception as e:
            logging.error(f"获取模板变量失败: {str(e)}")
            return []

    @staticmethod
    def render_sql_template(template_content, rule_data):
        """渲染SQL模板"""
        try:
            # 定义替换映射
            replacements = {
                "{规则名称}": str(rule_data.get("规则名称", "")),
                "{医保编码1}": str(rule_data.get("医保编码1", "")),
                "{医保名称1}": str(rule_data.get("医保名称1", "")),
                "{医保编码2}": str(rule_data.get("医保编码2", "")),
                "{医保名称2}": str(rule_data.get("医保名称2", "")),
                "{国家医保编码1}": str(rule_data.get("国家医保编码1", "")),
                "{国家医保名称1}": str(rule_data.get("国家医保名称1", "")),
                "{国家医保编码2}": str(rule_data.get("国家医保编码2", "")),
                "{国家医保名称2}": str(rule_data.get("国家医保名称2", "")),
                "{违规数量}": str(rule_data.get("违规数量", "")),
                "{违规天数}": str(rule_data.get("违规天数", "")),
                "{违规小时数}": str(rule_data.get("违规小时数", "")),
                "{违规金额}": str(rule_data.get("违规金额", "")),
                "{年龄}": str(rule_data.get("年龄", "")),
                "{性别}": str(rule_data.get("性别", "")),
                "{排除科室}": str(rule_data.get("排除科室", "")),
                "{包含科室}": str(rule_data.get("包含科室", "")),
                "{排除诊断}": str(rule_data.get("排除诊断", "")),
                "{包含诊断}": str(rule_data.get("包含诊断", "")),
                "{时间类型}": str(rule_data.get("时间类型", "")),
                "{物价编码}": str(rule_data.get("物价编码", "")),
                "{备注}": str(rule_data.get("备注", ""))
            }
            
            # 执行替换
            result = template_content
            for placeholder, value in replacements.items():
                result = result.replace(placeholder, value)
                
            return result
        except Exception as e:
            logging.error(f"渲染SQL模板失败: {str(e)}")
            return template_content
