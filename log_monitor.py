#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志监控脚本
定期检查日志中的错误和异常情况
"""

import os
import time
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import re


class LogMonitor:
    """日志监控器"""
    
    def __init__(self, logs_dir='logs'):
        self.logs_dir = logs_dir
        self.last_check_time = None
        self.error_patterns = [
            r'ERROR',
            r'CRITICAL',
            r'Exception',
            r'Traceback',
            r'Failed',
            r'Error:',
            r'连接失败',
            r'数据库错误',
            r'导入失败'
        ]
        
    def get_today_log_file(self):
        """获取今天的日志文件路径"""
        today = datetime.now().strftime('%Y-%m-%d')
        return os.path.join(self.logs_dir, f'micra_{today}.log')
    
    def check_errors_since_last_check(self):
        """检查自上次检查以来的错误"""
        log_file = self.get_today_log_file()
        
        if not os.path.exists(log_file):
            return []
        
        errors = []
        current_time = datetime.now()
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    # 解析时间戳
                    timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if timestamp_match:
                        try:
                            log_time = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                            
                            # 只检查自上次检查以来的日志
                            if self.last_check_time and log_time <= self.last_check_time:
                                continue
                                
                        except ValueError:
                            continue
                    
                    # 检查错误模式
                    for pattern in self.error_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            errors.append({
                                'line_number': line_num,
                                'timestamp': timestamp_match.group(1) if timestamp_match else 'Unknown',
                                'content': line.strip(),
                                'pattern': pattern
                            })
                            break
        
        except Exception as e:
            print(f"读取日志文件失败: {e}")
        
        self.last_check_time = current_time
        return errors
    
    def get_error_summary(self, hours=24):
        """获取指定小时内的错误摘要"""
        log_file = self.get_today_log_file()
        
        if not os.path.exists(log_file):
            return {}
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        error_counts = Counter()
        error_details = defaultdict(list)
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    # 解析时间戳
                    timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if timestamp_match:
                        try:
                            log_time = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                            if log_time < cutoff_time:
                                continue
                        except ValueError:
                            continue
                    
                    # 检查错误级别
                    if ' ERROR ' in line:
                        error_counts['ERROR'] += 1
                        error_details['ERROR'].append(line.strip())
                    elif ' CRITICAL ' in line:
                        error_counts['CRITICAL'] += 1
                        error_details['CRITICAL'].append(line.strip())
                    elif ' WARNING ' in line:
                        error_counts['WARNING'] += 1
                        error_details['WARNING'].append(line.strip())
        
        except Exception as e:
            print(f"分析日志文件失败: {e}")
        
        return {
            'counts': dict(error_counts),
            'details': dict(error_details)
        }
    
    def check_application_health(self):
        """检查应用健康状态"""
        log_file = self.get_today_log_file()
        
        if not os.path.exists(log_file):
            return {
                'status': 'unknown',
                'message': '日志文件不存在'
            }
        
        # 检查最近的日志时间
        last_log_time = None
        recent_errors = 0
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
                # 检查最后几行日志
                for line in reversed(lines[-50:]):  # 检查最后50行
                    timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if timestamp_match:
                        if last_log_time is None:
                            last_log_time = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                        
                        # 统计最近的错误
                        if ' ERROR ' in line or ' CRITICAL ' in line:
                            recent_errors += 1
        
        except Exception as e:
            return {
                'status': 'error',
                'message': f'读取日志失败: {e}'
            }
        
        # 判断健康状态
        current_time = datetime.now()
        
        if last_log_time is None:
            status = 'unknown'
            message = '无法获取最后日志时间'
        elif current_time - last_log_time > timedelta(minutes=10):
            status = 'warning'
            message = f'应用可能已停止，最后日志时间: {last_log_time}'
        elif recent_errors > 5:
            status = 'error'
            message = f'最近有 {recent_errors} 个错误'
        else:
            status = 'healthy'
            message = '应用运行正常'
        
        return {
            'status': status,
            'message': message,
            'last_log_time': last_log_time.strftime('%Y-%m-%d %H:%M:%S') if last_log_time else None,
            'recent_errors': recent_errors
        }
    
    def generate_daily_report(self):
        """生成每日报告"""
        log_file = self.get_today_log_file()
        today = datetime.now().strftime('%Y-%m-%d')
        
        if not os.path.exists(log_file):
            return f"日期 {today} 的日志文件不存在"
        
        # 统计信息
        stats = {
            'total_lines': 0,
            'DEBUG': 0,
            'INFO': 0,
            'WARNING': 0,
            'ERROR': 0,
            'CRITICAL': 0,
            'requests': 0,
            'errors': []
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    stats['total_lines'] += 1
                    
                    # 统计日志级别
                    if ' DEBUG ' in line:
                        stats['DEBUG'] += 1
                    elif ' INFO ' in line:
                        stats['INFO'] += 1
                        # 统计HTTP请求
                        if 'HTTP/1.1' in line:
                            stats['requests'] += 1
                    elif ' WARNING ' in line:
                        stats['WARNING'] += 1
                    elif ' ERROR ' in line:
                        stats['ERROR'] += 1
                        stats['errors'].append(line.strip())
                    elif ' CRITICAL ' in line:
                        stats['CRITICAL'] += 1
                        stats['errors'].append(line.strip())
        
        except Exception as e:
            return f"生成报告失败: {e}"
        
        # 生成报告
        report = f"""
MICRA应用日志日报 - {today}
{'='*50}

基本统计:
  总日志行数: {stats['total_lines']:,}
  HTTP请求数: {stats['requests']:,}

日志级别分布:
  DEBUG:    {stats['DEBUG']:>8,}
  INFO:     {stats['INFO']:>8,}
  WARNING:  {stats['WARNING']:>8,}
  ERROR:    {stats['ERROR']:>8,}
  CRITICAL: {stats['CRITICAL']:>8,}

健康状态:
"""
        
        health = self.check_application_health()
        report += f"  状态: {health['status'].upper()}\n"
        report += f"  说明: {health['message']}\n"
        
        if stats['errors']:
            report += f"\n错误详情 (最近{min(5, len(stats['errors']))}条):\n"
            for error in stats['errors'][-5:]:
                report += f"  - {error}\n"
        
        return report
    
    def save_report(self, report, filename=None):
        """保存报告到文件"""
        if filename is None:
            today = datetime.now().strftime('%Y-%m-%d')
            filename = f"daily_report_{today}.txt"
        
        reports_dir = os.path.join(self.logs_dir, 'reports')
        os.makedirs(reports_dir, exist_ok=True)
        
        report_path = os.path.join(reports_dir, filename)
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            return report_path
        except Exception as e:
            print(f"保存报告失败: {e}")
            return None


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MICRA日志监控工具')
    parser.add_argument('--logs-dir', default='logs', help='日志目录路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 检查错误
    subparsers.add_parser('check', help='检查新错误')
    
    # 健康检查
    subparsers.add_parser('health', help='检查应用健康状态')
    
    # 错误摘要
    summary_parser = subparsers.add_parser('summary', help='错误摘要')
    summary_parser.add_argument('--hours', type=int, default=24, help='统计小时数')
    
    # 生成日报
    report_parser = subparsers.add_parser('report', help='生成日报')
    report_parser.add_argument('--save', action='store_true', help='保存到文件')
    
    # 监控模式
    monitor_parser = subparsers.add_parser('monitor', help='持续监控模式')
    monitor_parser.add_argument('--interval', type=int, default=60, help='检查间隔（秒）')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    monitor = LogMonitor(args.logs_dir)
    
    if args.command == 'check':
        errors = monitor.check_errors_since_last_check()
        if errors:
            print(f"发现 {len(errors)} 个新错误:")
            for error in errors:
                print(f"  [{error['timestamp']}] {error['content']}")
        else:
            print("没有发现新错误")
    
    elif args.command == 'health':
        health = monitor.check_application_health()
        print(f"应用健康状态: {health['status'].upper()}")
        print(f"说明: {health['message']}")
        if health['last_log_time']:
            print(f"最后日志时间: {health['last_log_time']}")
        print(f"最近错误数: {health['recent_errors']}")
    
    elif args.command == 'summary':
        summary = monitor.get_error_summary(args.hours)
        print(f"最近 {args.hours} 小时错误摘要:")
        print("-" * 40)
        
        if summary['counts']:
            for level, count in summary['counts'].items():
                print(f"{level}: {count}")
        else:
            print("没有发现错误")
    
    elif args.command == 'report':
        report = monitor.generate_daily_report()
        print(report)
        
        if args.save:
            report_path = monitor.save_report(report)
            if report_path:
                print(f"\n报告已保存到: {report_path}")
    
    elif args.command == 'monitor':
        print(f"开始监控模式，检查间隔: {args.interval} 秒")
        print("按 Ctrl+C 退出")
        
        try:
            while True:
                errors = monitor.check_errors_since_last_check()
                if errors:
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 发现 {len(errors)} 个新错误:")
                    for error in errors:
                        print(f"  {error['content']}")
                
                time.sleep(args.interval)
                
        except KeyboardInterrupt:
            print("\n监控已停止")


if __name__ == '__main__':
    main()
