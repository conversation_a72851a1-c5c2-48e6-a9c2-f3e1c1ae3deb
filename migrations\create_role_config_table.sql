-- 创建角色配置表
CREATE TABLE role_configs (
    id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    role_id NUMBER NOT NULL,
    config_key VARCHAR2(100) NOT NULL,
    config_value VARCHAR2(500),
    config_type VARCHAR2(20) DEFAULT 'string', -- string, number, boolean, json
    description VARCHAR2(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_role_configs_role FOREIGN KEY (role_id) REFERENCES roles(id),
    CONSTRAINT uk_role_configs_key UNIQUE (role_id, config_key)
);

-- 创建索引
CREATE INDEX idx_role_configs_role_id ON role_configs(role_id);
CREATE INDEX idx_role_configs_key ON role_configs(config_key);

-- 创建序列
CREATE SEQUENCE role_configs_seq START WITH 1 INCREMENT BY 1;

-- 插入默认配置
-- 管理员角色（假设ID为1）- 无限制
INSERT INTO role_configs (role_id, config_key, config_value, config_type, description) 
VALUES (1, 'upload_max_size_mb', '-1', 'number', '上传文件总大小限制(MB)，-1表示无限制');

INSERT INTO role_configs (role_id, config_key, config_value, config_type, description) 
VALUES (1, 'upload_max_file_count', '-1', 'number', '最大文件数量限制，-1表示无限制');

INSERT INTO role_configs (role_id, config_key, config_value, config_type, description) 
VALUES (1, 'upload_allowed_types', 'csv,dmp,dp,bak', 'string', '允许上传的文件类型');

-- 普通用户角色配置示例
-- 注意：需要根据实际的角色ID进行调整
COMMENT ON TABLE role_configs IS '角色配置表，用于存储各角色的个性化配置';
COMMENT ON COLUMN role_configs.config_key IS '配置键名';
COMMENT ON COLUMN role_configs.config_value IS '配置值';
COMMENT ON COLUMN role_configs.config_type IS '配置类型：string, number, boolean, json';
