#!/usr/bin/env python3
"""
迁移selfcheck_tasks表结构
将upload_id和rule_id改为data_source和scheme_id
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def migrate_tasks_table():
    """迁移任务表结构"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            print("=== 迁移selfcheck_tasks表结构 ===")
            
            # 1. 检查当前表结构
            print("\n1. 检查当前表结构...")
            
            structure_query = """
            SELECT column_name, data_type, nullable, data_default
            FROM user_tab_columns 
            WHERE table_name = 'SELFCHECK_TASKS'
            ORDER BY column_id
            """
            columns = db_manager.execute_query(structure_query)
            
            print("当前表结构:")
            column_names = []
            for col in columns:
                column_names.append(col['column_name'])
                nullable = "NULL" if col['nullable'] == 'Y' else "NOT NULL"
                default = f" DEFAULT {col['data_default']}" if col['data_default'] else ""
                print(f"  {col['column_name']}: {col['data_type']} {nullable}{default}")
            
            # 2. 检查是否需要迁移
            needs_migration = False
            if 'DATA_SOURCE' not in column_names:
                needs_migration = True
                print("\n❌ 缺少DATA_SOURCE字段，需要迁移")
            
            if 'SCHEME_ID' not in column_names:
                needs_migration = True
                print("❌ 缺少SCHEME_ID字段，需要迁移")
            
            if not needs_migration:
                print("\n✅ 表结构已经是最新的，无需迁移")
                return
            
            # 3. 备份现有数据
            print("\n2. 备份现有数据...")
            
            backup_query = "SELECT * FROM selfcheck_tasks"
            existing_data = db_manager.execute_query(backup_query)
            print(f"备份了 {len(existing_data)} 条现有数据")
            
            # 4. 删除外键约束
            print("\n3. 删除外键约束...")
            
            # 查询现有约束
            constraint_query = """
            SELECT constraint_name, constraint_type
            FROM user_constraints
            WHERE table_name = 'SELFCHECK_TASKS'
            AND constraint_type = 'R'
            """
            constraints = db_manager.execute_query(constraint_query)
            
            for constraint in constraints:
                try:
                    drop_constraint_sql = f"ALTER TABLE selfcheck_tasks DROP CONSTRAINT {constraint['constraint_name']}"
                    db_manager.execute_update(drop_constraint_sql)
                    print(f"  删除约束: {constraint['constraint_name']}")
                except Exception as e:
                    print(f"  删除约束失败: {constraint['constraint_name']} - {str(e)}")
            
            # 5. 添加新字段
            print("\n4. 添加新字段...")
            
            if 'DATA_SOURCE' not in column_names:
                add_data_source_sql = """
                ALTER TABLE selfcheck_tasks 
                ADD data_source VARCHAR2(100)
                """
                db_manager.execute_update(add_data_source_sql)
                print("  添加data_source字段")
            
            if 'SCHEME_ID' not in column_names:
                add_scheme_id_sql = """
                ALTER TABLE selfcheck_tasks 
                ADD scheme_id NUMBER
                """
                db_manager.execute_update(add_scheme_id_sql)
                print("  添加scheme_id字段")
            
            # 6. 迁移现有数据（如果有的话）
            if existing_data:
                print("\n5. 迁移现有数据...")
                
                for task in existing_data:
                    # 为现有任务设置默认值
                    update_sql = """
                    UPDATE selfcheck_tasks 
                    SET data_source = 'pool1', scheme_id = 1
                    WHERE id = :task_id
                    """
                    db_manager.execute_update(update_sql, {'task_id': task['id']})
                
                print(f"  迁移了 {len(existing_data)} 条数据")
            
            # 7. 设置新字段为NOT NULL（在有数据后）
            print("\n6. 设置字段约束...")
            
            if existing_data:
                # 如果有数据，先设置默认值再设置NOT NULL
                modify_data_source_sql = """
                ALTER TABLE selfcheck_tasks 
                MODIFY data_source VARCHAR2(100) NOT NULL
                """
                db_manager.execute_update(modify_data_source_sql)
                
                modify_scheme_id_sql = """
                ALTER TABLE selfcheck_tasks 
                MODIFY scheme_id NUMBER NOT NULL
                """
                db_manager.execute_update(modify_scheme_id_sql)
                print("  设置字段为NOT NULL")
            
            # 8. 添加新的外键约束
            print("\n7. 添加新的外键约束...")
            
            try:
                add_scheme_fk_sql = """
                ALTER TABLE selfcheck_tasks 
                ADD CONSTRAINT fk_selfcheck_tasks_scheme 
                FOREIGN KEY (scheme_id) REFERENCES selfcheck_schemes(id)
                """
                db_manager.execute_update(add_scheme_fk_sql)
                print("  添加scheme_id外键约束")
            except Exception as e:
                print(f"  添加scheme_id外键约束失败: {str(e)}")
            
            # 9. 创建索引
            print("\n8. 创建索引...")
            
            try:
                create_data_source_index_sql = """
                CREATE INDEX idx_selfcheck_tasks_data_source 
                ON selfcheck_tasks(data_source)
                """
                db_manager.execute_update(create_data_source_index_sql)
                print("  创建data_source索引")
            except Exception as e:
                print(f"  创建data_source索引失败: {str(e)}")
            
            try:
                create_scheme_index_sql = """
                CREATE INDEX idx_selfcheck_tasks_scheme 
                ON selfcheck_tasks(scheme_id)
                """
                db_manager.execute_update(create_scheme_index_sql)
                print("  创建scheme_id索引")
            except Exception as e:
                print(f"  创建scheme_id索引失败: {str(e)}")
            
            # 10. 验证迁移结果
            print("\n9. 验证迁移结果...")
            
            final_structure_query = """
            SELECT column_name, data_type, nullable
            FROM user_tab_columns 
            WHERE table_name = 'SELFCHECK_TASKS'
            ORDER BY column_id
            """
            final_columns = db_manager.execute_query(final_structure_query)
            
            print("迁移后表结构:")
            for col in final_columns:
                nullable = "NULL" if col['nullable'] == 'Y' else "NOT NULL"
                print(f"  {col['column_name']}: {col['data_type']} {nullable}")
            
            # 检查数据完整性
            count_query = "SELECT COUNT(*) as total FROM selfcheck_tasks"
            count_result = db_manager.execute_query(count_query)
            total_count = count_result[0]['total'] if count_result else 0
            
            print(f"\n迁移后数据总数: {total_count}")
            
            print("\n✅ 表结构迁移完成！")
            
        except Exception as e:
            print(f"❌ 迁移失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    migrate_tasks_table()
