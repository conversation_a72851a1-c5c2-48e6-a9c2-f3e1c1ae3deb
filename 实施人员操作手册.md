# MICRA自查自纠模块实施人员操作手册

**文档版本：** V1.0
**编写日期：** 2025年1月7日
**适用对象：** 项目实施工程师、技术支持人员
**文档类型：** 实施人员操作手册

## 1. 实施准备

### 1.1 实施前检查清单

#### 1.1.1 硬件环境检查
```
服务器配置检查：
□ CPU: 4核心以上 (推荐8核心)
□ 内存: 8GB以上 (推荐16GB)
□ 存储: 100GB以上可用空间
□ 网络: 千兆网卡，稳定的网络连接
□ 操作系统: CentOS 7.6+ / Ubuntu 18.04+ / RHEL 7.6+

数据库环境检查：
□ Oracle 11g+ 或 PostgreSQL 12+
□ 数据库服务正常运行
□ 足够的表空间和用户权限
□ 网络连接正常
```

#### 1.1.2 软件环境检查
```
基础软件检查：
□ Python 3.9.0+ 已安装
□ pip 包管理器可用
□ Git 版本控制工具
□ 文本编辑器 (vim/nano)
□ 网络工具 (curl/wget)

Web服务器检查：
□ Nginx 1.20+ 已安装
□ SSL证书准备就绪
□ 防火墙配置正确
□ 域名解析配置
```

#### 1.1.3 网络和安全检查
```
网络配置检查：
□ 服务器间网络连通性
□ 防火墙端口开放 (80, 443, 5000)
□ DNS解析配置正确
□ 负载均衡配置 (如需要)

安全配置检查：
□ SSL证书有效性
□ 安全组规则配置
□ 访问控制策略
□ 数据加密要求
```

### 1.2 客户环境调研

#### 1.2.1 业务需求调研表
```
基本信息：
- 医疗机构名称: ________________
- 联系人: ____________________
- 联系电话: __________________
- 邮箱地址: __________________

业务需求：
- 主要数据类型: □门诊 □住院 □其他
- 数据量级: □<1万 □1-10万 □10-100万 □>100万
- 检查频率: □每日 □每周 □每月 □按需
- 用户数量: □<10 □10-50 □50-100 □>100
- 特殊需求: ___________________

技术环境：
- 现有数据库: □Oracle □MySQL □PostgreSQL □其他
- 操作系统: □Windows □Linux □其他
- 网络环境: □内网 □外网 □混合
- 安全要求: □一般 □较高 □很高
```

#### 1.2.2 现状分析
```
数据质量现状：
- 当前数据质量检查方式: ___________
- 主要数据质量问题: ______________
- 检查规则数量: _________________
- 人工投入时间: _________________

技术现状：
- IT团队规模: __________________
- 技术水平: ___________________
- 运维能力: ___________________
- 培训需求: ___________________
```

### 1.3 实施计划制定

#### 1.3.1 项目时间表模板
```
阶段一: 环境准备 (第1-2周)
- 第1天: 硬件环境检查和准备
- 第2-3天: 软件环境安装和配置
- 第4-5天: 网络和安全配置
- 第6-7天: 基础功能测试
- 第8-10天: 环境优化和调整

阶段二: 系统部署 (第3-4周)
- 第11-12天: 应用系统部署
- 第13-14天: 数据库初始化
- 第15-16天: 系统配置和调优
- 第17-18天: 集成测试
- 第19-20天: 性能测试和优化

阶段三: 业务配置 (第5-6周)
- 第21-22天: 用户和权限配置
- 第23-24天: 规则导入和配置
- 第25-26天: 方案设计和测试
- 第27-28天: 数据迁移
- 第29-30天: 业务流程测试

阶段四: 试运行 (第7-8周)
- 第31-32天: 小范围试运行
- 第33-34天: 问题修复和优化
- 第35-36天: 用户培训
- 第37-38天: 全面测试
- 第39-40天: 上线准备

阶段五: 正式上线 (第9周)
- 第41-42天: 正式上线
- 第43-44天: 监控和支持
- 第45天: 项目验收
```

## 2. 系统安装配置

### 2.1 环境准备操作

#### 2.1.1 创建系统用户
```bash
# 创建应用用户
sudo useradd -m -s /bin/bash micra
sudo usermod -aG wheel micra  # CentOS
sudo usermod -aG sudo micra   # Ubuntu

# 设置密码
sudo passwd micra

# 创建必要目录
sudo mkdir -p /data/micra/{uploads,logs,backups,temp}
sudo chown -R micra:micra /data/micra
sudo chmod 755 /data/micra/uploads
sudo chmod 755 /data/micra/logs
sudo chmod 700 /data/micra/backups
sudo chmod 755 /data/micra/temp
```

#### 2.1.2 安装Python环境
```bash
# CentOS/RHEL 安装Python 3.9
sudo yum install -y python39 python39-pip python39-devel

# Ubuntu 安装Python 3.9
sudo apt update
sudo apt install -y python3.9 python3.9-pip python3.9-dev python3.9-venv

# 验证安装
python3.9 --version
pip3.9 --version

# 创建符号链接 (可选)
sudo ln -sf /usr/bin/python3.9 /usr/bin/python3
sudo ln -sf /usr/bin/pip3.9 /usr/bin/pip3
```

#### 2.1.3 安装Oracle客户端
```bash
# 下载Oracle Instant Client
cd /tmp
wget https://download.oracle.com/otn_software/linux/instantclient/1921000/instantclient-basic-linux.x64-*********.0dbru.zip
wget https://download.oracle.com/otn_software/linux/instantclient/1921000/instantclient-devel-linux.x64-*********.0dbru.zip

# 安装
sudo mkdir -p /opt/oracle
sudo unzip instantclient-basic-linux.x64-*********.0dbru.zip -d /opt/oracle/
sudo unzip instantclient-devel-linux.x64-*********.0dbru.zip -d /opt/oracle/

# 配置环境变量
echo 'export ORACLE_HOME=/opt/oracle/instantclient_19_21' | sudo tee -a /etc/environment
echo 'export LD_LIBRARY_PATH=$ORACLE_HOME:$LD_LIBRARY_PATH' | sudo tee -a /etc/environment
echo 'export PATH=$ORACLE_HOME:$PATH' | sudo tee -a /etc/environment

# 重新加载环境变量
source /etc/environment
```

### 2.2 应用部署操作

#### 2.2.1 获取应用代码
```bash
# 切换到应用用户
sudo su - micra

# 创建应用目录
mkdir -p /home/<USER>/micra-app
cd /home/<USER>/micra-app

# 从Git仓库获取代码
git clone https://github.com/your-org/micra-selfcheck.git .

# 或从压缩包部署
# tar -xzf micra-selfcheck-v1.0.tar.gz
# mv micra-selfcheck-v1.0/* .
```

#### 2.2.2 创建虚拟环境
```bash
# 创建虚拟环境
python3.9 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt

# 验证安装
pip list | grep -E "(Flask|oracledb|SQLAlchemy)"
```

#### 2.2.3 配置应用
```bash
# 复制配置文件模板
cp config/config.example.py config/config.py

# 编辑配置文件
vim config/config.py
```

配置文件示例：
```python
# config/config.py
import os

class ProductionConfig:
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-production-secret-key'
    DEBUG = False

    # 数据库配置
    ORACLE_HOST = '*************'
    ORACLE_PORT = '1521'
    ORACLE_SERVICE = 'MICRA'
    ORACLE_USER = 'micra_prod'
    ORACLE_PASSWORD = os.environ.get('ORACLE_PASSWORD')

    # 文件配置
    UPLOAD_FOLDER = '/data/micra/uploads'
    LOG_FOLDER = '/data/micra/logs'
    BACKUP_FOLDER = '/data/micra/backups'

    # 性能配置
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
```

### 2.3 数据库配置操作

#### 2.3.1 创建数据库用户和表空间
```sql
-- 连接到Oracle数据库
sqlplus sys/password@localhost:1521/xe as sysdba

-- 创建表空间
CREATE TABLESPACE MICRA_PROD_DATA
DATAFILE '/oracle/data/micra_prod_data01.dbf' SIZE 5G
AUTOEXTEND ON NEXT 500M MAXSIZE 50G;

CREATE TABLESPACE MICRA_PROD_INDEX
DATAFILE '/oracle/data/micra_prod_index01.dbf' SIZE 2G
AUTOEXTEND ON NEXT 200M MAXSIZE 20G;

-- 创建用户
CREATE USER micra_prod IDENTIFIED BY "ProdPass2025!"
DEFAULT TABLESPACE MICRA_PROD_DATA
TEMPORARY TABLESPACE TEMP;

-- 授权
GRANT CONNECT, RESOURCE TO micra_prod;
GRANT CREATE VIEW, CREATE SEQUENCE, CREATE TRIGGER TO micra_prod;
GRANT UNLIMITED TABLESPACE TO micra_prod;

-- 退出
EXIT;
```

#### 2.3.2 初始化数据库表
```bash
# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export ORACLE_PASSWORD="ProdPass2025!"
export FLASK_ENV="production"

# 执行数据库初始化
python scripts/init_database.py

# 创建管理员用户
python scripts/create_admin_user.py
```

### 2.4 Web服务器配置

#### 2.4.1 安装和配置Nginx
```bash
# 安装Nginx
sudo yum install -y nginx  # CentOS
# sudo apt install -y nginx  # Ubuntu

# 创建配置文件
sudo vim /etc/nginx/conf.d/micra.conf
```

Nginx配置示例：
```nginx
upstream micra_app {
    server 127.0.0.1:5000;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;

    location /static {
        alias /home/<USER>/micra-app/app/static;
        expires 30d;
    }

    location / {
        proxy_pass http://micra_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        client_max_body_size 50M;
    }
}
```

#### 2.4.2 配置SSL证书
```bash
# 创建SSL目录
sudo mkdir -p /etc/ssl/certs /etc/ssl/private

# 复制证书文件
sudo cp your-domain.crt /etc/ssl/certs/
sudo cp your-domain.key /etc/ssl/private/
sudo chmod 600 /etc/ssl/private/your-domain.key

# 测试Nginx配置
sudo nginx -t

# 启动Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

## 3. 系统配置操作

### 3.1 用户和权限配置

#### 3.1.1 创建系统角色
```bash
# 激活虚拟环境
source venv/bin/activate

# 执行角色初始化脚本
python scripts/init_roles.py
```

角色配置脚本示例：
```python
# scripts/init_roles.py
from app.models.user import Role, Permission

def create_roles():
    """创建系统角色"""

    roles_config = [
        {
            'code': 'admin',
            'name': '系统管理员',
            'permissions': ['system.admin', 'selfcheck.*']
        },
        {
            'code': 'data_manager',
            'name': '数据管理员',
            'permissions': [
                'selfcheck.uploads.*',
                'selfcheck.schemes.*',
                'selfcheck.tasks.*'
            ]
        },
        {
            'code': 'quality_controller',
            'name': '质控人员',
            'permissions': [
                'selfcheck.uploads.view',
                'selfcheck.schemes.view',
                'selfcheck.tasks.view',
                'selfcheck.tasks.create'
            ]
        },
        {
            'code': 'viewer',
            'name': '查看者',
            'permissions': [
                'selfcheck.*.view'
            ]
        }
    ]

    for role_config in roles_config:
        role = Role(
            code=role_config['code'],
            name=role_config['name']
        )
        role.save()
        print(f"创建角色: {role.name}")
```

#### 3.1.2 创建初始用户
```bash
# 创建管理员用户
python scripts/create_users.py --role admin --username admin --password "Admin123!" --name "系统管理员"

# 创建测试用户
python scripts/create_users.py --role data_manager --username test_user --password "Test123!" --name "测试用户"
```

### 3.2 业务数据配置

#### 3.2.1 导入检查规则
```bash
# 从Excel文件导入规则
python scripts/import_rules.py --file rules_template.xlsx

# 从CSV文件导入规则
python scripts/import_rules.py --file rules_data.csv

# 验证导入结果
python scripts/verify_rules.py
```

规则导入脚本示例：
```python
# scripts/import_rules.py
import pandas as pd
from app.models.selfcheck import SelfCheckRule

def import_rules_from_excel(file_path):
    """从Excel导入规则"""

    df = pd.read_excel(file_path)
    success_count = 0
    error_count = 0

    for index, row in df.iterrows():
        try:
            rule = SelfCheckRule(
                rule_name=row['规则名称'],
                rule_type=row['规则类型'],
                sql_content=row['SQL内容'],
                description=row.get('描述', ''),
                city=row.get('城市', ''),
                visit_type=row.get('就诊类型', 'both'),
                data_type=row.get('数据类型', 'quantitative'),
                created_by=1  # 系统用户
            )
            rule.save()
            success_count += 1
            print(f"导入规则: {rule.rule_name}")

        except Exception as e:
            error_count += 1
            print(f"导入失败: {row['规则名称']}, 错误: {e}")

    print(f"导入完成: 成功 {success_count}, 失败 {error_count}")
```

#### 3.2.2 创建默认方案
```bash
# 创建默认检查方案
python scripts/create_default_schemes.py
```

方案创建脚本示例：
```python
# scripts/create_default_schemes.py
from app.models.selfcheck import SelfCheckScheme, SelfCheckSchemeRule, SelfCheckRule

def create_default_schemes():
    """创建默认检查方案"""

    schemes_config = [
        {
            'name': '门诊数据检查方案',
            'description': '适用于门诊数据的标准检查方案',
            'rule_types': ['duplicate_check', 'format_validation', 'business_rule']
        },
        {
            'name': '住院数据检查方案',
            'description': '适用于住院数据的标准检查方案',
            'rule_types': ['duplicate_check', 'format_validation', 'business_rule', 'cost_check']
        }
    ]

    for scheme_config in schemes_config:
        # 创建方案
        scheme = SelfCheckScheme(
            scheme_name=scheme_config['name'],
            description=scheme_config['description'],
            created_by=1
        )
        scheme.save()

        # 添加规则到方案
        rules = SelfCheckRule.query.filter(
            SelfCheckRule.rule_type.in_(scheme_config['rule_types'])
        ).all()

        for i, rule in enumerate(rules):
            scheme_rule = SelfCheckSchemeRule(
                scheme_id=scheme.id,
                rule_id=rule.id,
                sort_order=i + 1
            )
            scheme_rule.save()

        print(f"创建方案: {scheme.scheme_name}, 包含 {len(rules)} 个规则")
```

## 4. 系统测试验证

### 4.1 功能测试

#### 4.1.1 基础功能测试脚本
```bash
# 创建测试脚本
vim scripts/test_basic_functions.py
```

测试脚本示例：
```python
# scripts/test_basic_functions.py
import requests
import json

def test_system_health():
    """测试系统健康状态"""

    tests = [
        ('数据库连接', 'http://localhost:5000/api/health/db'),
        ('应用服务', 'http://localhost:5000/api/health/app'),
        ('文件系统', 'http://localhost:5000/api/health/fs'),
    ]

    results = []
    for name, url in tests:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                results.append((name, 'PASS', response.json()))
                print(f"✓ {name}: 通过")
            else:
                results.append((name, 'FAIL', f"HTTP {response.status_code}"))
                print(f"✗ {name}: 失败 - HTTP {response.status_code}")
        except Exception as e:
            results.append((name, 'FAIL', str(e)))
            print(f"✗ {name}: 失败 - {e}")

    return results

def test_user_login():
    """测试用户登录"""

    session = requests.Session()

    # 获取登录页面
    login_page = session.get('http://localhost:5000/auth/login')
    if login_page.status_code != 200:
        return False, "无法访问登录页面"

    # 执行登录
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }

    login_response = session.post(
        'http://localhost:5000/auth/login',
        data=login_data,
        allow_redirects=False
    )

    if login_response.status_code == 302:
        return True, "登录成功"
    else:
        return False, "登录失败"

if __name__ == '__main__':
    print("=== 系统功能测试 ===")

    # 健康检查
    health_results = test_system_health()

    # 登录测试
    login_success, login_message = test_user_login()
    print(f"登录测试: {login_message}")

    # 生成测试报告
    total_tests = len(health_results) + 1
    passed_tests = len([r for r in health_results if r[1] == 'PASS'])
    if login_success:
        passed_tests += 1

    print(f"\n测试结果: {passed_tests}/{total_tests} 通过")
```

#### 4.1.2 执行功能测试
```bash
# 启动应用服务
sudo systemctl start micra

# 等待服务启动
sleep 10

# 执行测试
python scripts/test_basic_functions.py

# 查看测试结果
echo "基础功能测试完成"
```

### 4.2 性能测试

#### 4.2.1 并发测试
```bash
# 安装压测工具
sudo yum install -y httpd-tools

# 执行并发测试
ab -n 1000 -c 10 http://localhost:5000/

# 查看结果
echo "并发测试完成，查看上述输出结果"
```

#### 4.2.2 文件上传测试
```bash
# 创建测试文件
echo "测试数据" > /tmp/test_upload.csv

# 测试文件上传
curl -X POST \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/tmp/test_upload.csv" \
  http://localhost:5000/selfcheck/api/uploads

# 清理测试文件
rm /tmp/test_upload.csv
```

### 4.3 安全测试

#### 4.3.1 端口扫描测试
```bash
# 检查开放端口
nmap -sT localhost

# 检查SSL配置
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

#### 4.3.2 权限测试
```bash
# 检查文件权限
ls -la /home/<USER>/micra-app/
ls -la /data/micra/

# 检查进程用户
ps aux | grep gunicorn
ps aux | grep nginx
```

## 5. 故障排除

### 5.1 常见问题诊断

#### 5.1.1 应用无法启动
```bash
# 检查应用日志
tail -f /data/micra/logs/micra.log

# 检查系统服务状态
sudo systemctl status micra

# 检查端口占用
netstat -tlnp | grep 5000

# 检查Python环境
source /home/<USER>/micra-app/venv/bin/activate
python -c "import flask; print(flask.__version__)"
```

#### 5.1.2 数据库连接问题
```bash
# 测试数据库连接
python -c "
import oracledb
try:
    conn = oracledb.connect(user='micra_prod', password='ProdPass2025!', dsn='localhost:1521/xe')
    print('数据库连接成功')
    conn.close()
except Exception as e:
    print(f'数据库连接失败: {e}')
"

# 检查Oracle客户端
echo $ORACLE_HOME
echo $LD_LIBRARY_PATH
```

#### 5.1.3 文件上传问题
```bash
# 检查上传目录权限
ls -la /data/micra/uploads/

# 检查磁盘空间
df -h /data/micra/

# 检查Nginx配置
sudo nginx -t
```

### 5.2 日志分析

#### 5.2.1 应用日志分析
```bash
# 查看错误日志
grep -i error /data/micra/logs/micra.log

# 查看最近的日志
tail -100 /data/micra/logs/micra.log

# 实时监控日志
tail -f /data/micra/logs/micra.log
```

#### 5.2.2 系统日志分析
```bash
# 查看系统日志
sudo journalctl -u micra -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 5.3 性能优化

#### 5.3.1 数据库优化
```sql
-- 检查表统计信息
SELECT table_name, num_rows, last_analyzed
FROM user_tables
WHERE table_name LIKE 'SELFCHECK%';

-- 收集统计信息
BEGIN
    DBMS_STATS.GATHER_TABLE_STATS('MICRA_PROD', 'SELFCHECK_UPLOADS');
    DBMS_STATS.GATHER_TABLE_STATS('MICRA_PROD', 'SELFCHECK_SCHEMES');
END;
/

-- 检查索引使用情况
SELECT index_name, table_name, uniqueness, status
FROM user_indexes
WHERE table_name LIKE 'SELFCHECK%';
```

#### 5.3.2 应用优化
```bash
# 调整Gunicorn配置
vim /home/<USER>/micra-app/gunicorn.conf.py

# 重启应用服务
sudo systemctl restart micra

# 监控资源使用
top -p $(pgrep -f gunicorn)
```

---

**文档状态：** 已审核
**版本历史：**
- V1.0 (2025-01-07): 初始版本，包含完整实施操作指南