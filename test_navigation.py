#!/usr/bin/env python3
"""测试导航功能"""

import requests
import json

def test_navigation():
    """测试导航功能"""
    base_url = "http://127.0.0.1:5002"
    
    print("=== 导航功能测试 ===")
    
    # 创建会话
    session = requests.Session()
    
    # 1. 测试主页
    print("\n1. 测试主页...")
    main_response = session.get(f"{base_url}/")
    
    if main_response.status_code == 200:
        print("✅ 主页访问成功")
        
        # 检查是否包含方案配置按钮
        if '方案配置' in main_response.text:
            print("✅ 主页包含方案配置按钮")
        else:
            print("❌ 主页缺少方案配置按钮")
        
        # 检查是否包含左边栏导航
        if '方案管理' in main_response.text:
            print("✅ 左边栏包含方案管理")
        else:
            print("❌ 左边栏缺少方案管理")
            
        if '规则SQL生成器' in main_response.text:
            print("✅ 左边栏包含规则SQL生成器")
        else:
            print("❌ 左边栏缺少规则SQL生成器")
    else:
        print(f"❌ 主页访问失败: {main_response.status_code}")
    
    # 2. 测试方案管理页面
    print("\n2. 测试方案管理页面...")
    schemes_response = session.get(f"{base_url}/selfcheck/schemes")
    
    if schemes_response.status_code == 200:
        print("✅ 方案管理页面访问成功")
    elif schemes_response.status_code == 302:
        print("⚠️ 方案管理页面需要登录")
    else:
        print(f"❌ 方案管理页面访问失败: {schemes_response.status_code}")
    
    # 3. 测试规则SQL生成器页面
    print("\n3. 测试规则SQL生成器页面...")
    sql_gen_response = session.get(f"{base_url}/rules/sql_generator")
    
    if sql_gen_response.status_code == 200:
        print("✅ 规则SQL生成器页面访问成功")
    elif sql_gen_response.status_code == 302:
        print("⚠️ 规则SQL生成器页面需要登录")
    else:
        print(f"❌ 规则SQL生成器页面访问失败: {sql_gen_response.status_code}")
    
    # 4. 测试自查自纠主页
    print("\n4. 测试自查自纠主页...")
    selfcheck_response = session.get(f"{base_url}/selfcheck/")
    
    if selfcheck_response.status_code == 200:
        print("✅ 自查自纠主页访问成功")
        
        # 检查是否包含方案配置按钮
        if '方案配置' in selfcheck_response.text or '方案管理' in selfcheck_response.text:
            print("✅ 自查自纠主页包含方案相关功能")
        else:
            print("❌ 自查自纠主页缺少方案相关功能")
    elif selfcheck_response.status_code == 302:
        print("⚠️ 自查自纠主页需要登录")
    else:
        print(f"❌ 自查自纠主页访问失败: {selfcheck_response.status_code}")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_navigation()
