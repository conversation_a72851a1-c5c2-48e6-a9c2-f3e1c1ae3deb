<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.selfcheck.mapper.SelfCheckTaskMapper">
    
    <resultMap type="SelfCheckTask" id="SelfCheckTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="uploadId"    column="upload_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="userId"    column="user_id"    />
        <result property="status"    column="status"    />
        <result property="progress"    column="progress"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="resultSummary"    column="result_summary"    />
        <result property="resultDetail"    column="result_detail"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSelfCheckTaskVo">
        select t.task_id, t.task_name, t.upload_id, t.rule_id, t.user_id, t.status, t.progress, t.start_time, t.end_time, t.result_summary, t.result_detail, t.error_message, t.create_by, t.create_time, t.update_by, t.update_time, t.remark,
               u.file_name, r.rule_name, su.user_name
        from biz_selfcheck_task t
        left join biz_upload_record u on t.upload_id = u.upload_id
        left join biz_selfcheck_rule r on t.rule_id = r.rule_id
        left join sys_user su on t.user_id = su.user_id
    </sql>

    <select id="selectSelfCheckTaskList" parameterType="SelfCheckTask" resultMap="SelfCheckTaskResult">
        <include refid="selectSelfCheckTaskVo"/>
        <where>  
            <if test="taskName != null  and taskName != ''"> and t.task_name like concat('%', #{taskName}, '%')</if>
            <if test="uploadId != null "> and t.upload_id = #{uploadId}</if>
            <if test="ruleId != null "> and t.rule_id = #{ruleId}</if>
            <if test="userId != null "> and t.user_id = #{userId}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
        </where>
        order by t.create_time desc
    </select>
    
    <select id="selectSelfCheckTaskByTaskId" parameterType="Long" resultMap="SelfCheckTaskResult">
        <include refid="selectSelfCheckTaskVo"/>
        where t.task_id = #{taskId}
    </select>
        
    <insert id="insertSelfCheckTask" parameterType="SelfCheckTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into biz_selfcheck_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="uploadId != null">upload_id,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="progress != null">progress,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="resultSummary != null">result_summary,</if>
            <if test="resultDetail != null">result_detail,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="uploadId != null">#{uploadId},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status != null">#{status},</if>
            <if test="progress != null">#{progress},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="resultSummary != null">#{resultSummary},</if>
            <if test="resultDetail != null">#{resultDetail},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSelfCheckTask" parameterType="SelfCheckTask">
        update biz_selfcheck_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="uploadId != null">upload_id = #{uploadId},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="resultSummary != null">result_summary = #{resultSummary},</if>
            <if test="resultDetail != null">result_detail = #{resultDetail},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteSelfCheckTaskByTaskId" parameterType="Long">
        delete from biz_selfcheck_task where task_id = #{taskId}
    </delete>

    <delete id="deleteSelfCheckTaskByTaskIds" parameterType="String">
        delete from biz_selfcheck_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

</mapper>
