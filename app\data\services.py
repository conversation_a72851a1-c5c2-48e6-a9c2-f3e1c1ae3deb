"""数据处理服务类"""

import os
import hashlib
import pandas as pd
import numpy as np
import tempfile
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict
from datetime import datetime
from app.utils.file_manager import create_temp_file, create_temp_dir, sanitize_filename

class DuplicateFileService:
    """重复文件查找服务类"""
    
    @staticmethod
    def find_duplicate_files(directory_paths: List[str], 
                           check_content: bool = True,
                           file_extensions: List[str] = None) -> Dict[str, Any]:
        """查找重复文件"""
        try:
            file_info = {}
            duplicates = defaultdict(list)
            
            # 遍历所有目录
            for directory in directory_paths:
                if not os.path.exists(directory):
                    continue
                
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        
                        # 检查文件扩展名
                        if file_extensions:
                            file_ext = os.path.splitext(file)[1].lower()
                            if file_ext not in file_extensions:
                                continue
                        
                        try:
                            # 获取文件基本信息
                            stat = os.stat(file_path)
                            file_size = stat.st_size
                            file_mtime = stat.st_mtime
                            
                            # 生成文件标识
                            if check_content:
                                # 基于内容的哈希
                                file_hash = DuplicateFileService._calculate_file_hash(file_path)
                                file_key = f"{file_size}_{file_hash}"
                            else:
                                # 基于文件名和大小
                                file_key = f"{file}_{file_size}"
                            
                            file_info[file_path] = {
                                'name': file,
                                'path': file_path,
                                'size': file_size,
                                'modified_time': datetime.fromtimestamp(file_mtime),
                                'hash': file_hash if check_content else None,
                                'key': file_key
                            }
                            
                            duplicates[file_key].append(file_path)
                            
                        except (OSError, IOError) as e:
                            logging.warning(f"无法访问文件 {file_path}: {str(e)}")
                            continue
            
            # 筛选出真正的重复文件
            duplicate_groups = {}
            for key, paths in duplicates.items():
                if len(paths) > 1:
                    duplicate_groups[key] = [file_info[path] for path in paths]
            
            # 统计信息
            total_files = len(file_info)
            duplicate_files = sum(len(group) for group in duplicate_groups.values())
            duplicate_groups_count = len(duplicate_groups)
            
            # 计算可节省的空间
            space_savings = 0
            for group in duplicate_groups.values():
                if group:
                    file_size = group[0]['size']
                    space_savings += file_size * (len(group) - 1)
            
            return {
                'success': True,
                'total_files': total_files,
                'duplicate_files': duplicate_files,
                'duplicate_groups': duplicate_groups_count,
                'space_savings': space_savings,
                'duplicates': duplicate_groups,
                'scan_directories': directory_paths,
                'check_content': check_content
            }
            
        except Exception as e:
            logging.error(f"查找重复文件失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def _calculate_file_hash(file_path: str, chunk_size: int = 8192) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(chunk_size), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    @staticmethod
    def delete_duplicate_files(file_paths: List[str], keep_newest: bool = True) -> Dict[str, Any]:
        """删除重复文件"""
        try:
            deleted_files = []
            failed_deletions = []
            
            for file_path in file_paths:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        deleted_files.append(file_path)
                    else:
                        failed_deletions.append(f"{file_path}: 文件不存在")
                except Exception as e:
                    failed_deletions.append(f"{file_path}: {str(e)}")
            
            return {
                'success': True,
                'deleted_count': len(deleted_files),
                'failed_count': len(failed_deletions),
                'deleted_files': deleted_files,
                'failed_deletions': failed_deletions
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

class DataValidationService:
    """数据校验服务类"""
    
    @staticmethod
    def validate_excel_data(file_path: str, validation_rules: Dict[str, Any]) -> Dict[str, Any]:
        """验证Excel数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            validation_results = {
                'file_info': {
                    'rows': len(df),
                    'columns': len(df.columns),
                    'column_names': df.columns.tolist()
                },
                'validation_errors': [],
                'validation_warnings': [],
                'summary': {
                    'total_errors': 0,
                    'total_warnings': 0,
                    'validation_passed': True
                }
            }
            
            # 执行各种验证规则
            for rule_name, rule_config in validation_rules.items():
                if rule_name == 'required_columns':
                    DataValidationService._validate_required_columns(df, rule_config, validation_results)
                elif rule_name == 'data_types':
                    DataValidationService._validate_data_types(df, rule_config, validation_results)
                elif rule_name == 'value_ranges':
                    DataValidationService._validate_value_ranges(df, rule_config, validation_results)
                elif rule_name == 'unique_constraints':
                    DataValidationService._validate_unique_constraints(df, rule_config, validation_results)
                elif rule_name == 'null_constraints':
                    DataValidationService._validate_null_constraints(df, rule_config, validation_results)
                elif rule_name == 'custom_rules':
                    DataValidationService._validate_custom_rules(df, rule_config, validation_results)
            
            # 更新汇总信息
            validation_results['summary']['total_errors'] = len(validation_results['validation_errors'])
            validation_results['summary']['total_warnings'] = len(validation_results['validation_warnings'])
            validation_results['summary']['validation_passed'] = validation_results['summary']['total_errors'] == 0
            
            return validation_results
            
        except Exception as e:
            logging.error(f"数据验证失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def _validate_required_columns(df: pd.DataFrame, required_columns: List[str], results: Dict):
        """验证必需列"""
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            results['validation_errors'].append({
                'type': 'missing_columns',
                'message': f"缺少必需列: {', '.join(missing_columns)}",
                'columns': list(missing_columns)
            })
    
    @staticmethod
    def _validate_data_types(df: pd.DataFrame, type_rules: Dict[str, str], results: Dict):
        """验证数据类型"""
        for column, expected_type in type_rules.items():
            if column not in df.columns:
                continue
            
            if expected_type == 'numeric':
                non_numeric = df[~pd.to_numeric(df[column], errors='coerce').notna()]
                if not non_numeric.empty:
                    results['validation_errors'].append({
                        'type': 'invalid_data_type',
                        'message': f"列 '{column}' 包含非数值数据",
                        'column': column,
                        'invalid_rows': non_numeric.index.tolist()
                    })
            elif expected_type == 'date':
                try:
                    pd.to_datetime(df[column], errors='raise')
                except:
                    results['validation_errors'].append({
                        'type': 'invalid_data_type',
                        'message': f"列 '{column}' 包含无效日期格式",
                        'column': column
                    })
    
    @staticmethod
    def _validate_value_ranges(df: pd.DataFrame, range_rules: Dict[str, Dict], results: Dict):
        """验证数值范围"""
        for column, range_config in range_rules.items():
            if column not in df.columns:
                continue
            
            min_val = range_config.get('min')
            max_val = range_config.get('max')
            
            if min_val is not None:
                invalid_rows = df[df[column] < min_val].index.tolist()
                if invalid_rows:
                    results['validation_errors'].append({
                        'type': 'value_out_of_range',
                        'message': f"列 '{column}' 存在小于最小值 {min_val} 的数据",
                        'column': column,
                        'invalid_rows': invalid_rows
                    })
            
            if max_val is not None:
                invalid_rows = df[df[column] > max_val].index.tolist()
                if invalid_rows:
                    results['validation_errors'].append({
                        'type': 'value_out_of_range',
                        'message': f"列 '{column}' 存在大于最大值 {max_val} 的数据",
                        'column': column,
                        'invalid_rows': invalid_rows
                    })
    
    @staticmethod
    def _validate_unique_constraints(df: pd.DataFrame, unique_columns: List[str], results: Dict):
        """验证唯一性约束"""
        for column in unique_columns:
            if column not in df.columns:
                continue
            
            duplicates = df[df.duplicated(subset=[column], keep=False)]
            if not duplicates.empty:
                results['validation_errors'].append({
                    'type': 'duplicate_values',
                    'message': f"列 '{column}' 存在重复值",
                    'column': column,
                    'duplicate_rows': duplicates.index.tolist(),
                    'duplicate_values': duplicates[column].unique().tolist()
                })
    
    @staticmethod
    def _validate_null_constraints(df: pd.DataFrame, not_null_columns: List[str], results: Dict):
        """验证非空约束"""
        for column in not_null_columns:
            if column not in df.columns:
                continue
            
            null_rows = df[df[column].isnull()].index.tolist()
            if null_rows:
                results['validation_errors'].append({
                    'type': 'null_values',
                    'message': f"列 '{column}' 存在空值",
                    'column': column,
                    'null_rows': null_rows
                })
    
    @staticmethod
    def _validate_custom_rules(df: pd.DataFrame, custom_rules: List[Dict], results: Dict):
        """验证自定义规则"""
        for rule in custom_rules:
            rule_name = rule.get('name', '自定义规则')
            rule_expression = rule.get('expression', '')
            
            try:
                # 这里可以实现自定义规则的验证逻辑
                # 例如：eval(rule_expression) 但需要注意安全性
                pass
            except Exception as e:
                results['validation_warnings'].append({
                    'type': 'custom_rule_error',
                    'message': f"自定义规则 '{rule_name}' 执行失败: {str(e)}",
                    'rule': rule_name
                })

class DataStandardizationService:
    """数据标准化服务类"""
    
    @staticmethod
    def standardize_excel_data(file_path: str, 
                             standardization_rules: Dict[str, Any],
                             output_path: str = None) -> Dict[str, Any]:
        """标准化Excel数据"""
        try:
            if not output_path:
                output_path = create_temp_file(suffix='.xlsx')
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            original_rows = len(df)
            
            standardization_log = []
            
            # 执行各种标准化规则
            for rule_name, rule_config in standardization_rules.items():
                if rule_name == 'remove_duplicates':
                    df, log = DataStandardizationService._remove_duplicates(df, rule_config)
                    standardization_log.extend(log)
                elif rule_name == 'fill_missing_values':
                    df, log = DataStandardizationService._fill_missing_values(df, rule_config)
                    standardization_log.extend(log)
                elif rule_name == 'normalize_text':
                    df, log = DataStandardizationService._normalize_text(df, rule_config)
                    standardization_log.extend(log)
                elif rule_name == 'format_dates':
                    df, log = DataStandardizationService._format_dates(df, rule_config)
                    standardization_log.extend(log)
                elif rule_name == 'normalize_numbers':
                    df, log = DataStandardizationService._normalize_numbers(df, rule_config)
                    standardization_log.extend(log)
                elif rule_name == 'remove_outliers':
                    df, log = DataStandardizationService._remove_outliers(df, rule_config)
                    standardization_log.extend(log)
            
            # 保存标准化后的文件
            df.to_excel(output_path, index=False)
            
            return {
                'success': True,
                'output_file': output_path,
                'original_rows': original_rows,
                'final_rows': len(df),
                'rows_changed': original_rows - len(df),
                'standardization_log': standardization_log
            }
            
        except Exception as e:
            logging.error(f"数据标准化失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def _remove_duplicates(df: pd.DataFrame, config: Dict) -> Tuple[pd.DataFrame, List[str]]:
        """移除重复数据"""
        original_count = len(df)
        subset_columns = config.get('subset_columns')
        keep = config.get('keep', 'first')
        
        df_cleaned = df.drop_duplicates(subset=subset_columns, keep=keep)
        removed_count = original_count - len(df_cleaned)
        
        log = [f"移除重复数据: 删除了 {removed_count} 行重复记录"]
        return df_cleaned, log
    
    @staticmethod
    def _fill_missing_values(df: pd.DataFrame, config: Dict) -> Tuple[pd.DataFrame, List[str]]:
        """填充缺失值"""
        log = []
        df_filled = df.copy()
        
        for column, fill_config in config.items():
            if column not in df.columns:
                continue
            
            fill_method = fill_config.get('method', 'constant')
            fill_value = fill_config.get('value', '')
            
            missing_count = df_filled[column].isnull().sum()
            if missing_count > 0:
                if fill_method == 'constant':
                    df_filled[column].fillna(fill_value, inplace=True)
                elif fill_method == 'mean':
                    df_filled[column].fillna(df_filled[column].mean(), inplace=True)
                elif fill_method == 'median':
                    df_filled[column].fillna(df_filled[column].median(), inplace=True)
                elif fill_method == 'mode':
                    df_filled[column].fillna(df_filled[column].mode().iloc[0], inplace=True)
                
                log.append(f"列 '{column}' 填充了 {missing_count} 个缺失值")
        
        return df_filled, log
    
    @staticmethod
    def _normalize_text(df: pd.DataFrame, config: Dict) -> Tuple[pd.DataFrame, List[str]]:
        """标准化文本"""
        log = []
        df_normalized = df.copy()
        
        for column in config.get('columns', []):
            if column not in df.columns:
                continue
            
            operations = config.get('operations', [])
            
            if 'strip' in operations:
                df_normalized[column] = df_normalized[column].astype(str).str.strip()
            if 'lower' in operations:
                df_normalized[column] = df_normalized[column].astype(str).str.lower()
            if 'upper' in operations:
                df_normalized[column] = df_normalized[column].astype(str).str.upper()
            if 'title' in operations:
                df_normalized[column] = df_normalized[column].astype(str).str.title()
            
            log.append(f"列 '{column}' 文本已标准化")
        
        return df_normalized, log
    
    @staticmethod
    def _format_dates(df: pd.DataFrame, config: Dict) -> Tuple[pd.DataFrame, List[str]]:
        """格式化日期"""
        log = []
        df_formatted = df.copy()
        
        for column, date_config in config.items():
            if column not in df.columns:
                continue
            
            input_format = date_config.get('input_format')
            output_format = date_config.get('output_format', '%Y-%m-%d')
            
            try:
                if input_format:
                    df_formatted[column] = pd.to_datetime(df_formatted[column], format=input_format)
                else:
                    df_formatted[column] = pd.to_datetime(df_formatted[column])
                
                df_formatted[column] = df_formatted[column].dt.strftime(output_format)
                log.append(f"列 '{column}' 日期格式已标准化")
            except Exception as e:
                log.append(f"列 '{column}' 日期格式化失败: {str(e)}")
        
        return df_formatted, log
    
    @staticmethod
    def _normalize_numbers(df: pd.DataFrame, config: Dict) -> Tuple[pd.DataFrame, List[str]]:
        """标准化数值"""
        log = []
        df_normalized = df.copy()
        
        for column, number_config in config.items():
            if column not in df.columns:
                continue
            
            decimal_places = number_config.get('decimal_places')
            
            try:
                df_normalized[column] = pd.to_numeric(df_normalized[column], errors='coerce')
                
                if decimal_places is not None:
                    df_normalized[column] = df_normalized[column].round(decimal_places)
                
                log.append(f"列 '{column}' 数值已标准化")
            except Exception as e:
                log.append(f"列 '{column}' 数值标准化失败: {str(e)}")
        
        return df_normalized, log
    
    @staticmethod
    def _remove_outliers(df: pd.DataFrame, config: Dict) -> Tuple[pd.DataFrame, List[str]]:
        """移除异常值"""
        log = []
        df_cleaned = df.copy()
        
        for column, outlier_config in config.items():
            if column not in df.columns:
                continue
            
            method = outlier_config.get('method', 'iqr')
            
            if method == 'iqr':
                Q1 = df_cleaned[column].quantile(0.25)
                Q3 = df_cleaned[column].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                original_count = len(df_cleaned)
                df_cleaned = df_cleaned[(df_cleaned[column] >= lower_bound) & 
                                      (df_cleaned[column] <= upper_bound)]
                removed_count = original_count - len(df_cleaned)
                
                log.append(f"列 '{column}' 移除了 {removed_count} 个异常值")
        
        return df_cleaned, log
