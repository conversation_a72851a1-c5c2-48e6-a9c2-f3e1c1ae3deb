#!/usr/bin/env python3
"""手动添加方案管理权限"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def add_scheme_permissions():
    """手动添加方案管理权限"""
    app = create_app()
    
    with app.app_context():
        try:
            from app.selfcheck.database import get_db_manager
            db_manager = get_db_manager()
            
            print("=== 手动添加方案管理权限 ===")
            
            # 定义方案管理权限
            scheme_permissions = [
                {
                    'name': 'selfcheck.schemes.view',
                    'description': '查看自查方案'
                },
                {
                    'name': 'selfcheck.schemes.create',
                    'description': '创建自查方案'
                },
                {
                    'name': 'selfcheck.schemes.edit',
                    'description': '编辑自查方案'
                },
                {
                    'name': 'selfcheck.schemes.delete',
                    'description': '删除自查方案'
                },
                {
                    'name': 'selfcheck.schemes.manage_rules',
                    'description': '管理方案规则'
                }
            ]
            
            print("\n1. 检查现有权限...")
            for perm in scheme_permissions:
                check_query = "SELECT id, name, description FROM permissions WHERE name = :name"
                result = db_manager.execute_query(check_query, {'name': perm['name']})
                
                if result:
                    print(f"   ✅ {perm['name']}: 已存在 (ID: {result[0]['id']})")
                else:
                    print(f"   ❌ {perm['name']}: 不存在，需要创建")
                    
                    # 获取下一个ID
                    max_id_query = "SELECT MAX(id) as max_id FROM permissions"
                    max_id_result = db_manager.execute_query(max_id_query)
                    next_id = (max_id_result[0]['max_id'] or 0) + 1
                    
                    # 创建权限
                    try:
                        insert_query = """
                        INSERT INTO permissions (id, name, code, description, module, is_active)
                        VALUES (:id, :name, :code, :description, 'selfcheck', 1)
                        """

                        db_manager.execute_update(insert_query, {
                            'id': next_id,
                            'name': perm['name'],
                            'code': perm['name'],  # 使用name作为code
                            'description': perm['description']
                        })
                        print(f"     ✅ 已创建权限: {perm['name']} (ID: {next_id})")

                    except Exception as e:
                        print(f"     ❌ 创建权限失败: {str(e)}")
            
            print("\n2. 验证管理员权限...")
            
            # 检查管理员是否有这些权限（管理员应该自动拥有所有权限）
            admin_query = """
            SELECT u.username, u.is_admin
            FROM users u
            WHERE u.username = 'admin'
            """
            
            admin_result = db_manager.execute_query(admin_query)
            
            if admin_result and admin_result[0]['is_admin'] == 1:
                print("   ✅ 管理员用户存在且为管理员，自动拥有所有权限")
            else:
                print("   ❌ 管理员用户不存在或不是管理员")
            
            print("\n3. 验证权限配置...")
            
            # 再次检查所有方案权限
            all_scheme_perms_query = """
            SELECT id, name, description
            FROM permissions
            WHERE name LIKE 'selfcheck.schemes.%'
            ORDER BY name
            """
            
            all_perms = db_manager.execute_query(all_scheme_perms_query)
            
            if all_perms:
                print("   当前所有方案权限:")
                for perm in all_perms:
                    print(f"     ✅ {perm['name']}: {perm['description']} (ID: {perm['id']})")
            else:
                print("   ❌ 没有找到方案权限")
            
            print("\n🎉 方案管理权限配置完成！")
            
        except Exception as e:
            print(f"❌ 权限配置失败: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    add_scheme_permissions()
