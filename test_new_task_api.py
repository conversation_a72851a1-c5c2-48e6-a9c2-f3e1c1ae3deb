#!/usr/bin/env python3
"""
测试新的任务创建API
"""

import requests
import json
from bs4 import BeautifulSoup

def test_new_task_api():
    """测试新的任务创建API"""
    
    # 创建会话
    session = requests.Session()
    
    print("=== 测试新的任务创建API ===")
    
    # 1. 登录
    print("\n1. 登录...")
    login_url = 'http://127.0.0.1:5002/auth/login'
    login_data = {
        'username': 'admin',
        'password': 'Admin123!'
    }
    
    # 获取登录页面以获取CSRF令牌
    login_page = session.get(login_url)
    soup = BeautifulSoup(login_page.text, 'html.parser')
    csrf_token = soup.find('input', {'name': 'csrf_token'})
    if csrf_token:
        login_data['csrf_token'] = csrf_token['value']
    
    # 执行登录
    login_response = session.post(login_url, data=login_data, allow_redirects=True)
    
    if '/index' not in login_response.url:
        print("❌ 登录失败")
        return
    
    print("✅ 登录成功")
    
    # 2. 测试数据源API
    print("\n2. 测试数据源API...")
    data_sources_url = 'http://127.0.0.1:5002/selfcheck/api/data-sources'
    data_sources_response = session.get(data_sources_url)
    
    print(f"状态码: {data_sources_response.status_code}")
    
    if data_sources_response.status_code == 200:
        try:
            data_sources_data = data_sources_response.json()
            if data_sources_data.get('success'):
                data_sources = data_sources_data['data_sources']
                print(f"✅ 获取到 {len(data_sources)} 个数据源:")
                for ds in data_sources:
                    print(f"  - {ds['name']} ({ds['type']}) - {ds['description']}")
            else:
                print(f"❌ 数据源API返回错误: {data_sources_data.get('message', '未知错误')}")
        except json.JSONDecodeError:
            print(f"❌ 数据源API返回非JSON格式: {data_sources_response.text[:200]}")
    else:
        print(f"❌ 数据源API失败: {data_sources_response.status_code}")
        print(f"响应内容: {data_sources_response.text[:500]}")
    
    # 3. 测试授权方案API
    print("\n3. 测试授权方案API...")
    schemes_url = 'http://127.0.0.1:5002/selfcheck/api/authorized-schemes'
    schemes_response = session.get(schemes_url)
    
    print(f"状态码: {schemes_response.status_code}")
    
    if schemes_response.status_code == 200:
        try:
            schemes_data = schemes_response.json()
            if schemes_data.get('success'):
                schemes = schemes_data['schemes']
                print(f"✅ 获取到 {len(schemes)} 个授权方案:")
                for scheme in schemes:
                    rule_count = scheme.get('rule_count', 0)
                    print(f"  - {scheme['scheme_name']} ({rule_count}个规则) - {scheme.get('description', '无描述')}")
            else:
                print(f"❌ 授权方案API返回错误: {schemes_data.get('message', '未知错误')}")
        except json.JSONDecodeError:
            print(f"❌ 授权方案API返回非JSON格式: {schemes_response.text[:200]}")
    else:
        print(f"❌ 授权方案API失败: {schemes_response.status_code}")
        print(f"响应内容: {schemes_response.text[:500]}")
    
    # 4. 测试任务页面
    print("\n4. 测试任务页面...")
    tasks_page_url = 'http://127.0.0.1:5002/selfcheck/tasks'
    tasks_page_response = session.get(tasks_page_url)
    
    print(f"状态码: {tasks_page_response.status_code}")
    
    if tasks_page_response.status_code == 200:
        print("✅ 任务页面访问成功")
        
        # 检查页面是否包含新的字段
        page_content = tasks_page_response.text
        if 'dataSourceSelect' in page_content:
            print("✅ 页面包含数据源选择器")
        else:
            print("❌ 页面缺少数据源选择器")
            
        if 'schemeSelect' in page_content:
            print("✅ 页面包含方案选择器")
        else:
            print("❌ 页面缺少方案选择器")
            
        if 'loadDataSources' in page_content:
            print("✅ 页面包含加载数据源函数")
        else:
            print("❌ 页面缺少加载数据源函数")
            
        if 'loadAuthorizedSchemes' in page_content:
            print("✅ 页面包含加载授权方案函数")
        else:
            print("❌ 页面缺少加载授权方案函数")
    else:
        print(f"❌ 任务页面访问失败: {tasks_page_response.status_code}")
        print(f"响应内容: {tasks_page_response.text[:500]}")
    
    # 5. 测试任务列表API
    print("\n5. 测试任务列表API...")
    tasks_list_url = 'http://127.0.0.1:5002/selfcheck/api/tasks?page=1&per_page=10'
    tasks_list_response = session.get(tasks_list_url)
    
    print(f"状态码: {tasks_list_response.status_code}")
    
    if tasks_list_response.status_code == 200:
        try:
            tasks_list_data = tasks_list_response.json()
            if tasks_list_data.get('success'):
                tasks = tasks_list_data['tasks']
                print(f"✅ 获取到 {len(tasks)} 个任务:")
                for task in tasks:
                    print(f"  - {task['task_name']}")
                    print(f"    数据源: {task.get('data_source', 'N/A')}")
                    print(f"    方案: {task.get('scheme_name', 'N/A')}")
                    print(f"    状态: {task.get('status', 'N/A')}")
            else:
                print(f"❌ 任务列表API返回错误: {tasks_list_data.get('message', '未知错误')}")
        except json.JSONDecodeError:
            print(f"❌ 任务列表API返回非JSON格式: {tasks_list_response.text[:200]}")
    else:
        print(f"❌ 任务列表API失败: {tasks_list_response.status_code}")
        print(f"响应内容: {tasks_list_response.text[:500]}")
    
    print("\n🎉 API测试完成！")

if __name__ == '__main__':
    try:
        test_new_task_api()
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
