{% extends "base.html" %}

{% block title %}数据处理 - MICRA飞检数据处理工具箱{% endblock %}



{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        数据处理模块
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 查找重复文件 -->
                        {% if current_user.has_permission('data.find_duplicates') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-file-alt fa-3x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">查找重复文件</h5>
                                    <p class="card-text">智能检测和清理重复文件，释放存储空间</p>
                                    <a href="{{ url_for('data.find_duplicates') }}" class="btn btn-primary">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        开始检测
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 数据校验 -->
                        {% if current_user.has_permission('data.validator') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-check-circle fa-3x text-success"></i>
                                    </div>
                                    <h5 class="card-title">数据校验</h5>
                                    <p class="card-text">验证数据完整性和准确性，确保数据质量</p>
                                    <a href="{{ url_for('data.validator') }}" class="btn btn-success">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        开始校验
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 数据标准化 -->
                        {% if current_user.has_permission('data.standardization') %}
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-cogs fa-3x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">数据标准化</h5>
                                    <p class="card-text">统一数据格式和规范，提高数据一致性</p>
                                    <a href="{{ url_for('data.standardization') }}" class="btn btn-warning">
                                        <i class="fas fa-arrow-right me-1"></i>
                                        开始标准化
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- 功能说明 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>
                                        功能说明
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <h6 class="text-primary">查找重复文件</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 基于文件内容的智能比较</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 支持多种文件格式</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 批量删除重复文件</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 安全备份机制</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-success">数据校验</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 数据完整性检查</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 格式规范验证</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 异常数据识别</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 详细校验报告</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-warning">数据标准化</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 统一数据格式</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 字段名称规范化</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 数据类型转换</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 编码格式统一</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
