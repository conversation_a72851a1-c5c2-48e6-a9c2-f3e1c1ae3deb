#!/usr/bin/env python3
"""
分析权限结构，找出重复问题
"""

import requests
import json

def analyze_permissions():
    """分析权限结构"""
    
    # 获取权限数据
    response = requests.get('http://127.0.0.1:5002/admin/api/permissions')
    if response.status_code != 200:
        print(f'获取权限失败: {response.status_code}')
        return
    
    data = response.json()
    permissions = data['permissions']
    
    print('=== 权限结构分析 ===')
    print(f'总权限数: {len(permissions)}')
    
    # 按模块分组
    modules = {}
    for perm in permissions:
        module = perm.get('module', 'other')
        if module not in modules:
            modules[module] = []
        modules[module].append(perm)
    
    print(f'模块数: {len(modules)}')
    
    # 分析每个模块的权限结构
    for module, perms in modules.items():
        print(f'\n=== {module} 模块 ({len(perms)}个权限) ===')
        
        # 分析层级结构
        top_level = [p for p in perms if not p.get('parent_id')]
        has_parent = [p for p in perms if p.get('parent_id')]
        
        print(f'  顶级权限: {len(top_level)}个')
        print(f'  子权限: {len(has_parent)}个')
        
        # 显示所有顶级权限
        for p in top_level:
            name = p.get('name', '')
            pid = p.get('id', '')
            rtype = p.get('resource_type', '')
            code = p.get('code', '')
            print(f'    - {name} (ID: {pid}, 类型: {rtype}, 代码: {code})')
    
    # 检查重复问题
    print('\n=== 检查重复问题 ===')
    
    # 1. 检查是否有相同名称的权限
    name_count = {}
    for perm in permissions:
        name = perm.get('name', '')
        if name not in name_count:
            name_count[name] = []
        name_count[name].append(perm)
    
    duplicates = {name: perms for name, perms in name_count.items() if len(perms) > 1}
    if duplicates:
        print('发现重复名称的权限:')
        for name, perms in duplicates.items():
            print(f'  权限名称: "{name}" ({len(perms)}个)')
            for p in perms:
                print(f'    - ID: {p["id"]}, 模块: {p["module"]}, 类型: {p["resource_type"]}, 代码: {p["code"]}')
    else:
        print('没有发现重复名称的权限')
    
    # 2. 检查是否有模块级权限和页面级权限重复
    print('\n=== 检查模块级权限问题 ===')
    module_permissions = [p for p in permissions if p.get('resource_type') == 'menu' and not p.get('parent_id')]
    print(f'模块级权限: {len(module_permissions)}个')
    
    for p in module_permissions:
        name = p.get('name', '')
        module = p.get('module', '')
        code = p.get('code', '')
        print(f'  - {name} (模块: {module}, 代码: {code})')
        
        # 检查是否有同名的页面权限
        same_name_pages = [pp for pp in permissions if pp.get('name') == name and pp.get('resource_type') == 'page']
        if same_name_pages:
            print(f'    ⚠️  发现同名页面权限: {len(same_name_pages)}个')
            for sp in same_name_pages:
                print(f'      - ID: {sp["id"]}, 代码: {sp["code"]}, 父权限: {sp.get("parent_id")}')

if __name__ == '__main__':
    analyze_permissions()
