-- 更新智能转换工具数据库表结构，支持标准化输出格式
-- 2025-06-16

-- 删除现有表
DROP TABLE converter_rules;
DROP SEQUENCE converter_rules_seq;

-- 创建新的规则表，支持19列标准格式
CREATE TABLE converter_rules (
    id NUMBER PRIMARY KEY,
    upload_id NUMBER NOT NULL,
    
    -- 标准化字段（对应Excel的19列）
    rule_source VARCHAR2(200),           -- 规则来源
    city VARCHAR2(50),                   -- 城市
    sequence_number NUMBER,              -- 序号
    department VARCHAR2(100),            -- 涉及科室
    violation_type VARCHAR2(100),        -- 行为认定
    rule_name CLOB,                      -- 规则名称
    rule_content CLOB,                   -- 规则内涵
    medical_name1 VARCHAR2(200),         -- 医保名称1
    medical_name2 VARCHAR2(200),         -- 医保名称2
    violation_count VARCHAR2(50),        -- 违规数量
    type VARCHAR2(100),                  -- 类型
    time_type VARCHAR2(50),              -- 时间类型
    item_count VARCHAR2(50),             -- 项目数量
    age VARCHAR2(50),                    -- 年龄
    gender VARCHAR2(20),                 -- 性别
    exclude_diagnosis CLOB,              -- 排除诊断
    exclude_department VARCHAR2(200),    -- 排除科室
    include_diagnosis CLOB,              -- 包含诊断
    other CLOB,                          -- 其他
    
    -- 元数据字段
    original_content CLOB,               -- 原始内容
    confidence NUMBER(3,2),              -- 置信度
    source_info CLOB,                    -- 来源信息
    status VARCHAR2(20) DEFAULT 'pending_review',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建序列
CREATE SEQUENCE converter_rules_seq START WITH 1 INCREMENT BY 1;

-- 创建索引
CREATE INDEX idx_converter_rules_upload ON converter_rules(upload_id);
CREATE INDEX idx_converter_rules_source ON converter_rules(rule_source);
CREATE INDEX idx_converter_rules_status ON converter_rules(status);

COMMIT;
