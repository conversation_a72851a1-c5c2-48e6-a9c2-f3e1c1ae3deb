{% extends "base.html" %}

{% block title %}飞检规则知识库 - MICRA飞检数据处理工具箱{% endblock %}

{% block page_title %}
<h4><i class="fas fa-book"></i> 飞检规则知识库</h4>
{% endblock %}

{% block content %}
<!-- 工具栏 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-tools"></i> 操作工具栏
                    </h6>
                    <div class="toolbar">
                        {% if current_user.has_permission('rules.knowledge_base.create') %}
                        <button onclick="addNewRule()" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增规则
                        </button>
                        {% endif %}
                        {% if current_user.has_permission('rules.knowledge_base.import') %}
                        <button onclick="showImportModal()" class="btn btn-success ms-2">
                            <i class="fas fa-file-excel"></i> 导入规则
                        </button>
                        {% endif %}
                        {% if current_user.has_permission('rules.knowledge_base.merge') %}
                        <button onclick="batchMergeRules()" class="btn btn-warning ms-2" id="batchMergeBtn" disabled>
                            <i class="fas fa-compress-arrows-alt"></i> 批量合并
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索筛选区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-search"></i> 规则搜索
                </h6>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="row">
                        <div class="col-md-2">
                            <label class="form-label">行为认定</label>
                            <select class="form-select" id="searchBehavior" name="behavior_type">
                                <option value="">全部</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">城市</label>
                            <select class="form-select" id="searchCity" name="city">
                                <option value="">全部</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">类型</label>
                            <select class="form-select" id="searchType" name="type">
                                <option value="">全部</option>
                                <option value="__unset__">未设置</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">规则类型</label>
                            <select class="form-select" id="searchRuleType" name="rule_type">
                                <option value="">全部</option>
                                <option value="__unset__">未设置</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">规则来源</label>
                            <select class="form-select" id="searchRuleSource" name="rule_source">
                                <option value="">全部</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">规则名称</label>
                            <input type="text" class="form-control" id="searchRuleName" name="rule_name" placeholder="输入规则名称">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="button" class="btn btn-primary me-2" onclick="searchRules()">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetSearch()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 规则列表区域 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list"></i> 规则列表 (共 <span id="totalCount">0</span> 条)
                </h6>
                <div>
                    <button class="btn btn-outline-primary btn-sm" onclick="selectAllRules()">
                        <i class="fas fa-check-square"></i> 全选
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                        <i class="fas fa-square"></i> 清空选择
                    </button>
                    <button class="btn btn-info btn-sm" onclick="refreshRules()">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="rulesTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                                </th>
                                <th width="4%">ID</th>
                                <th width="8%">规则来源</th>
                                <th width="4%">序号</th>
                                <th width="10%">规则名称</th>
                                <th width="4%">城市</th>
                                <th width="5%">类型</th>
                                <th width="6%">规则类型</th>
                                <th width="8%">行为认定</th>
                                <th width="4%">用途</th>
                                <th width="8%">医保编码1</th>
                                <th width="10%">医保名称1</th>
                                <th width="8%">医保编码2</th>
                                <th width="10%">医保名称2</th>
                                <th width="20%">规则内涵</th>
                                <th width="8%">操作</th>
                            </tr>
                        </thead>
                        <tbody id="rulesTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="规则列表分页">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 新增/编辑规则模态框 -->
<div class="modal fade" id="ruleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ruleModalTitle">
                    <i class="fas fa-plus"></i> 新增规则
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="ruleForm">
                    <input type="hidden" id="ruleId" name="id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">序号</label>
                                <input type="text" class="form-control" id="ruleSeq" name="序号">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">规则名称 *</label>
                                <input type="text" class="form-control" id="ruleName" name="规则名称" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">城市</label>
                                <input type="text" class="form-control" id="ruleCity" name="城市">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">类型</label>
                                <select class="form-select" id="ruleType" name="类型">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">规则类型</label>
                                <select class="form-select" id="ruleRuleType" name="规则类型">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">行为认定</label>
                                <select class="form-select" id="ruleBehavior" name="行为认定">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">规则来源</label>
                                <select class="form-select" id="ruleSource" name="规则来源">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">规则内涵</label>
                        <textarea class="form-control" id="ruleContent" name="规则内涵" rows="4"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">医保编码1</label>
                                <input type="text" class="form-control" id="medicalCode1" name="医保编码1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">医保名称1</label>
                                <input type="text" class="form-control" id="medicalName1" name="医保名称1">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">医保编码2</label>
                                <input type="text" class="form-control" id="medicalCode2" name="医保编码2">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">医保名称2</label>
                                <input type="text" class="form-control" id="medicalName2" name="医保名称2">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRule()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 导入规则模态框 -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导入规则</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-4">
                    <h6>第一步：下载模板</h6>
                    <a href="/rules/api/template" class="btn btn-outline-primary">
                        <i class="fas fa-download"></i> 下载Excel模板
                    </a>
                </div>
                <div class="mb-4">
                    <h6>第二步：选择文件</h6>
                    <div class="input-group">
                        <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls">
                    </div>
                    <small class="text-muted">支持.xlsx和.xls格式的Excel文件</small>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 提示：
                    <ul class="mb-0">
                        <li>请使用下载的模板填写数据</li>
                        <li>确保Excel中的列名与模板一致</li>
                        <li>日期格式请使用YYYY-MM-DD</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="handleImport()">
                    <i class="fas fa-upload"></i> 导入
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量合并模态框 -->
<div class="modal fade" id="batchMergeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量合并规则</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">选择目标规则</label>
                    <select class="form-select" id="targetRuleSelect">
                        <option value="">请选择目标规则</option>
                    </select>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    所有选中的规则将被合并到目标规则中，此操作不可撤销！
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="confirmBatchMerge()">确认合并</button>
            </div>
        </div>
    </div>
</div>

<!-- SQL测试模态框 -->
<div class="modal fade" id="sqlTestModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">SQL测试</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label class="form-label">数据库类型</label>
                        <select class="form-select" id="databaseType">
                            <option value="oracle">Oracle</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">数据库主机IP</label>
                        <input type="text" class="form-control" id="hostIP" placeholder="默认使用本地数据库">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">站点Schema</label>
                        <select class="form-select" id="schemaSelect">
                            <option value="">请先选择主机</option>
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">SQL语句</label>
                    <textarea class="form-control" id="sqlContent" rows="8" placeholder="请输入SQL语句..."></textarea>
                </div>
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="executeSQLTest()">
                        <i class="fas fa-play"></i> 执行查询
                    </button>
                    <button class="btn btn-secondary" onclick="loadSchemas()">
                        <i class="fas fa-sync"></i> 刷新Schema
                    </button>
                </div>
                <div id="sqlTestResult" class="mt-3" style="display: none;">
                    <h6>查询结果：</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered" id="sqlResultTable">
                            <thead id="sqlResultHead"></thead>
                            <tbody id="sqlResultBody"></tbody>
                        </table>
                    </div>
                    <div id="sqlResultInfo" class="text-muted"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
let currentRuleId = null;
let selectedRules = new Set();

// 页面加载完成后初始化
$(document).ready(function() {
    loadFilterOptions();
    loadRules(1);
});

// 加载筛选选项
function loadFilterOptions() {
    // 加载行为认定类型
    loadSelectOptions('/rules/api/behavior_types', '#searchBehavior');
    loadSelectOptions('/rules/api/behavior_types', '#ruleBehavior');

    // 加载城市列表
    loadSelectOptions('/rules/api/city_types', '#searchCity');

    // 加载规则来源
    loadSelectOptions('/rules/api/rule_sources', '#searchRuleSource');
    loadSelectOptions('/rules/api/rule_sources', '#ruleSource');

    // 加载类型选项
    loadSelectOptions('/rules/api/type_types', '#searchType');
    loadSelectOptions('/rules/api/type_types', '#ruleType');

    // 加载规则类型选项
    loadSelectOptions('/rules/api/rule_type_types', '#searchRuleType');
    loadSelectOptions('/rules/api/rule_type_types', '#ruleRuleType');
}

// 通用加载选项函数
function loadSelectOptions(url, selectId) {
    $.get(url, function(response) {
        if (response.success && Array.isArray(response.types)) {
            const $select = $(selectId);
            if (selectId.includes('search')) {
                $select.empty();
                $select.append('<option value="">全部</option>');
                if (selectId === '#searchType' || selectId === '#searchRuleType') {
                    $select.append('<option value="__unset__">未设置</option>');
                }
            } else {
                $select.find('option:not(:first)').remove();
            }
            response.types.forEach(function(item) {
                $select.append('<option value="' + item + '">' + item + '</option>');
            });
        }
    }).fail(function() {
        console.error('Failed to load options for', selectId);
    });
}

// 加载规则列表
function loadRules(page = 1) {
    currentPage = page;
    showLoading();

    $.get('/rules/api/rules', {
        page: page,
        per_page: 20
    }, function(response) {
        hideLoading();
        if (response.success) {
            displayRules(response.rules || []);
            updateTotalCount(response.total || 0);
            displayPagination(response);
        } else {
            showAlert('加载规则列表失败: ' + (response.error || '未知错误'), 'danger');
        }
    }).fail(function(xhr) {
        hideLoading();
        console.error('API Error:', xhr.responseText);
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

// 显示规则列表
function displayRules(rules) {
    const tbody = $('#rulesTableBody');
    tbody.empty();

    if (!Array.isArray(rules) || rules.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="16" class="text-center text-muted">暂无数据</td>
            </tr>
        `);
        return;
    }

    rules.forEach((rule, index) => {
        const ruleId = rule.ID || rule.id || index;
        tbody.append(`
            <tr>
                <td>
                    <input type="checkbox" class="rule-checkbox" value="${ruleId}" onchange="handleCheckboxChange()">
                </td>
                <td>${rule.ID || rule.id || ''}</td>
                <td>${formatText(rule.规则来源 || rule['规则来源'] || '')}</td>
                <td>${rule.序号 || rule['序号'] || ''}</td>
                <td>${formatText(rule.规则名称 || rule['规则名称'] || '')}</td>
                <td>${formatText(rule.城市 || rule['城市'] || '')}</td>
                <td>${formatText(rule.类型 || rule['类型'] || '')}</td>
                <td>${formatText(rule.规则类型 || rule['规则类型'] || '')}</td>
                <td>${formatText(rule.行为认定 || rule['行为认定'] || '')}</td>
                <td>${formatText(rule.用途 || rule['用途'] || '')}</td>
                <td>${formatText(rule.医保编码1 || rule['医保编码1'] || '')}</td>
                <td>${formatText(rule.医保名称1 || rule['医保名称1'] || '')}</td>
                <td>${formatText(rule.医保编码2 || rule['医保编码2'] || '')}</td>
                <td>${formatText(rule.医保名称2 || rule['医保名称2'] || '')}</td>
                <td>${formatText(rule.规则内涵 || rule['规则内涵'] || '', 100)}</td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-info" onclick="viewRule(${ruleId})" title="查看">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editRule(${ruleId})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteRule(${ruleId})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="testRuleSQL(${ruleId})" title="测试SQL">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
    });
}

// 格式化文本显示
function formatText(text, maxLength = 50) {
    if (!text) return '';
    const formatted = text.toString().replace(/,/g, '<br>');
    if (maxLength && formatted.length > maxLength) {
        return `<span title="${text}">${formatted.substring(0, maxLength)}...</span>`;
    }
    return formatted;
}

// 更新总数显示
function updateTotalCount(count) {
    $('#totalCount').text(count);
}

// 显示分页
function displayPagination(data) {
    const pagination = $('#pagination');
    pagination.empty();

    if (data.pages <= 1) return;

    // 上一页
    if (data.page > 1) {
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadRules(${data.page - 1})">上一页</a>
            </li>
        `);
    }

    // 页码
    for (let i = Math.max(1, data.page - 2); i <= Math.min(data.pages, data.page + 2); i++) {
        pagination.append(`
            <li class="page-item ${i === data.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadRules(${i})">${i}</a>
            </li>
        `);
    }

    // 下一页
    if (data.page < data.pages) {
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadRules(${data.page + 1})">下一页</a>
            </li>
        `);
    }
}

// 搜索规则
function searchRules() {
    const formData = new FormData(document.getElementById('searchForm'));
    const params = new URLSearchParams();

    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            params.append(key, value);
        }
    }

    showLoading();

    fetch(`/rules/api/rules?${params}&page=1&per_page=20`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                displayRules(data.rules || data.data || []);
                updateTotalCount(data.total || 0);
                displayPagination(data);
                currentPage = 1;
            } else {
                showAlert('搜索失败: ' + (data.error || '未知错误'), 'danger');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('搜索失败:', error);
            showAlert('搜索失败: ' + error.message, 'danger');
        });
}

// 重置搜索
function resetSearch() {
    document.getElementById('searchForm').reset();
    loadRules(1);
}

// 查看规则详情
function viewRule(ruleId) {
    currentRuleId = ruleId;

    $.get(`/rules/api/rules/${ruleId}`, function(response) {
        if (response.success && response.data.length > 0) {
            const rule = response.data[0];
            displayRuleDetail(rule);
            $('#ruleDetailModal').modal('show');
        } else {
            showAlert('获取规则详情失败', 'danger');
        }
    });
}

// 显示规则详情
function displayRuleDetail(rule) {
    const content = $('#ruleDetailContent');
    content.html(`
        <div class="row">
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr><th>规则ID:</th><td>${rule.ID || ''}</td></tr>
                    <tr><th>序号:</th><td>${rule.序号 || ''}</td></tr>
                    <tr><th>规则名称:</th><td>${rule.规则名称 || ''}</td></tr>
                    <tr><th>行为认定:</th><td>${rule.行为认定 || ''}</td></tr>
                    <tr><th>适用范围:</th><td>${rule.适用范围 || ''}</td></tr>
                    <tr><th>城市:</th><td>${rule.城市 || ''}</td></tr>
                    <tr><th>规则来源:</th><td>${rule.规则来源 || ''}</td></tr>
                    <tr><th>类型:</th><td>${rule.类型 || ''}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr><th>医保编码1:</th><td>${rule.医保编码1 || ''}</td></tr>
                    <tr><th>医保名称1:</th><td>${rule.医保名称1 || ''}</td></tr>
                    <tr><th>医保编码2:</th><td>${rule.医保编码2 || ''}</td></tr>
                    <tr><th>医保名称2:</th><td>${rule.医保名称2 || ''}</td></tr>
                    <tr><th>违规数量:</th><td>${rule.违规数量 || ''}</td></tr>
                    <tr><th>违规金额:</th><td>${rule.违规金额 || ''}</td></tr>
                    <tr><th>年龄:</th><td>${rule.年龄 || ''}</td></tr>
                    <tr><th>性别:</th><td>${rule.性别 || ''}</td></tr>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <h6>规则内涵:</h6>
                <p class="border p-3">${rule.规则内涵 || ''}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <h6>包含科室:</h6>
                <p class="border p-2">${rule.包含科室 || ''}</p>
            </div>
            <div class="col-md-6">
                <h6>排除科室:</h6>
                <p class="border p-2">${rule.排除科室 || ''}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <h6>包含诊断:</h6>
                <p class="border p-2">${rule.包含诊断 || ''}</p>
            </div>
            <div class="col-md-6">
                <h6>排除诊断:</h6>
                <p class="border p-2">${rule.排除诊断 || ''}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <h6>备注:</h6>
                <p class="border p-2">${rule.备注 || ''}</p>
            </div>
        </div>
    `);
}

// 新增规则
function addNewRule() {
    currentRuleId = null;
    document.getElementById('ruleModalTitle').innerHTML = '<i class="fas fa-plus"></i> 新增规则';
    document.getElementById('ruleForm').reset();

    const modal = new bootstrap.Modal(document.getElementById('ruleModal'));
    modal.show();
}

// 编辑规则
function editRule(ruleId) {
    currentRuleId = ruleId;
    document.getElementById('ruleModalTitle').innerHTML = '<i class="fas fa-edit"></i> 编辑规则';

    showLoading();

    fetch(`/rules/api/rules/${ruleId}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success && data.data && data.data.length > 0) {
                const rule = data.data[0];
                fillRuleForm(rule);

                const modal = new bootstrap.Modal(document.getElementById('ruleModal'));
                modal.show();
            } else {
                showAlert('获取规则详情失败', 'danger');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('获取规则详情失败:', error);
            showAlert('获取规则详情失败: ' + error.message, 'danger');
        });
}

// 填充规则表单
function fillRuleForm(rule) {
    document.getElementById('ruleId').value = rule.ID || rule.id || '';
    document.getElementById('ruleSeq').value = rule.序号 || rule['序号'] || '';
    document.getElementById('ruleName').value = rule.规则名称 || rule['规则名称'] || '';
    document.getElementById('ruleCity').value = rule.城市 || rule['城市'] || '';
    document.getElementById('ruleType').value = rule.类型 || rule['类型'] || '';
    document.getElementById('ruleRuleType').value = rule.规则类型 || rule['规则类型'] || '';
    document.getElementById('ruleBehavior').value = rule.行为认定 || rule['行为认定'] || '';
    document.getElementById('ruleSource').value = rule.规则来源 || rule['规则来源'] || '';
    document.getElementById('ruleContent').value = rule.规则内涵 || rule['规则内涵'] || '';
    document.getElementById('medicalCode1').value = rule.医保编码1 || rule['医保编码1'] || '';
    document.getElementById('medicalName1').value = rule.医保名称1 || rule['医保名称1'] || '';
    document.getElementById('medicalCode2').value = rule.医保编码2 || rule['医保编码2'] || '';
    document.getElementById('medicalName2').value = rule.医保名称2 || rule['医保名称2'] || '';
}

// 保存规则
function saveRule() {
    const form = document.getElementById('ruleForm');
    const formData = new FormData(form);
    const ruleData = {};

    for (let [key, value] of formData.entries()) {
        ruleData[key] = value;
    }

    const url = currentRuleId ? `/rules/api/rules/${currentRuleId}` : '/rules/api/rules';
    const method = currentRuleId ? 'PUT' : 'POST';

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    showLoading();

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(ruleData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert(currentRuleId ? '规则更新成功' : '规则创建成功', 'success');

            const modal = bootstrap.Modal.getInstance(document.getElementById('ruleModal'));
            modal.hide();

            refreshRules();
        } else {
            showAlert('保存失败: ' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('保存失败:', error);
        showAlert('保存失败: ' + error.message, 'danger');
    });
}

// 删除规则
function deleteRule(ruleId) {
    if (!confirm('确定要删除这条规则吗？此操作不可撤销！')) {
        return;
    }

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    showLoading();

    fetch(`/rules/api/rules/${ruleId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert('规则删除成功', 'success');
            refreshRules();
        } else {
            showAlert('删除失败: ' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('删除失败:', error);
        showAlert('删除失败: ' + error.message, 'danger');
    });
}

// 刷新规则列表
function refreshRules() {
    loadRules(currentPage);
}

// 显示加载指示器
function showLoading() {
    if (!document.getElementById('loadingIndicator')) {
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loadingIndicator';
        loadingDiv.className = 'position-fixed top-50 start-50 translate-middle';
        loadingDiv.style.zIndex = '9999';
        loadingDiv.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        `;
        document.body.appendChild(loadingDiv);
    }
    document.getElementById('loadingIndicator').style.display = 'block';
}

// 隐藏加载指示器
function hideLoading() {
    const loadingDiv = document.getElementById('loadingIndicator');
    if (loadingDiv) {
        loadingDiv.style.display = 'none';
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const checkAll = document.getElementById('selectAll').checked;
    const checkboxes = document.getElementsByClassName('rule-checkbox');
    selectedRules.clear();

    for (let i = 0; i < checkboxes.length; i++) {
        checkboxes[i].checked = checkAll;
        if (checkAll) {
            selectedRules.add(checkboxes[i].value);
        }
    }
    updateBatchButtons();
}

// 选择所有规则
function selectAllRules() {
    document.getElementById('selectAll').checked = true;
    toggleSelectAll();
}

// 清空选择
function clearSelection() {
    document.getElementById('selectAll').checked = false;
    toggleSelectAll();
}

// 处理复选框变化
function handleCheckboxChange() {
    const checkboxes = document.getElementsByClassName('rule-checkbox');
    selectedRules.clear();
    let checkedCount = 0;

    for (let i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].checked) {
            selectedRules.add(checkboxes[i].value);
            checkedCount++;
        }
    }

    document.getElementById('selectAll').checked = (checkedCount > 0 && checkedCount === checkboxes.length);
    updateBatchButtons();
}

// 更新批量操作按钮状态
function updateBatchButtons() {
    const batchMergeBtn = document.getElementById('batchMergeBtn');
    if (batchMergeBtn) {
        batchMergeBtn.disabled = selectedRules.size < 2;
    }
}

// 显示导入模态框
function showImportModal() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

// 处理导入
function handleImport() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];

    if (!file) {
        showAlert('请选择要导入的文件', 'warning');
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    showLoading();

    fetch('/rules/api/import', {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert(`导入成功，共导入 ${data.count || 0} 条规则`, 'success');

            const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
            modal.hide();

            refreshRules();
        } else {
            showAlert('导入失败: ' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('导入失败:', error);
        showAlert('导入失败: ' + error.message, 'danger');
    });
}

// 批量合并规则
function batchMergeRules() {
    if (selectedRules.size < 2) {
        showAlert('请至少选择2条规则进行合并', 'warning');
        return;
    }

    // 填充目标规则选择框
    const targetSelect = document.getElementById('targetRuleSelect');
    targetSelect.innerHTML = '<option value="">请选择目标规则</option>';

    selectedRules.forEach(ruleId => {
        // 这里应该从当前显示的规则中找到对应的规则名称
        const checkbox = document.querySelector(`input[value="${ruleId}"]`);
        if (checkbox) {
            const row = checkbox.closest('tr');
            const ruleName = row.cells[4].textContent; // 规则名称列
            targetSelect.innerHTML += `<option value="${ruleId}">${ruleName}</option>`;
        }
    });

    const modal = new bootstrap.Modal(document.getElementById('batchMergeModal'));
    modal.show();
}

// 确认批量合并
function confirmBatchMerge() {
    const targetRuleId = document.getElementById('targetRuleSelect').value;

    if (!targetRuleId) {
        showAlert('请选择目标规则', 'warning');
        return;
    }

    const sourceRuleIds = Array.from(selectedRules).filter(id => id !== targetRuleId);

    if (sourceRuleIds.length === 0) {
        showAlert('没有可合并的源规则', 'warning');
        return;
    }

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    showLoading();

    fetch('/rules/api/merge', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            target_rule_id: targetRuleId,
            source_rule_ids: sourceRuleIds
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert(`合并成功，共合并 ${sourceRuleIds.length} 条规则`, 'success');

            const modal = bootstrap.Modal.getInstance(document.getElementById('batchMergeModal'));
            modal.hide();

            clearSelection();
            refreshRules();
        } else {
            showAlert('合并失败: ' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('合并失败:', error);
        showAlert('合并失败: ' + error.message, 'danger');
    });
}

// SQL测试功能
function testRuleSQL(ruleId) {
    // 获取规则详情
    fetch(`/rules/api/rules/${ruleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data && data.data.length > 0) {
                const rule = data.data[0];

                // 生成基础SQL模板
                const sqlTemplate = generateSQLTemplate(rule);
                document.getElementById('sqlContent').value = sqlTemplate;

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('sqlTestModal'));
                modal.show();

                // 加载Schema列表
                loadSchemas();
            } else {
                showAlert('获取规则详情失败', 'danger');
            }
        })
        .catch(error => {
            console.error('获取规则详情失败:', error);
            showAlert('获取规则详情失败: ' + error.message, 'danger');
        });
}

// 生成SQL模板
function generateSQLTemplate(rule) {
    const medicalCode1 = rule.医保编码1 || rule['医保编码1'] || '';
    const medicalName1 = rule.医保名称1 || rule['医保名称1'] || '';
    const ruleName = rule.规则名称 || rule['规则名称'] || '';

    let sql = `-- 规则: ${ruleName}\n`;
    sql += `SELECT * FROM (\n`;
    sql += `    SELECT \n`;
    sql += `        患者姓名,\n`;
    sql += `        就诊号,\n`;
    sql += `        项目名称,\n`;
    sql += `        项目编码,\n`;
    sql += `        费用,\n`;
    sql += `        开单科室,\n`;
    sql += `        开单医生,\n`;
    sql += `        开单时间\n`;
    sql += `    FROM 医疗费用明细表\n`;
    sql += `    WHERE 1=1\n`;

    if (medicalCode1) {
        sql += `        AND 项目编码 = '${medicalCode1}'\n`;
    }
    if (medicalName1) {
        sql += `        AND 项目名称 LIKE '%${medicalName1}%'\n`;
    }

    sql += `        AND 开单时间 >= SYSDATE - 30\n`;
    sql += `    ORDER BY 开单时间 DESC\n`;
    sql += `) WHERE ROWNUM <= 10`;

    return sql;
}

// 加载Schema列表
function loadSchemas() {
    const databaseType = document.getElementById('databaseType').value;
    const hostIP = document.getElementById('hostIP').value || 'default';

    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    fetch('/rules/api/database/schemas', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            database_type: databaseType,
            host_ip: hostIP
        })
    })
    .then(response => response.json())
    .then(data => {
        const schemaSelect = document.getElementById('schemaSelect');
        schemaSelect.innerHTML = '<option value="">请选择Schema</option>';

        if (data.success && Array.isArray(data.schemas)) {
            data.schemas.forEach(schema => {
                schemaSelect.innerHTML += `<option value="${schema}">${schema}</option>`;
            });
        } else {
            showAlert('获取Schema列表失败: ' + (data.error || '未知错误'), 'warning');
        }
    })
    .catch(error => {
        console.error('获取Schema列表失败:', error);
        showAlert('获取Schema列表失败: ' + error.message, 'danger');
    });
}

// 执行SQL测试
function executeSQLTest() {
    const sql = document.getElementById('sqlContent').value.trim();
    const databaseType = document.getElementById('databaseType').value;
    const hostIP = document.getElementById('hostIP').value || 'default';
    const schema = document.getElementById('schemaSelect').value;

    if (!sql) {
        showAlert('请输入SQL语句', 'warning');
        return;
    }

    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    showLoading();

    fetch('/rules/api/rules/test-sql', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            sql: sql,
            database_type: databaseType,
            host_ip: hostIP,
            schema: schema
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            displaySQLResult(data.result);
        } else {
            showAlert('SQL执行失败: ' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('SQL执行失败:', error);
        showAlert('SQL执行失败: ' + error.message, 'danger');
    });
}

// 显示SQL执行结果
function displaySQLResult(result) {
    const resultDiv = document.getElementById('sqlTestResult');
    const resultHead = document.getElementById('sqlResultHead');
    const resultBody = document.getElementById('sqlResultBody');
    const resultInfo = document.getElementById('sqlResultInfo');

    // 清空之前的结果
    resultHead.innerHTML = '';
    resultBody.innerHTML = '';

    if (result.columns && result.columns.length > 0) {
        // 显示表头
        const headerRow = document.createElement('tr');
        result.columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column;
            headerRow.appendChild(th);
        });
        resultHead.appendChild(headerRow);

        // 显示数据
        if (result.data && result.data.length > 0) {
            result.data.forEach(row => {
                const dataRow = document.createElement('tr');
                result.columns.forEach((column, index) => {
                    const td = document.createElement('td');
                    td.textContent = row[index] || '';
                    dataRow.appendChild(td);
                });
                resultBody.appendChild(dataRow);
            });
        } else {
            const noDataRow = document.createElement('tr');
            const noDataCell = document.createElement('td');
            noDataCell.colSpan = result.columns.length;
            noDataCell.textContent = '查询无结果';
            noDataCell.className = 'text-center text-muted';
            noDataRow.appendChild(noDataCell);
            resultBody.appendChild(noDataRow);
        }

        // 显示查询信息
        resultInfo.textContent = `数据库: ${result.database || ''}, 影响行数: ${result.affected_rows || 0}`;
    } else {
        resultInfo.textContent = '查询执行成功，但无返回数据';
    }

    // 显示结果区域
    resultDiv.style.display = 'block';
}

// 监听主机IP变化，自动加载Schema
document.addEventListener('DOMContentLoaded', function() {
    const hostIPInput = document.getElementById('hostIP');
    if (hostIPInput) {
        hostIPInput.addEventListener('blur', function() {
            if (this.value.trim()) {
                loadSchemas();
            }
        });
    }
});

// 使用base.html中的统一showAlert函数
</script>
{% endblock %}
